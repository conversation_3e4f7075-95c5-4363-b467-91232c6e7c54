import React, { createContext, useState, useEffect, ReactNode } from 'react';
import { Dimensions, Platform, Text, TouchableOpacity, View, ScaledSize } from 'react-native';
import { HelperMemo } from './source/Helper';
import I18n from './source/I18n';
import { Tme, Colors } from './source/ThemeStyle';
import { mainRadius } from './source/Tools';
import PubSub from 'pubsub-js';
import { hideLoading, showLoading } from './ILoading';
import { DashboardHelper } from './source/Helper';
import AlertModal from './source/share/AlertModal';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { PubSubEvent } from './source/types/PubSubEvent';
import { useErrorHandler } from './source/share/useErrorHandler';

interface ScreenSizeContextType {
  winWidth: number;
  winHeight: number;
  width: number;
  viewHeight: number;
}

interface ScreenSizeProviderProps {
  children: ReactNode;
}

const ScreenSizeContext = createContext<ScreenSizeContextType>({} as ScreenSizeContextType);

const initWidth = (window: ScaledSize): number => {
  if (Platform.OS == 'android') {
    return window.width;
  } else {
    if (window.width > window.height) {
      return (
        window.width -
        HelperMemo.STATUS_BAR_HEIGHT -
        HelperMemo.BOTTOM_BAR_HEIGHT -
        22
      );
    } else {
      return window.width;
    }
  }
};

export const ScreenSizeProvider: React.FC<ScreenSizeProviderProps> = ({ children }) => {
  const [screenSize, setScreenSize] = useState({
    winWidth: initWidth(Dimensions.get('window')),
    winHeight: Dimensions.get('window').height,
    width: Dimensions.get('window').width,
    viewHeight: Dimensions.get('window').height,
  });
  const {showError, errorMessage, hideErrorView} = useErrorHandler();

  const handleResize = ({ window }: { window: ScaledSize }) => {
    setScreenSize({
      winWidth: initWidth(window),
      winHeight: window.height,
      width: window.width,
      viewHeight: window.height,
    });
  };

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', handleResize);

    return () => {
      if (subscription) {
        subscription.remove();
      }
    };
  }, []);

  const doReFetchData = () => {
    PubSub.publish(PubSubEvent.ERROR_REFETCH);
  };

  const touchRow = () => {
    showLoading();
    const sn = HelperMemo.user_data.sn;
    const dashboard = HelperMemo.dashboard;
    if (!dashboard) {
      hideLoading();
      return;
    }
    var body = {
      sn: dashboard.sn,
      salt: sn ? sn.salt : '',
    };
    HelperMemo.dashboard_ip = dashboard.ip;
    DashboardHelper.httpPOST(
      '/auth',
      {
        ensure: () => {
          hideLoading();
        },
        success: (data: { sessionid?: string; need_set_password?: boolean }) => {
          if (data.sessionid) {
            HelperMemo.dashboard_sessionid = data.sessionid;
            if (data.need_set_password) {
              hideErrorView();
              PubSub.publish(PubSubEvent.DASHBOARD_EVENT, {
                type: 'DashboardSignUp',
              });
            } else {
              hideErrorView();
              PubSub.publish(PubSubEvent.DASHBOARD_EVENT, {
                type: 'DashboardScreen',
              });
            }
          } else {
            AlertModal.alert(I18n.t('home.failed'), I18n.t('home.try_again'));
          }
        },
        error: (data: string) => {
          authError(data);
        },
      },
      body,
    );
  };

  const authError = (data: string) => {
    switch (data) {
      case 'sn_auth_failed':
      case 'sn_salt_empty':
        AlertModal.alert(I18n.t('home.error'), data);
        break;
      case 'passwd_auth_failed':
        AlertModal.alert('dashboard.pin_error');
        break;
      case 'no_initial_password':
        AlertModal.alert('dashboard.no_initial_password');
        break;
    }
  };

  return (
    <ScreenSizeContext.Provider
      value={{
        ...screenSize,
      }}>
      {showError && (
        <View
          style={{
            position: 'absolute',
            width: screenSize.winWidth,
            height: screenSize.viewHeight + 100,
            zIndex: 99,
          }}>
          <View
            style={{
              flex: 1,
              backgroundColor: Tme('bgColor'),
              padding: 20,
            }}>
            <View style={{ marginTop: 168, alignItems: 'center' }}>
              <Ionicons
                name="alert-circle-outline"
                size={40}
                color={Tme('smallTextColor')}
                style={{ marginBottom: 20 }}
              />
              <Text style={{ fontSize: 14, color: Tme('smallTextColor') }}>
                {errorMessage === 'system'
                  ? I18n.t('global.error_desp')
                  : errorMessage}
              </Text>
              <TouchableOpacity
                onPress={doReFetchData}
                activeOpacity={0.8}
                style={{
                  marginTop: 40,
                  paddingHorizontal: 20,
                  paddingVertical: 8,
                  borderRadius: mainRadius(),
                  borderWidth: 1,
                  borderColor: Tme('smallTextColor'),
                  borderStyle: 'solid',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text style={{ color: Tme('smallTextColor') }}>
                  {I18n.t('home.click_try_again')}
                </Text>
              </TouchableOpacity>
              {HelperMemo.dashboard && (
                <TouchableOpacity
                  onPress={() => {
                    touchRow();
                  }}
                  activeOpacity={0.8}
                  style={{
                    marginTop: 40,
                    paddingHorizontal: 40,
                    paddingVertical: 10,
                    borderRadius: mainRadius(),
                    backgroundColor: Colors.MainColor,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text style={{ color: '#fff' }}>
                    {I18n.t('global.dashboard')}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
        // </Modal>
      )}
      {children}
    </ScreenSizeContext.Provider>
  );
};

export default ScreenSizeContext;
