{"name": "presen", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "postinstall": "patch-package", "test": "jest", "lint": "eslint .", "androidRelease": "cd ./android && ./gradlew app:assembleRelease", "build:ios": "detox build --configuration ios.sim.debug", "build:android-debug": "detox build --configuration android.emu.debug", "build:android-release": "detox build --configuration android.emu.release", "test:ios": "detox test --reuse --configuration ios.sim.debug", "test:android-debug": "detox test --reuse --configuration android.emu.debug", "test:android-release": "detox test --reuse --configuration android.emu.release", "test:android-release-ci": "detox test --configuration android.emu.release -l verbose --headless --record-logs all --take-screenshots all", "e2e:ios": "npm run build:ios && npm run test:ios", "e2e:android-debug": "npm run build:android-debug && npm run test:android-debug", "e2e:android-release": "npm run build:android-release && npm run test:android-release"}, "dependencies": {"@alessiocancian/react-native-actionsheet": "^3.2.0", "@gorhom/bottom-sheet": "^4", "@notifee/react-native": "^9.1.8", "@openspacelabs/react-native-zoomable-view": "^2.3.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-camera-roll/camera-roll": "^7.10.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-community/segmented-control": "^2.2.2", "@react-native-community/slider": "^4.5.2", "@react-native-firebase/app": "^21.12.2", "@react-native-firebase/messaging": "^21.12.2", "@react-native-picker/picker": "^2.2.1", "@react-native-segmented-control/segmented-control": "^2.5.2", "@react-navigation/bottom-tabs": "^7.2.1", "@react-navigation/elements": "^2.2.6", "@react-navigation/native": "^7.0.15", "@react-navigation/native-stack": "^7.2.1", "@shopify/flash-list": "^1.7.3", "@stripe/stripe-react-native": "^0.38.4", "@types/markdown-it": "^10.0.3", "@types/supercluster": "^5.0.3", "ahooks": "^3.7.10", "axios": "^1.2.2", "base-64": "^1.0.0", "buffer": "^6.0.3", "deprecated-react-native-prop-types": "^4.0.0", "lodash": "^4.17.20", "memoize-one": "^5.1.1", "mobx": "^5.9.4", "mobx-react": "^5.4.3", "moment": "^2.29.1", "moment-timezone": "0.5.27", "paho-mqtt": "^1.1.0", "prop-types": "^15.7.2", "pubsub-js": "^1.9.4", "react": "19.0.0", "react-native": "0.79.1", "react-native-amap-geolocation": "../react-native-amap-geolocation", "react-native-amap3d": "../react-native-amap3d", "react-native-android-widget": "^0.16.1", "react-native-background-fetch": "^4.2.7", "react-native-background-geolocation": "^4.18.6", "react-native-camera-kit": "^14.2.0", "react-native-color-matrix-image-filters": "^6.0.9", "react-native-confirmation-code-field": "^7.4.0", "react-native-date-picker": "^5.0.2", "react-native-device-info": "^14.0.4", "react-native-fast-image": "^8.5.11", "react-native-fs": "^2.18.0", "react-native-gesture-handler": "^2.25.0", "react-native-i18n": "^2.0.15", "react-native-idle-timer": "^2.1.6", "react-native-image-zoom-viewer": "^3.0.1", "react-native-incall-manager": "^4.2.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.5.6", "react-native-localize": "^3.4.1", "react-native-maps": "^1.15.6", "react-native-markdown-display": "6.1.6", "react-native-orientation-locker": "^1.6.0", "react-native-pager-view": "^6.7.1", "react-native-paper": "^5.13.5", "react-native-permissions": "^4.1.5", "react-native-progress": "^5.0.0", "react-native-qrcode-svg": "^6.1.1", "react-native-reanimated": "^3.17.4", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.9.1", "react-native-shadow-2": "^7.0.6", "react-native-shared-group-preferences": "^1.1.23", "react-native-svg": "^15.11.2", "react-native-tab-view": "^3.1.1", "react-native-udp": "^4.1.7", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.6.2", "react-native-video-player": "^0.16.3", "react-native-webrtc": "^124.0.5", "react-native-webview": "^13.10.5", "react-native-widget-center": "^0.0.9", "react-native-wifi-reborn": "^4.12.1", "react-native-zeroconf": "^0.13.8", "react-navigation-header-buttons": "10.0.0", "rn-placeholder": "^3.0.3", "tinycolor2": "^1.4.2", "zustand": "^4.5.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.1", "@react-native/codegen": "^0.79.2", "@react-native/eslint-config": "0.79.1", "@react-native/metro-config": "0.79.1", "@react-native/typescript-config": "0.79.1", "@types/base-64": "^1.0.2", "@types/jest": "^29.5.13", "@types/lodash": "^4.17.16", "@types/node": "^22.14.1", "@types/pubsub-js": "^1.8.6", "@types/react": "^19.0.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "@types/tinycolor2": "^1.4.6", "babel-jest": "^29.6.3", "babel-plugin-import": "^1.13.1", "babel-plugin-inline-import": "^3.0.0", "babel-plugin-transform-remove-console": "^6.9.4", "detox": "^17.13.1", "detox-recorder": "^1.0.149", "enzyme": "^3.11.0", "eslint": "^8.19.0", "jest": "^29.6.3", "jest-circus": "^26.6.3", "jest-environment-enzyme": "^7.1.2", "jest-environment-node": "^26.6.2", "jest-enzyme": "^7.1.2", "jetifier": "^2.0.0", "mocha": "^8.2.1", "patch-package": "^6.5.1", "postinstall-postinstall": "^2.1.0", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}