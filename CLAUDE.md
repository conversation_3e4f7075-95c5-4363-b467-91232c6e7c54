# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a React Native application called "presen" - a smart home/IoT control app with device management, automation, and mapping capabilities. The app supports both iOS and Android platforms with native modules for geolocation (AMap) and device communication.

## Development Commands

### Setup

```bash
# Install dependencies
yarn install

# Install iOS pods (M1 Mac requires x86_64 architecture)
npx pod-install
# or for M1 Mac: arch -x86_64 pod install

# Copy development config (required)
cp app_config.dev.sample.ts app_config.dev.ts
```

### Development

```bash
# Start Metro bundler
yarn start

# Run on specific platforms
yarn android      # Run on Android
yarn ios         # Run on iOS

# Build releases
yarn androidRelease  # Build Android release
```

### Testing

```bash
# Unit tests
yarn test

# E2E tests with Detox
yarn e2e:ios           # Build and run iOS E2E tests
yarn e2e:android-debug # Build and run Android debug E2E tests
yarn e2e:android-release # Build and run Android release E2E tests

# Individual commands
yarn build:ios           # Build iOS test app
yarn test:ios            # Run iOS tests
yarn build:android-debug # Build Android debug test app
yarn test:android-debug  # Run Android debug tests
```

### Code Quality

```bash
# Linting
yarn lint

# Type checking (if TypeScript files are present)
# Note: This project primarily uses JavaScript with some TypeScript definitions
```

## Architecture

### Core Structure

- **App.js**: Main application entry point with navigation setup and internationalization
- **source/**: Main application source code directory
  - **home/**: Home screen and dashboard components
  - **device/**: Device management and IPC (Inter-Process Communication) features
  - **smart/**: Smart home automation and scenes
  - **setting/**: App settings and configuration
  - **scenes/**: Scene management functionality
  - **action/**: Action and automation components
  - **session/**: User authentication (login/signup)
  - **share/**: Reusable UI components and utilities
  - **amap/**: AMap (Chinese mapping service) integration with custom components
  - **utils/**: Utility functions and helpers

### Key Technologies

- **React Native 0.79.1** with React 19.0.0
- **Navigation**: React Navigation v7 with bottom tabs and native stack
- **State Management**: MobX for state management, Zustand for newer features
- **Maps**: Custom AMap integration for Chinese market, react-native-maps for international
- **Styling**: Custom theme system with dark mode support
- **Testing**: Jest for unit tests, Detox for E2E testing
- **Push Notifications**: Firebase Cloud Messaging (international) and JPush (Chinese market)

### Native Modules

- **react-native-amap-geolocation**: Custom AMap geolocation module
- **react-native-amap3d**: Custom AMap 3D mapping module
- **Background services**: Background geolocation and fetch for device monitoring

### Configuration

- **app_config.ts**: Main application configuration
- **app_config.dev.ts**: Development-specific configuration
- Environment-specific builds for Chinese vs International markets

## Build and Deployment

### Android Builds

- Use `MARKET=cn` environment variable for Chinese market builds (affects push notification provider)
- Chinese market uses JPush, international uses Firebase Cloud Messaging

### CodePush Integration

```bash
# Install AppCenter CLI
npm install -g appcenter-cli

# Login and deploy updates
appcenter login
appcenter codepush release-react -a presen-dev-qq.com/presen-ios -d Production
appcenter codepush release-react -a presen-dev-qq.com/presen-android -d Production
```

## Development Notes

### Multi-Market Support

The app supports both Chinese and international markets with different:

- Map providers (AMap for China, Google Maps for international)
- Push notification providers (JPush for China, FCM for international)
- App store configurations

### IPC Device Integration

- Includes UDP communication for IPC (Inter-Process Communication) devices
- Device discovery and management through WiFi
- Firmware update capabilities via S3 upload

### Theme System

- Custom theme implementation with dark mode support
- Golden color scheme: rgb(225,191,92), rgb(248,214,117)
- Consistent styling across components

### Testing Requirements

- E2E tests require iOS simulator setup with specific environment configuration
- Test configuration files need to be copied from samples (e2e/app_config.sample.js → e2e/app_config.js)
