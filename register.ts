import Country from './source/Country';
import SettingRoom from './source/room/SettingRoom';

import RoomShow from './source/room/RoomShow';
import EventScreen from './source/event/EventScreen';
import AddDevice from './source/device/AddDevice';
import ChangeController from './source/setting/ChangeController';
import UserScreen from './source/user/UserScreen';
import UserShowView from './source/user/UserShowView';
import ProfileScreen from './source/profile/ProfileScreen';
import RemoveDevice from './source/device/RemoveDevice';
import LangScreen from './source/profile/LangScreen';
import TimezoneList from './source/profile/TimezoneList';
import NotifyScreen from './source/notify/NotifyScreen';
import NotifyDate from './source/notify/NotifyDate';
import UrgentAlertScreen from './source/device/UrgentAlertScreen';
import LookImage from './source/event/LookImage';
import LookVideo from './source/event/LookVideo';

import DeviceSpec from './source/select_device_spec/DeviceSpec';
import LoginDemo from './source/session/LoginDemo';
import WifiLink from './source/wifi_link/WifiLink';
import ProfileList from './source/profile/ProfileList';
import TimezoneScreen from './source/profile/TimezoneScreen';
import ChangeDatacenter from './source/profile/ChangeDatacenter';
import WifiScreen from './source/wifi_link/WifiScreen';
import AdvancedSettings from './source/sn/AdvancedSettings';
import SmartList from './source/device/SmartList';
import InputDsk from './source/device/InputDsk';
import AboutScreen from './source/setting/AboutScreen';
import OpenLicense from './source/setting/OpenLicense';
import ChangeScale from './source/profile/ChangeScale';
import VoiceScreen from './source/setting/VoiceScreen';
import AddCamera from './source/device/AddCamera';
import Add433Device from './source/device/Add433Device';
import AlexaScreen from './source/setting/AlexaScreen';
import AlexaLoginWithAmazonScreen from './source/setting/AlexaLoginWithAmazonScreen';
import MoreScreen from './source/setting/MoreScreen';
import CityList from './source/sun/CityList';
import GuardModeScreen from './source/setting/GuardModeScreen';
import GuardModeView from './source/setting/GuardModeView';
import SmartSceneView from './source/smart/SmartSceneView';
import SmartTimeView from './source/smart/SmartTimeView';
import SendSn from './source/wifi_link/SendSn';
import LinkPresen from './source/wifi_link/LinkPresen';
import AddSuccess from './source/device/AddSuccess';
import NotifyScene from './source/notify/NotifyScene';
import ZigbeeSmart from './source/device/ZigbeeSmart';
import EventDeviceList from './source/event/EventDeviceList';
import DeviceList from './source/device/d433/DeviceList';
import DeviceType from './source/device/d433/DeviceType';
import DeviceNotifyScreen from './source/device/DeviceNoifyScreen';
import FeedbackScreen from './source/setting/FeedbackScreen';
import TestGoogleMap from './source/TestGoogleMap';
import ChangeUserName from './source/profile/ChangeUserName';
import DeviceNotifyDate from './source/device/DeviceNotifyDate';
import BackgroundImage from './source/setting/BackgroundImage';
import DeviceInfo from './source/device/DeviceInfo';

import AddIPCDevice from './source/device/ipc/AddIPCDevice';
import IpcCameraShow from './source/device/ipc/IpcCameraShow';

import ScanCodeScreen from './source/setting/ScanCodeScreen';
import ScrollTabView from './source/share/ScrollTabView';
import TuyaCameraSelectControl from './source/device/tuya/TuyaCameraSelectControl';
import ControllerInfo from './source/sn/ControllerInfo';
import SelectSceneIcon from './source/scenes/SelectSceneIcon';
import ControllerUpdate from './source/setting/controller/ControllerUpdate';
import ChangeHome from './source/setting/ChangeHome';

import AddHome from './source/setting_home/AddHome';
import ChangeSnName from './source/sn/ChangeSnName';
import ThemeScreen from './source/setting/ThemeScreen';
import WidgetScreen from './source/setting/WidgetScreen';
import ScanQrScreen from './source/device/add_device/ScanQrScreen';
import AddIPCAPDevice from './source/device/ipc/AddIPCAPDevice';
import AddWlanDevice from './source/device/ipc/AddWlanDevice';
import AlarmTime from './source/device/ipc/AlarmTime';
import IpcCameraSetting from './source/device/ipc/IpcCameraSetting';
import IpcRegion from './source/device/ipc/IpcRegion';

import IpcPlayBackScreen from './source/device/ipc/IpcPlayBackScreen';
import LocalCameraShow from './source/device/ipc/LocalCameraShow';
import LocalPlayBack from './source/device/ipc/LocalPlayBack';
import ProductScreen from './source/payment/ProductScreen';
import CreateStripeCustomer from './source/payment/CreateStripeCustomer';
import OrderScreen from './source/payment/OrderScreen';
import PaymentResultScreen from './source/payment/PaymentResultScreen';
import OrderList from './source/payment/OrderList';
import DownloadBilling from './source/payment/DownloadBilling';
import SettingCard from './source/payment/SettingCard';
import IpcListSetting from './source/device/IpcListSetting';

interface ScreenItem {
  name: string;
  screen: any;
  hoc: boolean;
}

const mainScreen: ScreenItem[] = [];

function init(name: string, screen: any) {
  mainScreen.push({
    name,
    screen,
    hoc: true,
  });
}

export function registerScreens() {
  init('langScreen', LangScreen);
  init('removeDevice', RemoveDevice);
  init('profileScreen', ProfileScreen);
  init('userShowView', UserShowView);
  init('userScreen', UserScreen);
  init('changeController', ChangeController);
  init('addDevice', AddDevice);
  init('eventScreen', EventScreen);
  init('roomShow', RoomShow);
  init('settingRoom', SettingRoom);
  init('country', Country);
  init('timezoneList', TimezoneList);
  init('notifyScreen', NotifyScreen);
  init('notifyDate', NotifyDate);
  init('urgentAlertScreen', UrgentAlertScreen);
  init('lookImage', LookImage);
  init('LookVideo', LookVideo);
  init('DeviceSpec', DeviceSpec);
  init('LoginDemo', LoginDemo);
  init('WifiLink', WifiLink);
  init('ProfileList', ProfileList);
  init('TimezoneScreen', TimezoneScreen);
  init('ChangeDatacenter', ChangeDatacenter);
  init('WifiScreen', WifiScreen);
  init('AdvancedSettings', AdvancedSettings);
  init('SmartList', SmartList);
  init('InputDsk', InputDsk);
  init('AboutScreen', AboutScreen);
  init('OpenLicense', OpenLicense);
  init('ChangeScale', ChangeScale);
  init('VoiceScreen', VoiceScreen);
  init('AddCamera', AddCamera);
  init('Add433Device', Add433Device);
  init('AlexaScreen', AlexaScreen);
  init('AlexaLoginWithAmazonScreen', AlexaLoginWithAmazonScreen);
  init('MoreScreen', MoreScreen);
  init('ProductScreen', ProductScreen);
  init('CreateStripeCustomer', CreateStripeCustomer);
  init('CityList', CityList);
  init('OrderScreen', OrderScreen);
  init('GuardModeScreen', GuardModeScreen);
  init('GuardModeView', GuardModeView);
  init('SmartSceneView', SmartSceneView);
  init('SmartTimeView', SmartTimeView);
  init('SendSn', SendSn);
  init('LinkPresen', LinkPresen);
  init('AddSuccess', AddSuccess);
  init('NotifyScene', NotifyScene);
  init('ZigbeeSmart', ZigbeeSmart);
  init('EventDeviceList', EventDeviceList);
  init('DeviceList', DeviceList);
  init('DeviceType', DeviceType);
  init('DeviceNotifyScreen', DeviceNotifyScreen);

  init('FeedbackScreen', FeedbackScreen);
  init('TestGoogleMap', TestGoogleMap);
  init('ChangeUserName', ChangeUserName);
  init('DeviceNotifyDate', DeviceNotifyDate);
  init('BackgroundImage', BackgroundImage);
  init('DeviceInfo', DeviceInfo);

  // init('AddTuyaCamera', AddTuyaCamera);
  // init('AddTuyaDevice', AddTuyaDevice);
  // init('TuyaCameraShow', TuyaCameraShow);
  // init('CameraPlayBackScreen', CameraPlayBackScreen);
  // init('PlayBackViewShow', PlayBackViewShow);
  // init('TuyaCameraState', TuyaCameraState);

  init('AddIPCDevice', AddIPCDevice);
  init('AddIPCAPDevice', AddIPCAPDevice);
  init('AddWlanDevice', AddWlanDevice);
  init('IpcCameraShow', IpcCameraShow);
  init('IpcCameraSetting', IpcCameraSetting);

  init('IpcPlayBackScreen', IpcPlayBackScreen);
  init('LocalCameraShow', LocalCameraShow);
  init('LocalPlayBack', LocalPlayBack);
  init('AlarmTime', AlarmTime);
  init('IpcRegion', IpcRegion);

  init('ScanCodeScreen', ScanCodeScreen);
  init('ScrollTabView', ScrollTabView);
  init('TuyaCameraSelectControl', TuyaCameraSelectControl);
  init('ControllerInfo', ControllerInfo);
  init('SelectSceneIcon', SelectSceneIcon);
  init('ControllerUpdate', ControllerUpdate);
  init('ChangeHome', ChangeHome);
  init('AddHome', AddHome);
  init('ChangeSnName', ChangeSnName);
  init('ThemeScreen', ThemeScreen);
  init('WidgetScreen', WidgetScreen);
  init('OrderList', OrderList);
  init('DownloadBilling', DownloadBilling);
  init('SettingCard', SettingCard);
  init('ScanQrScreen', ScanQrScreen);
  init('PaymentResultScreen', PaymentResultScreen);
  init('IpcListSetting', IpcListSetting);

  return mainScreen;
  // return mainScreen;
}
