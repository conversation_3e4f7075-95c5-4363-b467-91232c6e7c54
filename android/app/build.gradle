apply plugin: "com.android.application"
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.huawei.agconnect'
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '../..'
    // root = file("../../")
    //   The folder where the react-native NPM package is. Default is ../../node_modules/react-native
    // reactNativeDir = file("../../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../../node_modules/@react-native/codegen
    // codegenDir = file("../../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../../node_modules/react-native/cli.js
    // cliFile = file("../../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]

    /* Autolinking */
    autolinkLibrariesWithApp()
}


/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = io.github.react-native-community:jsc-android-intl:2026004.+`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'io.github.react-native-community:jsc-android:2026004.+'

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    namespace "com.presen"
    defaultConfig {
        applicationId "com.presen"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 402
        versionName "4.0.2"
        manifestPlaceholders = [
                GETUI_APPID    : "nhwfKoDhpZAqLPixxarIpA",
                XIAOMI_APP_KEY : "5301979219836",
                XIAOMI_APP_ID  : "2882303761519792836",
                VIVO_APP_ID    : "105471768",
                VIVO_APP_KEY   : "70e3d74d2dd1087b748f4ec3e9f4bacd",
                OPPO_APP_KEY   : "9f3b9b6eb7014adb971186d9a510c986",
                OPPO_APP_SECRET: "e90c3fd95dbb47ce93bad74cc0ab4aa8",
                MEIZU_APP_ID   : "139711",
                MEIZU_APP_KEY  : "49f203a400054309b377b61bdd5e05b7",
                HUAWEI_APP_ID  : "103995701",
        ]

        resConfigs "zh", "en"

    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            storeFile file(MYAPP_RELEASE_STORE_FILE)
            keyAlias MYAPP_RELEASE_KEY_ALIAS
            storePassword MYAPP_RELEASE_STORE_PASSWORD
            keyPassword MYAPP_RELEASE_KEY_PASSWORD
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
            resValue "string", "CodePushDeploymentKey", '""'
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            resValue 'string', "CODE_PUSH_APK_BUILD_TIME", String.format("\"%d\"", System.currentTimeMillis())
            resValue "string", "CodePushDeploymentKey", '"VQSFDpcPOBFKI-Xmi2EOo6uC-uKZrprO9RCY8"'
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
        releaseStaging {
            resValue 'string', "CODE_PUSH_APK_BUILD_TIME", String.format("\"%d\"", System.currentTimeMillis())
            resValue "string", "CodePushDeploymentKey", '"zYrAgnNQ2WdxmkO2q1hMdfL4xNkY0YqVmGD9R"'
            matchingFallbacks = ['release']
        }
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['lib']
        }
    }

     packagingOptions {
    //     exclude 'META-INF/LICENSE.txt'
    //     exclude 'META-INF/NOTICE'
    //     exclude 'META-INF/LICENSE'
    //     exclude 'META-INF/NOTICE.txt'
    //     exclude 'META-INF/INDEX.LIST'
    //     exclude 'META-INF/maven/com.squareup.okio/okio/pom.properties'
    //     exclude 'META-INF/maven/com.squareup.okio/okio/pom.xml'
    //     exclude 'META-INF/android-offlinelog-api_release.kotlin_module'
    //     exclude 'META-INF/android-offlinelog-upload_release.kotlin_module'
    //     pickFirst 'lib/*/libc++_shared.so'
    //     pickFirst 'lib/*/libgnustl_shared.so'
    //     doNotStrip '*/mips/*.so'
    //     doNotStrip '*/mips64/*.so'
     }
    // buildFeatures {
    //     viewBinding true
    // }

    // applicationVariants are e.g. debug, release
    // applicationVariants.all { variant ->
    //     variant.outputs.each { output ->
    //         // For each separate APK per architecture, set a unique version code as described here:
    //         // https://developer.android.com/studio/build/configure-apk-splits.html
    //         // Example: versionCode 1 will generate 1001 for armeabi-v7a, 1002 for x86, etc.
    //         def versionCodes = ["armeabi-v7a": 1, "x86": 2, "arm64-v8a": 3, "x86_64": 4]
    //         def abi = output.getFilter(OutputFile.ABI)
    //         if (abi != null) {  // null for the universal-debug, universal-release variants
    //             output.versionCodeOverride =
    //                     defaultConfig.versionCode * 1000 + versionCodes.get(abi)
    //         }

    //     }
    // }
}
project.ext.vectoricons = [
        iconFontNames: ['FontAwesome.ttf', 'Ionicons.ttf', 'MaterialIcons.ttf', "MaterialCommunityIcons.ttf"] // Name of the font files you want to copy
]

apply from: "../../node_modules/react-native-vector-icons/fonts.gradle"
Project background_geolocation = project(':react-native-background-geolocation')
apply from: "${background_geolocation.projectDir}/app.gradle"

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar", "*.aar"])

    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }

    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'com.google.code.gson:gson:2.8.6'
    
    // implementation project(':react-native-notifications')
    implementation 'com.google.firebase:firebase-core:21.1.1'

    implementation(project(':react-native-maps')) {
        exclude group: 'com.google.android.gms'
    }

    implementation 'com.google.android.gms:play-services-base:18.5.0'
    implementation 'com.google.android.gms:play-services-location:21.3.0'
    implementation 'com.google.android.gms:play-services-maps:18.0.2'

    implementation 'com.google.android.material:material:1.4.0'
    implementation 'com.google.android.gms:play-services-location:16.0.0'

    implementation 'com.android.support:multidex:1.0.2'
    implementation 'androidx.work:work-runtime:2.7.1'

    androidTestImplementation('com.wix:detox:+')
    compileOnly "javax.annotation:javax.annotation-api:1.2"

    // Import the BoM for the Firebase platform
    implementation platform('com.google.firebase:firebase-bom:28.4.1')

    // Declare the dependencies for the Firebase Cloud Messaging and Analytics libraries
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation 'com.google.firebase:firebase-analytics:17.3.0'

    implementation 'com.alibaba:fastjson:1.1.67.android'
    implementation 'com.squareup.okhttp3:okhttp-urlconnection:3.14.9'
    // Tuya Home 当前最新稳定版本：
    implementation 'com.tuya.smart:tuyasmart:4.0.2'

    //Camera
    implementation 'com.tuya.smart:tuyasmart-ipcsdk:3.31.5'
//    implementation 'com.tuya.smart:tuyasmart-ipc-camera-v1:3.20.0'


    implementation 'com.tuya.smart:tuyasmart-base-utils:3.18.0r143-rc.9'
    //可选
//    implementation 'com.tuya.smart:tuyasmart-ipc-camera-message:3.13.0r128'
    implementation 'com.tuya.smart:tuyasmart-ipc-devicecontrol:3.17.0r139'

    implementation 'com.tuya.smart:tuyasmart-imagepipeline-okhttp3:0.0.1'
    implementation 'com.facebook.fresco:fresco:2.2.0'

    implementation 'com.tuya.smart:tuyasmart-lock-sdk:1.0.6'
    implementation 'com.tuya.smart:sweeper:0.1.0-beta1'

    implementation 'com.getui:gtsdk:*******'
    implementation 'com.getui:gtc:********'

    // 根据所需厂商选择集成
     implementation 'com.getui.opt:hwp:3.1.1'   // 华为
    implementation 'com.getui.opt:xmp:3.3.0'   // 小米
    implementation 'com.assist-v3:oppo:3.2.0'  // oppo
    implementation 'com.assist-v3:vivo:3.1.1'  // vivo
    implementation 'com.getui.opt:mzp:3.2.2'   // 魅族
    implementation 'com.getui.opt:ups:3.0.3'   // ups，ups目前支持坚果，索尼，海信手机

    implementation 'com.huawei.hms:push:6.7.0.300'

    implementation 'com.google.zxing:core:3.3.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    implementation "org.java-websocket:Java-WebSocket:1.4.0"



}

// Run this once to be able to run the application with BUCK
// puts all compile dependencies into folder libs for BUCK to use
task copyDownloadableDepsToLibs(type: Copy) {
    from configurations.implementation
    into 'libs'
}

