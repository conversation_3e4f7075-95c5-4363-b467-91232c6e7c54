{"agcgw": {"backurl": "connect-drcn.hispace.hicloud.com", "url": "connect-drcn.dbankcloud.cn", "websocketbackurl": "connect-ws-drcn.hispace.dbankcloud.com", "websocketurl": "connect-ws-drcn.hispace.dbankcloud.cn"}, "agcgw_all": {"CN": "connect-drcn.dbankcloud.cn", "CN_back": "connect-drcn.hispace.hicloud.com", "DE": "connect-dre.dbankcloud.cn", "DE_back": "connect-dre.hispace.hicloud.com", "RU": "connect-drru.dbankcloud.cn", "RU_back": "connect-drru.hispace.hicloud.com", "SG": "connect-dra.dbankcloud.cn", "SG_back": "connect-dra.hispace.hicloud.com"}, "client": {"cp_id": "2850086000473299144", "product_id": "736430079244847376", "client_id": "582990682109522560", "client_secret": "9E427361770C3EE0C939891B9B2E2A79BCEFC8A13C823AEF8643F43B2A0E1BDA", "project_id": "736430079244847376", "app_id": "*********", "api_key": "CgB6e3x9m9sqCN8tMTWbl12j2Byiv0lDSKzH1EQjxUjQR+VQhRL7eYCuS5br8al0NUMFTroDIqPuCxp03zmv+m9l", "package_name": "com.presen"}, "oauth_client": {"client_id": "*********", "client_type": 1}, "app_info": {"app_id": "*********", "package_name": "com.presen"}, "service": {"analytics": {"collector_url": "datacollector-drcn.dt.hicloud.com,datacollector-drcn.dt.dbankcloud.cn", "collector_url_ru": "datacollector-drru.dt.hicloud.com,datacollector-drru.dt.dbankcloud.cn", "collector_url_sg": "datacollector-dra.dt.hicloud.com,datacollector-dra.dt.dbankcloud.cn", "collector_url_de": "datacollector-dre.dt.hicloud.com,datacollector-dre.dt.dbankcloud.cn", "collector_url_cn": "datacollector-drcn.dt.hicloud.com,datacollector-drcn.dt.dbankcloud.cn", "resource_id": "p1", "channel_id": ""}, "search": {"url": "https://search-drcn.cloud.huawei.com"}, "cloudstorage": {"storage_url": "https://agc-storage-drcn.platform.dbankcloud.cn"}, "ml": {"mlservice_url": "ml-api-drcn.ai.dbankcloud.com,ml-api-drcn.ai.dbankcloud.cn"}}, "region": "CN", "configuration_version": "3.0", "appInfos": [{"package_name": "com.presen", "client": {"app_id": "*********"}, "app_info": {"package_name": "com.presen", "app_id": "*********"}, "oauth_client": {"client_type": 1, "client_id": "*********"}}]}