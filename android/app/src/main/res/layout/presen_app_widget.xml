<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/Widget.Presen.AppWidget.Container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:theme="@style/AppTheme.AppWidgetContainer">

    <LinearLayout
        android:id="@+id/widget"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 第一行：logo和产品名称 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical|start"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/imageView"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:src="@drawable/logo" />

            <TextView
                android:id="@+id/textView2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:text="Presen"
                android:textColor="@color/textColor"
                android:textSize="@dimen/mg_20"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- 第二行：四个卡片 -->
        <LinearLayout
            android:id="@+id/widget_body"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="horizontal"></LinearLayout>
    </LinearLayout>
</RelativeLayout>