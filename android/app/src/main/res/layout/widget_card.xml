<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_card"
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    android:layout_marginLeft="6dp"
    android:layout_marginRight="6dp"
    android:layout_weight="1"
    android:background="@drawable/widget_card_bg"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="8dp">

    <ImageView
        android:id="@+id/widget_card_1_image"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginBottom="8dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center|center_horizontal"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/red_dot"
            android:layout_width="6dp"
            android:layout_height="6dp"
            android:layout_marginRight="4dp"
            android:background="@drawable/circle"
            android:visibility="gone" />

        <TextView
            android:id="@+id/widget_card_1_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="@color/widget_item_color" />
    </LinearLayout>
</LinearLayout>

