<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    </style>

    <!-- <style name="NegativeButtonStyle" parent="Widget.AppCompat.Button.ButtonBar.AlertDialog">
        <item name="android:textColor">#fc577a</item>
    </style>

    <style name="PositiveButtonStyle" parent="Widget.AppCompat.Button.ButtonBar.AlertDialog">
        <item name="android:textColor">#fc577a</item>
    </style> -->

    <style name="Widget.Presen.AppWidget.Container" parent="android:Widget">
        <item name="android:id">@android:id/background</item>
        <item name="android:background">?android:attr/colorBackground</item>
    </style>
    <style name="Widget.Presen.AppWidget.InnerView" parent="android:Widget">
        <item name="android:background">?android:attr/colorBackground</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
</resources>
