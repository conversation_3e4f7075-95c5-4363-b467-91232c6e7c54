<?xml version="1.0" encoding="utf-8"?>
<!--
   Sample data extraction rules file; uncomment and customize as necessary.
   See https://developer.android.com/about/versions/12/backup-restore#xml-changes
   for details.
-->
<data-extraction-rules>
    <cloud-backup>
        <!--
        TODO: Use <include> and <exclude> to control what is backed up.
        The domain can be file, database, sharedpref, external or root.
        Examples:

        <include domain="file" path="file_to_include"/>
        <exclude domain="file" path="file_to_exclude"/>
        <include domain="file" path="include_folder"/>
        <exclude domain="file" path="include_folder/file_to_exclude"/>
        <exclude domain="file" path="exclude_folder"/>
        <include domain="file" path="exclude_folder/file_to_include"/>

        <include domain="sharedpref" path="include_shared_pref1.xml"/>
        <include domain="database" path="db_name/file_to_include"/>
        <exclude domain="database" path="db_name/include_folder/file_to_exclude"/>
        <include domain="external" path="file_to_include"/>
        <exclude domain="external" path="file_to_exclude"/>
        <include domain="root" path="file_to_include"/>
        <exclude domain="root" path="file_to_exclude"/>
        -->
    </cloud-backup>
    <!--
    <device-transfer>
        <include .../>
        <exclude .../>
    </device-transfer>
    -->
</data-extraction-rules>