{"hybrid_plugins": ["com.tuya.smart.jsbridge.jscomponent.origin.UserJSComponent", "com.tuya.smart.jsbridge.jscomponent.plugin.UIKitJSComponent", "com.tuya.smart.jsbridge.jscomponent.plugin.AppJSComponent", "com.tuya.smart.jsbridge.jscomponent.plugin.NavJSComponent", "com.tuya.smart.jsbridge.jscomponent.plugin.NetJSComponent", "com.tuya.smart.jsbridge.jscomponent.plugin.KVJSComponent", "com.tuya.smart.logupload.LogJSComponent", "com.tuya.smart.jsbridge.jscomponent.origin.ImageJSComponent", "com.tuya.smart.jsbridge.jscomponent.origin.TokenJSComponent", "com.tuya.smart.jsbridge.jscomponent.plugin.ImagePickerJSComponent", "com.tuya.smart.deviceconfig.plugin.TuyaConfigDeviceJSComponent", "com.tuya.smart.jsbridge.jscomponent.plugin.LifecycleJSComponent", "com.tuya.smart.jsbridge.jscomponent.plugin.ShareJSComponent", "com.tuya.smart.jsbridge.jscomponent.plugin.PopupJSComponent", "com.tuya.smart.jsbridge.jscomponent.origin.NavigatorJSComponent", "com.tuya.smart.jsbridge.jscomponent.origin.ToastJSComponent", "com.tuya.smart.jsbridge.jscomponent.origin.AppInfoJSComponent", "com.tuya.smart.jsbridge.jscomponent.plugin.ClipboardJSComponent", "com.tuya.smart.jsbridge.jscomponent.origin.PhoneJSComponent", "com.tuya.smart.jsbridge.jscomponent.plugin.MediaPreviewJSComponent", "com.tuya.smart.jsbridge.jscomponent.plugin.UserJSComponent", "com.tuya.smart.jsbridge.apm.plugins.APMJSComponent", "com.tuya.smart.jsbridge.jscomponent.plugin.PageJSComponent"], "rct_packages": {"panel": ["com.tuya.smart.rnplugin.tyrctnavmanager.TYRCTNavManagerPackage", "com.tuya.smart.rnplugin.tyrctslider.TYRCTSliderPackage", "com.tuya.smart.rnplugin.tyrctpanelmanager.TYRCTPanelManagerPackage", "com.tuya.smart.rnplugin.tyrctpublicmanager.TYRCTPublicManagerPackage", "com.tuya.smart.rnplugin.imagepickermanager.ImagePickerManagerPackage", "com.tuya.smart.rnplugin.tyrctcountryselectmanager.TYRCTCountrySelectManagerPackage", "com.horcrux.svg.SvgPackage", "com.tuya.smart.rnplugin.tyrctbluetoothutilmanager.TYRCTBluetoothUtilManagerPackage", "com.tuya.smart.rnplugin.tyrctgesturelockviewmanager.TYRCTGestureLockViewManagerPackage", "com.tuya.smart.rnplugin.tyrctwheelviewmanager.TYRCTWheelViewManagerPackage", "com.tuya.smart.rnplugin.tyrctimageencryptuploadmanager.TYRCTImageEncryptUploadManagerPackage", "com.tuya.smart.rnplugin.tyrctavsmanager.TYRCTAVSManagerPackage", "com.tuya.smart.rnplugin.tyrctmeshpanelmanager.TYRCTMeshPanelManagerPackage", "com.tuya.smart.amap.TYRCTAMapPackage", "com.tuya.smart.rnplugin.rctvideomanager.RCTVideoManagerPackage", "com.tuya.smart.rnplugin.tyrctnewtopbar.TYRCTNewTopBarPackage", "com.tuya.smart.rnplugin.tyrctencryptimagemanager.TYRCTEncryptImageManagerPackage", "com.tuya.fetch.RNFetchBlobPackage", "com.tuya.smart.rnplugin.exceptionsmanager.ExceptionsManagerPackage", "com.tuya.smart.rnplugin.tyrctvisionmap.TYRCTVisionMapPackage", "com.tuya.smart.panel.reactnative.manager.lottie.LottiePackage", "com.tuya.smart.rnplugin.tyrctpicker.TYRCTPickerPackage", "com.tuya.smart.rnplugin.tyrcthomedevmanager.TYRCTHomeDevManagerPackage", "com.tuya.smart.rnplugin.rnviewshot.RNViewShotPackage", "com.tuya.smart.rnplugin.tyrctnumberpicker.TYRCTNumberPickerPackage", "com.tuya.smart.rnplugin.tyrctmqttmanager.TYRCTMqttManagerPackage", "com.tuya.smart.rnplugin.tyrctstandardgroupmanager.TYRCTStandardGroupManagerPackage", "com.tuya.smart.rnplugin.tyrctpointmap.TYRCTPointMapPackage", "com.tuya.smart.rnplugin.tyrctapmeventmanager.TYRCTAPMEventManagerPackage", "com.tuya.smart.rnplugin.tyrctvisionmanager.TYRCTVisionManagerPackage", "com.tuya.smart.rnplugin.tyrctlasermanager.TYRCTLaserManagerPackage", "com.tuya.smart.rnplugin.tyrctpaneldevicemanager.TYRCTPanelDeviceManagerPackage", "com.tuya.smart.rnplugin.tyrctcurvechartview.TYRCTCurveChartViewPackage", "com.tuya.smart.rnplugin.tyrctencryptimagedownloadmanager.TYRCTEncryptImageDownloadManagerPackage", "com.tuya.smart.googlemap.TYRCTGoogleMapPackage", "com.tuya.smart.rnplugin.frescomodule.FrescoModulePackage", "com.tuya.smart.rnplugin.tyrctswitch.TYRCTSwitchPackage", "com.tuya.smart.rnplugin.tyrctspeakermanager.TYRCTSpeakerManagerPackage", "com.tuya.smart.rnplugin.tyrctapmtrackmanager.TYRCTAPMTrackManagerPackage", "com.tuya.smart.rnplugin.tyrctlinechartview.TYRCTLineChartViewPackage", "com.tuya.smart.rnplugin.tyrcthomemanager.TYRCTHomeManagerPackage", "com.tuya.smart.rnplugin.tyrcttypemapmanager.TYRCTTypeMapManagerPackage", "com.tuya.smart.rnplugin.tyrctmultilinechartview.TYRCTMultiLineChartViewPackage", "com.tuya.smart.rnplugin.tyrctchartmarker.TYRCTChartMarkerPackage", "com.tuya.smart.rnplugin.tyrcttransfermanager.TYRCTTransferManagerPackage", "com.tuya.smart.rnplugin.tyrctblemanager.TYRCTBLEManagerPackage", "com.tuya.smart.rnplugin.tyrcthuecircleview.TYRCTHueCircleViewPackage", "com.tuya.smart.rnplugin.tyrctlasermap.TYRCTLaserMapPackage", "com.tuya.smart.rnplugin.tyrctzigbeeeventmanager.TYRCTZigbeeEventManagerPackage", "com.tuya.smart.rnplugin.tyrctscenepanelmanager.TYRCTScenePanelManagerPackage", "com.tuya.smart.rnplugin.tyrctmultiimagepickermanager.TYRCTMultiImagePickerManagerPackage", "com.tuya.smart.rnplugin.tyrcttopbar.TYRCTTopBarPackage", "com.tuya.smart.rnplugin.tyrctmusicmanager.TYRCTMusicManagerPackage"], "camera": ["com.tuya.smart.rnplugin.tyrctcameramanager.TYRCTCameraManagerPackage", "com.tuya.smart.rnplugin.tyrctcameratimelineviewmanager.TYRCTCameraTimeLineViewManagerPackage", "com.tuya.smart.rnplugin.tyrcttuyacameraplayer.TYRCTTuyaCameraPlayerPackage", "com.tuya.smart.rnplugin.tyrctcameraplayer.TYRCTCameraPlayerPackage"]}}