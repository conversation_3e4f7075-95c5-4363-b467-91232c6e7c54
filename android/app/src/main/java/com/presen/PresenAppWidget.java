package com.presen;

import android.app.PendingIntent;
import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProvider;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.view.View;
import android.widget.RemoteViews;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;

import org.json.JSONException;

/**
 * Implementation of App Widget functionality.
 */
public class PresenAppWidget extends AppWidgetProvider {

    static void updateAppWidget(Context context, AppWidgetManager appWidgetManager,
                                int appWidgetId) throws JSONException {


        SharedPreferences sharedPref = context.getSharedPreferences("DATA", Context.MODE_PRIVATE);
        String appString = sharedPref.getString("appData", "");

        Intent intent = new Intent(context, MainActivity.class);
        PendingIntent pendingIntent;

        RemoteViews views = new RemoteViews(context.getPackageName(), R.layout.presen_app_widget);
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        views.setOnClickPendingIntent(R.id.widget, pendingIntent);


        if (!appString.isEmpty()) {
            Gson gson = new Gson();
            JsonArray data = gson.fromJson(appString, JsonArray.class);
            views.removeAllViews(R.id.widget_body);
            int requestCode = 0;
            for (JsonElement element : data) {
                RemoteViews card = new RemoteViews(context.getPackageName(), R.layout.widget_card);
                PendingIntent pendingIntent2;


                Datum d = gson.fromJson(element, Datum.class);
                Intent intent2 = new Intent(context, WidgetActivity.class);
                intent2.putExtra("payload", gson.toJson(d));

                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                    pendingIntent2 = PendingIntent.getActivity(context, requestCode++, intent2, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
                } else {
                    pendingIntent2 = PendingIntent.getActivity(context, requestCode++, intent2, PendingIntent.FLAG_UPDATE_CURRENT);
                }
                switch (d.name) {
                    case "home":
                        card.setTextViewText(R.id.widget_card_1_text, context.getText(R.string.home));
                        break;
                    case "sleep":
                        card.setTextViewText(R.id.widget_card_1_text, context.getText(R.string.sleep));
                        break;
                    case "away":
                        card.setTextViewText(R.id.widget_card_1_text, context.getText(R.string.away));
                        break;
                    case "vacation":
                        card.setTextViewText(R.id.widget_card_1_text, context.getText(R.string.vacation));
                        break;
                    default:
                        card.setTextViewText(R.id.widget_card_1_text, d.name);
                }

                card.setImageViewResource(R.id.widget_card_1_image, getIcon(d.icon));

                if (d.isEnabled) {
                    card.setViewVisibility(R.id.red_dot, View.VISIBLE);
                } else {
                    card.setViewVisibility(R.id.red_dot, View.GONE);
                }
                card.setOnClickPendingIntent(R.id.widget_card, pendingIntent2);
                views.addView(R.id.widget_body, card);
            }
        } else {
            RemoteViews noLogin = new RemoteViews(context.getPackageName(), R.layout.widget_no_login);
            noLogin.setTextViewText(R.id.widget_card_1_text, context.getText(R.string.widget_no_login));
            views.addView(R.id.widget_body, noLogin);
        }

        appWidgetManager.updateAppWidget(appWidgetId, views);
    }

    public static class Datum {
        public String icon;
        public boolean isEnabled;
        public String name;
        public String type;
        public String uuid;
    }

    public static int getIcon(String name) {
        switch (name) {
            case "icons8-birthday-cake-96":
                return R.drawable.icons8_birthday_cake_96;
            case "icons8-confetti-96":
                return R.drawable.icons8_confetti_96;
            case "icons8-food-and-wine-96":
                return R.drawable.icons8_food_and_wine_96;
            case "icons8-game-controller-96":
                return R.drawable.icons8_game_controller_96;
            case "icons8-holiday-96":
                return R.drawable.icons8_holiday_96;
            case "icons8-home-page-96":
                return R.drawable.icons8_home_page_96;
            case "icons8-keyboard-96":
                return R.drawable.icons8_keyboard_96;
            case "icons8-ladybird-96":
                return R.drawable.icons8_ladybird_96;
            case "icons8-moon-and-stars-96":
                return R.drawable.icons8_moon_and_stars_96;
            case "icons8-party-96":
                return R.drawable.icons8_party_96;
            case "icons8-restaurant-96":
                return R.drawable.icons8_restaurant_96;
            case "icons8-romance-96":
                return R.drawable.icons8_romance_96;
            case "icons8-sofa-96":
                return R.drawable.icons8_sofa_96;
            case "icons8-sunny-side-up-eggs-96":
                return R.drawable.icons8_sunny_side_up_eggs_96;
            case "icons8-traveler-96":
                return R.drawable.icons8_traveler_96;
            case "icons8-clock-96":
                return R.drawable.icons8_clock_96;
            case "icons8-guard-96":
                return R.drawable.icons8_guard_96;
            case "icons8-sedan-96":
                return R.drawable.icons8_sedan_96;
            case "icons8-airplane-96":
                return R.drawable.icons8_airplane_96;
            case "icons8-violin-96":
                return R.drawable.icons8_violin_96;
        }
        return R.drawable.icons8_airplane_96;
    }

    @Override
    public void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
        // There may be multiple widgets active, so update all of them
        for (int appWidgetId : appWidgetIds) {
            try {
                updateAppWidget(context, appWidgetManager, appWidgetId);
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public void onEnabled(Context context) {
        // Enter relevant functionality for when the first widget is created
    }

    @Override
    public void onDisabled(Context context) {
        // Enter relevant functionality for when the last widget is disabled
    }
}