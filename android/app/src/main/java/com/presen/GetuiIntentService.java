package com.presen;

import android.content.Context;
import android.util.Log;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.igexin.sdk.GTIntentService;
import com.igexin.sdk.PushConsts;
import com.igexin.sdk.PushManager;
import com.igexin.sdk.message.BindAliasCmdMessage;
import com.igexin.sdk.message.FeedbackCmdMessage;
import com.igexin.sdk.message.GTCmdMessage;
import com.igexin.sdk.message.GTNotificationMessage;
import com.igexin.sdk.message.GTTransmitMessage;
import com.igexin.sdk.message.SetTagCmdMessage;
import com.igexin.sdk.message.UnBindAliasCmdMessage;

public class GetuiIntentService extends GTIntentService {
    /**
     * 为了观察透传数据变化.
     */
    private static int cnt;

    @Override
    public void onReceiveServicePid(Context context, int pid) {
        GetuiLogger.log("onReceiveServicePid -> " + pid);
    }

    @Override
    public void onReceiveMessageData(Context context, GTTransmitMessage msg) {
        GetuiLogger.log("----------------------------------------------------------------------------------------------");
        String appid = msg.getAppid();
        String taskid = msg.getTaskId();
        String messageid = msg.getMessageId();
        byte[] payload = msg.getPayload();
        String pkg = msg.getPkgName();
        String cid = msg.getClientId();

        // 第三方回执调用接口，actionid范围为90000-90999，可根据业务场景执行
        boolean result = PushManager.getInstance().sendFeedbackMessage(context, taskid, messageid, 60002);
        GetuiLogger.log("call sonReceiveServicePidendFeedbackMessage = " + (result ? "success" : "failed"));
        GetuiLogger.log("onReceiveMessageData -> " + "appid = " + appid + "\ntaskid = " + taskid + "\nmessageid = " + messageid + "\npkg = " + pkg
                + "\ncid = " + cid);

        if (payload == null) {
            GetuiLogger.log("receiver payload = null");
        } else {
            String data = new String(payload);
            if (GetuiModule.reactContext != null) {
                if (!GetuiModule.isAppForeground) GetuiModule.launchApp(context);
                WritableMap writableMap = Arguments.createMap();
                writableMap.putString("notificationEventType", "notificationOpened");
                writableMap.putString("message_id", messageid);
                writableMap.putString("extras", data);
                sendEvent("NotificationEvent", writableMap);
            } else {
                super.onReceiveMessageData(context, msg);
            }
            GetuiLogger.log("receiver payload = " + data);
        }
    }

    @Override
    public void onReceiveClientId(Context context, String clientid) {
        GetuiLogger.log("onReceiveClientId -> " + "clientid = " + clientid);
    }

    @Override
    public void onReceiveOnlineState(Context context, boolean online) {
        GetuiLogger.log("onReceiveOnlineState -> " + (online ? "online" : "offline"));
        WritableMap writableMap = Arguments.createMap();
        writableMap.putString("onReceiveOnlineState", online ? "online" : "offline");
        sendEvent("onReceiveOnlineState", writableMap);
    }

    @Override
    public void onReceiveCommandResult(Context context, GTCmdMessage cmdMessage) {
        GetuiLogger.log( "onReceiveCommandResult -> " + cmdMessage);

        int action = cmdMessage.getAction();

        if (action == PushConsts.SET_TAG_RESULT) {
            setTagResult((SetTagCmdMessage) cmdMessage);
        } else if (action == PushConsts.BIND_ALIAS_RESULT) {
            bindAliasResult((BindAliasCmdMessage) cmdMessage);
        } else if (action == PushConsts.UNBIND_ALIAS_RESULT) {
            unbindAliasResult((UnBindAliasCmdMessage) cmdMessage);
        } else if ((action == PushConsts.THIRDPART_FEEDBACK)) {
            feedbackResult((FeedbackCmdMessage) cmdMessage);
        }
    }

    @Override
    public void onNotificationMessageArrived(Context context, GTNotificationMessage message) {
        GetuiLogger.log("onNotificationMessageArrived -> " + "appid = " + message.getAppid() + "\ntaskid = " + message.getTaskId() + "\nmessageid = "
                + message.getMessageId() + "\npkg = " + message.getPkgName() + "\ncid = " + message.getClientId() + "\ntitle = "
                + message.getTitle() + "\ncontent = " + message.getContent());
        WritableMap writableMap = Arguments.createMap();
        writableMap.putString("notificationEventType", "notificationArrived");
        writableMap.putString("message_id", message.getMessageId());
        writableMap.putString("title", message.getTitle());
        writableMap.putString("content", message.getContent());
        sendEvent("NotificationEvent", writableMap);
    }

    @Override
    public void onNotificationMessageClicked(Context context, GTNotificationMessage message) {
        GetuiLogger.log( "onNotificationMessageClicked -> " + "appid = " + message.getAppid() + "\ntaskid = " + message.getTaskId() + "\nmessageid = "
                + message.getMessageId() + "\npkg = " + message.getPkgName() + "\ncid = " + message.getClientId() + "\ntitle = "
                + message.getTitle() + "\ncontent = " + message.getContent());
    }

    private void setTagResult(SetTagCmdMessage setTagCmdMsg) {
        String sn = setTagCmdMsg.getSn();
        String code = setTagCmdMsg.getCode();
    }

    private void bindAliasResult(BindAliasCmdMessage bindAliasCmdMessage) {
        String sn = bindAliasCmdMessage.getSn();
        String code = bindAliasCmdMessage.getCode();
    }

    private void unbindAliasResult(UnBindAliasCmdMessage unBindAliasCmdMessage) {
        String sn = unBindAliasCmdMessage.getSn();
        String code = unBindAliasCmdMessage.getCode();
    }


    private void feedbackResult(FeedbackCmdMessage feedbackCmdMsg) {
        String appid = feedbackCmdMsg.getAppid();
        String taskid = feedbackCmdMsg.getTaskId();
        String actionid = feedbackCmdMsg.getActionId();
        String result = feedbackCmdMsg.getResult();
        long timestamp = feedbackCmdMsg.getTimeStamp();
        String cid = feedbackCmdMsg.getClientId();

        GetuiLogger.log( "onReceiveCommandResult -> " + "appid = " + appid + "\ntaskid = " + taskid + "\nactionid = " + actionid + "\nresult = " + result
                + "\ncid = " + cid + "\ntimestamp = " + timestamp);
    }

    public static void sendEvent(String eventName, WritableMap params) {
        try {
            GetuiModule.reactContext.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class).emit(eventName, params);
        } catch (Throwable throwable) {
            GetuiLogger.log( "sendEvent error:" + throwable.getMessage());
        }
    }
}

