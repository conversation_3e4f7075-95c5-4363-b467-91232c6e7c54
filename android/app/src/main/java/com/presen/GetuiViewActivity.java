package com.presen;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;

public class GetuiViewActivity extends AppCompatActivity {
    @Override
    public void onCreate(Bundle savedInstanceState) {
//        setTheme(R.style.Theme_AppCompat_Empty);
        super.onCreate(savedInstanceState);
        GetuiLogger.log("start getui Activity");
        //获取自定义透传参数值
        Intent intent = getIntent();
        if (null != intent) {
            GetuiLogger.log("start getui intent");
            String payload = intent.getStringExtra("payload");
            SharedPreferences sharedPreferences = getSharedPreferences("data", Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.putString("payload", payload);
            editor.commit();
            Intent i = new Intent(this, MainActivity.class);
            i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            this.startActivity(i);
            WritableMap writableMap = Arguments.createMap();
            writableMap.putString("notificationEventType", "notificationOpened");
            writableMap.putString("extras", payload);
            sendEvent("NotificationEvent", writableMap);
        }
        this.finish();
    }

    @Override
    public void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        GetuiLogger.log("new getui Activity");
        //获取自定义透传参数值
        if (null != intent) {
            String payload = intent.getStringExtra("payload");
            GetuiLogger.log("start getui Activity");
        }
    }

    public static void sendEvent(String eventName, WritableMap params) {
        try {
            GetuiModule.reactContext.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class).emit(eventName, params);
        } catch (Throwable throwable) {
            GetuiLogger.log( "sendEvent error:" + throwable.getMessage());
        }
    }

}
