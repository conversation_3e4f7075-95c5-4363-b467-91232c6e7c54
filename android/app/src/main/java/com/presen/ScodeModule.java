package com.presen;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class ScodeModule extends ReactContextBaseJavaModule {
    public static ReactApplicationContext reactContext;

    public ScodeModule(ReactApplicationContext reactContext) {
        super(reactContext);
        reactContext = reactContext;
    }

    @NonNull
    @Override
    public String getName() {
        return "ScodeModule";
    }

    @ReactMethod
    public void Scode(String env, String data, Callback callback) {
        String s = "";
        WritableMap event = Arguments.createMap();
        if(env.equals("0")){
            s = "5565b79e2ef34b62b7216cee05ae8571";
        }else{
            s = "e1e1d4f9449c4e738047ad3e68352bb6";
        }
        event.putString("scode", md5((s.substring(2, 8) + data + s.substring(2, 8)).toUpperCase()));
        callback.invoke(null, event);
    }

    // 添加空实现的 addListener 方法
    @ReactMethod
    public void addListener(String eventName) {
        // 空实现
    }

    // 添加空实现的 removeListeners 方法
    @ReactMethod
    public void removeListeners(double count) {
        // 空实现
    }

    public static String md5(String string) {
        if (TextUtils.isEmpty(string)) {
            return "";
        }
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
            byte[] bytes = md5.digest(string.getBytes());
            String result = "";
            for (byte b : bytes) {
                String temp = Integer.toHexString(b & 0xff);
                if (temp.length() == 1) {
                    temp = "0" + temp;
                }
                result += temp;
            }
            return result;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }
}