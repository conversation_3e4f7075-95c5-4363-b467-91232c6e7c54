package com.presen;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;

import androidx.appcompat.app.AppCompatActivity;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.WritableMap;

public class WidgetActivity extends AppCompatActivity {

    @Override
    public void onCreate(Bundle savedInstanceState) {
//        setTheme(R.style.Theme_AppCompat_Empty);
        super.onCreate(savedInstanceState);
        GetuiLogger.log("start widget Activity");
        //获取自定义透传参数值
        Intent intent = getIntent();
        if (null != intent) {
            String payload = intent.getStringExtra("payload");
            GetuiLogger.log(payload);
            SharedPreferences sharedPreferences = getSharedPreferences("widget", Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.putString("widget", payload);
            editor.commit();
            Intent i = new Intent(this, MainActivity.class);
            i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            this.startActivity(i);
        }
        this.finish();
    }
}
