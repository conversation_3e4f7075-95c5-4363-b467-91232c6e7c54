package com.presen
 
import android.app.Application
import com.facebook.drawee.backends.pipeline.Fresco
import com.facebook.react.PackageList
import com.facebook.react.ReactApplication
import com.facebook.react.ReactHost
import com.facebook.react.ReactNativeHost
import com.facebook.react.ReactPackage
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.load
import com.facebook.react.defaults.DefaultReactHost.getDefaultReactHost
import com.facebook.react.defaults.DefaultReactNativeHost
import com.facebook.soloader.SoLoader
import com.presen.sha256lib.Sha256Package
import org.wonday.orientation.OrientationActivityLifecycle
//import com.microsoft.codepush.react.CodePush
import com.facebook.react.soloader.OpenSourceMergedSoMapping
//import com.tuya.smart.home.sdk.TuyaHomeSdk

class MainApplication : Application(), ReactApplication {
 
  override val reactNativeHost: ReactNativeHost =
      object : DefaultReactNativeHost(this) {
        override fun getPackages(): List<ReactPackage> =
            PackageList(this).packages.apply {
              // Packages that cannot be autolinked yet can be added manually here, for example:
              add(GetuiPackage())
              add(ScodePackage())
              add(SharedStoragePackager())
//              add(IpcPackage())
              add(Sha256Package())
//              add(TuyaPackage())
            }
 
        override fun getJSMainModuleName(): String = "index"
 
        override fun getUseDeveloperSupport(): Boolean = BuildConfig.DEBUG
//        override fun getJSBundleFile(): String {
//          return CodePush.getJSBundleFile()
//        }
        override val isNewArchEnabled: Boolean = BuildConfig.IS_NEW_ARCHITECTURE_ENABLED
        override val isHermesEnabled: Boolean = BuildConfig.IS_HERMES_ENABLED
      }
 
  override val reactHost: ReactHost
    get() = getDefaultReactHost(applicationContext, reactNativeHost)
 
  override fun onCreate() {
    super.onCreate()
    SoLoader.init(this, OpenSourceMergedSoMapping)
//    TuyaHomeSdk.setDebugMode(true);
//    TuyaHomeSdk.init(this);
      Fresco.initialize(this);
      GetuiModule.initPush(this);
      GetuiModule.registerActivityLifecycle(this);
      registerActivityLifecycleCallbacks(OrientationActivityLifecycle.getInstance());
    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      // If you opted-in for the New Architecture, we load the native entry point for this app.
      load()
    }
  }
}