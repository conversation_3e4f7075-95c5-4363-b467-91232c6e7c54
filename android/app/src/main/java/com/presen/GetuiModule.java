package com.presen;

import android.app.Activity;
import android.app.AppOpsManager;
import android.app.Application;
import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.core.app.NotificationManagerCompat;
import androidx.core.content.ContextCompat;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.JSApplicationIllegalArgumentException;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.igexin.sdk.GetuiPushException;
import com.igexin.sdk.IUserLoggerInterface;
import com.igexin.sdk.PushManager;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.logging.Logger;

import static android.content.Context.MODE_PRIVATE;
import static androidx.core.content.ContextCompat.startActivity;
import static com.facebook.react.bridge.UiThreadUtil.runOnUiThread;

public class GetuiModule extends ReactContextBaseJavaModule {

    public static ReactApplicationContext reactContext;
    public static PushManager PushManager;
    public static boolean isAppForeground = false;
    private static Context mContext;

    public GetuiModule(ReactApplicationContext reactApplicationContext) {
        super(reactApplicationContext);
        reactContext = reactApplicationContext;
    }

    @NonNull
    @Override
    public String getName() {
        return "GetuiModule";
    }

    @Override
    public void initialize() {
        super.initialize();
    }

    @Override
    public void invalidate() {
        super.invalidate();
    }

    public static void initPush(Context context) {
        mContext = context;
        GetuiLogger.log("initPush, mContext = " + mContext);
        PushManager.getInstance().initialize(context);
    }

    @ReactMethod
    public void testGeTui() {
        Intent i = new Intent(mContext, MainActivity.class);
        i.putExtra("dss", "ssss");
        i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        GetuiLogger.log("test linking");
        mContext.startActivity(i);
    }


    // 添加空实现的 addListener 方法
    @ReactMethod
    public void addListener(String eventName) {
        // 空实现
    }

    // 添加空实现的 removeListeners 方法
    @ReactMethod
    public void removeListeners(double count) {
        // 空实现
    }

    @ReactMethod
    public void getWidgetData(Callback callback) {
        SharedPreferences sharedPreferences = mContext.getSharedPreferences("widget", MODE_PRIVATE);
        String payload = sharedPreferences.getString("widget", "");
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.remove("widget");
        editor.apply();
        callback.invoke(payload);
    }

    @ReactMethod
    public void gotoNotificationSetting() {
        Activity activity = reactContext.getCurrentActivity();
        if (activity != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                //这种方案适用于 API 26, 即8.0（含8.0）以上可以用
                Intent intent = new Intent();
                intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
                intent.putExtra(Settings.EXTRA_APP_PACKAGE, activity.getPackageName());
                intent.putExtra(Settings.EXTRA_CHANNEL_ID, activity.getApplicationInfo().uid);
                activity.startActivity(intent);
            } else {
                if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.LOLLIPOP_MR1) {
                    try {
                        Intent intent = new Intent(Settings.ACTION_SETTINGS);
                        activity.startActivity(intent);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    try {
                        Intent localIntent = new Intent();
                        localIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        localIntent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                        localIntent.setData(Uri.fromParts("package", activity.getPackageName(), null));
                        activity.startActivity(localIntent);
                    } catch (Exception e) {
                        e.printStackTrace();
                        try {
                            Intent intent = new Intent(Settings.ACTION_SETTINGS);
                            activity.startActivity(intent);
                        } catch (Exception er) {
                            er.printStackTrace();
                        }
                    }
                }
            }
        }
    }

    @ReactMethod
    public void setDebugMode(boolean enable) {
        if (enable) {
            PushManager.getInstance().setDebugLogger(mContext, new IUserLoggerInterface() {
                @Override
                public void log(String s) {
                    GetuiLogger.log(s);
                }
            });
        }
    }

    @ReactMethod
    public void onPush() {
        PushManager.getInstance().turnOnPush(mContext);
    }

    @ReactMethod
    public void stopPush() {
        PushManager.getInstance().turnOffPush(mContext);
    }

    @ReactMethod
    public void isPushTurnedOn(Callback callback) {
        boolean isPushTurnedOn = PushManager.getInstance().isPushTurnedOn(mContext);
        if (callback == null) {
            GetuiLogger.log("callback null");
            return;
        }
        callback.invoke(isPushTurnedOn);
    }

    @ReactMethod
    public void getRegistrationID(Callback callback) {
        if (callback == null) {
            GetuiLogger.log("callback null");
            return;
        }

        SharedPreferences sharedPreferences = mContext.getSharedPreferences("data", MODE_PRIVATE);
        String payload = sharedPreferences.getString("payload", "");
        GetuiLogger.log(payload);
        WritableMap notification = Arguments.createMap();
        notification.putString("notificationEventType", "notificationOpened");
        notification.putString("extras", payload);
        sendEvent("NotificationEvent", notification);

        SharedPreferences.Editor editor = sharedPreferences.edit();//获取Editor
        editor.remove("payload");
        editor.commit();

        String registrationID = PushManager.getInstance().getClientid(mContext);
        GetuiLogger.log("registrationID = " + registrationID);
        WritableMap writableMap = Arguments.createMap();
        writableMap.putString("registerID", registrationID);
        callback.invoke(writableMap);
    }

    @ReactMethod
    public void deleteNotify() {
        SharedPreferences sharedPreferences = mContext.getSharedPreferences("data", MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();//获取Editor
        editor.remove("payload");
        editor.commit();
    }

    @ReactMethod
    public void isNotificationEnabled(Callback callback) {
        callback.invoke(areNotificationsEnabled(mContext));
    }

    @ReactMethod
    public void openNotification(){
        PushManager.getInstance().openNotification(mContext);
    }

    @ReactMethod
    public void isDark(Callback callback) {
        int currentNightMode = reactContext.getResources().getConfiguration().uiMode & Configuration.UI_MODE_NIGHT_MASK;
        switch (currentNightMode) {
            case Configuration.UI_MODE_NIGHT_NO:
                // Night mode is not active, we're using the light theme
                callback.invoke("light");
                break;
            case Configuration.UI_MODE_NIGHT_YES:
                // Night mode is active, we're using dark theme
                callback.invoke("dark");
                break;
        }
    }

    @ReactMethod
    public void setNavigationBarColor(String color) {
        Activity activity = reactContext.getCurrentActivity();
        Window window = activity.getWindow();
//        runOnUiThread(new Runnable() {
//            @Override
//            public void run() {
////                window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
//                if (color.equals("white")) {
//                    window.setNavigationBarColor(ContextCompat.getColor(reactContext, R.color.light_color));
//                } else if (color.equals("bgColor")) {
//                    window.setNavigationBarColor(ContextCompat.getColor(reactContext, R.color.black_color));
//                } else {
//                    window.setNavigationBarColor(ContextCompat.getColor(reactContext, R.color.light_color));
//                }
//            }
//        });
    }

    @ReactMethod
    public void exitApp() {
//        android.os.Process.killProcess(android.os.Process.myPid());
        Activity activity = reactContext.getCurrentActivity();
        Intent home = new Intent(Intent.ACTION_MAIN);
        home.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        home.addCategory(Intent.CATEGORY_HOME);
        activity.startActivity(home);
    }

    @ReactMethod
    public void openAPP(){
        openApp(mContext);
    }

    public static void launchApp(Context context) {
        try {
            Intent intent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP);
            context.startActivity(intent);
        } catch (Throwable throwable) {
            GetuiLogger.log("");
        }
    }

    /**
     * 判断通知栏权限是否打开 * @param context * @return
     */
    public static boolean areNotificationsEnabled(Context context) {

        NotificationManagerCompat.from(context).areNotificationsEnabled();
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {

            return true;
        }
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {

            return isEnableV19(context);
        } else {

            return isEnableV26(context);
        }
    }


    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    private static boolean isEnableV19(Context context) {

        final String CHECK_OP_NO_THROW = "checkOpNoThrow";
        final String OP_POST_NOTIFICATION = "OP_POST_NOTIFICATION";
        AppOpsManager mAppOps = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
        ApplicationInfo appInfo = context.getApplicationInfo();
        String pkg = context.getApplicationContext().getPackageName();
        int uid = appInfo.uid;
        Class appOpsClass = null; /* Context.APP_OPS_MANAGER */
        try {

            appOpsClass = Class.forName(AppOpsManager.class.getName());
            Method checkOpNoThrowMethod = appOpsClass.getMethod(CHECK_OP_NO_THROW, Integer.TYPE, Integer.TYPE, String.class);
            Field opPostNotificationValue = appOpsClass.getDeclaredField(OP_POST_NOTIFICATION);
            int value = (int) opPostNotificationValue.get(Integer.class);
            return ((int) checkOpNoThrowMethod.invoke(mAppOps, value, uid, pkg) == AppOpsManager.MODE_ALLOWED);
        } catch (ClassNotFoundException e) {

        } catch (NoSuchMethodException e) {

        } catch (NoSuchFieldException e) {

        } catch (InvocationTargetException e) {

        } catch (IllegalAccessException e) {

        } catch (Exception e) {

        }
        return false;
    }


    private static boolean isEnableV26(Context context) {

        ApplicationInfo appInfo = context.getApplicationInfo();
        String pkg = context.getApplicationContext().getPackageName();
        int uid = appInfo.uid;
        try {

            NotificationManager notificationManager = (NotificationManager)
                    context.getSystemService(Context.NOTIFICATION_SERVICE);
            Method sServiceField = notificationManager.getClass().getDeclaredMethod("getService");
            sServiceField.setAccessible(true);
            Object sService = sServiceField.invoke(notificationManager);

            Method method = sService.getClass().getDeclaredMethod("areNotificationsEnabledForPackage"
                    , String.class, Integer.TYPE);
            method.setAccessible(true);
            return (boolean) method.invoke(sService, pkg, uid);
        } catch (Exception e) {

            return true;
        }
    }

    public static void sendEvent(String eventName, WritableMap params) {
        try {
            GetuiModule.reactContext.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class).emit(eventName, params);
        } catch (Throwable throwable) {
            GetuiLogger.log("sendEvent error:" + throwable.getMessage());
        }
    }

    private void openApp(Context context) {
        try {
            Intent launchIntent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
            context.startActivity(launchIntent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //*****************************应用前后台状态监听*****************************
    public static void registerActivityLifecycle(Application application) {
        application.registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(Activity activity, Bundle bundle) {
                GetuiLogger.log("onActivityCreated");
            }

            @Override
            public void onActivityStarted(Activity activity) {
                GetuiLogger.log("onActivityStarted");
            }

            @Override
            public void onActivityResumed(Activity activity) {
                GetuiLogger.log("onActivityResumed");
                isAppForeground = true;
            }

            @Override
            public void onActivityPaused(Activity activity) {
                GetuiLogger.log("onActivityPaused");
                isAppForeground = false;
            }

            @Override
            public void onActivityStopped(Activity activity) {
                GetuiLogger.log("onActivityStopped");
            }

            @Override
            public void onActivitySaveInstanceState(Activity activity, Bundle bundle) {
                GetuiLogger.log("onActivitySaveInstanceState");
            }

            @Override
            public void onActivityDestroyed(Activity activity) {
                GetuiLogger.log("onActivityDestroyed");
            }
        });
    }

}
