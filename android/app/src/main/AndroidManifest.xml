<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature android:name="android.hardware.camera.autofocus" />
    <uses-feature android:name="android.hardware.audio.output" />
    <uses-feature android:name="android.hardware.microphone" />
    <application
        android:name=".MainApplication"
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:supportsRtl"
        >
        <activity
            android:name=".MainActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="true"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="com.presen" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="presen"
                    android:host="safepay" />
            </intent-filter>
        </activity>

        <activity
            android:name=".GetuiViewActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoDisplay">
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="com.presen.push"
                    android:path="/detail"
                    android:scheme="presenpushscheme" />
            </intent-filter>
        </activity>

        <activity
            android:name=".WidgetActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoDisplay">
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="presen_widget" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="com.transistorsoft.locationmanager.license"
            android:value="56920d78b2c70ac1a8c6f831e6fb315f5a42ef76c058fe016c9c94425bc45bc7" />
        <meta-data
            android:name="PUSH_APPID"
            android:value="nhwfKoDhpZAqLPixxarIpA" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyBcZgAHo_kkpzJBoy5KRKumfkJUDJKJPN8" />
        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="23a06fadbbf30d80cb3bb4685821c23f" />
        <meta-data
            android:name="TUYA_SMART_APPKEY"
            android:value="sd4xvmpdk7nq78w8k8m8" />
        <meta-data
            android:name="TUYA_SMART_SECRET"
            android:value="3h5xjgmmehcajsrq3dwvvndgvfr4ykmg" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_stat_push_small" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/colorAccent"
            tools:replace="android:resource" />
        <receiver
            android:name=".PresenAppWidget"
            android:exported="false">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/presen_app_widget_info" />
        </receiver>
        <service
            android:name=".GetuiPushService"
            android:exported="false"
            android:label="PushService"
            android:process=":pushservice" />
        <service android:name=".GetuiIntentService" />
        <service
            android:name="com.reactnativeandroidwidget.RNWidgetCollectionService"
            android:permission="android.permission.BIND_REMOTEVIEWS" />
    </application>
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- 这个权限用于进行网络定位 -->
    <uses-permission android:name="android.permission.VIBRATE" /> <!-- 这个权限用于访问GPS定位 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" /> <!-- 用于访问wifi网络信息，wifi信息会用于进行网络定位 -->
    <uses-permission android:name="android.permission.CAMERA" /> <!-- 获取运营商信息，用于支持提供运营商信息相关的接口 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- 这个权限用于获取wifi的获取权限，wifi信息会用来进行网络定位 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- 用于读取手机当前的状态 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>
    
    <uses-permission android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <uses-permission
        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <uses-permission android:name="android.permission.GET_TASKS" />

    <queries>
        <intent>
            <action android:name="com.getui.sdk.action" />
        </intent>
    </queries>

</manifest>