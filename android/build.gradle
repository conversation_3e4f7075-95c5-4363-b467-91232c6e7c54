// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        googlePlayServicesLocationVersion = "21.0.1"
        kotlin_version = "1.9.22"

        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.0.21"
    }
    repositories {
        google()
        mavenCentral()
        maven { url 'https://developer.huawei.com/repo/' }
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.2.1")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
    
        classpath("de.undercouch:gradle-download-task:5.0.1")
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.huawei.agconnect:agcp:1.9.1.300'
    }
}

allprojects {
    repositories {
        // maven {
        //     // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
        //     url("$rootDir/../node_modules/react-native/android")
        // }
        // maven {
        //     // Android JSC is installed from npm
        //     url("$rootDir/../node_modules/jsc-android/dist")
        // }
        // mavenCentral {
        //     // We don't want to fetch react-native from Maven Central as there are
        //     // older versions over there.
        //     content {
        //         excludeGroup "com.facebook.react"
        //     }
        // }
        google()
        maven { url 'https://www.jitpack.io' }
        maven { url 'https://developer.huawei.com/repo/' }
        maven {
            url "https://mvn.getui.com/nexus/content/repositories/releases/"
        }
        maven { url 'https://maven-other.tuya.com/repository/maven-releases/' }
        maven { url "https://maven-other.tuya.com/repository/maven-commercial-releases/" }
        maven {
            // Required for react-native-background-geolocation
            url("${project(':react-native-background-geolocation').projectDir}/libs")
        }
        maven {
            // Required for react-native-background-fetch
            url("${project(':react-native-background-fetch').projectDir}/libs")
        }
    }
}
apply plugin: "com.facebook.react.rootproject"