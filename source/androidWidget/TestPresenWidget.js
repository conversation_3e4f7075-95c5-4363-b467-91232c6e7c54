/* eslint-disable react/react-in-jsx-scope */
import {View, StyleSheet, useWindowDimensions} from 'react-native';
import {WidgetPreview} from 'react-native-android-widget';
import {PresenWidget} from './PresenWidget';

export default function TestPresenWidget({scens}) {
  const {width} = useWindowDimensions();

  return (
    <View style={styles.container}>
      <WidgetPreview
        renderWidget={() => (
          <PresenWidget
            width={width}
            height={200}
            scenes={scens}
            noLogin={'ssssssssssssssssss'}
          />
        )}
        width={width}
        height={200}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
