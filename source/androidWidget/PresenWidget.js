import React from 'react';
import {
  FlexWidget,
  TextWidget,
  ImageWidget,
  OverlapWidget,
} from 'react-native-android-widget';
import { getImageFromKey } from '../Tools';
import { Tme, IsDark } from '../ThemeStyle';

export function PresenWidget(props) {
  const { scenes } = props;
  console.log('info', props);
  if (scenes.length === 0) {
    return (
      <OverlapWidget
        clickAction="OPEN_APP"
        style={{
          height: 'match_parent',
          width: 'match_parent',
        }}>
        {/* <ImageWidget
          imageWidth={props.width}
          imageHeight={props.height}
          radius={10}
          image={require('../../img/background.png')}
        /> */}
        <FlexWidget
          style={{
            borderRadius: 10,
            alignItems: 'center',
            width: 'match_parent',
            height: 'match_parent',
            padding: 20,
            backgroundColor: IsDark() ? 'rgb(72,77,80)' : 'rgb(255,255,255)',
          }}>
          {/* <FlexWidget
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              width: 'match_parent',
            }}>
            <ImageWidget
              imageWidth={30}
              imageHeight={30}
              radius={6}
              image={require('../../img/presen-logo.jpg')}
            />
            <TextWidget
              text="Presen"
              style={{
                fontSize: 14,
                marginLeft: 10,
                color: Tme('cardTextColor'),
              }}
            />
          </FlexWidget> */}
          <FlexWidget
            style={{
              marginTop: 20,
              alignItems: 'center',
              justifyContent: 'center',
              width: 'match_parent',
            }}>
            <TextWidget
              text={props.noLogin}
              truncate="MIDDLE"
              maxLines={1}
              style={{
                fontSize: 16,
                marginTop: 4,
                color: Tme('cardTextColor'),
              }}
            />
          </FlexWidget>
        </FlexWidget>
      </OverlapWidget>
    );
  }

  return (
    <OverlapWidget
      style={{
        height: 'match_parent',
        width: 'match_parent',
      }}>
      <FlexWidget
        clickAction="OPEN_APP"
        style={{
          alignItems: 'center',
          width: 'match_parent',
          height: 'match_parent',
          borderRadius: 10,
          padding: 20,
          backgroundColor: IsDark() ? 'rgb(72,77,80)' : 'rgb(255,255,255)',
        }}>
        {/* <FlexWidget
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            width: 'match_parent',
          }}>
          <ImageWidget
            imageWidth={30}
            imageHeight={30}
            radius={6}
            image={require('../../img/presen-logo.jpg')}
          />
          <TextWidget
            text="Presen"
            style={{
              fontSize: 14,
              marginLeft: 10,
              fontWeight: 'bold',
              // color: Tme('cardTextColor'),
              color: 'red',
            }}
          />
        </FlexWidget> */}
        <FlexWidget
          style={{
            marginTop: 40,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-around',
            width: 'match_parent',
          }}>
          {scenes[0] && (
            <FlexWidget
              clickAction="click"
              clickActionData={{
                data: scenes[0],
              }}
              style={{
                width: props.width / 4 - 20,
                paddingVertical: 16,
                backgroundColor: Tme('cardColor2'),
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: 10,
                paddingHorizontal: 4,
              }}>
              <ImageWidget
                imageWidth={25}
                imageHeight={25}
                image={getImageFromKey(scenes[0].icon + '.png').value}
              />
              <FlexWidget
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginTop: 4,
                }}>
                {scenes[0].isEnabled && (
                  <FlexWidget
                    style={{
                      width: 6,
                      height: 6,
                      borderRadius: 4,
                      backgroundColor: '#fc577a',
                      marginRight: 4,
                    }}
                  />
                )}
                <TextWidget
                  text={scenes[0].name}
                  truncate="MIDDLE"
                  maxLines={1}
                  style={{
                    fontSize: 12,
                    color: Tme('cardTextColor'),
                  }}
                />
              </FlexWidget>
            </FlexWidget>
          )}
          {scenes[1] && (
            <FlexWidget
              clickAction="click"
              clickActionData={{
                data: scenes[1],
              }}
              style={{
                width: props.width / 4 - 20,
                paddingVertical: 16,
                backgroundColor: Tme('cardColor2'),
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: 10,
                paddingHorizontal: 4,
              }}>
              <ImageWidget
                imageWidth={25}
                imageHeight={25}
                image={getImageFromKey(scenes[1].icon + '.png').value}
              />
              <FlexWidget
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginTop: 4,
                }}>
                {scenes[1].isEnabled && (
                  <FlexWidget
                    style={{
                      width: 6,
                      height: 6,
                      borderRadius: 4,
                      backgroundColor: '#fc577a',
                      marginRight: 4,
                    }}
                  />
                )}
                <TextWidget
                  text={scenes[1].name}
                  truncate="MIDDLE"
                  maxLines={1}
                  style={{
                    fontSize: 12,
                    color: Tme('cardTextColor'),
                  }}
                />
              </FlexWidget>
            </FlexWidget>
          )}
          {scenes[2] && (
            <FlexWidget
              clickAction="click"
              clickActionData={{
                data: scenes[2],
              }}
              style={{
                width: props.width / 4 - 20,
                paddingVertical: 16,
                backgroundColor: Tme('cardColor2'),
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: 10,
                paddingHorizontal: 4,
              }}>
              <ImageWidget
                imageWidth={25}
                imageHeight={25}
                image={getImageFromKey(scenes[2].icon + '.png').value}
              />
              <FlexWidget
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginTop: 4,
                }}>
                {scenes[2].isEnabled && (
                  <FlexWidget
                    style={{
                      width: 6,
                      height: 6,
                      borderRadius: 4,
                      backgroundColor: '#fc577a',
                      marginRight: 4,
                    }}
                  />
                )}
                <TextWidget
                  text={scenes[2].name}
                  truncate="MIDDLE"
                  maxLines={1}
                  style={{
                    fontSize: 12,
                    color: Tme('cardTextColor'),
                  }}
                />
              </FlexWidget>
            </FlexWidget>
          )}
          {scenes[3] && (
            <FlexWidget
              clickAction="click"
              clickActionData={{
                data: scenes[3],
              }}
              style={{
                width: props.width / 4 - 20,
                paddingVertical: 16,
                backgroundColor: Tme('cardColor2'),
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: 10,
                paddingHorizontal: 4,
              }}>
              <ImageWidget
                imageWidth={25}
                imageHeight={25}
                image={getImageFromKey(scenes[3].icon + '.png').value}
              />
              <FlexWidget
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginTop: 4,
                }}>
                {scenes[3].isEnabled && (
                  <FlexWidget
                    style={{
                      width: 6,
                      height: 6,
                      borderRadius: 4,
                      backgroundColor: '#fc577a',
                      marginRight: 4,
                    }}
                  />
                )}
                <TextWidget
                  text={scenes[3].name}
                  truncate="MIDDLE"
                  maxLines={1}
                  style={{
                    fontSize: 12,
                    color: Tme('cardTextColor'),
                  }}
                />
              </FlexWidget>
            </FlexWidget>
          )}
        </FlexWidget>
      </FlexWidget>
    </OverlapWidget>
  );
}
