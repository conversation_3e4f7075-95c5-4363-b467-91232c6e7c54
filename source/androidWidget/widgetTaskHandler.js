import {Linking} from 'react-native';
import GetuiPush from '../Getui';
import AsyncStorage from '@react-native-async-storage/async-storage';
import I18n from '../I18n';
import {PresenWidget} from './PresenWidget';

export async function widgetTaskHandler(props) {
  switch (props.widgetAction) {
    case 'WIDGET_ADDED':
      const d = await AsyncStorage.getItem('androidWidgetData');
      const add = d ? JSON.parse(d) : [];
      console.log('add', props);
      props.renderWidget(
        // eslint-disable-next-line react/react-in-jsx-scope
        <PresenWidget
          width={props.widgetInfo.width}
          height={props.widgetInfo.height}
          scenes={add}
          noLogin={I18n.t('setting.widget_login')}
        />,
      );
      break;
    case 'WIDGET_UPDATE':
      const da = await AsyncStorage.getItem('androidWidgetData');
      const upD = da ? JSON.parse(da) : [];
      props.renderWidget(
        // eslint-disable-next-line react/react-in-jsx-scope
        <PresenWidget
          width={props.widgetInfo.width}
          height={props.widgetInfo.height}
          scenes={upD}
          noLogin={I18n.t('setting.widget_login')}
        />,
      );
      break;
    case 'WIDGET_RESIZED':
      console.log('re', props);
      break;
    case 'WIDGET_CLICK':
      if (props.clickAction === 'click') {
        const {data} = props.clickActionData;
        await AsyncStorage.setItem('isGetUrlAsync', '2');
        Linking.openURL(`com.presen://${data.uuid},${data.type}`);
      }
      break;
    default:
      GetuiPush.openAPP();
      break;
  }
}
