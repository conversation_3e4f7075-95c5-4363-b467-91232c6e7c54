import { useEffect, useRef, useCallback } from 'react';
import { Platform } from 'react-native';
import { Router } from './Router';
import { Helper, HelperMemo } from './Helper';
import I18n from './I18n';
import { PERMISSIONS, request } from 'react-native-permissions';
import { getApp } from '@react-native-firebase/app';
import {
  getMessaging,
  getToken,
  onMessage,
  onTokenRefresh,
  getInitialNotification,
  onNotificationOpenedApp,
  AuthorizationStatus,
} from '@react-native-firebase/messaging';

// 引入Notifications用于创建前台通知
import notifee, { EventType, AndroidImportance } from '@notifee/react-native';
import PubSub from 'pubsub-js';
import { PubSubEvent } from './types/PubSubEvent';
import { Colors } from './ThemeStyle';

export const backgroundMessageHandler = async remoteMessage => {
  console.log('收到后台消息:', JSON.stringify(remoteMessage));
  // 在后台，系统会自动显示通知，这里只需要返回Promise
  return Promise.resolve();
};

// 处理消息跳转的工具函数
const handleNotificationNavigation = (navigation, data) => {
  console.log('准备处理通知跳转数据:', JSON.stringify(data));

  // 检查数据是否有效，如果无效则直接返回
  if (!data || Object.keys(data).length === 0) {
    console.warn('通知数据为空或无效，无法处理跳转');
    return;
  }

  // 尝试解析数据（如果数据是字符串形式的JSON）
  let parsedData = data;
  if (typeof data === 'string') {
    try {
      parsedData = JSON.parse(data);
      console.log('成功解析字符串格式的通知数据:', JSON.stringify(parsedData));
    } catch (e) {
      console.warn('解析通知数据字符串失败:', e);
      // 继续使用原始数据
    }
  }

  // 检查data字段（FCM可能嵌套data对象）
  if (parsedData.data && typeof parsedData.data === 'object') {
    console.log('检测到嵌套的data字段，使用内部数据');
    parsedData = parsedData.data;
  }

  console.log('最终处理的通知数据:', JSON.stringify(parsedData));

  if (parsedData.type === 'spec_change') {
    // 新增：特殊处理IPC设备通知
    if (parsedData.urgent === 'true' || parsedData.urgent === true) {
      setTimeout(() => {
        navigation.popToTop();
        HelperMemo.message = parsedData;
        navigation.navigate('Tabs', {
          screen: 'homeScreen',
        });
        PubSub.publish(PubSubEvent.EVENT_APP_NOTIFY);
      }, 100);
    } else {
      if (parsedData.dv_type){
        if (parsedData.dv_type === 'ipc') {
          setTimeout(() => {
            navigation.navigate('Tabs', {
              screen: 'homeScreen',
            });
            navigation.push('IpcList', {
              initialTab: 'events',
              initialDeviceUUID: parsedData.uuid,
              title: I18n.t('home.event'),
            });
          }, 500);
        } else {
          setTimeout(() => {
            navigation.navigate('Tabs', {
              screen: 'homeScreen',
            });
            navigation.push('eventScreen', {
              title: I18n.t('home.event'),
            });
          }, 500);
        }
      }else {
        setTimeout(() => {
          navigation.navigate('Tabs', {
            screen: 'homeScreen',
          });
          Router.pushDeviceShow(navigation, {
            data: {uuid: parsedData.uuid, notify: true},
          });
        }, 500);
      }
    }
  } else if (parsedData.type === 'event') {
    setTimeout(() => {
      navigation.navigate('Tabs', {
        screen: 'homeScreen',
      });
      navigation.push('eventScreen', {
        sn_id: parsedData.sn_id,
        title: I18n.t('home.event'),
      });
    }, 500);
  } else {
    console.log('未知的通知类型或无效数据结构:', JSON.stringify(parsedData));
  }
};

// 创建本地通知的函数
const createLocalNotification = async (remoteMessage) => {
  try {
    // 提取通知数据
    const notification = remoteMessage.notification || {};
    const data = remoteMessage.data || {};

    // 确保通知有标题和内容
    const title = notification.title || data.title;
    const body = notification.body || data.body;

    // 如果没有标题或内容，则不创建通知
    if (!title || !body) {
      console.warn('无法创建本地通知: 缺少标题或内容', { title, body });
      return;
    }

    // 确定通知重要程度和通道ID
    const isUrgent = data.urgent === 'true' || data.urgent === true;
    const channelId = isUrgent ? 'com.presen.urgent' : 'com.presen.message';

    // 显示通知
    await notifee.displayNotification({
      title: title,
      body: body,
      android: {
        smallIcon: 'ic_stat_push_small',
        color: Colors.MainColor, // 设置通知图标颜色为白色
        channelId: notification.android?.channelId || channelId,
        importance: isUrgent ? AndroidImportance.HIGH : AndroidImportance.DEFAULT,
        pressAction: {
          id: 'default',
        },
      },
      data: data, // 存储原始数据，用于用户点击时处理
    });
  } catch (error) {
    console.error('创建本地通知失败:', error);
  }
};

// 在应用启动时设置后台通知处理程序
notifee.onBackgroundEvent(async ({ type, detail }) => {
  if (type === EventType.PRESS) {
    // 处理通知点击
    // 储存数据以便应用启动时处理
    if (detail.notification && detail.notification.data) {
      console.log('后台收到通知点击，数据:', JSON.stringify(detail.notification.data));
      // 使用AsyncStorage存储最后点击的通知数据，以便冷启动时使用
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        await AsyncStorage.setItem('LAST_NOTIFICATION_DATA', JSON.stringify(detail.notification.data));
      } catch (error) {
        console.error('保存通知数据失败:', error);
      }
    }
  }
});

export function FcmNotification({ navigation }) {
  // 引用保存消息订阅的取消函数
  const unsubscribeRef = useRef({
    foregroundMessages: null,
    backgroundOpened: null,
  });

  // 加载上次点击通知的数据
  const loadLastNotificationData = useCallback(async () => {
    try {
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      const savedDataString = await AsyncStorage.getItem('LAST_NOTIFICATION_DATA');

      if (savedDataString) {
        const savedData = JSON.parse(savedDataString);
        console.log('检测到上次点击的通知数据:', savedData);

        // 处理跳转并清除已使用的数据
        handleNotificationNavigation(navigation, savedData);
        await AsyncStorage.removeItem('LAST_NOTIFICATION_DATA');
      }
    } catch (error) {
      console.error('加载上次通知数据失败:', error);
    }
  }, [navigation]);

  // 设置FCM相关功能
  const setupFCM = useCallback(async () => {
    if (Platform.OS !== 'android') { return; }

    try {
      // 初始化 Firebase 应用
      const app = getApp();
      const messagingInstance = getMessaging(app);

      // 注册设备以接收远程消息
      await messagingInstance.registerDeviceForRemoteMessages();

      // 获取FCM令牌
      const fcmToken = await getToken(messagingInstance);
      console.log('FCM Token:', fcmToken);

      if (fcmToken) {
        // 保存令牌到服务器
        Helper.httpPOST(
          '/device_tokens',
          {
            ignore_error: true,
            success: data => {
              HelperMemo.token_id = data.device_token.id;
              HelperMemo.device_token = fcmToken;
            },
            error: (err) => {
              console.error('保存设备令牌失败', err);
            },
          },
          {
            token: fcmToken,
            token_type: 'gcm',
            device_type: Platform.OS,
          },
        );
      }

      // 先加载上次点击通知的数据（如果存在）
      await loadLastNotificationData();

      // 处理应用因通知打开的情况（冷启动）
      getInitialNotification(messagingInstance)
        .then(remoteMessage => {
          if (remoteMessage) {
            // 处理跳转
            const data = remoteMessage.data || {};
            console.log('应用冷启动通过通知，数据:', JSON.stringify(data));
            handleNotificationNavigation(navigation, data);
          }
        });

      // 处理应用在后台时通知被点击的情况
      const unsubscribeFromBackgroundOpened = onNotificationOpenedApp(messagingInstance,
        remoteMessage => {
          // 处理跳转
          const data = remoteMessage.data || {};
          console.log('应用从后台唤醒通过通知，数据:', JSON.stringify(data));
          handleNotificationNavigation(navigation, data);
        }
      );
      // 保存取消订阅函数
      unsubscribeRef.current.backgroundOpened = unsubscribeFromBackgroundOpened;

      // 处理应用在前台时接收到的消息
      const unsubscribeFromForegroundMessages = onMessage(messagingInstance, async remoteMessage => {
        console.log('前台收到消息:', JSON.stringify(remoteMessage));
        if (remoteMessage.originalPriority === 0 && remoteMessage.sentTime === 0 && remoteMessage.ttl === 0) {
        } else {
          await createLocalNotification(remoteMessage);
        }
        // 在前台收到消息时创建本地通知

      });
      // 保存取消订阅函数
      unsubscribeRef.current.foregroundMessages = unsubscribeFromForegroundMessages;

      // 设置通知点击处理（适用于由notifee创建的本地通知）
      notifee.onForegroundEvent(({ type, detail }) => {
        if (type === EventType.PRESS) {
          // 处理本地通知点击
          if (detail.notification && detail.notification.data) {
            console.log('前台通知点击，数据:', JSON.stringify(detail.notification.data));
            handleNotificationNavigation(navigation, detail.notification.data);
          }
        }
      });

      // 监听令牌刷新
      onTokenRefresh(messagingInstance, newToken => {
        // 更新服务器上的令牌
        Helper.httpPOST(
          '/device_tokens',
          {
            ignore_error: true,
            success: data => {
              HelperMemo.token_id = data.device_token.id;
              HelperMemo.device_token = newToken;
            },
            error: () => { },
          },
          {
            token: newToken,
            token_type: 'gcm',
            device_type: Platform.OS,
          },
        );
      });
    } catch (error) {
      console.error('设置FCM失败:', error);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigation]);

  // 仅在Android平台使用Firebase消息
  useEffect(() => {
    if (Platform.OS !== 'android') { return; }

    // 请求Android通知权限
    request(PERMISSIONS.ANDROID.POST_NOTIFICATIONS).then((result) => {
      // console.log('通知权限结果:', result);
    });

    // 保存取消订阅函数的引用，以便在清理时使用
    let foregroundMessagesUnsubscribe = null;
    let backgroundOpenedUnsubscribe = null;

    // 设置引用更新的函数
    function updateUnsubscribeFunctions() {
      foregroundMessagesUnsubscribe = unsubscribeRef.current.foregroundMessages;
      backgroundOpenedUnsubscribe = unsubscribeRef.current.backgroundOpened;
    }

    // 监听ref变化
    const intervalId = setInterval(updateUnsubscribeFunctions, 1000);
    updateUnsubscribeFunctions();

    const setupMessaging = async () => {
      try {
        // 请求用户权限
        const app = getApp();
        const messagingInstance = getMessaging(app);
        const authStatus = await messagingInstance.requestPermission();
        const enabled =
          authStatus === AuthorizationStatus.AUTHORIZED ||
          authStatus === AuthorizationStatus.PROVISIONAL;

        if (enabled) {
          // 继续设置推送通知
          await setupFCM();
          // 在设置完成后立即更新取消订阅函数
          updateUnsubscribeFunctions();
        } else {
          // console.log('用户拒绝了推送通知权限');
        }
      } catch (error) {
        console.error('设置消息权限失败:', error);
      }
    };

    setupMessaging();

    // 添加应用状态监听，以便在应用从后台恢复时检查通知数据
    const AppState = require('react-native').AppState;
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (nextAppState === 'active') {
        console.log('应用从后台恢复，检查是否有待处理的通知');
        loadLastNotificationData();
      }
    });

    // 返回清理函数，使用本地变量而不是ref
    return () => {
      clearInterval(intervalId);
      if (foregroundMessagesUnsubscribe) {
        foregroundMessagesUnsubscribe();
      }
      if (backgroundOpenedUnsubscribe) {
        backgroundOpenedUnsubscribe();
      }
      subscription.remove();
    };
  }, [navigation, setupFCM, loadLastNotificationData]);

  return null;
}
