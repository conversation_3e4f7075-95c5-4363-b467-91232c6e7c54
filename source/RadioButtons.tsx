import React, { useState, useCallback } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { Tme } from './ThemeStyle';

interface RadioButtonItem {
  key: string;
  value: string;
}

interface RadioButtonsProps {
  data: RadioButtonItem[];
  defaultKey?: string;
  backgroundColor?: string;
  onChange: (key: string) => void;
}

const RadioButtons: React.FC<RadioButtonsProps> = ({
  data,
  defaultKey = '',
  backgroundColor,
  onChange,
}) => {
  const [checkedKey, setCheckedKey] = useState<string>(defaultKey);

  const handleClick = useCallback(
    (key: string) => {
      setCheckedKey(key);
      onChange(key);
    },
    [onChange, setCheckedKey],
  );

  return (
    <View
      style={{
        backgroundColor: backgroundColor || Tme('cardColor'),
      }}>
      <View style={{ paddingHorizontal: 20 }}>
        {data.map((v, k) => (
          <TouchableOpacity
            testID="radioBtn"
            key={v.key}
            activeOpacity={0.8}
            onPress={() => handleClick(v.key)}
            style={[
              {
                height: 59,
              },
              k !== data.length - 1 && {
                borderBottomWidth: 1,
                borderBottomColor: Tme('inputBorderColor'),
              },
            ]}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <Text
                style={{
                  lineHeight: 59,
                  fontSize: 17,
                  color: Tme('cardTextColor'),
                }}>
                {v.value}
              </Text>
              {v.key === (checkedKey || defaultKey) && (
                <MaterialIcons
                  name="check"
                  size={24}
                  color={Tme('cardTextColor')}
                />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

export default RadioButtons;
