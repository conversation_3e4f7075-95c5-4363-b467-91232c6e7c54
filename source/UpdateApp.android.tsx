import React, { useEffect } from 'react';
import { Linking } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Helper, HelperMemo } from './Helper';
import DeviceInfo from 'react-native-device-info';
import I18n from 'react-native-i18n';
import AlertModal from './share/AlertModal';
import notifee, { AndroidImportance } from '@notifee/react-native';

const UpdateApp: React.FC = () => {
  const skipVersion = async (v: string) => {
    await AsyncStorage.setItem('android_skip_version', v);
  };

  useEffect(() => {
    const createNotificationChannels = async () => {
      await notifee.createChannel({
        id: 'com.presen.message',
        name: 'Presen Message',
        importance: AndroidImportance.HIGH,
        sound: 'default',
        bypassDnd: true, // 免打扰模式下仍然显示
      });

      await notifee.createChannel({
        id: 'com.presen.urgent',
        name: 'Presen Urgent',
        importance: AndroidImportance.HIGH,
        sound: 'message',
        bypassDnd: true, // 免打扰模式下仍然显示
      });
    };

    const checkUpdate = async () => {
      const id = DeviceInfo.getVersion();
      Helper.httpGET('/update_app/check_update?id=' + id, {
        success: async (data) => {
          if (data.message === '1') {
            const value = await AsyncStorage.getItem('android_skip_version');
            if (value !== data.app_name) {
              if (HelperMemo.user_data.appUpdateNotify !== data.down_url) {
                await AsyncStorage.mergeItem(
                  'user_data',
                  JSON.stringify({ appUpdateNotify: data.down_url }),
                );
                HelperMemo.user_data.appUpdateNotify = data.down_url;

                AlertModal.alert(
                  I18n.t('home.have_version'),
                  I18n.t('home.vs_update'),
                  [
                    {
                      text: I18n.t('home.skip_vs'),
                      onPress: () => skipVersion(data.app_name),
                    },
                    { text: I18n.t('home.cancel') },
                    {
                      text: I18n.t('home.confirm'),
                      onPress: () => Linking.openURL(data.down_url),
                    },
                  ],
                );
              }
            }
          }
        },
        error: () => { },
      });
    };

    createNotificationChannels();
    checkUpdate();
  }, []);

  return null;
};

export default UpdateApp;
