import { useState, useEffect } from 'react';
import { AppState, Platform } from 'react-native';
import { HelperMemo } from './Helper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import GetuiPush from './Getui';
import SharedStorageData from './share/SharedStorage';
import PubSub from 'pubsub-js';
import { PubSubEvent } from './types/PubSubEvent';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

const BackgroundStateRe = /inactive|background/i;

interface AppStateCenterProps {
  navigation: NativeStackNavigationProp<any>;
}

interface WidgetData {
  uuid: string;
  name: string;
  type: string;
}

const AppStateCenter: React.FC<AppStateCenterProps> = ({ navigation }) => {
  const [appState, setAppState] = useState<string>(AppState.currentState);

  const handleAppStateChange = (nextAppState: string): void => {
    if (appState.match(BackgroundStateRe) && nextAppState === 'active') {
      PubSub.publish(PubSubEvent.EVENT_APP_STATE_ACTIVE);
    } else if (
      nextAppState.match(BackgroundStateRe) &&
      appState === 'active'
    ) {
      PubSub.publish(PubSubEvent.EVENT_APP_STATE_BACKGROUND);
    }
    setAppState(nextAppState);

    //安卓紧急通知
    if (Platform.OS === 'android') {
      if (nextAppState === 'active') {
        if (HelperMemo.message) {
          setTimeout(() => {
            navigation.navigate('Tabs', {
              screen: 'homeScreen',
              params: { reset: true },
            });
            PubSub.publish(PubSubEvent.EVENT_APP_NOTIFY);
          }, 100);
        }

        GetuiPush.getWidgetData((data: string) => {
          if (data) {
            const pa: WidgetData = JSON.parse(data);
            navigation.push('LinkOpenAlert', {
              uuid: pa.uuid,
              name: pa.name,
              type: pa.type,
            });
          }
        });

        AsyncStorage.getItem('androidWidgetData').then((res: string | null) => {
          if (!res) {
            SharedStorageData.setData(res);
          }
        });
      }
    }
  };

  useEffect(() => {
    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange
    );

    return () => {
      subscription.remove();
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // 添加 appState 作为依赖项以正确追踪状态变化

  return null;
};

export default AppStateCenter;
