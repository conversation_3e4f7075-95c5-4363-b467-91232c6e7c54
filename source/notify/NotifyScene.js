import React, { Component } from 'react';
import { View, ScrollView } from 'react-native';
import { observer } from 'mobx-react/native';
import CheckBox from '../share/CheckBox';
import { Tme } from '../ThemeStyle';
import { getSceneName } from '../Tools';
import _ from 'lodash';
import I18n from '../I18n';
import NavBarView from '../share/NavBarView';
import HeaderRightBtn from '../share/HeaderRightBtn';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';

@observer
class NotifyScene extends Component {
  constructor(props) {
    super(props);
    this.state = {
      scene_ids: this.props.route.params.notify.scene_ids,
    };
    this.props.navigation.setOptions({
      // eslint-disable-next-line react/no-unstable-nested-components
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.next')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this._save();
  }
  _save() {
    PubSub.publish(PubSubEvent.SMART_SELECT_EVENT, {
      type: 'scene',
      data: this.props.route.params.notify,
    });
    this.props.navigation.goBack();
  }

  render() {
    var checkbox = [];
    var that = this;
    var length = this.props.route.params.scenes.length - 1;
    _.forEach(this.props.route.params.scenes, function (data, index) {
      checkbox.push(
        <View key={index} style={{ backgroundColor: Tme('cardColor') }}>
          <View style={{ paddingHorizontal: 20 }}>
            <CheckBox
              isLast={index == length}
              value={getSceneName(data.name)}
              index={data.uuid}
              isChecked={_.includes(that.state.scene_ids, data.uuid)}
              onClick={that.onClick.bind(that)}
            />
          </View>
        </View>,
      );
    });

    return (
      <NavBarView>
        <View
          style={[
            {
              backgroundColor: Tme('bgColor'),
              flex: 1,
            },
          ]}>
          <ScrollView showsVerticalScrollIndicator={false}>
            <View
              style={{
                flex: 1,
                marginTop: 20,
              }}>
              {checkbox}
            </View>
          </ScrollView>
        </View>
      </NavBarView>
    );
  }

  onClick(uuid) {
    var ids = this.state.scene_ids;
    if (_.includes(ids, uuid)) {
      ids = _.pull(ids, uuid);
    } else {
      ids.push(uuid);
    }
    this.setState({ scene_ids: ids }, () => {
      this.props.route.params.notify.scene_ids = ids;
    });
  }
}
export default NotifyScene;
