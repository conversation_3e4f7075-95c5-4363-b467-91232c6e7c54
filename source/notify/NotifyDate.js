import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Helper, DEVICE_WIDTH } from '../Helper';
import I18n from '../I18n';
import { convertDateUTCForIos, formatDate } from '../Tools';
import { Tme } from '../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import _ from 'lodash';
import SwitchBtn from '../share/SwitchBtn';
import NavBarView from '../share/NavBarView';
import { SelectDate } from '../share/SelectDateView';
import { Toast } from '../Toast';
import AlertModal from '../share/AlertModal';
import moment from 'moment';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { hideLoading, showLoading } from '../../ILoading';
import { PubSubEvent } from '../types/PubSubEvent';
import PubSub from 'pubsub-js';

const nowTimeStamp = Date.now();
const now = new Date(nowTimeStamp);

const start = moment().startOf('day').toDate();
const end_time = moment().endOf('day').toDate();

class NotifyDate extends Component {
  constructor(props) {
    super(props);

    this.state = {
      clickRow: '',
      begin_at: '',
      end_at: '',
      week_day: [],
      is_enable: false,
    };

    this.week = [
      { key: 0, value: I18n.t('setting.sunday') },
      { key: 1, value: I18n.t('setting.monday') },
      { key: 2, value: I18n.t('setting.tuesday') },
      { key: 3, value: I18n.t('setting.wednesday') },
      { key: 4, value: I18n.t('setting.thursday') },
      { key: 5, value: I18n.t('setting.friday') },
      { key: 6, value: I18n.t('setting.saturday') },
    ];

    this.props.navigation.setOptions({
      // eslint-disable-next-line react/no-unstable-nested-components
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  componentDidMount() {
    var that = this;

    this.doFetchData();
    PubSub.subscribe(PubSubEvent.SMART_SELECT_EVENT, (msg, data) => {
      if (data.type == 'date') {
        that.setState({
          week_day: data.wday,
        });
      }
    });
  }

  rightClick() {
    this._save();
  }

  componentWillUnmount() {
    PubSub.unsubscribe(PubSubEvent.SMART_SELECT_EVENT);
  }

  render() {
    return (
      <NavBarView>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{
            backgroundColor: Tme('bgColor'),
            flex: 1,
          }}>
          <TouchableOpacity
            activeOpacity={1.0}
            onPress={this.isEnabled.bind(this)}
            style={{
              backgroundColor: Tme('cardColor'),
              marginTop: 20,
            }}>
            <View
              style={{
                marginHorizontal: 16,
                flexDirection: 'row',
                paddingVertical: 16,
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <Text style={{ color: Tme('cardTextColor'), fontSize: 17 }}>
                  {I18n.t('setting.notify_by_time')}
                </Text>
              </View>
              <View
                style={{
                  marginLeft: 38,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <SwitchBtn
                  key={Math.random()}
                  value={this.state.is_enable}
                  change={this.isEnabled.bind(this)}
                />
              </View>
            </View>
          </TouchableOpacity>

          {this.state.is_enable ? (
            <View>
              <View style={{ height: 20 }} />
              <SelectDate
                from="start_at"
                title={I18n.t('setting.starting_time')}
                value={this.state.begin_at}
                onChange={this.time.bind(this)}
                navigation={this.props.navigation}
              />
              <SelectDate
                from="end_at"
                title={I18n.t('setting.end_time')}
                value={this.state.end_at}
                navigation={this.props.navigation}
                onChange={this.endTime.bind(this)}
              />
              <View style={{ height: 20 }} />
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={this.click.bind(this, 'week')}
                style={{ backgroundColor: Tme('cardColor') }}>
                <View
                  style={{
                    padding: 16,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                  <View style={styles.rowTitle}>
                    <Text
                      style={{
                        color: Tme('cardTextColor'),
                        textAlign: 'center',
                        fontSize: 17,
                      }}>
                      {I18n.t('setting.select_date')}
                    </Text>
                  </View>
                  <View style={styles.touchRow}>
                    <View
                      style={{
                        flexDirection: 'column',
                        alignItems: 'flex-end',
                      }}>
                      <Text
                        style={{ color: Tme('textColor'), marginLeft: 8 }}
                        numberOfLines={2}>
                        {this.showWday()}
                      </Text>
                    </View>
                    <MaterialIcons
                      name="keyboard-arrow-right"
                      size={20}
                      color={Tme('textColor')}
                    />
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          ) : null}
        </ScrollView>
      </NavBarView>
    );
  }

  click() {
    this.props.navigation.push('SmartTimeView', {
      wday: this.state.week_day,
      cycle: true,
      show_cycle: false,
      title: I18n.t('setting.select_date'),
    });
  }

  isEnabled() {
    this.setState({
      is_enable: !this.state.is_enable,
    });
  }

  time(date) {
    if (date) {
      this.setState({
        begin_at: date,
      });
    }
  }

  endTime(date) {
    if (date) {
      this.setState({
        end_at: date,
      });
    }
  }

  showWday() {
    var that = this;
    var temp = [];
    var week_day = this.state.week_day.sort();
    _.forEach(week_day, (day, key) => {
      _.forEach(that.week, (w, k) => {
        if (w.key == day) {
          temp.push(w.value);
        }
      });
    });
    if (temp.length == 7) {
      return I18n.t('global.every_day');
    } else {
      return temp.length == 0 ? I18n.t('global.every_day') : temp.join(' ');
    }
  }

  doFetchData() {
    var that = this;
    showLoading();
    Helper.httpGET(Helper.urlWithQuery('/notifies'), {
      cloud: true,
      success: data => {
        var notify_dates = data.notify.notify_dates;
        if (notify_dates && notify_dates.length > 0) {
          var week = [];
          _.forEach(notify_dates[0].week_day, day => {
            week.push(day);
          });
          that.setState({
            week_day: week,
            begin_at: new Date(
              convertDateUTCForIos(notify_dates[0].begin_at).getTime() +
              now.getTimezoneOffset() * 60000,
            ),
            end_at: new Date(
              convertDateUTCForIos(notify_dates[0].end_at).getTime() +
              now.getTimezoneOffset() * 60000,
            ),
            uuid: notify_dates[0].uuid,
            is_enable: notify_dates[0].is_enable,
          });
        } else {
          that.setState({
            week_day: [],
            begin_at: start,
            end_at: end_time,
            is_enable: false,
            uuid: '',
          });
        }
      },
      ensure: () => {
        hideLoading();
      },
    });
  }

  _save() {
    var errors = [];

    if (this.state.begin_at === null && this.state.end_at !== null) {
      errors.push(I18n.t('setting.place_end_time'));
    }
    if (this.state.begin_at !== null && this.state.end_at === null) {
      errors.push(I18n.t('setting.place_start_time'));
    }
    if (errors.length > 0) {
      AlertModal.alert(I18n.t('home.warning_message'), errors.join('\n'));
      return;
    }
    showLoading();
    // submit to server

    var body = {
      startTime: formatDate(this.state.begin_at, 'hh:mm'),
      endTime: formatDate(this.state.end_at, 'hh:mm'),
      wday: this.state.week_day,
      uuid: this.state.uuid,
      is_enable: this.state.is_enable,
      type: 'date',
    };

    Helper.httpPOST(
      '/notifies/notify_date',
      {
        ensure: () => {
          hideLoading();
        },
        success: data => {
          this.props.navigation.goBack();
          Toast.show();
        },
      },
      body,
    );
  }
}
const styles = StyleSheet.create({
  touchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    width: DEVICE_WIDTH - 120 - 80,
  },
  rowTitle: {
    width: 120,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  account_view: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 3,
    marginBottom: 20,
    marginTop: 16,
  },
  acount: {
    height: 40,
    marginLeft: 6,
    marginRight: 6,
  },
});
export default NotifyDate;
