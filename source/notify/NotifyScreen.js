import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Platform,
  Linking,
  Switch,
  AppState,
} from 'react-native';
import { Helper } from '../Helper';
import I18n from '../I18n';
import { Tme, Colors } from '../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import SwitchBtn from '../share/SwitchBtn';
import NavBarView from '../share/NavBarView';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import Getui from '../Getui';
import { mainRadius } from '../Tools';
import { hideLoading, showLoading } from '../../ILoading';

export default class NotifyScreen extends Component {
  constructor(props) {
    super(props);

    this.state = {
      notify: false,
      showView: false,
      cellphoneNotify: false,
    };
    this.appState = AppState.currentState;
  }

  componentDidMount() {
    this.appStateEvent = AppState.addEventListener(
      'change',
      this.handleAppStateChange.bind(this),
    );
    this.doFetchData();
    this.checkState();
  }

  componentWillUnmount() {
    if (this.appStateEvent) {
      this.appStateEvent.remove();
    }
  }

  handleAppStateChange(nextAppState) {
    if (
      this.appState.match(/inactive|background/) &&
      nextAppState === 'active'
    ) {
      this.checkState();
    }
    this.appState = AppState.currentState;
  }

  checkState() {
    const that = this;
    if (Platform.OS == 'ios') {
      PushNotificationIOS.checkPermissions(function (permissions) {
        if (permissions.alert || permissions.badge || permissions.sound) {
          that.setState({
            cellphoneNotify: true,
          });
        } else {
          that.setState({
            cellphoneNotify: false,
          });
        }
      });
    } else {
      Getui.isNotificationEnabled(isEnabled => {
        if (!isEnabled) {
          that.setState({
            cellphoneNotify: false,
          });
        } else {
          that.setState({
            cellphoneNotify: true,
          });
        }
      });
    }
  }

  onCellphoneNotify() {
    if (Platform.OS == 'ios') {
      Linking.openURL('app-settings:').catch(err => console.log('error', err));
    } else {
      Getui.openNotification();
    }
  }

  render() {
    return (
      <NavBarView>
        {this.state.showView ? (
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={{
              flex: 1,
            }}>
            <View
              style={{
                marginTop: 20,
                paddingBottom: 20,
              }}>
              <TouchableOpacity
                activeOpacity={1.0}
                style={{
                  backgroundColor: Tme('cardColor'),
                  borderRadius: mainRadius(),
                }}>
                <View
                  style={{
                    marginHorizontal: 16,
                    flexDirection: 'row',
                    paddingVertical: 16,
                    justifyContent: 'space-between',
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      flex: 1,
                    }}>
                    <Text style={{ color: Tme('cardTextColor') }}>
                      {I18n.t('setting.notify_desp')}
                    </Text>
                  </View>
                  <View
                    style={{
                      marginLeft: 38,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <SwitchBtn
                      value={this.state.notify}
                      change={this.changeAll.bind(this)}
                    />
                  </View>
                </View>
              </TouchableOpacity>
            </View>
            <TouchableOpacity
              activeOpacity={1.0}
              onPress={this.onCellphoneNotify.bind(this)}
              style={{
                backgroundColor: Tme('cardColor'),
                borderRadius: mainRadius(),
              }}>
              <View
                style={{
                  marginHorizontal: 16,
                  flexDirection: 'row',
                  paddingVertical: 16,
                  justifyContent: 'space-between',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    flex: 1,
                  }}>
                  <Text style={{ color: Tme('cardTextColor') }}>
                    {I18n.t('setting.system_notification_state')}
                  </Text>
                </View>
                <View
                  style={{
                    marginLeft: 38,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Switch
                    thumbColor="#fff"
                    disabled={false}
                    trackColor={{ true: Colors.MainColor }}
                    style={[{ transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }] }]}
                    value={this.state.cellphoneNotify}
                    onValueChange={this.onCellphoneNotify.bind(this)}
                  />
                </View>
              </View>
            </TouchableOpacity>
            {false ? (
              <View>
                <View style={{ height: 2 }} />
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={this.clickDate.bind(this)}
                  style={{
                    backgroundColor: Tme('cardColor'),
                  }}>
                  <View
                    style={{
                      marginHorizontal: 20,
                      flexDirection: 'row',
                      paddingVertical: 16,
                      justifyContent: 'space-between',
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <Text style={{ color: Tme('cardTextColor') }}>
                        {I18n.t('setting.notify_by_time')}
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <MaterialIcons
                        name="keyboard-arrow-right"
                        size={20}
                        color={Tme('textColor')}
                      />
                    </View>
                  </View>
                </TouchableOpacity>

                <View style={{ height: 2 }} />
              </View>
            ) : null}
          </ScrollView>
        ) : null}
      </NavBarView>
    );
  }

  changeAll(e) {
    this.setState({
      notify: e,
    });
    showLoading();
    Helper.httpPOST(
      '/notifies/change_notify',
      {
        ensure: () => {
          hideLoading();
        },
        cloud: true,
        success: data => { },
      },
      { type: 'notify' },
    );
  }

  clickDate() {
    this.props.navigation.push('notifyDate', {
      title: I18n.t('setting.notify_by_time'),
    });
  }

  doFetchData() {
    var that = this;
    showLoading();
    Helper.httpGET(Helper.urlWithQuery('/notifies'), {
      cloud: true,
      success: data => {
        that.setState({
          notify: data.notify ? data.notify.is_enable : false,
          showView: true,
        });
      },
      ensure: () => {
        hideLoading();
        that.setState({
          showView: true,
        });
      },
    });
  }
}
