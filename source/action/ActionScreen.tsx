import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import {
  View,
  Text,
  Platform,
  FlatList,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {Helper, HelperMemo} from '../Helper';
import Ionicons from 'react-native-vector-icons/Ionicons';
import I18n from '../I18n';
import {Tme, Colors, IsDark} from '../ThemeStyle';
import ActionSheet from '@alessiocancian/react-native-actionsheet';
import CardView from '../share/CardView';
import {isOwnerOrAdmin} from '../Router';
import {Toast} from '../Toast';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {SkeletionDevice} from '../share/Skeletion';
import {getImageFromKey, mainRadius, mainTitle} from '../Tools';
import AlertModal from '../share/AlertModal';
import GrayscaledImage from '../share/GrayscaledImage';
import PubSub from 'pubsub-js';
import {hideLoading, showLoading} from '../../ILoading';
import {EVENT_ACTION, PubSubEvent} from '../types/PubSubEvent';
import ActionBottomDrawer from '../share/ActionBottomDrawer';
import {ActionItem, SmartScreenProps} from '../types/smart';

interface ActionScreenProps extends SmartScreenProps {
  actions?: ActionItem[];
  parents?: any;
  parent?: any;
}

// 使用 forwardRef 包装组件，以便父组件可以调用子组件方法
const ActionScreen = forwardRef<any, ActionScreenProps>(({
  navigation,
  actions = [],
}, ref) => {
  const [showBtn, setShowBtn] = useState(true);
  const [dataSource, setDataSource] = useState<ActionItem[]>(actions || []);
  const [refreshing, setRefreshing] = useState(false);
  const [winWidth, setWinWidth] = useState(initWidth(Dimensions.get('window')));
  const [selectedScene, setSelectedScene] = useState<ActionItem | null>(null);
  // 添加 optionsState 用于强制刷新 ActionSheet 选项
  const [optionsState, setOptionsState] = useState<string[]>([]);

  const firstFetch = useRef(true);
  const firstFetchRef = useRef<any>(null);
  const actionSheet = useRef<any>(null);
  const clickRowRef = useRef<ActionItem | null>(null);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    setData: (data: ActionItem[]) => {
      if (firstFetch.current) {
        firstFetch.current = false;
        setDataSource(data);
        setShowBtn(true);
      }
    },
    setScenes: (data: ActionItem[]) => {
      if (firstFetch.current) {
        firstFetch.current = false;
        setDataSource(data);
        setShowBtn(true);
      }
    },
  }));

  useEffect(() => {
    if (!firstFetchRef.current) {
      doFetchData();
      firstFetchRef.current = 'first';
    }

    const dimensionsEvent = Dimensions.addEventListener('change', onResize);
    PubSub.subscribe(EVENT_ACTION, () => {
      doFetchData();
    });

    PubSub.subscribe(PubSubEvent.ERROR_REFETCH, () => {
      doFetchData();
    });

    return () => {
      PubSub.unsubscribe(PubSubEvent.ERROR_REFETCH);
      if (dimensionsEvent) {
        dimensionsEvent.remove();
      }
      PubSub.unsubscribe(EVENT_ACTION);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  function initWidth(window: any) {
    if (Platform.OS == 'android') {
      return window.width;
    } else {
      if (window.width > window.height) {
        return (
          window.width -
          HelperMemo.STATUS_BAR_HEIGHT -
          HelperMemo.BOTTOM_BAR_HEIGHT -
          22
        );
      } else {
        return window.width;
      }
    }
  }

  const onResize = ({window}: any) => {
    setWinWidth(initWidth(window));
  };

  // 更新 options 计算，使用 useState 而不是直接计算
  useEffect(() => {
    const newOptions = [
      I18n.t('home.cancel'),
      I18n.t('home.edit'),
      clickRowRef.current == null
        ? I18n.t('action.favorite')
        : clickRowRef.current.is_favor
        ? I18n.t('action.remove_favorite')
        : I18n.t('action.add_favorite'),
      I18n.t('home.remove_btn'),
    ];
    setOptionsState(newOptions);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [clickRowRef.current]);

  if (!showBtn) {
    return <SkeletionDevice />;
  }

  const renderRow = ({ item }: { item: ActionItem }) => {
    if (item.type === 'add') {
      return (
        <View style={{marginHorizontal: 4, marginBottom: 12}}>
          <CardView
            onChange={addAction}
            styles={[
              {
                width: winWidth / 2 - 26,
                borderRadius: mainRadius(),
                height: 78,
                padding: 12,
                flexDirection: 'row',
                alignItems: 'center',
              },
            ]}>
            <View
              style={{
                width: 32,
                height: 32,
                marginVertical: 2,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Ionicons
                name="add-outline"
                size={20}
                color={Tme('cardTextColor')}
              />
            </View>
            <Text
              style={{
                fontSize: mainTitle(),
                color: Tme('cardTextColor'),
                marginLeft: 6,
                fontWeight: '500',
              }}>
              {I18n.t('action.add_action')}
            </Text>
          </CardView>
        </View>
      );
    } else {
      return (
        <View style={{marginHorizontal: 4, marginBottom: 12}}>
          <CardView
            onChange={() => {
              setSelectedScene(item);
            }}
            styles={[
              {
                width: winWidth / 2 - 26,
                borderRadius: mainRadius(),
                padding: 12,
                height: 78,
              },
            ]}>
            <View style={{flexDirection: 'row', flex: 1}}>
              <View style={{flexDirection: 'row', flex: 1}}>
                <GrayscaledImage
                  source={getImageFromKey(item.icon!).value}
                  style={{width: 32, height: 32}}
                />
                <View
                  style={{marginLeft: 8, flex: 1, justifyContent: 'center'}}>
                  <Text
                    numberOfLines={2}
                    style={[
                      {
                        width: winWidth / 2 - 120,
                        color: Tme('cardTextColor'),
                      },
                      Colors.CardFontStyle,
                    ]}>
                    {item.name}
                  </Text>
                  <View style={{flexDirection: 'row', marginTop: 6}}>
                    <Text
                      style={{
                        color: Tme('smallTextColor'),
                        marginRight: 5,
                        fontSize: 11,
                      }}>
                      {item.target_length}
                    </Text>
                    <Text
                      style={{fontSize: 11, color: Tme('smallTextColor')}}>
                      {I18n.t('global.device_count')}
                    </Text>
                  </View>
                </View>
              </View>
              {isOwnerOrAdmin() ? (
                <TouchableOpacity
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  onPress={() => sheetShow(item)}
                  activeOpacity={0.8}
                  hitSlop={{top: 20, right: 20, bottom: 20, left: 20}}>
                  <MCIcons
                    name="dots-vertical"
                    size={20}
                    color={Tme('smallTextColor')}
                  />
                </TouchableOpacity>
              ) : (
                <View style={{height: 20}} />
              )}
            </View>
          </CardView>
        </View>
      );
    }
  };

  const sheetShow = (rowData: ActionItem) => {
    clickRowRef.current = rowData;
    // 更新 options 状态，强制重新渲染
    setOptionsState([
      I18n.t('home.cancel'),
      I18n.t('home.edit'),
      rowData.is_favor
        ? I18n.t('action.remove_favorite')
        : I18n.t('action.add_favorite'),
      I18n.t('home.remove_btn'),
    ]);
    actionSheet.current?.show();
  };

  const sheetClick = (index: number) => {
    if (!clickRowRef.current) {
      return;
    }

    if (index === 0) {
      actionSetting(clickRowRef.current);
    }
    if (index == 1) {
      favorite(clickRowRef.current);
    }
    if (index == 2) {
      remove(clickRowRef.current);
    }
  };

  const remove = (data: ActionItem) => {
    setTimeout(() => {
      AlertModal.alert(I18n.t('global.activate_sure'), '', [
        {
          text: I18n.t('home.cancel'),
          onPress: () => {},
        },
        {
          text: I18n.t('home.confirm'),
          onPress: () => {
            Helper.httpPOST(
              '/partner/actions/delete',
              {
                ensure: () => {},
                success: (resp: any) => {
                  Toast.show();
                  let TempActions = resp.actions;
                  if (isOwnerOrAdmin()) {
                    TempActions.unshift({type: 'add'});
                  }
                  setDataSource(TempActions);
                },
              },
              {uuid: data.uuid},
            );
          },
        },
        {userInterfaceStyle: 'automatic'},
      ]);
    }, 100);
  };

  const favorite = (data: ActionItem) => {
    showLoading();
    Helper.httpPOST(
      '/partner/actions/favor',
      {
        ensure: () => {
          hideLoading();
        },
        success: (resp: any) => {
          let tempActions = resp.actions;
          if (isOwnerOrAdmin()) {
            tempActions.unshift({type: 'add'});
          }
          setDataSource(tempActions);

          // 刷新当前选中的行，确保下次打开 ActionSheet 时显示正确的状态
          if (clickRowRef.current) {
            const updatedItem = actions.find(item => item.uuid === clickRowRef.current?.uuid);
            if (updatedItem) {
              clickRowRef.current = updatedItem;
              // 更新 options
              setOptionsState([
                I18n.t('home.cancel'),
                I18n.t('home.edit'),
                updatedItem.is_favor
                  ? I18n.t('action.remove_favorite')
                  : I18n.t('action.add_favorite'),
                I18n.t('home.remove_btn'),
              ]);
            }
          }

          Toast.show();
        },
      },
      {uuid: data.uuid},
    );
  };

  const actionSetting = (data: ActionItem) => {
    navigation.push('actionView', {
      uuid: data.uuid,
      name: data.name,
      title: I18n.t('action.action'),
    });
  };

  const runAction = (v: ActionItem) => {
    showLoading();

    Helper.httpPOST(
      '/partner/actions/run',
      {
        ensure: () => {
          hideLoading();
        },
        success: () => {
          Toast.show();
          setSelectedScene(null);
        },
      },
      {uuid: v.uuid},
    );
  };

  const addAction = () => {
    navigation.push('actionView', {
      title: I18n.t('action.action'),
    });
  };

  const doRefreshData = () => {
    doFetchData();
  };

  const doFetchData = (type?: string) => {
    if (type === 'refresh') {
      setRefreshing(true);
    } else {
      if (firstFetch.current) {
        hideLoading();
      }
    }

    Helper.httpGET('/partner/actions', {
      success: (resp: any) => {
        let TempActions = resp.actions;
        if (isOwnerOrAdmin()) {
          TempActions.unshift({type: 'add'});
        }
        setDataSource(TempActions);
      },
      ensure: () => {
        setShowBtn(true);
        if (type == 'refresh') {
          setRefreshing(false);
        } else {
          if (firstFetch.current) {
            firstFetch.current = false;
            hideLoading();
          }
        }
      },
    });
  };

  return (
    <View style={{backgroundColor: 'trasparent', flex: 1}}>
      <FlatList
        style={{
          flex: 1,
          paddingHorizontal: 16,
          paddingTop: 20,
        }}
        columnWrapperStyle={{justifyContent: 'space-between'}}
        data={dataSource}
        renderItem={renderRow}
        numColumns={2}
        onEndReachedThreshold={0.1}
        refreshing={refreshing}
        onRefresh={doRefreshData}
        // eslint-disable-next-line react/no-unstable-nested-components
        ListFooterComponent={() => <View style={{height: 20, width: 1}} />}
        keyExtractor={(_, index) => index.toString()}
      />
      {selectedScene && (
        <ActionBottomDrawer
          viewHeight={250}
          title={selectedScene.name}
          desp={I18n.t('global.activate_action')}
          confirm={I18n.t('home.confirm')}
          cancel={I18n.t('home.cancel')}
          action={'action'}
          uuid={selectedScene.uuid}
          type={'screen'}
          onClose={() => {
            setSelectedScene(null);
          }}
          onConfirm={() => {
            runAction(selectedScene);
          }}
        />
      )}
      <ActionSheet
        ref={actionSheet}
        options={optionsState.length > 0 ? optionsState : [
          I18n.t('home.cancel'),
          I18n.t('home.edit'),
          I18n.t('action.favorite'),
          I18n.t('home.remove_btn'),
        ]}
        cancelButtonIndex={0}
        userInterfaceStyle={IsDark() ? 'dark' : 'light'}
        destructiveButtonIndex={3}
        theme="ios"
        styles={{
          cancelButtonBox: {
            height: 50,
            marginTop: 6,
            alignItems: 'center',
            justifyContent: 'center',
          },
          buttonBox: {
            height: 50,
            alignItems: 'center',
            justifyContent: 'center',
          },
        }}
        onPress={index => {
          sheetClick(index - 1);
        }}
      />
    </View>
  );
});

export default ActionScreen;
