import React, { Component } from 'react';
import { View, TextInput, Keyboard, TouchableOpacity } from 'react-native';
import I18n from '../I18n';
import { Helper } from '../Helper';
import { observer } from 'mobx-react/native';
import SelectDevice from '../select_device_spec/SelectDevice';
import _ from 'lodash';
import Action from '../models/Action';
import Device from '../models/Device';
import { Tme, Colors } from '../ThemeStyle';
import NavBarView from '../share/NavBarView';
import { toJS } from 'mobx';
import { Toast } from '../Toast';
import { getImageFromKey } from '../Tools';
import AlertModal from '../share/AlertModal';
import GrayscaledImage from '../share/GrayscaledImage';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { hideLoading, showLoading } from '../../ILoading';
import ScreenSizeContext from '../../WindowResizeContext';
import PubSub from 'pubsub-js';
import { EVENT_ACTION, PubSubEvent } from '../types/PubSubEvent';

@observer
class ActionView extends Component {
  action = new Action();
  device = new Device();
  constructor(props) {
    super(props);

    this.state = {
      targets: [],
      viewShow: false,
      rooms: '',
    };
    this.inputRef = React.createRef();
    this.props.navigation.setOptions({
      // eslint-disable-next-line react/no-unstable-nested-components
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  static contextType = ScreenSizeContext;

  componentDidMount() {
    this.doFetchData();
    PubSub.subscribe(
      PubSubEvent.SELECT_SCENE_ICON, (msg, data) => {
        this.action.icon = data.icon;
      });

    PubSub.subscribe(PubSubEvent.ERROR_REFETCH, (msg, data) => {
      this.doFetchData();
    });
  }

  componentWillUnmount() {
    PubSub.unsubscribe(PubSubEvent.ERROR_REFETCH);
    PubSub.unsubscribe(PubSubEvent.SELECT_SCENE_ICON);
  }

  rightClick() {
    if (this.action.name != '') {
      showLoading();
      var targets = _.groupBy(this.action.targets, 'checked').true;
      if (targets == undefined) {
        targets = [];
      } else {
        var temp = [];
        _.forEach(targets, function (v, k) {
          temp.push(toJS(v));
        });
        targets = temp;
      }
      if (targets) {
        Helper.httpPOST(
          '/partner/actions',
          {
            ensure: () => {
              hideLoading();
            },
            success: data => {
              PubSub.publish(EVENT_ACTION);
              this.props.navigation.goBack();
              Toast.show();
            },
            error: data => {
              AlertModal.alert(_.uniq(data).join('\n'));
            },
          },
          {
            name: this.action.name,
            icon: this.action.icon,
            uuid: this.props.route.params.uuid,
            targets: targets,
          },
        );
      } else {
        AlertModal.alert(I18n.t('action.action_device'));
      }
    } else {
      AlertModal.alert(I18n.t('scene.scene_input'));
    }
  }

  doFetchData() {
    showLoading();
    Helper.httpGET(
      Helper.urlWithQuery(
        '/partner/actions/data',
        this.props.route.params.uuid
          ? { uuid: this.props.route.params.uuid }
          : '',
      ),
      {
        context: this.context,
        success: data => {
          if (data.action) {
            this.action.name = data.action.name;
            this.action.icon = data.action.icon;
            if (data.action.targets) {
              this.action.spec_targets(data.action.targets);
            }
          }
          var devices = [];
          _.forEach(data.devices, function (v, k) {
            if (v.index != 1) {
              devices.push(v);
            }
          });
          this.device.devices = devices;
          this.setState(
            {
              viewShow: true,
              rooms: data.rooms,
            },
            () => {
              if (_.isEmpty(this.props.route.params.uuid)) {
                setTimeout(() => {
                  this.inputRef.current.focus();
                }, 500);
              }
            },
          );
        },
        ensure: () => {
          hideLoading();
        },
      },
    );
  }

  showIcons(icon) {
    this.props.navigation.push('SelectSceneIcon', {
      icon: icon,
      title: I18n.t('scene.select_icon'),
    });
  }

  render() {
    return (
      <NavBarView>
        {this.state.viewShow ? (
          <>
            <View
              style={{
                backgroundColor: Tme('bgColor'),
                marginTop: 20,
                marginBottom: 20,
              }}>
              <View
                style={{
                  paddingHorizontal: 20,
                  paddingVertical: 10,
                  backgroundColor: Tme('cardColor'),
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <TouchableOpacity
                  onPress={this.showIcons.bind(this, this.action.icon)}
                  activeOpacity={0.8}
                  style={{
                    marginRight: 4,
                    borderWidth: 1,
                    borderColor: Colors.MainColor,
                    borderRadius: 6,
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: 34,
                    height: 34,
                  }}>
                  <GrayscaledImage
                    source={getImageFromKey(this.action.icon).value}
                    style={{ width: 22, height: 22 }}
                  />
                </TouchableOpacity>
                <TextInput
                  ref={this.inputRef}
                  testID="actionInput"
                  placeholderTextColor={Tme('placeholder')}
                  style={[
                    Colors.TextInputStyle(),
                    { flex: 1, fontWeight: '600', fontSize: 16 },
                  ]}
                  autoCapitalize="none"
                  underlineColorAndroid="transparent"
                  placeholder={I18n.t('action.action_name')}
                  value={this.action.name}
                  onBlur={() => {
                    Keyboard.dismiss();
                  }}
                  onChangeText={name => {
                    this.action.name = name;
                  }}
                />
              </View>
            </View>
            <SelectDevice
              showTitle={true}
              rooms={this.state.rooms}
              product="action"
              spec_settings={this.action}
              device={this.device}
              navigation={this.props.navigation}
              type="target"
            />
          </>
        ) : null}
      </NavBarView>
    );
  }
}
export default ActionView;
