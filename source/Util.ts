import {Platform} from 'react-native';
import {
  PERMISSIONS,
  RESULTS,
  checkMultiple,
  requestMultiple,
  Permission,
  PermissionStatus,
} from 'react-native-permissions';

type MediaType = 'all' | 'image' | 'video';

interface PermissionConfig {
  permissions: Permission[];
  checkPermissions: typeof checkMultiple;
  requestPermissions: typeof requestMultiple;
}

/**
 * 检查并请求保存图片和视频的权限
 * @param mediaType - 需要请求的媒体类型权限
 * @returns Promise<boolean> - 是否获得了权限
 * @throws Error - 当权限被永久拒绝时抛出错误
 */
export async function hasSavePermission(mediaType: MediaType = 'all'): Promise<boolean> {
  try {
    const {permissions, checkPermissions, requestPermissions} = getPermissionConfig(mediaType);
    const statuses = await checkPermissions(permissions);

    // 检查是否所有权限都已授予
    if (isPermissionGranted(statuses)) {
      return true;
    }

    // 检查是否有权限被永久拒绝
    const isPermanentlyDenied = Object.values(statuses).some(
      status => status === RESULTS.BLOCKED || status === RESULTS.UNAVAILABLE,
    );

    if (isPermanentlyDenied) {
      throw new Error('permissions.permanently_denied');
    }

    // 请求权限
    const requestResults = await requestPermissions(permissions);
    console.log('Permission request results:', requestResults);
    return isPermissionGranted(requestResults);
  } catch (error) {
    console.error('Error checking permissions:', error);
    throw error;
  }
}

/**
 * 获取权限配置
 * @param mediaType - 媒体类型
 * @returns PermissionConfig - 权限配置对象
 */
function getPermissionConfig(mediaType: MediaType = 'all'): PermissionConfig {
  if (Platform.OS === 'ios') {
    const permissions: Permission[] = [];
    if (parseInt(String(Platform.Version), 10) >= 14) {
      // iOS 14+ 区分读写权限
      permissions.push(PERMISSIONS.IOS.PHOTO_LIBRARY);
      if (mediaType === 'all' || mediaType === 'video') {
        permissions.push(PERMISSIONS.IOS.PHOTO_LIBRARY_ADD_ONLY);
      }
    } else {
      // iOS 14 以下版本
      permissions.push(PERMISSIONS.IOS.PHOTO_LIBRARY);
    }

    return {
      permissions,
      checkPermissions: checkMultiple,
      requestPermissions: requestMultiple,
    };
  }

  // Android 权限配置
  const permissions: Permission[] = [];
  const androidVersion = parseInt(String(Platform.Version), 10);

  if (androidVersion >= 33) {
    // Android 13 及以上版本需要分别请求图片和视频权限
    if (mediaType === 'all' || mediaType === 'image') {
      permissions.push(PERMISSIONS.ANDROID.READ_MEDIA_IMAGES);
    }
    if (mediaType === 'all' || mediaType === 'video') {
      permissions.push(PERMISSIONS.ANDROID.READ_MEDIA_VIDEO);
    }
  } else if (androidVersion >= 29) {
    // Android 10-12 不需要存储权限也可以保存媒体文件到公共目录
    return {
      permissions: [],
      checkPermissions: checkMultiple,
      requestPermissions: requestMultiple,
    };
  } else {
    // Android 9 及以下版本需要存储权限
    permissions.push(PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE);
  }

  return {
    permissions,
    checkPermissions: checkMultiple,
    requestPermissions: requestMultiple,
  };
}

/**
 * 检查权限结果是否都已授予
 * @param result - 权限检查结果
 * @returns boolean - 是否所有权限都已授予
 */
function isPermissionGranted(result: Record<Permission, PermissionStatus>): boolean {
  // 如果没有需要的权限（Android 10-12的情况），直接返回true
  if (Object.keys(result).length === 0) {
    return true;
  }

  // 检查所有权限是否都已授予或有限制访问（iOS的LIMITED状态）
  return Object.values(result).every(
    status => status === RESULTS.GRANTED || status === RESULTS.LIMITED,
  );
}
