import React from 'react';
import {View, Image} from 'react-native';
import {Helper} from '../Helper';
import I18n from '../I18n';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import CheckBox from '../check_box/index';
import {Tme, Colors} from '../ThemeStyle';
import DeviceControl from '../DeviceControl';
import {DelayInputModal} from '../share/DelayInputModal';

export default class DeviceSelectorTargetDoorLock extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      value: this.props.spec.value == 'Unlocked' ? 1 : 0,
      delay: this.props.target.delay || '0',
      modalVisible: false,
      checked: this.props.target.checked,
    };
  }

  render() {
    return (
      <View style={{paddingHorizontal: 8}}>
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            borderRadius: 4,
          }}>
          <View style={{padding: 10}}>
            <View
              testID="selectDeviceItemRow"
              style={{marginBottom: 20, marginTop: 16}}>
              <CheckBox
                rightTextStyle={{
                  fontWeight: '300',
                  fontSize: 16,
                  color: Tme('cardTextColor'),
                }}
                onClick={() => this.onClick()}
                rightText={Helper.i(this.props.spec.name)}
                isChecked={this.state.checked}
                checkedImage={
                  <Image
                    source={require('../../img/checkbox-checked.png')}
                    style={{width: 17, height: 17}}
                  />
                }
                unCheckedImage={
                  <Image
                    source={require('../../img/Checkbox.png')}
                    style={{width: 17, height: 17}}
                  />
                }
              />
            </View>
            <View style={{height: 40}}>
              <SegmentedControl
                tintColor={Colors.MainColor}
                values={[I18n.t('spec.lock'), I18n.t('spec.unlock')]}
                selectedIndex={this.state.value}
                fontStyle={{color: Tme('cardTextColor'), fontSize: 16}}
                activeFontStyle={{color: '#ffffff', fontSize: 16}}
                style={{flex: 1, height: 42}}
                onChange={this.handleRadioChange.bind(this)}
              />
            </View>
            <DelayInputModal
              delay={this.state.delay}
              navigation={this.props.navigation}
              changeDelay={this.changeDelay.bind(this)}
            />
          </View>
        </View>
      </View>
    );
  }

  changeDelay(e) {
    this.setState(
      {
        delay: e,
      },
      () => {
        this.save();
      },
    );
  }

  handleRadioChange(e) {
    var status = e.nativeEvent.selectedSegmentIndex;
    this.setState(
      {
        value: status,
      },
      () => {
        this.save();
      },
    );
  }

  onClick() {
    if (this.state.checked == true) {
      this.setState(
        {
          checked: false,
        },
        () => {
          this.props.target.checked = false;
        },
      );
    } else {
      this.save();
    }
  }

  save() {
    var value = this.state.value;
    var param, status;
    if (value == 0) {
      status = 'lock';
      param = 255;
    } else {
      status = 'unlock';
      param = 0;
    }

    this.setState({
      checked: false,
    });
    const {device} = this.props;
    var deviceControl = new DeviceControl({
      spec: this.props.spec,
      param: param,
      sn_id: device.sn_id,
      home_id: device.home_id,
      sn: device.sn,
    });

    deviceControl.switch(cmd => {
      this.props.target.commands = cmd;
    });
    this.props.target.sn_id = this.props.device.sn_id;
    this.props.target.home_id = this.props.device.home_id;
    this.props.target.sn = this.props.device.sn;
    this.props.target.device_uuid = this.props.device.uuid;
    this.props.target.node_id = this.props.spec.device_id;
    this.props.target.value_id = this.props.spec.value_id;
    this.props.target.instance_id = this.props.spec.instance_id;
    this.props.target.spec_cc = this.props.spec.cc;
    this.props.target.spec_name = this.props.spec.name;
    this.props.target.app_url = this.props.spec.app_url;
    this.props.target.spec_value = status;
    this.props.target.params = param;
    this.props.target.desp = status;
    this.props.target.delay = this.state.delay;
    this.props.target.checked = true;
    this.props.target.target_type = this.props.spec.dv_type;
    this.props.spec_settings.targets.push(this.props.target);
  }
}
