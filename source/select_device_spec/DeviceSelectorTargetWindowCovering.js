/* eslint-disable radix */
import React from 'react';
import {Text, View, Image} from 'react-native';
import {Helper} from '../Helper';
import Slider from '@react-native-community/slider';
import {observer} from 'mobx-react/native';
import CheckBox from '../check_box/index';
import {Tme, Colors} from '../ThemeStyle';
import DeviceControl from '../DeviceControl';
import {DelayInputModal} from '../share/DelayInputModal';

@observer
class DeviceSelectorTargetWindowCovering extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      initialValue: 0,
      delay: this.props.target.delay || '0',
      checked: this.props.target.checked,
    };
  }

  componentDidMount() {
    var params = this.props.target.params;
    var value = params === '' ? 0 : parseInt(params);
    var that = this;
    if (params >= 0 && params !== '') {
      that.setState({
        initialValue: value === 0 ? 0 : value / 100,
      });
    }
  }

  render() {
    return (
      <View style={{paddingHorizontal: 8}}>
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            borderRadius: 4,
          }}>
          <View style={{padding: 10}}>
            <View
              testID="selectDeviceItemRow"
              style={{marginBottom: 20, marginTop: 16}}>
              <CheckBox
                rightTextStyle={{
                  fontWeight: '300',
                  fontSize: 16,
                  color: Tme('cardTextColor'),
                }}
                onClick={() => this.onClick()}
                rightText={Helper.i(this.props.spec.name)}
                isChecked={this.state.checked}
                checkedImage={
                  <Image
                    source={require('../../img/checkbox-checked.png')}
                    style={{width: 17, height: 17}}
                  />
                }
                unCheckedImage={
                  <Image
                    source={require('../../img/Checkbox.png')}
                    style={{width: 17, height: 17}}
                  />
                }
              />
            </View>
            <View style={{marginTop: 8}}>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 8,
                  marginTop: 20,
                }}>
                <Text
                  style={{
                    fontSize: 22,
                    fontWeight: '500',
                    color: Tme('cardTextColor'),
                  }}>
                  {this.state.initialValue}
                </Text>
              </View>
              <Slider
                minimumValue={0}
                maximumValue={100}
                step={1}
                minimumTrackTintColor={Colors.MainColor}
                value={this.state.initialValue}
                onValueChange={this.slider_change.bind(this, 'change')}
                onSlidingComplete={this.slider_change.bind(this, 'after')}
              />
              <View
                style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                <Text style={{fontWeight: '500', color: Tme('cardTextColor')}}>
                  0
                </Text>
                <Text style={{fontWeight: '500', color: Tme('cardTextColor')}}>
                  100
                </Text>
              </View>
            </View>
            <DelayInputModal
              delay={this.state.delay}
              navigation={this.props.navigation}
              changeDelay={this.changeDelay.bind(this)}
            />
          </View>
        </View>
      </View>
    );
  }

  changeDelay(e) {
    this.setState(
      {
        delay: e,
      },
      () => {
        this.save();
      },
    );
  }

  slider_change(type, e) {
    this.setState(
      {
        initialValue: e,
      },
      () => {
        if (type == 'after') {
          this.save();
        }
      },
    );
  }

  onClick() {
    if (this.state.checked == true) {
      this.setState(
        {
          checked: false,
        },
        () => {
          this.props.target.checked = false;
        },
      );
    } else {
      this.save();
    }
  }

  save() {
    var param = this.state.initialValue;

    this.setState({
      checked: true,
    });
    const {device} = this.props;

    var deviceControl = new DeviceControl({
      spec: this.props.spec,
      param: param * 100,
      param_type: 'percentage',
      sn_id: device.sn_id,
      sn: device.sn,
      home_id: device.home_id,
    });

    deviceControl.windowcovering(cmd => {
      this.props.target.commands = cmd;
    });
    this.props.target.sn_id = this.props.device.sn_id;
    this.props.target.home_id = this.props.device.home_id;
    this.props.target.sn = this.props.device.sn;
    this.props.target.device_uuid = this.props.device.uuid;
    this.props.target.node_id = this.props.spec.device_id;
    this.props.target.value_id = this.props.spec.value_id;
    this.props.target.instance_id = this.props.spec.instance_id;
    this.props.target.spec_cc = this.props.spec.cc;
    this.props.target.spec_name = this.props.spec.name;
    this.props.target.spec_value = param * 100;
    this.props.target.params = param * 100;
    this.props.target.desp = param * 100;
    this.props.target.app_url = this.props.spec.app_url;
    this.props.target.delay = this.state.delay;
    this.props.target.checked = true;
    this.props.target.target_type = this.props.spec.dv_type;
    this.props.spec_settings.targets.push(this.props.target);
  }
}
export default DeviceSelectorTargetWindowCovering;
