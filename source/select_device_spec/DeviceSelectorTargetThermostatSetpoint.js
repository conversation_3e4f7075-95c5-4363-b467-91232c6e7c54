/* eslint-disable radix */
import React from 'react';
import {Text, View, Image} from 'react-native';
import {Helper, HelperMemo} from '../Helper';
import Slider from '@react-native-community/slider';
import CheckBox from '../check_box/index';
import {Tme, Colors} from '../ThemeStyle';
import DeviceControl from '../DeviceControl';
import {DelayInputModal} from '../share/DelayInputModal';
import {observer} from 'mobx-react/native';
@observer
class DeviceSelectorTargetThermostatSetpoint extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      initialValue: 0,
      delay: this.props.target.delay || '0',
      checked: this.props.target.checked,
      min: 0,
      max: 99,
    };
  }

  componentDidMount() {
    this.setState({
      initialValue: Helper.temperatureScale(
        this.props.target.spec_value !== ''
          ? parseInt(this.props.target.spec_value) || 0
          : parseInt(this.props.spec.value) || 0,
        this.props.spec.unit_index,
        false,
      ),
      max:
        HelperMemo.user_data.user.scale == 'F'
          ? this.props.spec.max * 1.8 + 32
          : this.props.spec.max,
      min: HelperMemo.user_data.user.scale == 'C' ? 0 : 32,
    });
  }

  renderReturn() {
    return (
      <View style={{paddingHorizontal: 8}}>
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            borderRadius: 4,
          }}>
          <View style={{padding: 10}}>
            <View
              testID="selectDeviceItemRow"
              style={{marginBottom: 20, marginTop: 16}}>
              <CheckBox
                rightTextStyle={{
                  fontWeight: '300',
                  fontSize: 16,
                  color: Tme('cardTextColor'),
                }}
                onClick={() => this.onClick()}
                rightText={Helper.i(this.props.spec.name)}
                isChecked={this.state.checked}
                checkedImage={
                  <Image
                    source={require('../../img/checkbox-checked.png')}
                    style={{width: 17, height: 17}}
                  />
                }
                unCheckedImage={
                  <Image
                    source={require('../../img/Checkbox.png')}
                    style={{width: 17, height: 17}}
                  />
                }
              />
            </View>
            <View style={{marginTop: 8}}>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 8,
                  marginTop: 20,
                  flexDirection: 'row',
                }}>
                <Text
                  style={{
                    fontSize: 22,
                    fontWeight: '500',
                    color: Tme('cardTextColor'),
                  }}>
                  {this.state.initialValue}
                </Text>
                <Text
                  style={{
                    fontSize: 22,
                    fontWeight: '500',
                    color: Tme('cardTextColor'),
                  }}>
                  {' '}
                  °{HelperMemo.user_data.user.scale}
                </Text>
              </View>
              <Slider
                minimumValue={this.state.min}
                maximumValue={this.state.max}
                step={1}
                minimumTrackTintColor={Colors.MainColor}
                value={this.state.initialValue}
                // onChange={this.handleSliderChange.bind(this)}
                onSlidingComplete={this.handleSliderChange.bind(this)}
              />
              <View
                style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                <Text style={{fontWeight: '500', color: Tme('cardTextColor')}}>
                  {this.state.min} °{HelperMemo.user_data.user.scale}
                </Text>
                <Text style={{fontWeight: '500', color: Tme('cardTextColor')}}>
                  {this.state.max} °{HelperMemo.user_data.user.scale}
                </Text>
              </View>
            </View>
            <DelayInputModal
              delay={this.state.delay}
              navigation={this.props.navigation}
              changeDelay={this.changeDelay.bind(this)}
            />
          </View>
        </View>
      </View>
    );
  }

  render() {
    return this.renderReturn();
  }

  changeDelay(e) {
    this.setState(
      {
        delay: e,
      },
      () => {
        this.save();
      },
    );
  }

  onClick() {
    if (this.state.checked == true) {
      this.setState(
        {
          checked: false,
        },
        () => {
          this.props.target.checked = false;
        },
      );
    } else {
      this.save();
    }
  }

  handleSliderChange(e) {
    this.setState(
      {
        initialValue: e,
      },
      () => {
        this.save();
      },
    );
  }
  save() {
    this.setState({
      checked: true,
    });
    var params = Helper.temperatureScale(
      this.state.initialValue,
      this.props.spec.unit_index,
      true,
    );

    this.props.target.node_id = this.props.spec.device_id;
    this.props.target.sn_id = this.props.device.sn_id;
    this.props.target.home_id = this.props.device.home_id;
    this.props.target.sn = this.props.device.sn;
    this.props.target.device_uuid = this.props.device.uuid;
    this.props.target.value_id = this.props.spec.value_id;
    this.props.target.instance_id = this.props.spec.instance_id;
    this.props.target.spec_cc = this.props.spec.cc;
    this.props.target.spec_name = this.props.spec.name;
    this.props.target.spec_value = params;
    this.props.target.params = params;
    this.props.target.desp = params + (this.props.spec.scale_string || '');

    const {device} = this.props;
    var deviceControl = new DeviceControl({
      spec: this.props.spec,
      param: params * 100,
      device: this.props.device,
      setpoint_type: this.props.fromItem,
      sn_id: device.sn_id,
      home_id: device.home_id,
      sn: device.sn,
    });
    deviceControl.thermostat_setpoint(cmd => {
      this.props.target.commands = cmd;
    });
    this.props.target.delay = this.state.delay;
    this.props.target.checked = true;
    this.props.target.target_type = this.props.spec.dv_type;
    this.props.spec_settings.targets.push(this.props.target);
  }
}
export default DeviceSelectorTargetThermostatSetpoint;
