import React from 'react';
import {View, StyleSheet, TextInput, Image} from 'react-native';
import {Helper} from '../Helper';
import I18n from '../I18n';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import CheckBox from '../check_box/index';
import {Tme, Colors} from '../ThemeStyle';
import {DelayInputModal} from '../share/DelayInputModal';

export default class DeviceSelectorTargetVirtualDevice extends React.Component {
  constructor(props) {
    super(props);

    var initialValue = 0;
    if (this.props.target.target_type == 'manual') {
      initialValue = 1;
    }

    this.state = {
      value: this.props.target.app_url || '',
      initialValue: initialValue,
      delay: this.props.target.delay || '0',
      delay_value: this.props.target.delay || '0',
      modalVisible: false,
      checked: this.props.target.checked,
    };
  }

  render() {
    return (
      <View style={{paddingHorizontal: 8}}>
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            borderRadius: 4,
          }}>
          <View style={{padding: 10}}>
            <View
              testID="selectDeviceItemRow"
              style={{marginBottom: 20, marginTop: 16}}>
              <CheckBox
                rightTextStyle={{
                  fontWeight: '300',
                  fontSize: 16,
                  color: Tme('cardTextColor'),
                }}
                onClick={() => this.onClick()}
                rightText={Helper.i(this.props.spec.name)}
                isChecked={this.state.checked}
                checkedImage={
                  <Image
                    source={require('../../img/checkbox-checked.png')}
                    style={{width: 17, height: 17}}
                  />
                }
                unCheckedImage={
                  <Image
                    source={require('../../img/Checkbox.png')}
                    style={{width: 17, height: 17}}
                  />
                }
              />
            </View>
            <View
              style={[
                styles.account_view,
                {borderColor: Tme('inputBorderColor')},
              ]}>
              <TextInput
                autoCapitalize="none"
                underlineColorAndroid="transparent"
                autoCorrect={false}
                placeholderTextColor={Tme('placeholder')}
                style={Colors.TextInputStyle()}
                value={this.state.value}
                onChangeText={value => {
                  this.setState({value: value}, () => {
                    this.save();
                  });
                }}
              />
            </View>
            <View style={{height: 40}}>
              <SegmentedControl
                tintColor={Colors.MainColor}
                values={[I18n.t('trigger.auto'), I18n.t('trigger.manual')]}
                selectedIndex={this.state.initialValue}
                fontStyle={{color: Tme('cardTextColor'), fontSize: 16}}
                activeFontStyle={{color: '#ffffff', fontSize: 16}}
                style={{flex: 1, height: 42}}
                onChange={this.switch_change.bind(this)}
              />
            </View>
            <DelayInputModal
              delay={this.state.delay}
              navigation={this.props.navigation}
              changeDelay={this.changeDelay.bind(this)}
            />
          </View>
        </View>
      </View>
    );
  }

  changeDelay(e) {
    this.setState(
      {
        delay: e,
      },
      () => {
        this.save();
      },
    );
  }

  onClick() {
    if (this.state.checked == true) {
      this.setState(
        {
          checked: false,
        },
        () => {
          this.props.target.checked = false;
        },
      );
    } else {
      this.save();
    }
  }

  switch_change(e) {
    this.setState(
      {
        initialValue: e.nativeEvent.selectedSegmentIndex,
      },
      () => {
        this.save();
      },
    );
  }

  save() {
    var param = 'auto';
    if (this.state.initialValue == 1) {
      param = 'manual';
    }
    this.setState({
      checked: true,
    });
    this.props.target.sn_id = this.props.device.sn_id;
    this.props.target.home_id = this.props.device.home_id;
    this.props.target.sn = this.props.device.sn;
    this.props.target.device_uuid = this.props.device.uuid;
    this.props.target.node_id = this.props.spec.device_id;
    this.props.target.value_id = this.props.spec.value_id;
    this.props.target.instance_id = this.props.spec.instance_id;
    this.props.target.spec_name = this.props.spec.name;
    this.props.target.app_url = this.state.value;
    this.props.target.target_type = param;
    this.props.target.delay = this.state.delay;
    this.props.target.checked = true;
    this.props.target.target_type = this.props.spec.dv_type;
    this.props.spec_settings.targets.push(this.props.target);
  }
}
const styles = StyleSheet.create({
  account_view: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 3,
    marginBottom: 20,
    marginTop: 16,
  },
});
