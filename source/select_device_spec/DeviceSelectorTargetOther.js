import React from 'react';
import {View, Image, Text, TouchableOpacity} from 'react-native';
import {Helper} from '../Helper';
import _ from 'lodash';
import Slider from '@react-native-community/slider';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import CheckBox from '../check_box/index';
import {Tme, Colors} from '../ThemeStyle';
import DeviceControl from '../DeviceControl';
import {DelayInputModal} from '../share/DelayInputModal';

export default class DeviceSelectorTargetOther extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: 0,
      initialValue: 0,
      delay: this.props.target.delay || '0',
      modalVisible: false,
      checked: this.props.target.checked,
    };
  }

  componentDidMount() {
    this.initValue();
  }

  initValue() {
    const {spec} = this.props;
    if (spec.options.length > 0) {
      var values = [];
      var keys = [];
      _.each(spec.options, (v, k) => {
        keys.push(Helper.i(v.name));
        values.push(v.val);
      });

      this.setState({
        initialValue: _.findIndex(values, v => {
          return v == spec.value;
        }),
        values: values,
        keys: keys,
      });
    }
  }

  render() {
    var that = this;
    const {spec} = this.props;
    var con;
    if (spec.min >= 0 && spec.max > 0) {
      con = that.showSlider(spec);
    } else if (spec.options.length > 0) {
      con = this.showOptions(spec);
    } else {
      return null;
    }
    return (
      <View style={{paddingHorizontal: 8}}>
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            borderRadius: 4,
          }}>
          <View style={{padding: 10}}>
            <View
              testID="selectDeviceItemRow"
              style={{marginBottom: 20, marginTop: 16}}>
              <CheckBox
                rightTextStyle={{
                  fontWeight: '300',
                  fontSize: 16,
                  color: Tme('cardTextColor'),
                }}
                onClick={() => this.onClick()}
                rightText={Helper.i(this.props.spec.name)}
                isChecked={this.state.checked}
                checkedImage={
                  <Image
                    source={require('../../img/checkbox-checked.png')}
                    style={{width: 17, height: 17}}
                  />
                }
                unCheckedImage={
                  <Image
                    source={require('../../img/Checkbox.png')}
                    style={{width: 17, height: 17}}
                  />
                }
              />
            </View>
            <View style={{marginTop: 8}}>{con}</View>
            <DelayInputModal
              delay={this.state.delay}
              navigation={this.props.navigation}
              changeDelay={this.changeDelay.bind(this)}
            />
          </View>
        </View>
      </View>
    );
  }

  showOptions(spec) {
    var html;
    const {keys, values, initialValue} = this.state;
    const that = this;
    switch (spec.options.length) {
      case 2:
      case 3:
        html = (
          <SegmentedControl
            key={Math.random()}
            tintColor={Colors.MainColor}
            values={keys}
            selectedIndex={initialValue}
            fontStyle={{color: Tme('cardTextColor'), fontSize: 16}}
            activeFontStyle={{color: '#ffffff', fontSize: 16}}
            style={{flex: 1, height: 42}}
            onChange={this.handleRadioChange.bind(this, values)}
          />
        );
        break;
      default:
        html = _.sortBy(spec.options, function (n) {
          return n.val;
        }).map(function (option, k) {
          if (that.state.value == option.val) {
            return (
              <TouchableOpacity
                key={'spec_' + k}
                activeOpacity={0.8}
                onPress={that.setMode.bind(that, option.val)}
                style={{
                  marginRight: 16,
                  paddingVertical: 10,
                  paddingHorizontal: 16,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: Colors.MainColor,
                  borderRadius: 8,
                  marginTop: 16,
                }}>
                <Text style={{color: 'white', fontSize: 17}}>
                  {Helper.i(option.name)}
                </Text>
              </TouchableOpacity>
            );
          } else {
            return (
              <TouchableOpacity
                key={'spec_' + k}
                activeOpacity={0.8}
                onPress={that.setMode.bind(that, option.val)}
                style={{
                  marginRight: 16,
                  paddingVertical: 10,
                  paddingHorizontal: 16,
                  borderWidth: 1,
                  borderColor: Colors.MainColor,
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderRadius: 8,
                  marginTop: 16,
                }}>
                <Text style={{color: Tme('cardTextColor'), fontSize: 17}}>
                  {Helper.i(option.name)}
                </Text>
              </TouchableOpacity>
            );
          }
        });
    }
    return html;
  }
  setMode(e) {
    this.setState(
      {
        value: e,
      },
      () => {
        this.save();
      },
    );
  }
  handleRadioChange(values, e) {
    var index = e.nativeEvent.selectedSegmentIndex;
    this.setState(
      {
        value: values[index],
        initialValue: index,
      },
      () => {
        this.save();
      },
    );
  }
  showSlider(spec) {
    return (
      <View style={{marginTop: 8}}>
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 8,
            marginTop: 20,
          }}>
          <Text
            style={{
              fontSize: 22,
              fontWeight: '500',
              color: Tme('cardTextColor'),
            }}>
            {this.state.initialValue}
          </Text>
        </View>
        <Slider
          minimumValue={spec.min}
          maximumValue={spec.max}
          step={1}
          minimumTrackTintColor={Colors.MainColor}
          value={this.state.initialValue}
          onChange={this.handleSliderChange.bind(this)}
          onSlidingComplete={this.sliderDidChange.bind(this)}
        />
        <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
          <Text style={{color: Tme('cardTextColor')}}>{spec.min}</Text>
          <Text style={{color: Tme('cardTextColor')}}>{spec.max}</Text>
        </View>
      </View>
    );
  }

  handleSliderChange(e) {
    this.setState(
      {
        initialValue: e,
        value: e,
      },
      () => {
        this.save();
      },
    );
  }

  sliderDidChange(e) {
    this.handleSliderChange(e);
  }

  changeDelay(e) {
    this.setState(
      {
        delay: e,
      },
      () => {
        this.save();
      },
    );
  }

  onClick() {
    if (this.state.checked == true) {
      this.setState(
        {
          checked: false,
        },
        () => {
          this.props.target.checked = false;
        },
      );
    } else {
      this.save();
    }
  }

  save() {
    var param = this.state.value;
    var status = this.state.value;

    this.setState({
      checked: true,
    });
    const {device} = this.props;
    var deviceControl = new DeviceControl({
      spec: this.props.spec,
      param: param,
      sn_id: device.sn_id,
      home_id: device.home_id,
      sn: device.sn,
    });
    deviceControl.other(cmd => {
      this.props.target.commands = cmd;
    });
    this.props.target.sn_id = this.props.device.sn_id;
    this.props.target.home_id = this.props.device.home_id;
    this.props.target.sn = this.props.device.sn;
    this.props.target.device_uuid = this.props.device.uuid;
    this.props.target.node_id = this.props.spec.device_id;
    this.props.target.value_id = this.props.spec.value_id;
    this.props.target.instance_id = this.props.spec.instance_id;
    this.props.target.spec_cc = this.props.spec.cc;
    this.props.target.spec_name = this.props.spec.name;
    this.props.target.spec_value = status;
    this.props.target.params = param;
    this.props.target.desp = status;
    this.props.target.checked = true;
    this.props.target.delay = this.state.delay;
    this.props.target.target_type = this.props.spec.dv_type;
    this.props.spec_settings.targets.push(this.props.target);
  }

  // handleRadioChange(e) {
  //   var value = e.nativeEvent.selectedSegmentIndex;

  //   this.setState(
  //     {
  //       value: value,
  //     },
  //     () => {
  //       this.save();
  //     },
  //   );
  // }
}
