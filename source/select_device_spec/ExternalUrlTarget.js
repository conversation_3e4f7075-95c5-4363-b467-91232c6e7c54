import React from 'react';
import { Text, View, TextInput, TouchableOpacity, Linking } from 'react-native';
import { mainRadius } from '../Tools';
import I18n from '../I18n';
import { Colors, Tme } from '../ThemeStyle';
import { observer } from 'mobx-react/native';
import { DelayInputModal } from '../share/DelayInputModal';

@observer
class ExternalUrlTarget extends React.Component {
  constructor(props) {
    super(props);
    // const {targets} = this.props.routine;
    const targets = this.props.data.targets ? this.props.data.targets : [];

    this.state = {
      delay: targets.length > 0 ? targets[0].delay : 0,
      url: targets.length > 0 ? targets[0].app_url : '',
    };
  }

  changeDelay(e) {
    this.setState({ delay: e }, () => {
      if (this.props.routine.targets.length > 0) {
        this.props.routine.targets[0].delay = e;
      } else {
        this.props.routine.targets.push({ delay: e });
      }
    });
  }

  onChange(e) {
    this.setState(
      {
        url: e,
      },
      () => {
        if (this.props.routine.targets.length > 0) {
          this.props.routine.targets[0].app_url = e;
        } else {
          this.props.routine.targets.push({ app_url: e });
        }
      },
    );
  }

  onTestClick() {
    Linking.openURL(this.state.url);
  }

  render() {
    return (
      <View style={{ marginBottom: 20 }}>
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            borderRadius: mainRadius(),
          }}>
          <View style={{ paddingHorizontal: 8 }}>
            <View
              style={{
                backgroundColor: Tme('cardColor'),
              }}>
              <View style={{ padding: 10 }}>
                <View style={{ marginTop: 10 }}>
                  <Text style={{ color: Tme('cardTextColor') }}>
                    {I18n.t('routine.call_url')}
                  </Text>
                  <View
                    style={{
                      padding: 3,
                      borderWidth: 1,
                      marginTop: 16,
                      marginBottom: 8,
                      borderRadius: 3,
                      borderColor: Tme('inputBorderColor'),
                    }}>
                    <TextInput
                      placeholderTextColor={Tme('placeholder')}
                      placeholder="https://"
                      style={Colors.TextInputStyle()}
                      autoCapitalize="none"
                      underlineColorAndroid="transparent"
                      // keyboardType="number-pad"
                      value={this.state.url}
                      onChangeText={this.onChange.bind(this)}
                    />
                  </View>
                  <Text
                    style={{
                      color: Tme('cardTextColor'),
                      fontSize: 12,
                    }}>
                    {I18n.t('routine.call_url_desp')}
                  </Text>
                </View>
                <DelayInputModal
                  delay={this.state.delay}
                  navigation={this.props.navigation}
                  changeDelay={this.changeDelay.bind(this)}
                />
                <View
                  style={{ justifyContent: 'flex-end', flexDirection: 'row' }}>
                  <TouchableOpacity
                    onPress={this.onTestClick.bind(this)}
                    activeOpacity={0.8}
                    style={{
                      backgroundColor: Colors.MainColor,
                      borderRadius: 6,
                      justifyContent: 'center',
                      alignItems: 'center',
                      paddingVertical: 6,
                      paddingHorizontal: 10,
                    }}>
                    <Text style={{ color: '#fff' }}>
                      {I18n.t('routine.test_app_url')}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
    );
  }
}
export default ExternalUrlTarget;
