import React from 'react';
import { Text, View, Image, TouchableOpacity } from 'react-native';
import _ from 'lodash';
import { Helper } from '../Helper';
import { showSpecValue, specNameEqual } from '../Tools';
import I18n from '../I18n';
import ActionSheet from '@alessiocancian/react-native-actionsheet';
import { observable } from 'mobx';
import CheckBox from '../check_box/index';
import { observer } from 'mobx-react/native';
import { Tme } from '../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { DelayInputModal } from '../share/DelayInputModal';

class Tool {
  @observable valueDomValue = '';
  @observable whenDomValue = '';
}
@observer
class DeviceSelectorConditionCommon extends React.Component {
  constructor(props) {
    super(props);
    this.tool = new Tool();

    this.state = {
      modalVisible: false,
      checked: this.props.condition.checked,
      valueOptions: [],
      valueValues: [],
      whenOptions: [],
      whenValues: [],
    };
    this.inputValue = this.props.condition.spec_value;

    // 创建 ActionSheet 的引用
    this.valueActionSheet = null;
    this.whenActionSheet = null;
  }

  componentDidMount() {
    var spec_value;

    if (this.props.spec.dv_type !== '433') {
      if (this.props.spec.name == 'Battery') {
        spec_value = this.props.spec.value;
      } else {
        spec_value = showSpecValue(this.props.spec.value);
      }
    } else {
      spec_value = showSpecValue(this.props.spec.value);
    }
    if (this.props.spec.spec_type == 3) {
      if (!this.props.condition.logic) {
        this.props.condition.logic = 'eq';
        this.tool.whenDomValue = '=';
      } else {
        this.tool.whenDomValue = '=';
      }
      if (!this.props.condition.spec_value) {
        this.props.condition.spec_value = 'yes';
        this.tool.valueDomValue = Helper.i('yes');
      } else {
        this.tool.valueDomValue = Helper.i('yes');
      }
      // 如果是yes | no, true | false 类型的
    } else if (
      _.includes(
        ['yes', 'no', 'true', 'false'],
        spec_value.toString().toLowerCase(),
      )
    ) {
      if (!this.props.condition.logic) {
        this.props.condition.logic = 'eq';
        this.tool.whenDomValue = '=';
      } else {
        this.tool.whenDomValue = '=';
      }
      if (!this.props.condition.spec_value) {
        this.props.condition.spec_value = 'yes';
        this.tool.valueDomValue = Helper.i('yes');
      } else {
        var temp = {
          yes: Helper.i('yes'),
          no: Helper.i('no'),
          true: Helper.i('true'),
          false: Helper.i('false'),
        };
        this.tool.valueDomValue = temp[this.props.condition.spec_value];
      }

      // 如果是 unlock | lock
    } else if (
      _.includes(['unlock', 'lock'], spec_value.toString().toLowerCase())
    ) {
      if (!this.props.condition.logic) {
        this.props.condition.logic = 'eq';
        this.tool.whenDomValue = '=';
      } else {
        this.tool.whenDomValue = '=';
      }

      if (!this.props.condition.spec_value) {
        this.props.condition.spec_value = 'unlock';
        this.tool.valueDomValue = I18n.t('spec.unlock');
      } else {
        this.tool.valueDomValue =
          this.props.condition.spec_value == 'unlock'
            ? I18n.t('spec.unlock')
            : I18n.t('spec.lock');
      }

      // low | normal (433电池) 类型
    } else if (
      _.includes(
        ['low', 'normal', 'alarm'],
        spec_value.toString().toLowerCase(),
      )
    ) {
      if (!this.props.condition.logic) {
        this.props.condition.logic = 'eq';
        this.tool.whenDomValue = '=';
      } else {
        this.tool.whenDomValue = '=';
      }
      if (!this.props.condition.spec_value) {
        this.props.condition.spec_value = 'normal';
        this.tool.valueDomValue = Helper.i('normal');
      } else {
        if (this.props.condition.spec_value == 'normal') {
          this.tool.valueDomValue = Helper.i('normal');
        } else if (this.props.condition.spec_value == 'low') {
          this.tool.valueDomValue = Helper.i('low');
        } else if (this.props.condition.spec_value == 'alarm') {
          this.tool.valueDomValue = Helper.i('alarm');
        }
      }
    } else if (_.includes(['on', 'off'], spec_value.toString().toLowerCase())) {
      if (!this.props.condition.logic) {
        this.props.condition.logic = 'eq';
        this.tool.whenDomValue = '=';
      } else {
        this.tool.whenDomValue = '=';
      }
      if (!this.props.condition.spec_value) {
        this.props.condition.spec_value = 'on';
        this.tool.valueDomValue = I18n.t('spec.on_b');
      } else {
        this.tool.valueDomValue =
          this.props.condition.spec_value == 'on'
            ? I18n.t('spec.on_b')
            : I18n.t('spec.off_b');
      }
    } else if (this.props.spec.options.length > 0) {
      if (!this.props.condition.logic) {
        this.props.condition.logic = 'eq';
        this.tool.whenDomValue = '=';
      } else {
        this.tool.whenDomValue = '=';
      }
      if (!this.props.condition.spec_value) {
        this.props.condition.spec_value = '0';
        this.tool.valueDomValue = '0';
      } else {
        var temp = {};
        _.forEach(this.props.spec.options, (v, k) => {
          temp[v.val] = Helper.i(v.name);
        });
        this.tool.valueDomValue = temp[this.props.condition.spec_value];
      }
      // 如果是数字类型 | 空字符串 | null
    } else if (
      !isNaN(parseFloat(spec_value)) ||
      spec_value == '' ||
      spec_value === null ||
      this.props.spec.name == 'ThermostatSetpoint'
    ) {
      if (!this.props.condition.logic) {
        this.props.condition.logic = 'gte';
        this.tool.whenDomValue = '>=';
      } else {
        this.tool.whenDomValue =
          this.props.condition.logic == 'gte' ? '>=' : '<';
      }
    }
  }

  render() {
    var whenDOM = null,
      valueDOM = null,
      spec_value = null,
      unit = null;

    // zwave

    if (this.props.spec.dv_type !== '433') {
      if (this.props.spec.name == 'Battery') {
        spec_value = this.props.spec.value;
      } else {
        spec_value = showSpecValue(this.props.spec.value);
      }

      if (this.props.spec.name == 'Battery') {
        unit = '%';
      } else if (this.props.spec.name == 'ThermostatSetpoint') {
        unit = '°C';
      } else {
        unit = this.props.spec.scale_string;
      }
    } else {
      spec_value = showSpecValue(this.props.spec.value);
      unit = this.props.spec.scale_string;
    }
    // 空调
    // if (this.props.spec.name == "ThermostatMode") {
    //   whenDOM = (
    //     <TouchableOpacity
    //       activeOpacity={0.8}
    //       onPress={this.whenDomShow.bind(this, ["=", I18n.t("home.cancel")], ["eq"], 2)}
    //       style={{
    //         flexDirection: "row", justifyContent: "space-between",
    //         alignItems: "center", backgroundColor: Tme("cardColor"),
    //       }}>
    //       <Text style={{ color: Tme("cardTextColor") }}>{I18n.t("device.when")}</Text>
    //       <View style={{ flexDirection: "row" }}>
    //         <Text style={{ color: Tme("cardTextColor") }}>{this.tool.whenDomValue}</Text>
    //         <MaterialIcons name="keyboard-arrow-right" size={20} color={Tme("textColor")} />
    //       </View>
    //     </TouchableOpacity>
    //   );
    //   var key = [], value = [];
    //   _.forEach(this.props.spec.options, (v, k) => {
    //     key.push(v.val);
    //     value.push(Helper.i(v.name));
    //   });
    //   value.push(I18n.t("home.cancel"));
    //   valueDOM = (
    //     <TouchableOpacity
    //       activeOpacity={0.8}
    //       style={{
    //         flexDirection: "row",
    //         justifyContent: "space-between", alignItems: "center",
    //         backgroundColor: Tme("cardColor"),
    //       }}
    //       onPress={
    //         this.valueDomShow.bind(this, value, key, value.length)
    //       }>
    //       <Text style={{ color: Tme("cardTextColor") }}>{I18n.t("device.value")}</Text>
    //       <View style={{ flexDirection: "row" }}>
    //         <Text style={{ color: Tme("cardTextColor") }}>{this.tool.valueDomValue}</Text>
    //         <MaterialIcons name="keyboard-arrow-right" size={20} color={Tme("textColor")} />
    //       </View>
    //     </TouchableOpacity>
    //   );
    // } else
    if (this.props.spec.spec_type === 3) {
      whenDOM = (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: Tme('cardColor'),
          }}>
          <Text style={{ color: Tme('cardTextColor') }}>
            {I18n.t('device.when')}
          </Text>
          <View style={{ flexDirection: 'row' }}>
            <Text style={{ color: Tme('cardTextColor') }}>=</Text>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={20}
              color={Tme('textColor')}
            />
          </View>
        </View>
      );
      valueDOM = (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: Tme('cardColor'),
          }}>
          <Text style={{ color: Tme('cardTextColor') }}>
            {I18n.t('device.value')}
          </Text>
          <View style={{ flexDirection: 'row' }}>
            <Text style={{ color: Tme('cardTextColor') }}>{Helper.i('yes')}</Text>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={20}
              color={Tme('textColor')}
            />
          </View>
        </View>
      );
      // 如果是yes | no, true | false 类型的
    } else if (
      _.includes(
        ['yes', 'no', 'true', 'false'],
        spec_value.toString().toLowerCase(),
      )
    ) {
      whenDOM = (
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={this.whenDomShow.bind(
            this,
            ['=', I18n.t('home.cancel')],
            ['eq'],
            2,
          )}
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: Tme('cardColor'),
          }}>
          <Text style={{ color: Tme('cardTextColor') }}>
            {I18n.t('device.when')}
          </Text>
          <View style={{ flexDirection: 'row' }}>
            <Text style={{ color: Tme('cardTextColor') }}>
              {this.tool.whenDomValue}
            </Text>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={20}
              color={Tme('textColor')}
            />
          </View>
        </TouchableOpacity>
      );
      valueDOM = (
        <TouchableOpacity
          activeOpacity={0.8}
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: Tme('cardColor'),
          }}
          onPress={this.valueDomShow.bind(
            this,
            [Helper.i('yes'), Helper.i('no'), I18n.t('home.cancel')],
            ['yes', 'no'],
            3,
          )}>
          <Text style={{ color: Tme('cardTextColor') }}>
            {I18n.t('device.value')}
          </Text>
          <View style={{ flexDirection: 'row' }}>
            <Text style={{ color: Tme('cardTextColor') }}>
              {this.tool.valueDomValue}
            </Text>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={20}
              color={Tme('textColor')}
            />
          </View>
        </TouchableOpacity>
      );
      // 如果是 unlock | lock
    } else if (
      _.includes(['unlock', 'lock'], spec_value.toString().toLowerCase())
    ) {
      whenDOM = (
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={this.whenDomShow.bind(
            this,
            ['=', I18n.t('home.cancel')],
            ['eq'],
            2,
          )}
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: Tme('cardColor'),
          }}>
          <Text style={{ color: Tme('cardTextColor') }}>
            {I18n.t('device.when')}
          </Text>
          <View style={{ flexDirection: 'row' }}>
            <Text style={{ color: Tme('cardTextColor') }}>
              {this.tool.whenDomValue}
            </Text>
            <Image
              opacity={0.5}
              style={{ width: 16, height: 16, marginLeft: 8 }}
              source={require('../../img/right.png')}
            />
          </View>
        </TouchableOpacity>
      );
      valueDOM = (
        <TouchableOpacity
          activeOpacity={0.8}
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: Tme('cardColor'),
          }}
          onPress={this.valueDomShow.bind(
            this,
            [I18n.t('spec.lock'), I18n.t('spec.lock'), I18n.t('home.cancel')],
            ['unlock', 'lock'],
            3,
          )}>
          <Text style={{ color: Tme('cardTextColor') }}>
            {I18n.t('device.value')}
          </Text>
          <View style={{ flexDirection: 'row' }}>
            <Text style={{ color: Tme('cardTextColor') }}>
              {this.tool.valueDomValue}
            </Text>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={20}
              color={Tme('textColor')}
            />
          </View>
        </TouchableOpacity>
      );
      // low | normal (433电池) 类型
    } else if (
      _.includes(
        ['low', 'normal', 'alarm'],
        spec_value.toString().toLowerCase(),
      )
    ) {
      whenDOM = (
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={this.whenDomShow.bind(
            this,
            ['=', I18n.t('home.cancel')],
            ['eq'],
            2,
          )}
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: Tme('cardColor'),
          }}>
          <Text style={{ color: Tme('cardTextColor') }}>
            {I18n.t('device.when')}
          </Text>
          <View style={{ flexDirection: 'row' }}>
            <Text style={{ color: Tme('cardTextColor') }}>
              {this.tool.whenDomValue}
            </Text>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={20}
              color={Tme('textColor')}
            />
          </View>
        </TouchableOpacity>
      );
      if (this.props.spec.dev_type === '433') {
        valueDOM = (
          <TouchableOpacity
            activeOpacity={0.8}
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              backgroundColor: Tme('cardColor'),
            }}
            onPress={this.valueDomShow.bind(
              this,
              [Helper.i('normal'), Helper.i('low'), I18n.t('home.cancel')],
              ['normal', 'low'],
              3,
            )}>
            <Text style={{ color: Tme('cardTextColor') }}>
              {I18n.t('device.value')}
            </Text>
            <View style={{ flexDirection: 'row' }}>
              <Text style={{ color: Tme('cardTextColor') }}>
                {this.tool.valueDomValue}
              </Text>
              <MaterialIcons
                name="keyboard-arrow-right"
                size={20}
                color={Tme('textColor')}
              />
            </View>
          </TouchableOpacity>
        );
      } else {
        valueDOM = (
          <TouchableOpacity
            activeOpacity={0.8}
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              backgroundColor: Tme('cardColor'),
            }}
            onPress={this.valueDomShow.bind(
              this,
              [Helper.i('normal'), Helper.i('alarm'), I18n.t('home.cancel')],
              ['normal', 'alarm'],
              3,
            )}>
            <Text style={{ color: Tme('cardTextColor') }}>
              {I18n.t('device.value')}
            </Text>
            <View style={{ flexDirection: 'row' }}>
              <Text style={{ color: Tme('cardTextColor') }}>
                {this.tool.valueDomValue}
              </Text>
              <MaterialIcons
                name="keyboard-arrow-right"
                size={20}
                color={Tme('textColor')}
              />
            </View>
          </TouchableOpacity>
        );
      }
      // 开关或者电机
    } else if (_.includes(['on', 'off'], spec_value.toString().toLowerCase())) {
      whenDOM = (
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={this.whenDomShow.bind(
            this,
            ['=', I18n.t('home.cancel')],
            ['eq'],
            2,
          )}
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: Tme('cardColor'),
          }}>
          <Text style={{ color: Tme('cardTextColor') }}>
            {I18n.t('device.when')}
          </Text>
          <View style={{ flexDirection: 'row' }}>
            <Text style={{ color: Tme('cardTextColor') }}>
              {this.tool.whenDomValue}
            </Text>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={20}
              color={Tme('textColor')}
            />
          </View>
        </TouchableOpacity>
      );
      valueDOM = (
        <TouchableOpacity
          activeOpacity={0.8}
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: Tme('cardColor'),
          }}
          onPress={this.valueDomShow.bind(
            this,
            [I18n.t('spec.on_b'), I18n.t('spec.off_b'), I18n.t('home.cancel')],
            ['on', 'off'],
            3,
          )}>
          <Text style={{ color: Tme('cardTextColor') }}>
            {I18n.t('device.value')}
          </Text>
          <View style={{ flexDirection: 'row' }}>
            <Text style={{ color: Tme('cardTextColor') }}>
              {this.tool.valueDomValue}
            </Text>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={20}
              color={Tme('textColor')}
            />
          </View>
        </TouchableOpacity>
      );
    } else if (this.props.spec.options.length > 0) {
      whenDOM = (
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={this.whenDomShow.bind(
            this,
            ['=', I18n.t('home.cancel')],
            ['eq'],
            2,
          )}
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: Tme('cardColor'),
          }}>
          <Text style={{ color: Tme('cardTextColor') }}>
            {I18n.t('device.when')}
          </Text>
          <View style={{ flexDirection: 'row' }}>
            <Text style={{ color: Tme('cardTextColor') }}>
              {this.tool.whenDomValue}
            </Text>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={20}
              color={Tme('textColor')}
            />
          </View>
        </TouchableOpacity>
      );
      var key = [],
        value = [];
      _.forEach(this.props.spec.options, (v, k) => {
        key.push(v.val);
        value.push(Helper.i(v.name));
      });
      value.push(I18n.t('home.cancel'));
      valueDOM = (
        <TouchableOpacity
          activeOpacity={0.8}
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: Tme('cardColor'),
          }}
          onPress={this.valueDomShow.bind(this, value, key, value.length)}>
          <Text style={{ color: Tme('cardTextColor') }}>
            {I18n.t('device.value')}
          </Text>
          <View style={{ flexDirection: 'row' }}>
            <Text style={{ color: Tme('cardTextColor') }}>
              {this.tool.valueDomValue}
            </Text>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={20}
              color={Tme('textColor')}
            />
          </View>
        </TouchableOpacity>
      );
      // 如果是数字类型 | 空字符串 | null
    } else if (
      !isNaN(parseFloat(spec_value)) ||
      spec_value == '' ||
      spec_value === null ||
      this.props.spec.name == 'ThermostatSetpoint'
    ) {
      whenDOM = (
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={this.whenDomShow.bind(
            this,
            ['>=', '<', I18n.t('home.cancel')],
            ['gte', 'lt'],
            3,
          )}
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: Tme('cardColor'),
          }}>
          <Text style={{ color: Tme('cardTextColor') }}>
            {I18n.t('device.when')}
          </Text>
          <View style={{ flexDirection: 'row' }}>
            <Text style={{ color: Tme('cardTextColor') }}>
              {this.tool.whenDomValue}
            </Text>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={20}
              color={Tme('textColor')}
            />
          </View>
        </TouchableOpacity>
      );

      valueDOM = (
        <DelayInputModal
          type="condition"
          desp={unit}
          title={I18n.t('device.value')}
          delay={this.props.condition.spec_value}
          navigation={this.props.navigation}
          changeDelay={this.changeDelay.bind(this)}
        />
      );
    } else {
      whenDOM = <Text style={{ color: Tme('textColor') }}>N/A</Text>;
      valueDOM = <Text style={{ color: Tme('textColor') }}>N/A</Text>;
    }

    if (specNameEqual('generalpurpose', this.props.spec.name)) {
      whenDOM = (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: Tme('cardColor'),
          }}>
          <Text style={{ color: Tme('cardTextColor') }}>
            {I18n.t('device.when')}
          </Text>
          <View style={{ flexDirection: 'row' }}>
            <Text style={{ color: Tme('cardTextColor') }}>=</Text>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={20}
              color={Tme('textColor')}
            />
          </View>
        </View>
      );
      valueDOM = (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: Tme('cardColor'),
          }}>
          <Text style={{ color: Tme('cardTextColor') }}>
            {I18n.t('device.value')}
          </Text>
          <View style={{ flexDirection: 'row' }}>
            <Text style={{ color: Tme('cardTextColor') }}>{Helper.i('yes')}</Text>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={20}
              color={Tme('textColor')}
            />
          </View>
        </View>
      );
    }

    return (
      <View
        style={{ backgroundColor: Tme('cardColor') }}
        key={'spec_' + this.props.spec.value_id}>
        <View testID="selectDeviceItemRow" style={{ padding: 20 }}>
          <CheckBox
            rightTextStyle={{
              fontWeight: '300',
              fontSize: 16,
              color: Tme('cardTextColor'),
            }}
            onClick={() => this.onClick()}
            rightText={Helper.i(this.props.spec.name)}
            isChecked={this.state.checked}
            checkedImage={
              <Image
                source={require('../../img/checkbox-checked.png')}
                style={{ width: 17, height: 17 }}
              />
            }
            unCheckedImage={
              <Image
                source={require('../../img/Checkbox.png')}
                style={{ width: 17, height: 17 }}
              />
            }
          />
        </View>
        <View>
          <View
            style={{
              paddingTop: 10,
              paddingBottom: 16,
              borderBottomWidth: 1,
              borderBottomColor: Tme('inputBorderColor'),
            }}>
            <View style={{ paddingHorizontal: 20 }}>{whenDOM}</View>
          </View>
          <View style={{ paddingTop: 16, paddingBottom: 16 }}>
            <View style={{ paddingHorizontal: 20 }}>{valueDOM}</View>
          </View>
        </View>

        {/* 用于选择值的 ActionSheet */}
        <ActionSheet
          ref={o => (this.valueActionSheet = o)}
          title={I18n.t('device.value')}
          options={[...this.state.valueOptions]}
          cancelButtonIndex={this.state.valueOptions.length - 1}
          onPress={(index) => {
            if (index < this.state.valueOptions.length - 1) {
              this.props.condition.spec_value = this.state.valueValues[index];
              this.tool.valueDomValue = this.state.valueOptions[index];
              this.save('save');
            }
          }}
        />

        {/* 用于选择逻辑的 ActionSheet */}
        <ActionSheet
          ref={o => (this.whenActionSheet = o)}
          title={I18n.t('device.when')}
          options={[...this.state.whenOptions]}
          cancelButtonIndex={this.state.whenOptions.length - 1}
          onPress={(index) => {
            if (index < this.state.whenOptions.length - 1) {
              this.props.condition.logic = this.state.whenValues[index];
              this.tool.whenDomValue = this.state.whenOptions[index];
              this.save('save');
            }
          }}
        />
      </View>
    );
  }

  changeDelay(e) {
    this.props.condition.spec_value = e;
  }

  onClick() {
    if (this.state.checked == true) {
      this.setState(
        {
          checked: false,
        },
        () => {
          this.save('delete');
        },
      );
    } else {
      this.save('save');
    }
  }

  valueDomShow(options, values, len) {
    // 保存当前选项和值到 state，以便在回调中使用
    this.setState({
      valueOptions: options,
      valueValues: values,
    }, () => {
      // 使用引用调用 show 方法
      this.valueActionSheet.show();
    });
  }

  whenDomShow(options, values, len) {
    // 保存当前选项和值到 state，以便在回调中使用
    this.setState({
      whenOptions: options,
      whenValues: values,
    }, () => {
      // 使用引用调用 show 方法
      this.whenActionSheet.show();
    });
  }

  save(type) {
    if (type == 'save') {
      this.setState({
        checked: true,
      });
      this.props.condition.checked = true;
      this.props.condition.sn_id = this.props.device.sn_id;
      this.props.condition.device_uuid = this.props.device.uuid;
      this.props.condition.sn = this.props.device.sn;
      this.props.condition.home_id = this.props.device.home_id;
      this.props.condition.node_id = this.props.spec.device_id;
      this.props.condition.value_id = this.props.spec.value_id;
      this.props.condition.instance_id = this.props.spec.instance_id;
      this.props.condition.spec_cc = this.props.spec.cc;
      this.props.condition.spec_name = this.props.spec.name;
      this.props.condition.target_type = this.props.spec.dv_type;
      this.props.spec_settings.conditions.push(this.props.condition);
    } else {
      this.props.condition.checked = false;
      // this.props.spec_settings.conditions.remove(this.props.condition)
    }
  }
}
export default DeviceSelectorConditionCommon;
