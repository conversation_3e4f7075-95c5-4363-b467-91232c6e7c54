import React from 'react';
import {Text, View, Image} from 'react-native';
import {Helper} from '../Helper';
import CheckBox from '../check_box/index';
import {Tme} from '../ThemeStyle';
import {DelayInputModal} from '../share/DelayInputModal';

export default class DeviceSelectorTargetCamera extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      delay: this.props.target.delay || '0',
      modalVisible: false,
      checked: this.props.target.checked,
    };
  }

  componentDidMount() {}

  render() {
    return (
      <View style={{paddingHorizontal: 8}}>
        <View
          style={{
            backgroundColor: Tme('cardColor'),
          }}>
          <View style={{padding: 10}}>
            <View
              testID="selectDeviceItemRow"
              style={{marginBottom: 20, marginTop: 16}}>
              <CheckBox
                rightTextStyle={{
                  fontWeight: '300',
                  fontSize: 16,
                  color: Tme('cardTextColor'),
                }}
                onClick={() => this.onClick()}
                rightText={Helper.i(this.props.spec.name)}
                isChecked={this.state.checked}
                checkedImage={
                  <Image
                    source={require('../../img/checkbox-checked.png')}
                    style={{width: 17, height: 17}}
                  />
                }
                unCheckedImage={
                  <Image
                    source={require('../../img/Checkbox.png')}
                    style={{width: 17, height: 17}}
                  />
                }
              />
            </View>
            <View style={{marginTop: 0}}>
              <Text style={{color: Tme('textColor')}}>
                {this.props.spec.screenshot_url}
              </Text>
            </View>
            <DelayInputModal
              delay={this.state.delay}
              navigation={this.props.navigation}
              changeDelay={this.changeDelay.bind(this)}
            />
          </View>
        </View>
      </View>
    );
  }

  changeDelay(e) {
    this.setState(
      {
        delay: e,
      },
      () => {
        this.save();
      },
    );
  }

  onClick() {
    if (this.state.checked == true) {
      this.setState(
        {
          checked: false,
        },
        () => {
          this.props.target.checked = false;
        },
      );
    } else {
      this.save();
    }
  }

  save() {
    this.setState({
      checked: true,
    });
    this.props.target.sn_id = this.props.device.sn_id;
    this.props.target.home_id = this.props.device.home_id;
    this.props.target.sn = this.props.device.sn;
    this.props.target.device_uuid = this.props.device.uuid;
    this.props.target.node_id = this.props.spec.device_id;
    this.props.target.instance_id = this.props.spec.instance_id;
    this.props.target.value_id = this.props.spec.value_id;
    this.props.target.spec_name = this.props.spec.name;
    this.props.target.target_type = 'camera';
    this.props.target.commands = [];
    this.props.target.delay = this.state.delay;
    this.props.target.checked = true;
    this.props.spec_settings.targets.push(this.props.target);
  }
}
