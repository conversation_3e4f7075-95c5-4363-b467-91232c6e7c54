import React from 'react';
import {Text, View, TouchableOpacity, Image} from 'react-native';
import {Helper} from '../Helper';
import _ from 'lodash';
import CheckBox from '../check_box/index';
import {Tme, Colors} from '../ThemeStyle';
import DeviceControl from '../DeviceControl';
import {DelayInputModal} from '../share/DelayInputModal';

export default class DeviceSelectorTargetThermostatMode extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: this.props.target.params,
      time: this.props.time,
      delay: this.props.target.delay || '0',
      checked: this.props.target.checked,
    };
  }

  render() {
    var that = this;
    var options = _.sortBy(this.props.spec.options, function (n) {
      return n.val;
    }).map(function (option, key) {
      if (that.state.value == option.val) {
        return (
          <TouchableOpacity
            key={key}
            activeOpacity={0.8}
            onPress={that.setMode.bind(that, option.val)}
            style={{
              marginRight: 16,
              paddingVertical: 10,
              paddingHorizontal: 16,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: Colors.MainColor,
              borderRadius: 8,
              marginTop: 16,
            }}>
            <Text style={{color: 'white', fontSize: 17}}>
              {Helper.i(option.name)}
            </Text>
          </TouchableOpacity>
        );
      } else {
        return (
          <TouchableOpacity
            key={key}
            activeOpacity={0.8}
            onPress={that.setMode.bind(that, option.val)}
            style={{
              marginRight: 16,
              paddingVertical: 10,
              paddingHorizontal: 16,
              borderWidth: 1,
              borderColor: Tme('smallTextColor'),
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: 8,
              marginTop: 16,
            }}>
            <Text style={{color: Tme('cardTextColor'), fontSize: 17}}>
              {Helper.i(option.name)}
            </Text>
          </TouchableOpacity>
        );
      }
    });
    return (
      <View style={{paddingHorizontal: 8}}>
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            borderRadius: 4,
          }}>
          <View style={{padding: 10}}>
            <View
              testID="selectDeviceItemRow"
              style={{marginBottom: 20, marginTop: 16}}>
              <CheckBox
                rightTextStyle={{
                  fontWeight: '300',
                  fontSize: 16,
                  color: Tme('cardTextColor'),
                }}
                onClick={() => this.onClick()}
                rightText={Helper.i(this.props.spec.name)}
                isChecked={this.state.checked}
                checkedImage={
                  <Image
                    source={require('../../img/checkbox-checked.png')}
                    style={{width: 17, height: 17}}
                  />
                }
                unCheckedImage={
                  <Image
                    source={require('../../img/Checkbox.png')}
                    style={{width: 17, height: 17}}
                  />
                }
              />
            </View>
            <View style={{flexDirection: 'row', flex: 1, flexWrap: 'wrap'}}>
              {options}
            </View>
            <DelayInputModal
              delay={this.state.delay}
              navigation={this.props.navigation}
              changeDelay={this.changeDelay.bind(this)}
            />
          </View>
        </View>
      </View>
    );
  }

  changeDelay(e) {
    this.setState(
      {
        delay: e,
      },
      () => {
        this.save();
      },
    );
  }

  onClick() {
    if (this.state.checked == true) {
      this.setState(
        {
          checked: false,
        },
        () => {
          this.props.target.checked = false;
        },
      );
    } else {
      this.save();
    }
  }

  setMode(e) {
    var status = e;
    this.setState(
      {
        value: status,
      },
      () => {
        this.save();
      },
    );
  }

  save() {
    this.setState({
      checked: true,
    });

    var status = this.state.value;

    this.props.spec_settings.setpoint_type = status;
    this.props.target.sn_id = this.props.device.sn_id;
    this.props.target.home_id = this.props.device.home_id;
    this.props.target.sn = this.props.device.sn;
    this.props.target.device_uuid = this.props.device.uuid;
    this.props.target.spec_value = status;
    this.props.target.params = status;
    this.props.target.node_id = this.props.spec.device_id;
    this.props.target.value_id = this.props.spec.value_id;
    this.props.target.instance_id = this.props.spec.instance_id;
    this.props.target.spec_cc = this.props.spec.cc;
    this.props.target.spec_name = this.props.spec.name;
    this.props.target.desp = this.props.target.spec_value;

    const {device} = this.props;
    var deviceControl = new DeviceControl({
      spec: this.props.spec,
      param: status,
      sn_id: device.sn_id,
      home_id: device.home_id,
      sn: device.sn,
    });
    deviceControl.thermostat_mode(cmd => {
      this.props.target.commands = cmd;
    });
    this.props.target.delay = this.state.delay;
    this.props.target.checked = true;
    this.props.target.target_type = this.props.spec.dv_type;
    this.props.spec_settings.targets.push(this.props.target);
  }
}
