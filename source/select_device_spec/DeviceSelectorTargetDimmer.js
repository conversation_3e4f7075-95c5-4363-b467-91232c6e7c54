/* eslint-disable radix */
import React from 'react';
import {Text, View, Image} from 'react-native';
import {Helper} from '../Helper';
import Slider from '@react-native-community/slider';
import {observer} from 'mobx-react/native';
import CheckBox from '../check_box/index';
import {Tme, Colors} from '../ThemeStyle';
import DeviceControl from '../DeviceControl';
import {DelayInputModal} from '../share/DelayInputModal';

@observer
class DeviceSelectorTargetDimmer extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: 0,
      initialValue: 0,
      delay: this.props.target.delay || '0',
      delay_value: this.props.target.delay || '0',
      modalVisible: false,
      checked: this.props.target.checked,
      runParam: 0,
      min: this.props.spec.min,
      max: this.props.spec.max,
    };
  }

  componentDidMount() {
    var params = parseInt(this.props.target.params);

    var value,
      initialValue,
      runParam = 0;
    if (this.props.spec.dv_type == 'zwave') {
      if (isNaN(params) || params == 0) {
        value = 0;
        runParam = initialValue = 0;
      } else if (params == 255) {
        value = 1;
        runParam = initialValue = 0;
      } else if (params == 99) {
        value = 2;
        runParam = initialValue = 99;
      } else if (params > 0 && params < 99) {
        value = 1;
        runParam = initialValue = params;
      }
    } else if (
      this.props.spec.dv_type == 'zigbee' ||
      this.props.spec.dv_type == 'matter_wifi' ||
      this.props.spec.dv_type == 'matter_thread'
    ) {
      if (isNaN(params)) {
        value = 1;
        initialValue = 0;
      } else {
        initialValue = parseInt((100 / 255) * params);
        runParam = params;
      }
    } else {
      value = initialValue = params = this.props.spec.value;
    }
    this.setState({
      value: value,
      initialValue: initialValue,
      runParam: runParam,
    });
  }

  render() {
    return (
      <View style={{paddingHorizontal: 8}}>
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            borderRadius: 4,
          }}>
          <View style={{padding: 10}}>
            <View
              testID="selectDeviceItemRow"
              style={{marginBottom: 20, marginTop: 16}}>
              <CheckBox
                rightTextStyle={{
                  fontWeight: '300',
                  fontSize: 16,
                  color: Tme('cardTextColor'),
                }}
                onClick={() => this.onClick()}
                rightText={Helper.i(this.props.spec.name)}
                isChecked={this.state.checked}
                checkedImage={
                  <Image
                    source={require('../../img/checkbox-checked.png')}
                    style={{width: 17, height: 17}}
                  />
                }
                unCheckedImage={
                  <Image
                    source={require('../../img/Checkbox.png')}
                    style={{width: 17, height: 17}}
                  />
                }
              />
            </View>

            <View style={{marginTop: 8}}>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 8,
                  marginTop: 20,
                }}>
                <Text
                  style={{
                    fontSize: 22,
                    fontWeight: '500',
                    color: Tme('cardTextColor'),
                  }}>
                  {this.state.initialValue}
                </Text>
              </View>
              <Slider
                minimumValue={0}
                maximumValue={99}
                step={1}
                minimumTrackTintColor={Colors.MainColor}
                value={this.state.initialValue}
                onSlidingComplete={this.slider_change.bind(this, 'after')}
              />
              <View
                style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                <Text style={{fontWeight: '500', color: Tme('cardTextColor')}}>
                  {0}
                </Text>
                <Text style={{fontWeight: '500', color: Tme('cardTextColor')}}>
                  {99}
                </Text>
              </View>
            </View>
            <DelayInputModal
              delay={this.state.delay}
              navigation={this.props.navigation}
              changeDelay={this.changeDelay.bind(this)}
            />
          </View>
        </View>
      </View>
    );
  }

  changeDelay(e) {
    this.setState(
      {
        delay: e,
      },
      () => {
        this.save();
      },
    );
  }

  parseParam(param) {
    if (
      this.props.spec.dv_type == 'zigbee' ||
      this.props.spec.dv_type === 'matter_wifi' ||
      this.props.spec.dv_type === 'matter_thread'
    ) {
      return parseInt((255 / 100) * param);
    } else {
      return param;
    }
  }

  slider_change(type, e) {
    var value;
    if (e === 0) {
      value = 0;
    } else if (e === 99) {
      value = 2;
    } else {
      value = 1;
    }
    var param = this.parseParam(e);
    this.setState(
      {
        value: value,
        initialValue: e,
        runParam: param,
      },
      () => {
        if (type == 'after') {
          this.save();
        }
      },
    );
  }

  // switch_change(e) {
  //   var value = e.nativeEvent.selectedSegmentIndex;
  //   var initialValue = this.state.initialValue;
  //   if (value == 2) {
  //     initialValue = 99;
  //   } else if (value == 0) {
  //     initialValue = 0;
  //   }
  //   this.setState(
  //     {
  //       value: value,
  //       initialValue: initialValue,
  //       runParam: initialValue,
  //     },
  //     () => {
  //       this.save();
  //     },
  //   );
  // }

  onClick() {
    if (this.state.checked == true) {
      this.setState(
        {
          checked: false,
        },
        () => {
          this.props.target.checked = false;
        },
      );
    } else {
      this.save();
    }
  }

  save() {
    var desp, param, status;
    if (this.state.value == 0) {
      param = 0;
      desp = 'Off';
      status = 'Off';
    } else if (this.state.value == 1) {
      status = 'On';
      param = 255;
      desp = 'On';
    } else if (this.state.value == 2) {
      param = 99;
      desp = 'On';
      status = 'Full';
    }

    if (this.state.runParam > 0) {
      param = this.state.runParam;
      if (this.props.spec.dv_type == 'zwave') {
        desp = this.state.runParam + '%';
      } else {
        desp = parseInt((100 / 255) * this.state.runParam) + '%';
      }
      status = 'On';
    }
    this.setState({
      checked: true,
    });

    const {device} = this.props;
    var deviceControl = new DeviceControl({
      spec: this.props.spec,
      param: param,
      sn_id: device.sn_id,
      home_id: device.home_id,
      sn: device.sn,
    });

    deviceControl.dimmer(cmd => {
      this.props.target.commands = cmd;
    });
    this.props.target.sn_id = this.props.device.sn_id;
    this.props.target.home_id = this.props.device.home_id;
    this.props.target.sn = this.props.device.sn;
    this.props.target.device_uuid = this.props.device.uuid;
    this.props.target.node_id = this.props.spec.device_id;
    this.props.target.value_id = this.props.spec.value_id;
    this.props.target.instance_id = this.props.spec.instance_id;
    this.props.target.spec_cc = this.props.spec.cc;
    this.props.target.spec_name = this.props.spec.name;
    this.props.target.spec_value = status;
    this.props.target.params = param;
    this.props.target.desp = desp;
    this.props.target.app_url = this.props.spec.app_url;
    this.props.target.delay = this.state.delay;
    this.props.target.checked = true;
    this.props.target.target_type = this.props.spec.dv_type;
    this.props.spec_settings.targets.push(this.props.target);
  }
}
export default DeviceSelectorTargetDimmer;
