/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable no-shadow */

import React from 'react';
import {Text, View, TouchableOpacity, FlatList} from 'react-native';
import I18n from '../I18n';
import _ from 'lodash';
import {toJS} from 'mobx';
import {observer} from 'mobx-react/native';
import {NotificationCenter, EVENT_SCENE_KEY} from '../NotificationCenter';
import {Toast} from '../Toast';
import {Tme, Colors} from '../ThemeStyle';
import Icons from 'react-native-vector-icons/Ionicons';
import EmptyView from '../share/EmptyView';
import DropdownMenu from './DropdownMenu';
import AlertModal from '../share/AlertModal';

@observer
class SelectDevice extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      key: '',
      options: [],
      sn_id: '',
      devices: this.deviceInit(this.props.device.devices),
      value: '',
    };
    // this.spec_settings = new SelectTargets()
  }

  componentDidMount() {
    var that = this;
    NotificationCenter.addObserver(this, EVENT_SCENE_KEY, data => {
      if (data.type == 'target') {
        that.props.spec_settings.targets = data.data;
      } else if (data.type == 'condition') {
        that.props.spec_settings.conditions = data.data;
      } else {
        that.props.spec_settings.device_ids = data.device_ids;
      }

      this.setState({
        key: Math.random(),
      });
    });

    var rooms = [];
    _.each(this.props.rooms, (cc, index) => {
      rooms.push({label: cc.name, value: cc.id});
    });

    this.setState({
      options: rooms,
    });
  }

  componentWillUnmount() {
    NotificationCenter.removeObserver(EVENT_SCENE_KEY);
  }

  deviceInit(devices) {
    var temp = [];
    if (this.props.type == 'target') {
      _.forEach(devices, (v, k) => {
        if (v.cc_specs.length > 0) {
          if (
            _.find(v.cc_specs, function (o) {
              return o.spec_type === 2;
            })
          ) {
            if (
              _.find(v.cc_specs, function (x) {
                return x.name == 'DoorLock';
              })
            ) {
            } else {
              temp.push(v);
            }
          }
        }
      });
    } else if (this.props.type == 'notify') {
      temp = devices;
    } else {
      _.forEach(devices, (v, k) => {
        if (
          v.dv_type !== 'camera' &&
          v.index !== -999 &&
          v.cc_specs.length > 0
        ) {
          temp.push(v);
        }
      });
    }
    return temp;
  }

  render() {
    return (
      <View style={{flex: 1, backgroundColor: Tme('bgColor')}}>
        {this._header()}
        <Text style={{display: 'none'}}>{this.state.key}</Text>
        <FlatList
          keyboardShouldPersistTaps="never"
          keyboardDismissMode="on-drag"
          ref={flatList => (this._flatList = flatList)}
          // ListHeaderComponent={this._header.bind(this)}
          data={this.state.devices}
          renderItem={this._renderRow.bind(this)}
          // ItemSeparatorComponent={this.line.bind(this)}
          numColumns={1}
          ListEmptyComponent={() => <EmptyView />}
          onEndReachedThreshold={0.1}
          keyExtractor={(item, index) => index.toString()}
        />
      </View>
    );
  }

  line() {
    return (
      <View
        style={{
          height: 1,
          backgroundColor: Tme('inputBorderColor'),
          marginLeft: 16,
        }}
      />
    );
  }

  _header() {
    return (
      <DropdownMenu
        value={this.state.value}
        rooms={this.state.options}
        sn_id={this.state.sn_id}
        onChange={this.onChange.bind(this)}
      />
    );
  }

  onChange(value, type) {
    var devices = this.deviceInit(this.props.device.devices);
    if (type == 'room') {
      if (_.isEmpty(value)) {
        this.setState({value: ''}, () => {
          if (_.isEmpty(this.state.sn_id)) {
            this.setState({devices: devices});
          } else {
            this.snChange(this.state.sn_id, devices);
          }
        });
      } else {
        this.roomChange(value, devices);
      }
    } else if (type == 'sn') {
      if (_.isEmpty(value)) {
        this.setState({sn_id: ''}, () => {
          if (_.isEmpty(this.state.value)) {
            this.setState({devices: devices});
          } else {
            this.roomChange(this.state.value, devices);
          }
        });
      } else {
        this.snChange(value, devices);
      }
    }
  }

  snChange(sn, devices) {
    this.setState({sn_id: sn}, () => {
      if (_.isEmpty(this.state.value)) {
        this.setState({
          devices: _.filter(devices, v => {
            return v.sn_id == sn;
          }),
        });
      } else {
        var room = _.find(this.props.rooms, v => {
          return v.id == this.state.value;
        });
        this.setState({
          devices: _.filter(devices, v => {
            return (
              _.findIndex(room.device_ids, n => n === v.uuid) !== -1 &&
              v.sn_id === sn
            );
          }),
        });
      }
    });
  }

  roomChange(value, devices) {
    this.setState(
      {
        value: value,
      },
      () => {
        if (_.isEmpty(this.state.sn_id)) {
          var room = _.find(this.props.rooms, v => {
            return v.id == value;
          });
          this.setState({
            devices: _.filter(devices, v => {
              return _.findIndex(room.device_ids, n => n === v.uuid) !== -1;
            }),
          });
        } else {
          var room = _.find(this.props.rooms, v => {
            return v.id == value;
          });
          this.setState({
            devices: _.filter(devices, v => {
              return (
                _.findIndex(room.device_ids, n => n === v.uuid) !== -1 &&
                v.sn_id === this.state.sn_id
              );
            }),
          });
        }
      },
    );
  }

  _renderRow({item, index}) {
    var check = false;
    var show = true;

    if (this.props.type === 'target') {
      var targets = [];

      if (item.dv_type == 'camera') {
        if (_.isEmpty(item.cc_specs[0].screenshot_url)) {
          show = false;
          return;
        }
      }

      if (item.is_tuya_camera) {
        show = false;
        return;
      }

      if (!_.isEmpty(this.props.spec_settings.select_sn)) {
        if (item.sn_id) {
          if (item.sn_id !== this.props.spec_settings.select_sn) {
            show = false;
            return;
          }
        }
      }

      _.each(this.props.spec_settings.targets, (v, key) => {
        targets.push('spec_' + v.value_id);
      });
      _.each(item.cc_specs, (cc, index) => {
        if (cc.spec_type !== 1) {
          if (_.includes(targets, 'spec_' + cc.value_id)) {
            check = true;
            show = true;
            return;
          }
          show = true;
        }
      });
    } else if (this.props.type === 'notify') {
      _.each(this.props.spec_settings.device_ids, (v, k) => {
        if (v.split('_')[1] == item.index) {
          check = true;
          show = true;
          return;
        }
      });
      show = true;
    } else {
      var conditions = [];
      if (item.index == -999) {
        show = false;
        return;
      }
      if (item.dv_type == 'camera') {
        show = false;
        return;
      }

      _.each(this.props.spec_settings.conditions, (v, key) => {
        conditions.push('spec_' + v.value_id);
      });
      _.each(item.cc_specs, (cc, index) => {
        if (_.includes(conditions, 'spec_' + cc.value_id)) {
          this.props.spec_settings.select_sn = item.sn_id;
          check = true;
          show = true;
          return;
        }
      });
      show = true;
    }
    if (show) {
      return (
        <TouchableOpacity
          testID="selectDeviceItem"
          activeOpacity={1.0}
          style={{
            backgroundColor: Tme('cardColor'),
            flex: 1,
            flexDirection: 'row',
            borderBottomWidth: 1,
            borderBottomColor: Tme('bgColor'),
          }}
          onPress={this.deviceShow.bind(this, item)}>
          <View
            style={{
              flex: 1,
              paddingHorizontal: 20,
            }}>
            <View
              style={{
                justifyContent: 'space-between',
                alignItems: 'center',
                flexDirection: 'row',
                paddingVertical: 20,
              }}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Text style={{fontSize: 16, color: Tme('cardTextColor')}}>
                  {item.display_name}
                </Text>
                {check ? (
                  <Icons
                    name="checkmark-circle-outline"
                    size={20}
                    color={Colors.MainColor}
                    style={{marginLeft: 8}}
                  />
                ) : null}
              </View>
              <View>
                <Icons
                  name="arrow-forward"
                  size={24}
                  color={Tme('textColor')}
                />
              </View>
            </View>
          </View>
        </TouchableOpacity>
      );
    } else {
      return null;
    }
  }

  deviceShow(rowData) {
    var push = 'true';
    if (this.props.type == 'target') {
      var spec_type = _.find(rowData.cc_specs, function (cc) {
        return cc.spec_type == 2;
      });
      if (!spec_type) {
        push = 'false';
        return;
      }
      _.forEach(rowData.cc_specs, (v, k) => {
        // if (this.props.product === "scene" && v.name == "Camera") {
        //   push = "false"
        //   return
        // }
        if (this.props.product == 'action' && v.device_id == -999) {
          push = 'false';
          return;
        }
      });
    } else if (this.props.type == 'condition') {
      if (this.props.spec_settings.conditions.length > 0) {
        const settingConditions = this.props.spec_settings.conditions;
        _.forEach(settingConditions, (c, i) => {
          if (c.sn_id && rowData.sn_id) {
            if (c.sn_id !== rowData.sn_id) {
              AlertModal.alert(I18n.t('automation.select_two_controller'));
              push = false;
              return;
            }
          }
        });
      }
      _.forEach(rowData.cc_specs, (v, k) => {
        if (v.name == 'Battery' && v.battery_type == 'Main Powered') {
          push = 'false';
          return;
        }

        if (v.name == 'Camera') {
          push = 'false';
          return;
        }
      });
    }
    if (push === 'true') {
      if (this.props.type == 'notify') {
        this.props.navigation.push('DeviceSpec', {
          device: toJS(rowData),
          spec_settings: this.props.spec_settings,
        });
      } else {
        this.props.navigation.push('DeviceSpecSetting', {
          device: toJS(rowData),
          type: this.props.type,
          spec_settings: this.props.spec_settings,
        });
      }
    } else if (push === 'false') {
      Toast.show(I18n.t('global.device_not_support'));
    }
  }
}
export default SelectDevice;
