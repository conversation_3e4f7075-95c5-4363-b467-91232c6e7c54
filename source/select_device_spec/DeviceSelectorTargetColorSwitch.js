import React from 'react';
import { Text, View, TouchableOpacity, Image } from 'react-native';
import { Helper } from '../Helper';
import I18n from '../I18n';
import { observer } from 'mobx-react/native';
import CheckBox from '../check_box/index';
import { Tme } from '../ThemeStyle';
import { observable } from 'mobx';
import tinycolor from 'tinycolor2';
import PubSub from 'pubsub-js';
import { DelayInputModal } from '../share/DelayInputModal';
import { Shadow } from 'react-native-shadow-2';

class Tool {
  @observable color = tinycolor('#fffff').toHsl();
}
import DeviceControl from '../DeviceControl';
import { colorSwitchCmdParams } from '../Tools';
import { SELECT_COLOR_DONE } from '../types/PubSubEvent';

@observer
class DeviceSelectorTargetColorSwitch extends React.Component {
  constructor(props) {
    super(props);

    this.tool = new Tool();
    var r = 255;
    var g = 255;
    var b = 255;
    if (this.props.target.spec_value !== '') {
      var rgb = this.props.target.spec_value.split('|');
      if (rgb.length == 3) {
        r = rgb[0];
        g = rgb[1];
        b = rgb[2];
      }
    }
    this.state = {
      r: r,
      g: g,
      b: b,
      delay: this.props.target.delay || '0',
      checked: this.props.target.checked,
    };

    this.color = tinycolor('rgb(' + r + ',' + g + ',' + b + ')').toHsl();
  }

  componentDidMount() {
    PubSub.subscribe(SELECT_COLOR_DONE, (msg, data) => {
      this.save(data);
    });
  }

  componentWillUnmount() {
    PubSub.unsubscribe(SELECT_COLOR_DONE);
  }

  render() {
    return (
      <View>
        <View
          style={{
            backgroundColor: Tme('cardColor'),
          }}>
          <View style={{ padding: 16 }}>
            <View
              testID="selectDeviceItemRow"
              style={{ marginBottom: 20, marginTop: 16 }}>
              <CheckBox
                rightTextStyle={{
                  fontWeight: '300',
                  fontSize: 16,
                  color: Tme('cardTextColor'),
                }}
                onClick={this.onClick.bind(this)}
                rightText={Helper.i(this.props.spec.name)}
                isChecked={this.state.checked}
                checkedImage={
                  <Image
                    source={require('../../img/checkbox-checked.png')}
                    style={{ width: 17, height: 17 }}
                  />
                }
                unCheckedImage={
                  <Image
                    source={require('../../img/Checkbox.png')}
                    style={{ width: 17, height: 17 }}
                  />
                }
              />
            </View>
            <View
              style={{
                paddingTop: 40,
                paddingBottom: 20,
                paddingHorizontal: 20,
              }}>
              <Shadow
                style={{ alignSelf: 'stretch' }}
                startColor={tinycolor(
                  'rgba(' +
                  this.state.r +
                  ',' +
                  this.state.g +
                  ',' +
                  this.state.b +
                  ', 0.1' +
                  ')',
                ).toHex8String()}>
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={this.settingColor.bind(this)}
                  style={[
                    {
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      paddingVertical: 12,
                      backgroundColor: Tme('cardColor'),
                      borderRadius: 8,
                    },
                  ]}>
                  <Text
                    style={{
                      color: Tme('cardTextColor'),
                      fontSize: 17,
                      fontWeight: '500',
                    }}>
                    {I18n.t('device.change_color')}
                  </Text>
                </TouchableOpacity>
              </Shadow>
            </View>
            <DelayInputModal
              delay={this.state.delay}
              navigation={this.props.navigation}
              changeDelay={this.changeDelay.bind(this)}
            />
          </View>
        </View>
      </View>
    );
  }

  changeDelay(e) {
    this.setState(
      {
        delay: e,
      },
      () => {
        this.save();
      },
    );
  }

  settingColor() {
    this.tool.color = tinycolor(
      'rgb(' + this.state.r + ',' + this.state.g + ',' + this.state.b + ')',
    ).toHsl();
    this.props.navigation.push('SelectColorDrawer', {
      from: 'device',
      r: this.state.r,
      g: this.state.g,
      b: this.state.b,
    });
  }

  onClick() {
    if (this.state.checked == true) {
      this.setState(
        {
          checked: false,
        },
        () => {
          this.props.target.checked = false;
        },
      );
    } else {
      this.setState(
        {
          checked: true,
        },
        () => {
          this.save();
        },
      );
    }
  }

  save(color) {
    var value = this.state.r + '|' + this.state.g + '|' + this.state.b;
    var param = colorSwitchCmdParams(
      this.props.spec.value_hash,
      [this.state.r, this.state.g, this.state.b],
      this.props.spec.dv_type,
    );
    var deviceControl = new DeviceControl({
      spec: this.props.spec,
      color: [this.state.r, this.state.g, this.state.b],
    });
    if (color) {
      var rgb = color.split(',');
      this.setState({
        r: rgb[0],
        g: rgb[1],
        b: rgb[2],
        checked: true,
      });

      value = rgb[0] + '|' + rgb[1] + '|' + rgb[2];
      param = colorSwitchCmdParams(
        this.props.spec.value_hash,
        rgb,
        this.props.spec.dv_type,
      );
      const { device } = this.props;
      deviceControl = new DeviceControl({
        sn_id: device.sn_id,
        home_id: device.home_id,
        sn: device.sn,
        spec: this.props.spec,
        color: rgb,
      });
    }
    deviceControl.colorSwitch(cmd => {
      this.props.target.commands = cmd;
    });
    this.props.target.sn_id = this.props.device.sn_id;
    this.props.target.home_id = this.props.device.home_id;
    this.props.target.sn = this.props.device.sn;
    this.props.target.device_uuid = this.props.device.uuid;
    this.props.target.node_id = this.props.spec.device_id;
    this.props.target.value_id = this.props.spec.value_id;
    this.props.target.instance_id = this.props.spec.instance_id;
    this.props.target.spec_cc = this.props.spec.cc;
    this.props.target.spec_name = this.props.spec.name;
    this.props.target.spec_value = value;
    this.props.target.params = param;
    this.props.target.desp = param;
    this.props.target.app_url = this.props.spec.app_url;
    this.props.target.delay = this.state.delay;
    this.props.target.checked = true;
    this.props.target.target_type = this.props.spec.dv_type;
    this.props.spec_settings.targets.push(this.props.target);
  }
}
export default DeviceSelectorTargetColorSwitch;
