/* eslint-disable react/no-unstable-nested-components */
import React from 'react';
import { Text, View, ScrollView } from 'react-native';
import { observer } from 'mobx-react/native';
import { DeviceCondition } from '../models/DeviceCondition';
import { DeviceTarget } from '../models/DeviceTarget';
import DeviceSelectorConditionCommon from './DeviceSelectorConditionCommon';
import DeviceSelectorTargetCamera from './DeviceSelectorTargetCamera';
import DeviceSelectorTargetDimmer from './DeviceSelectorTargetDimmer';
import DeviceSelectorTargetDoorLock from './DeviceSelectorTargetDoorLock';
import DeviceSelectorTargetSwitch from './DeviceSelectorTargetSwitch';
import DeviceSelectorTargetSwitchAll from './DeviceSelectorTargetSwitchAll';
import DeviceSelectorTargetThermostatMode from './DeviceSelectorTargetThermostatMode';
import DeviceSelectorTargetThermostatSetpoint from './DeviceSelectorTargetThermostatSetpoint';
import DeviceSelectorTargetVirtualDevice from './DeviceSelectorTargetVirtualDevice';
import DeviceSelectorTargetColorSwitch from './DeviceSelectorTargetColorSwitch';
import DeviceSelectorTargetWindowCovering from './DeviceSelectorTargetWindowCovering';
import DeviceSelectorTargetColorTemperature from './DeviceSelectorTargetColorTemperature';
import DeviceSelectorTargetOther from './DeviceSelectorTargetOther';
import _ from 'lodash';
import DeviceType from '../device_spec/DeviceType';
import I18n from '../I18n';
import { observable } from 'mobx';
import { NotificationCenter, EVENT_SCENE_KEY } from '../NotificationCenter';
import { Tme, Colors } from '../ThemeStyle';
import NavBarView from '../share/NavBarView';
import ShadowView from '../share/ShadowView';
import { mainRadius } from '../Tools';
import HeaderRightBtn from '../share/HeaderRightBtn';

class viewSpec {
  @observable targets = [];
  @observable conditions = [];
  @observable setpoint_type = '';
}

@observer
class DeviceSpecSetting extends React.Component {
  constructor(props) {
    super(props);

    this.view_spec = new viewSpec();
    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this.save();
    this.props.navigation.goBack();
  }

  render() {
    var that = this;
    var specRows = [];

    if (that.props.route.params.type === 'condition') {
      var c;
      _.each(that.props.route.params.device.cc_specs, function (spec, k) {
        var condition = new DeviceCondition({ new: true });
        _.each(that.props.route.params.spec_settings.conditions, (v, key) => {
          if (spec.value_id == v.value_id) {
            condition = v;
          }
        });
        that.view_spec.conditions.push(condition);
        c = that.genConditionSpec(
          spec,
          k,
          that.props.route.params.device,
          condition,
        );
        if (c) {
          specRows.push(c);
        }
      });
    } else if (that.props.route.params.type == 'target') {
      _.each(that.props.route.params.device.cc_specs, function (spec, k) {
        var target = new DeviceTarget({
          new: true,
        });
        _.each(that.props.route.params.spec_settings.targets, (v, key) => {
          if (spec.value_id == v.value_id) {
            target = v;
          }
        });

        that.view_spec.targets.push(target);
        c = that.genTargetSpec(
          spec,
          k,
          that.props.route.params.spec_settings,
          target,
        );
        if (c) {
          specRows.push(c);
        }
      });
    }
    return (
      <NavBarView>
        <ScrollView
          showsVerticalScrollIndicator={false}
          ref={component => (this._scrollView = component)}
          style={{
            flex: 1,
            backgroundColor: Tme('bgColor'),
          }}>
          <View style={{ padding: 20 }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: 16,
              }}>
              <Text
                style={[
                  { color: Tme('cardTextColor') },
                  Colors.RoomTitleFontStyle,
                ]}>
                {that.props.route.params.device
                  ? that.props.route.params.device.display_name
                  : null}
              </Text>
            </View>
            <DeviceType device={this.props.route.params.device} />
          </View>
          {specRows}
        </ScrollView>
      </NavBarView>
    );
  }

  save() {
    var data = [];
    if (this.props.route.params.type === 'condition') {
      var checked = [];
      data = this.props.route.params.spec_settings.conditions.toJS();
      _.forEach(_.uniq(this.view_spec.conditions), function (v, k) {
        if (v.node_id != '') {
          checked.push(v);
        }
      });

      if (checked.length > 0) {
        _.remove(data, function (t) {
          return t.node_id == checked[0].node_id;
        });
      }
      var conditions = _.groupBy(checked, 'checked').true;

      _.forEach(conditions, (v, k) => {
        if (_.indexOf(data, v) == -1 && v.checked) {
          this.props.route.params.spec_settings.select_sn = v.sn_id;
          data.push(v);
        }
      });
    } else {
      var checked = [];
      data = this.props.route.params.spec_settings.targets.toJS();
      _.forEach(_.uniq(this.view_spec.targets), function (v, k) {
        if (v.node_id != '') {
          checked.push(v);
        }
      });
      if (checked.length > 0) {
        _.remove(data, function (t) {
          return t.node_id == checked[0].node_id;
        });
      }
      var targets = _.groupBy(checked, 'checked').true;
      _.forEach(targets, (v, k) => {
        if (_.indexOf(data, v) == -1 && v.checked) {
          data.push(v);
        }
      });
    }
    // this.props.spec_settings.add_edit_targets(data)
    NotificationCenter.dispatchEvent(EVENT_SCENE_KEY, {
      type: this.props.route.params.type,
      data: data,
    });
  }

  genConditionSpec(spec, k, device, cond) {
    if (spec.name == 'Battery' && spec.battery_type == 'Main Powered') {
      return;
    }

    if (spec.name == 'Camera') {
      return;
    }
    if (spec.name == 'ColorSwitch') {
      return;
    }
    if (spec.name == 'ColorTemperature') {
      return;
    }
    if (spec.name == 'CoolingSetpoint') {
      return;
    }
    if (spec.name == 'HeatingSetpoint') {
      return;
    }
    if (spec.spec_type === 4) {
      return;
    }
    return (
      <View style={{ marginBottom: 20 }} key={'spec_' + spec.value_id}>
        <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
          <View
            style={{
              backgroundColor: Tme('cardColor'),
            }}>
            <DeviceSelectorConditionCommon
              device={device}
              spec={spec}
              navigation={this.props.navigation}
              condition={cond}
              spec_settings={this.view_spec}
            />
          </View>
        </ShadowView>
      </View>
    );
  }

  genTargetSpec(spec, k, spec_settings, target) {
    if (spec.spec_type === 1) {
      return;
    }
    if (spec.spec_type === 3) {
      return;
    }
    if (this.props.route.params.product == 'scene' && spec.name == 'Camera') {
      return;
    }
    if (
      this.props.route.params.product == 'action' &&
      (spec.name == 'Camera' || spec.device_id == '-999')
    ) {
      return;
    }
    if (spec.name === 'TemperatureUnit') {
      return;
    }
    var valueDOM = null;
    switch (spec.name) {
      case 'Switch':
        valueDOM = (
          <DeviceSelectorTargetSwitch
            device={this.props.route.params.device}
            spec_settings={this.view_spec}
            navigation={this.props.navigation}
            spec={spec}
            target={target}
          />
        );
        break;
      case 'SwitchAll':
        valueDOM = (
          <DeviceSelectorTargetSwitchAll
            device={this.props.route.params.device}
            spec_settings={this.view_spec}
            navigation={this.props.navigation}
            spec={spec}
            target={target}
          />
        );
        break;
      case 'Dimmer':
      case 'Motor':
        valueDOM = (
          <DeviceSelectorTargetDimmer
            device={this.props.route.params.device}
            spec_settings={this.view_spec}
            navigation={this.props.navigation}
            spec={spec}
            target={target}
          />
        );
        break;
      case 'ThermostatMode':
        valueDOM = (
          <DeviceSelectorTargetThermostatMode
            device={this.props.route.params.device}
            spec_settings={this.view_spec}
            spec={spec}
            navigation={this.props.navigation}
            target={target}
          />
        );
        break;
      case 'CoolingSetpoint':
        // if (
        //   this.view_spec.setpoint_type === 'Cool' ||
        //   this.view_spec.setpoint_type === 3
        // ) {
        valueDOM = (
          <DeviceSelectorTargetThermostatSetpoint
            key="cool_item"
            fromItem="Cool"
            device={this.props.route.params.device}
            spec_settings={this.view_spec}
            spec={spec}
            navigation={this.props.navigation}
            target={target}
          />
        );
        // }
        break;
      case 'HeatingSetpoint':
        // if (
        //   this.view_spec.setpoint_type === 'Heat' ||
        //   this.view_spec.setpoint_type === 4
        // ) {
        valueDOM = (
          <DeviceSelectorTargetThermostatSetpoint
            key="heat_item"
            fromItem="Heat"
            device={this.props.route.params.device}
            spec_settings={this.view_spec}
            navigation={this.props.navigation}
            spec={spec}
            target={target}
          />
        );
        // }
        break;
      case 'ThermostatSetpoint':
        valueDOM = (
          <DeviceSelectorTargetThermostatSetpoint
            key="setpoint_item"
            device={this.props.route.params.device}
            spec_settings={this.view_spec}
            navigation={this.props.navigation}
            spec={spec}
            target={target}
          />
        );
        break;
      case 'DoorLock':
        valueDOM = (
          <DeviceSelectorTargetDoorLock
            device={this.props.route.params.device}
            spec_settings={this.view_spec}
            navigation={this.props.navigation}
            spec={spec}
            target={target}
          />
        );
        break;
      case 'WindowCovering':
        valueDOM = (
          <DeviceSelectorTargetWindowCovering
            device={this.props.route.params.device}
            spec_settings={this.view_spec}
            navigation={this.props.navigation}
            spec={spec}
            target={target}
          />
        );
        break;
      case 'ColorSwitch':
        var temp = [];
        if (spec.dv_type == 'tuya') {
          valueDOM = (
            <DeviceSelectorTargetColorSwitch
              device={this.props.route.params.device}
              parent={this}
              spec_settings={this.view_spec}
              spec={spec}
              navigation={this.props.navigation}
              target={target}
            />
          );
        } else {
          // eslint-disable-next-line no-shadow
          _.each(spec.options, (v, k) => {
            temp.push(v.val);
          });
          if (_.intersection(temp, [2, 3, 4]).length == 3) {
            valueDOM = (
              <DeviceSelectorTargetColorSwitch
                device={this.props.route.params.device}
                parent={this}
                navigation={this.props.navigation}
                spec_settings={this.view_spec}
                spec={spec}
                target={target}
              />
            );
          } else {
            valueDOM = null;
          }
        }
        break;
      case 'ColorTemperature':
        valueDOM = (
          <DeviceSelectorTargetColorTemperature
            device={this.props.route.params.device}
            spec_settings={this.view_spec}
            navigation={this.props.navigation}
            spec={spec}
            target={target}
          />
        );
        break;
      case 'Camera':
        if (_.isEmpty(spec.screenshot_url)) {
          valueDOM = null;
        } else {
          valueDOM = (
            <DeviceSelectorTargetCamera
              device={this.props.route.params.device}
              spec_settings={this.view_spec}
              navigation={this.props.navigation}
              spec={spec}
              target={target}
            />
          );
        }
        break;
      default:
        if (spec.device_id == -999) {
          valueDOM = (
            <DeviceSelectorTargetVirtualDevice
              device={this.props.route.params.device}
              spec_settings={this.view_spec}
              navigation={this.props.navigation}
              spec={spec}
              target={target}
            />
          );
        } else if (spec.dv_type == 'tuya') {
          valueDOM = (
            <DeviceSelectorTargetOther
              device={this.props.route.params.device}
              spec_settings={this.view_spec}
              navigation={this.props.navigation}
              spec={spec}
              target={target}
            />
          );
        } else {
          valueDOM = (
            <View style={{ paddingHorizontal: 8 }}>
              <View
                style={{
                  backgroundColor: Tme('cardColor'),
                  borderRadius: 4,
                }}>
                <View style={{ padding: 10 }}>
                  <Text>N/A</Text>
                </View>
              </View>
            </View>
          );
          break;
        }
    }
    if (valueDOM) {
      return (
        <View style={{ marginBottom: 20 }} key={'spec_' + spec.value_id}>
          <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
            <View
              style={{
                backgroundColor: Tme('cardColor'),
                borderRadius: mainRadius(),
              }}>
              {valueDOM}
            </View>
          </ShadowView>
        </View>
      );
    }
    return null;
  }
}
export default DeviceSpecSetting;
