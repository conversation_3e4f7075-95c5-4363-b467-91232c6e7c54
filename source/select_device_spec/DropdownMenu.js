import React, {Component} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Animated,
  Easing,
  StyleSheet,
  Dimensions,
  Platform,
} from 'react-native';
import Icons from 'react-native-vector-icons/Ionicons';
import _ from 'lodash';
import {Tme, Colors} from '../ThemeStyle';
import I18n from '../I18n';

class DropdownMenu extends Component {
  constructor(props) {
    super(props);

    this.state = {
      value: '',
      label: '',
      sn: '',
      sn_id: '',
      activityIndex: -1,
      rotationAnims: new Animated.Value(0),
    };
  }

  componentDidMount() {
    const that = this;
    _.forEach(this.props.rooms, (option, index) => {
      if (option.value == that.props.value) {
        that.setState({
          value: option.value,
          label: option.label,
        });
      }
    });

    // if (that.sns.length > 1) {
    //   if (that.sns[0].sn !== 'ALL') {
    //     that.sns.unshift({sn: 'ALL', id: ''});
    //   }
    // }

    // _.forEach(this.sns, (sn, i) => {
    //   if (sn.id == that.props.sn_id) {
    //     that.setState({
    //       sn_id: sn.id,
    //       sn: sn.sn,
    //     });
    //   }
    // });
  }

  renderChcek(item) {
    let active = false;
    if (item.label) {
      active = item.value === this.state.value;
    } else if (item.sn) {
      active = item.id === this.state.sn_id;
    }

    if (active) {
      return (
        <View
          style={{
            flex: 1,
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 15,
            flexDirection: 'row',
          }}>
          <Text style={[styles.item_text_style, {color: Tme('cardTextColor')}]}>
            {item.sn ? item.sn : item.label}
          </Text>
          <Icons
            name="checkmark-circle-outline"
            size={18}
            color={Colors.MainColor}
            style={{marginLeft: 8}}
          />
        </View>
      );
    } else {
      return (
        <View
          style={{
            flex: 1,
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 15,
            flexDirection: 'row',
          }}>
          <Text style={[styles.item_text_style, {color: Tme('cardTextColor')}]}>
            {item.sn ? item.sn : item.label}
          </Text>
        </View>
      );
    }
  }

  renderActivityPanel() {
    if (this.state.activityIndex !== -1) {
      var currentTitles = [];
      if (this.state.activityIndex === 'sn') {
        // currentTitles = this.sns;
      } else if (this.state.activityIndex === 'room') {
        if (this.props.rooms.length > 0) {
          if (this.props.rooms[0].label !== 'ALL') {
            this.props.rooms.unshift({label: 'ALL', value: ''});
          }
        }
        currentTitles = this.props.rooms;
      }

      var heightStyle = {};
      if (
        this.props.maxHeight &&
        this.props.maxHeight < currentTitles.length * 44
      ) {
        heightStyle.height = this.props.maxHeight;
      }
      return (
        <View
          style={[
            {
              position: 'absolute',
              left: 0,
              right: 0,
              top: 41,
              bottom: 0,
              height: Dimensions.get('window').height,
            },
          ]}>
          <TouchableOpacity
            onPress={() => this.closePanel()}
            activeOpacity={1}
            style={{
              position: 'absolute',
              left: 0,
              right: 0,
              top: 0,
              bottom: 0,
            }}>
            <View
              style={{opacity: 0.8, backgroundColor: Tme('bgColor'), flex: 1}}
            />
          </TouchableOpacity>
          <ScrollView
            style={[
              {
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                backgroundColor: Tme('cardColor'),
              },
              heightStyle,
            ]}>
            {currentTitles.map((item, index) => (
              <TouchableOpacity
                key={index}
                activeOpacity={1}
                style={{flex: 1, height: 44}}
                onPress={this.itemOnPress.bind(this, item)}>
                {this.renderChcek(item)}
                <View
                  style={{
                    backgroundColor: Tme('inputBorderColor'),
                    height: 1,
                    marginLeft: 15,
                  }}
                />
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      );
    } else {
      return null;
    }
  }

  openPanel(index) {
    if (index === this.state.activityIndex) {
      this.closePanel();
    } else {
      this.setState(
        {
          activityIndex: index,
        },
        () => {
          Animated.timing(this.state.rotationAnims, {
            toValue: 0.5,
            duration: 300,
            useNativeDriver: true,
            easing: Easing.linear,
          }).start();
        },
      );
    }
  }

  closePanel() {
    this.setState({activityIndex: -1}, () => {
      Animated.timing(this.state.rotationAnims, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
        easing: Easing.linear,
      }).start();
    });
  }

  itemOnPress(item) {
    if (this.state.activityIndex !== -1) {
      if (this.props.onChange) {
        if (item.sn) {
          this.setState(
            {
              sn: item.sn,
              sn_id: item.id,
            },
            () => {
              this.props.onChange(item.id, 'sn');
            },
          );
        } else if (item.label) {
          this.setState(
            {
              value: item.value,
              label: item.label,
            },
            () => {
              this.props.onChange(item.value, 'room');
            },
          );
        }
      }
    }
    this.closePanel();
  }

  renderDropDownArrow(index) {
    if (index === this.state.activityIndex) {
      return (
        <Icons
          name="chevron-up"
          size={16}
          color={Tme('textColor')}
          style={{marginLeft: 6}}
        />
      );
    } else {
      return (
        <Icons
          name="chevron-down"
          size={16}
          color={Tme('textColor')}
          style={{marginLeft: 6}}
        />
      );
    }
  }

  render() {
    return (
      <View style={{zIndex: 2, elevation: Platform.OS === 'android' ? 50 : 0}}>
        <View
          style={{
            flexDirection: 'row',
            borderBottomWidth: 1,
            borderBottomColor: Tme('inputBorderColor'),
          }}>
          <TouchableOpacity
            activeOpacity={1}
            onPress={this.openPanel.bind(this, 'room')}
            style={{
              height: 40,
              flex: 1,
              justifyContent: 'center',
              paddingHorizontal: 20,
            }}>
            <View
              style={{
                flex: 1,
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <Text style={[styles.title_style, {color: Tme('cardTextColor')}]}>
                {this.state.label == ''
                  ? I18n.t('room.filter_room')
                  : this.state.label !== 'ALL'
                  ? this.state.label
                  : I18n.t('room.filter_room')}
              </Text>
              {this.renderDropDownArrow('room')}
            </View>
          </TouchableOpacity>
          {/* {this.sns.length > 1 && (
            <TouchableOpacity
              activeOpacity={1}
              onPress={this.openPanel.bind(this, 'sn')}
              style={{
                flex: 1,
                height: 40,
                paddingHorizontal: 20,
                borderLeftWidth: 1,
                borderLeftColor: Tme('inputBorderColor'),
                backgroundColor: Tme('cardColor'),
                justifyContent: 'center',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}>
                <Text
                  style={[styles.title_style, {color: Tme('cardTextColor')}]}>
                  {this.state.sn == ''
                    ? I18n.t('room.filter_controller')
                    : this.state.sn !== 'ALL'
                    ? this.state.sn
                    : I18n.t('room.filter_controller')}
                </Text>
                {this.renderDropDownArrow('sn')}
              </View>
            </TouchableOpacity>
          )} */}
        </View>
        {this.renderActivityPanel()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  title_style: {
    fontSize: 14,
  },
  item_text_style: {
    fontSize: 14,
    marginLeft: 20,
  },
});

export default DropdownMenu;
