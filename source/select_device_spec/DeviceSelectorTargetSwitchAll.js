import React from 'react';
import {View, Image} from 'react-native';
import {Helper} from '../Helper';
import I18n from '../I18n';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import CheckBox from '../check_box/index';
import {Tme, Colors} from '../ThemeStyle';
import DeviceControl from '../DeviceControl';
import {DelayInputModal} from '../share/DelayInputModal';

export default class DeviceSelectorTargetSwitchAll extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isOn: this.props.spec.params == 'SetOn()' ? 1 : 0,
      delay: this.props.target.delay || '0',
      checked: this.props.target.checked,
    };
  }

  render() {
    return (
      <View style={{paddingHorizontal: 8}}>
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            borderRadius: 4,
          }}>
          <View style={{padding: 10}}>
            <View
              testID="selectDeviceItemRow"
              style={{marginBottom: 20, marginTop: 16}}>
              <CheckBox
                rightTextStyle={{
                  fontWeight: '300',
                  fontSize: 16,
                  color: Tme('cardTextColor'),
                }}
                onClick={() => this.onClick()}
                rightText={Helper.i(this.props.spec.name)}
                isChecked={this.state.checked}
                checkedImage={
                  <Image
                    source={require('../../img/checkbox-checked.png')}
                    style={{width: 17, height: 17}}
                  />
                }
                unCheckedImage={
                  <Image
                    source={require('../../img/Checkbox.png')}
                    style={{width: 17, height: 17}}
                  />
                }
              />
            </View>
            <View style={{height: 40}}>
              <SegmentedControl
                tintColor={Colors.MainColor}
                values={[I18n.t('spec.off_b'), I18n.t('spec.on_b')]}
                selectedIndex={this.state.isOn}
                fontStyle={{color: Tme('cardTextColor'), fontSize: 16}}
                activeFontStyle={{color: '#ffffff', fontSize: 16}}
                style={{flex: 1, height: 42}}
                onChange={this.switchToggle.bind(this)}
              />
            </View>
            <DelayInputModal
              delay={this.state.delay}
              navigation={this.props.navigation}
              changeDelay={this.changeDelay.bind(this)}
            />
          </View>
        </View>
      </View>
    );
  }

  changeDelay(e) {
    this.setState(
      {
        delay: e,
      },
      () => {
        this.save();
      },
    );
  }

  onClick() {
    if (this.state.checked == true) {
      this.setState(
        {
          checked: false,
        },
        () => {
          this.props.target.checked = false;
        },
      );
    } else {
      this.save();
    }
  }

  switchToggle(e) {
    var value = e.nativeEvent.selectedSegmentIndex;
    this.setState(
      {
        isOn: value,
      },
      () => {
        this.save();
      },
    );
  }

  save() {
    var value = this.state.value;
    var param, status;
    if (value == 0) {
      param = 'SetOff()';
      status = 'Off';
    } else {
      // eslint-disable-next-line no-unused-vars
      param = 'SetOn()';
      status = 'On';
    }

    this.setState({
      checked: true,
    });
    this.props.target.desp = status;

    this.props.target.spec_value = status;
    this.props.target.params = status == 'On' ? 255 : 0;

    const {device} = this.props;
    var deviceControl = new DeviceControl({
      spec: this.props.spec,
      param: status == 'On' ? 255 : 0,
      sn_id: device.sn_id,
      home_id: device.home_id,
      sn: device.sn,
    });
    deviceControl.switch(cmd => {
      this.props.target.commands = cmd;
    });
    this.props.target.sn_id = this.props.device.sn_id;
    this.props.target.home_id = this.props.device.home_id;
    this.props.target.sn = this.props.device.sn;
    this.props.target.device_uuid = this.props.device.uuid;
    this.props.target.node_id = this.props.spec.device_id;
    this.props.target.value_id = this.props.spec.value_id;
    this.props.target.instance_id = this.props.spec.instance_id;
    this.props.target.spec_cc = this.props.spec.cc;
    this.props.target.spec_name = this.props.spec.name;
    this.props.target.delay = this.state.delay;
    this.props.target.checked = true;
    this.props.target.target_type = this.props.spec.dv_type;
    this.props.spec_settings.targets.push(this.props.target);
  }
}
