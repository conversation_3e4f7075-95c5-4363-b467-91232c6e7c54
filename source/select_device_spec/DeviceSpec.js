import React from 'react';
import {Text, View, Image, ScrollView} from 'react-native';
import {observer} from 'mobx-react/native';
import _ from 'lodash';
import DeviceType from '../device_spec/DeviceType';
import {Helper} from '../Helper';
import I18n from '../I18n';
import {NotificationCenter, EVENT_SCENE_KEY} from '../NotificationCenter';
import CheckBox from '../check_box/index';

import {Tme} from '../ThemeStyle';
import NavBarView from '../share/NavBarView';
import HeaderRightBtn from '../share/HeaderRightBtn';
// const Tool = observable({
//   device_ids: [],
//   check_ids: [],
//   delete_ids: [],
// })

@observer
class DeviceSpec extends React.Component {
  static options(passProps) {
    return {
      topBar: {
        rightButtons: [
          {
            id: 'save',
            text: I18n.t('home.next'),
          },
        ],
      },
    };
  }

  constructor(props) {
    super(props);
    this.device_ids = this.props.route.params.spec_settings.device_ids;

    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  componentWillUnmount() {}

  rightClick() {
    NotificationCenter.dispatchEvent(EVENT_SCENE_KEY, {
      device_ids: this.device_ids,
    });
    setTimeout(() => {
      this.props.navigation.goBack();
    }, 500);
  }

  render() {
    var that = this;
    var specRows = [];
    _.each(that.props.route.params.device.cc_specs, function (spec, k) {
      var c = that.genSpec(spec, k);
      if (c) {
        specRows.push(c);
      }
    });

    return (
      <NavBarView>
        <ScrollView
          showsVerticalScrollIndicator={false}
          ref={component => (this._scrollView = component)}
          style={{
            flex: 1,
            backgroundColor: Tme('bgColor'),
          }}>
          <View
            testID="selectDeviceView"
            style={{
              height: 150,
            }}>
            <View style={{padding: 20}}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginBottom: 16,
                }}>
                <Text style={{fontSize: 16, color: 'white', fontWeight: '600'}}>
                  {this.props.route.params.device.display_name}
                </Text>
              </View>
              <DeviceType device={this.props.route.params.device} />
            </View>
          </View>

          <View
            style={{
              backgroundColor: Tme('bgColor'),
            }}>
            <View style={{paddingHorizontal: 10, paddingVertical: 10}}>
              {specRows}
            </View>
          </View>
        </ScrollView>
      </NavBarView>
    );
  }

  genSpec(spec, k) {
    var sn_id = this.props.route.params.device.sn_id;
    var value = 'spec_' + spec.value_id + '_' + sn_id;
    var that = this;
    var length = this.props.route.params.device.cc_specs.length - 1;
    return (
      <CheckBox
        testID="selectDeviceItemCheckBox"
        key={k}
        rightTextStyle={{fontSize: 17, padding: 5, color: Tme('cardTextColor')}}
        style={[
          k == length
            ? {}
            : {
                borderBottomColor: Tme('inputBorderColor'),
                borderBottomWidth: 1,
              },
          {paddingTop: 8, paddingBottom: 8},
        ]}
        onClick={() => that.onClick(value)}
        rightText={Helper.i(spec.name)}
        isChecked={_.includes(
          that.props.route.params.spec_settings.device_ids,
          value,
        )}
        checkedImage={
          <Image
            source={require('../../img/checkbox-checked.png')}
            style={{width: 18, height: 18, marginLeft: 20}}
          />
        }
        unCheckedImage={
          <Image
            source={require('../../img/Checkbox.png')}
            style={{width: 18, height: 18, marginLeft: 20}}
          />
        }
      />
    );
  }

  onClick(ids) {
    if (_.includes(this.props.route.params.spec_settings.device_ids, ids)) {
      this.device_ids.remove(ids);
    } else {
      this.device_ids.push(ids);
    }

    // this.props.spec_settings.add_edit_targets(ids)
  }
}

export default DeviceSpec;
