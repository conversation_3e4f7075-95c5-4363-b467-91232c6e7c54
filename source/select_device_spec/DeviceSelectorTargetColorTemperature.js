/* eslint-disable radix */
import React from 'react';
import {Text, View, Image} from 'react-native';
import {observer} from 'mobx-react/native';
import {Helper} from '../Helper';
import Slider from '@react-native-community/slider';
import CheckBox from '../check_box/index';
import {Tme, Colors} from '../ThemeStyle';
import DeviceControl from '../DeviceControl';
import {DelayInputModal} from '../share/DelayInputModal';

@observer
class DeviceSelectorTargetColorTemperature extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: 0,
      initialValue: 0,
      delay: this.props.target.delay || '0',
      delay_value: this.props.target.delay || '0',
      modalVisible: false,
      checked: this.props.target.checked,
      runParam: 0,
      min: this.props.spec.min,
      max: this.props.spec.max,
    };
  }

  componentDidMount() {
    var params = parseInt(this.props.target.params);

    var value, initialValue, runParam;
    if (this.props.spec.dv_type == 'zigbee') {
      if (isNaN(params)) {
        value = 1;
        initialValue = 0;
      } else {
        initialValue = parseInt(params / 10);
        runParam = params;
      }
    } else {
      value = initialValue = runParam = this.props.spec.value;
    }
    this.setState({
      value: value,
      initialValue: initialValue,
      runParam: runParam,
    });
  }
  render() {
    return (
      <View style={{paddingHorizontal: 8}}>
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            borderRadius: 4,
          }}>
          <View style={{padding: 10}}>
            <View
              testID="selectDeviceItemRow"
              style={{marginBottom: 20, marginTop: 16}}>
              <CheckBox
                rightTextStyle={{
                  fontWeight: '300',
                  fontSize: 16,
                  color: Tme('cardTextColor'),
                }}
                onClick={() => this.onClick()}
                rightText={Helper.i(this.props.spec.name)}
                isChecked={this.state.checked}
                checkedImage={
                  <Image
                    source={require('../../img/checkbox-checked.png')}
                    style={{width: 17, height: 17}}
                  />
                }
                unCheckedImage={
                  <Image
                    source={require('../../img/Checkbox.png')}
                    style={{width: 17, height: 17}}
                  />
                }
              />
            </View>
            <View style={{marginTop: 8}}>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 8,
                  marginTop: 20,
                }}>
                <Text
                  style={{
                    fontSize: 22,
                    fontWeight: '500',
                    color: Tme('cardTextColor'),
                  }}>
                  {this.state.initialValue}
                </Text>
              </View>
              <Slider
                minimumValue={Number(this.state.min)}
                maximumValue={
                  Number(this.state.max) > 0 ? Number(this.state.max) : 99
                }
                step={1}
                minimumTrackTintColor={Colors.MainColor}
                value={this.state.initialValue}
                onChange={this.slider_change.bind(this, 'change')}
                onSlidingComplete={this.slider_change.bind(this, 'after')}
              />
              <View
                style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                <Text style={{fontWeight: '500', color: Tme('cardTextColor')}}>
                  {this.state.min}
                </Text>
                <Text style={{fontWeight: '500', color: Tme('cardTextColor')}}>
                  {Number(this.state.max) > 0 ? Number(this.state.max) : 99}
                </Text>
              </View>
            </View>
            <DelayInputModal
              delay={this.state.delay}
              navigation={this.props.navigation}
              changeDelay={this.changeDelay.bind(this)}
            />
          </View>
        </View>
      </View>
    );
  }
  onClick() {
    if (this.state.checked == true) {
      this.setState(
        {
          checked: false,
        },
        () => {
          this.props.target.checked = false;
        },
      );
    } else {
      this.save();
    }
  }
  changeDelay(e) {
    this.setState(
      {
        delay: e,
      },
      () => {
        this.save();
      },
    );
  }

  slider_change(type, e) {
    var param = this.parseParam(e);
    this.setState(
      {
        value: e,
        initialValue: e,
        runParam: param,
      },
      () => {
        if (type == 'after') {
          this.save();
        }
      },
    );
  }
  parseParam(param) {
    if (this.props.spec.dv_type == 'zigbee') {
      return parseInt(param * 10);
    } else {
      return param;
    }
  }
  save() {
    this.setState({
      checked: true,
    });

    const {device} = this.props;
    var deviceControl = new DeviceControl({
      spec: this.props.spec,
      param: this.state.runParam,
      sn_id: device.sn_id,
      home_id: device.home_id,
      sn: device.sn,
    });

    deviceControl.color_Temperature(cmd => {
      this.props.target.commands = cmd;
    });
    this.props.target.sn_id = this.props.device.sn_id;
    this.props.target.home_id = this.props.device.home_id;
    this.props.target.sn = this.props.device.sn;
    this.props.target.device_uuid = this.props.device.uuid;
    this.props.target.node_id = this.props.spec.device_id;
    this.props.target.value_id = this.props.spec.value_id;
    this.props.target.instance_id = this.props.spec.instance_id;
    this.props.target.spec_cc = this.props.spec.cc;
    this.props.target.spec_name = this.props.spec.name;
    this.props.target.spec_value = this.state.runParam;
    this.props.target.params = this.state.runParam;
    this.props.target.desp = this.state.runParam;
    this.props.target.app_url = this.props.spec.app_url;
    this.props.target.delay = this.state.delay;
    this.props.target.checked = true;
    this.props.target.target_type = this.props.spec.dv_type;
    this.props.spec_settings.targets.push(this.props.target);
  }
}
export default DeviceSelectorTargetColorTemperature;
