import {useState, useEffect} from 'react';
import {Animated, Easing} from 'react-native';

interface SnackbarProps {
  // 可以添加需要的 props
}

const Snackbar: React.FC<SnackbarProps> = () => {
  const [fadeInOpacity] = useState(new Animated.Value(0));

  useEffect(() => {
    Animated.timing(fadeInOpacity, {
      toValue: 1,
      easing: Easing.linear,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, [fadeInOpacity]);

  return null;
};

export const SnackbarShow = (): void => {
  // 实现显示逻辑
};

export const SnackbarHide = (): void => {
  // 实现隐藏逻辑
};

export default Snackbar;
