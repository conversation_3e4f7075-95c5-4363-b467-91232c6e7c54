import React from 'react';
import { View, ActivityIndicator, ViewStyle } from 'react-native';
import { mainRadius } from '../Tools';

interface LoadingViewProps {
  visible: boolean;
}

// 使用React.memo优化Loading组件，避免不必要的重新渲染
const LoadingView = React.memo(function Loading({ visible }: LoadingViewProps) {
  if (!visible) {
    return null;
  }

  const containerStyle: ViewStyle = {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    backgroundColor: 'transparent',
    zIndex: 1999,
  };

  const innerContainerStyle: ViewStyle[] = [
    {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'transparent',
    },
    { height: 89 },
  ];

  const loadingBoxStyle: ViewStyle = {
    alignItems: 'center',
    justifyContent: 'center',
    width: 89,
    height: 89,
    borderRadius: mainRadius(),
    backgroundColor: 'rgba(0,0,0,0.8)',
  };

  return (
    <View style={containerStyle}>
      <View style={innerContainerStyle}>
        <View style={loadingBoxStyle}>
          <ActivityIndicator color="#eeeeee" size="large" />
        </View>
      </View>
    </View>
  );
});

export default LoadingView;
