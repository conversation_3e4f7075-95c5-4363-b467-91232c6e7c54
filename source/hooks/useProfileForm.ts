import {useState, useCallback} from 'react';
import {Helper, HelperMemo} from '../Helper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {Toast} from '../Toast';
import AlertModal from '../share/AlertModal';
import {hideLoading, showLoading} from '../../ILoading';
import I18n from '../I18n';
import _ from 'lodash';
import {
  ProfileApiResponse,
  ValidationError,
  PASSWORD_REGEX,
  ChangeNameRequestBody,
  ChangePasswordRequestBody,
} from '../types/profile';

/**
 * 表单状态接口
 */
interface FormState {
  name: string;
  old_password: string;
  new_password: string;
  password_confirmation: string;
  cloud: boolean;
}

/**
 * Hook 配置选项
 */
interface UseProfileFormOptions {
  onSuccess?: () => void;
  onError?: (error: any) => void;
}

/**
 * 自定义 Hook 用于处理个人资料表单逻辑
 * 
 * 提供统一的表单状态管理、验证和 API 调用功能
 */
export const useProfileForm = (options: UseProfileFormOptions = {}) => {
  const [formData, setFormData] = useState<FormState>({
    name: HelperMemo.user_data?.user?.name || '',
    old_password: '',
    new_password: '',
    password_confirmation: '',
    cloud: HelperMemo.user_data?.network ? false : true,
  });

  const [isLoading, setIsLoading] = useState(false);

  // 更新表单字段
  const updateField = useCallback(<K extends keyof FormState>(
    field: K,
    value: FormState[K]
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  }, []);

  // 批量更新表单字段
  const updateFields = useCallback((updates: Partial<FormState>) => {
    setFormData(prev => ({
      ...prev,
      ...updates,
    }));
  }, []);

  // 重置表单
  const resetForm = useCallback(() => {
    setFormData({
      name: HelperMemo.user_data?.user?.name || '',
      old_password: '',
      new_password: '',
      password_confirmation: '',
      cloud: HelperMemo.user_data?.network ? false : true,
    });
  }, []);

  // 验证用户名
  const validateName = useCallback((name: string): ValidationError[] => {
    const errors: ValidationError[] = [];
    // 目前没有特殊的用户名验证要求
    // 可以根据需要添加验证规则
    return errors;
  }, []);

  // 验证密码表单
  const validatePasswordForm = useCallback((): ValidationError[] => {
    const errors: ValidationError[] = [];

    if (_.isEmpty(formData.old_password)) {
      errors.push(I18n.t('session.please_enter_password'));
    }

    if (_.isEmpty(formData.new_password)) {
      errors.push(I18n.t('session.new_input'));
    }

    if (!PASSWORD_REGEX.test(formData.new_password)) {
      errors.push(I18n.t('session.password_length'));
    }

    if (formData.new_password !== formData.password_confirmation) {
      errors.push(I18n.t('session.two_password'));
    }

    return errors;
  }, [formData]);

  // 通用的 API 成功处理
  const handleApiSuccess = useCallback((data: ProfileApiResponse) => {
    AsyncStorage.mergeItem(
      'user_data',
      JSON.stringify({user: data.user}),
      () => {
        HelperMemo.user_data.user = data.user;
      },
    );
    Toast.show();
    options.onSuccess?.();
  }, [options]);

  // 通用的 API 错误处理
  const handleApiError = useCallback((error: any) => {
    console.error('Profile API error:', error);
    options.onError?.(error);
  }, [options]);

  // 修改用户名
  const changeName = useCallback(async () => {
    const errors = validateName(formData.name);

    if (errors.length > 0) {
      AlertModal.alert(I18n.t('home.warning_message'), errors.join('\n'));
      return false;
    }

    const body: ChangeNameRequestBody = {
      name: formData.name.trim(),
    };

    try {
      setIsLoading(true);
      showLoading();

      return new Promise<boolean>((resolve) => {
        Helper.httpPOST(
          '/profiles/change_name',
          {
            ensure: () => {
              hideLoading();
              setIsLoading(false);
            },
            success: (data: ProfileApiResponse) => {
              handleApiSuccess(data);
              resolve(true);
            },
            error: (error: any) => {
              handleApiError(error);
              resolve(false);
            },
          },
          body,
        );
      });
    } catch (error) {
      hideLoading();
      setIsLoading(false);
      handleApiError(error);
      return false;
    }
  }, [formData.name, validateName, handleApiSuccess, handleApiError]);

  // 修改密码
  const changePassword = useCallback(async () => {
    const errors = validatePasswordForm();

    if (errors.length > 0) {
      AlertModal.alert(I18n.t('home.warning_message'), errors.join('\n'));
      return false;
    }

    const body: ChangePasswordRequestBody = {
      old: formData.old_password,
      new: formData.new_password,
      conf: formData.password_confirmation,
    };

    try {
      setIsLoading(true);
      showLoading();

      return new Promise<boolean>((resolve) => {
        Helper.httpPOST(
          '/profiles/change_password',
          {
            ensure: () => {
              hideLoading();
              setIsLoading(false);
            },
            success: (data: ProfileApiResponse) => {
              handleApiSuccess(data);
              resolve(true);
            },
            error: (error: any) => {
              handleApiError(error);
              resolve(false);
            },
          },
          body,
        );
      });
    } catch (error) {
      hideLoading();
      setIsLoading(false);
      handleApiError(error);
      return false;
    }
  }, [formData, validatePasswordForm, handleApiSuccess, handleApiError]);

  return {
    // 状态
    formData,
    isLoading,
    
    // 操作
    updateField,
    updateFields,
    resetForm,
    
    // 验证
    validateName,
    validatePasswordForm,
    
    // API 调用
    changeName,
    changePassword,
  };
};
