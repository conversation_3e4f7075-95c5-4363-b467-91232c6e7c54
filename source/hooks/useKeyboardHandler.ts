import {useRef, useCallback} from 'react';
import {TextInput} from 'react-native';
import {KeyboardHandlerRef} from '../KeyboardHandler';

/**
 * 键盘处理 Hook
 *
 * 提供统一的键盘处理逻辑，包括：
 * - KeyboardHandler 引用管理
 * - 聚焦处理
 * - 导航处理
 */
export const useKeyboardHandler = () => {
  const keyboardHandlerRef = useRef<KeyboardHandlerRef>(null);

  // 处理输入框聚焦
  const handleInputFocus = useCallback((inputRef: React.RefObject<TextInput | null>) => {
    return () => {
      keyboardHandlerRef.current?.inputFocused(inputRef.current);
    };
  }, []);

  // 创建导航到下一个输入框的处理函数
  const createNavigateToNext = useCallback((nextInputRef: React.RefObject<TextInput | null>) => {
    return () => {
      nextInputRef.current?.focus();
    };
  }, []);

  // 创建提交处理函数
  const createSubmitHandler = useCallback((onSubmit: () => void) => {
    return () => {
      onSubmit();
    };
  }, []);

  return {
    keyboardHandlerRef,
    handleInputFocus,
    createNavigateToNext,
    createSubmitHandler,
  };
};
