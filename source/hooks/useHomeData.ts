import {useState, useCallback} from 'react';
import {Platform} from 'react-native';
import _ from 'lodash';
import {Helper, HelperMemo} from '../Helper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SharedStorageData from '../share/SharedStorage';
import RNWidgetCenter from 'react-native-widget-center';
import SharedGroupPreferences from 'react-native-shared-group-preferences';
import BackgroundGeolocation from 'react-native-background-geolocation';
import I18n from '../I18n';
import {HomeData, HomeNavigation, LoaderProps, Device} from '../types/home';
import {PubSubEvent} from '../types/PubSubEvent';
import PubSub from 'pubsub-js';

// 应用程序组标识符常量
const appGroupIdentifier = 'group.com.presen.WidgetPresen';

export const useHomeData = (
  navigation: HomeNavigation,
  {showLoader, hideLoader, changeHomeName}: LoaderProps,
) => {
  // 状态定义
  const [homeData, setHomeData] = useState<HomeData>({
    is_guard_mode: false,
    state: null,
    event: null,
    time: '',
    device_size: '',
    scenes: [],
    actions: [],
    devices: [],
    ipcDevices: [],
    favorDevices: [],
    temp: '',
    humidity: '',
    show: false,
    sn: null,
    current_scene: null,
    refreshing: false,
    widget_data: [],
  });
  const [showEmpty, setShowEmpty] = useState<string>(
    HelperMemo.user_data ? HelperMemo.user_data.showEmpty : 'hide',
  );
  const [noSnPush, setNoSnPush] = useState<boolean>(true);

  // 保存数据到共享存储
  const saveUserDataToSharedStorage = async (data: any): Promise<void> => {
    try {
      await SharedGroupPreferences.setItem(
        'SceneData',
        data,
        appGroupIdentifier,
      );
      RNWidgetCenter.reloadTimelines('WidgetPresen');
    } catch (errorCode) {
      console.log(errorCode);
    }
  };

  // 设置地理围栏
  const settingGeofence = useCallback((routine: any): void => {
    const location = Helper.locationFormat(routine.location);
    BackgroundGeolocation.getState().then(state => {
      if (state.enabled) {
        BackgroundGeolocation.addGeofence({
          identifier: routine.uuid,
          radius: Helper.BackgroundGeolocationRadius,
          latitude: parseFloat(location[1]),
          longitude: parseFloat(location[0]),
          notifyOnEntry: routine.is_returned,
          notifyOnExit: !routine.is_returned,
        });
      } else {
        BackgroundGeolocation.startGeofences();
        BackgroundGeolocation.addGeofence({
          identifier: routine.uuid,
          radius: Helper.BackgroundGeolocationRadius,
          latitude: parseFloat(location[1]),
          longitude: parseFloat(location[0]),
          notifyOnEntry: routine.is_returned,
          notifyOnExit: !routine.is_returned,
        });
      }
    });
  }, []);

  // 统一数据获取
  const doUnifyData = useCallback((): void => {
    Helper.httpGET(Helper.urlWithQuery('/home/<USER>', {}), {
      success: (data: any) => {
        HelperMemo.unify_commands_schema = data;
      },
      error: () => {},
    });
  }, []);

  // 主要数据获取函数
  const doFetchData = useCallback(
    (type: string): void => {
      if (!HelperMemo.user_data || !HelperMemo.user_data.user) {
        return;
      }

      if (type === 'refresh') {
        setHomeData(prev => ({...prev, refreshing: true}));
      } else if (type !== 'initial') {
        showLoader();
      }

      Helper.httpGET('/home', {
        success: (data: any) => {
          doUnifyData();
          // 过滤设备类型
          // let ipc: Device[] = [];
          let tempDevice: Device[] = [];
          data.devices.forEach((item: Device) => {
            if (item.dv_type === 'ipc') {
              // ipc.push(item);
            } else {
              tempDevice.push(item);
            }
          });
          // 设置主要数据
          setHomeData({
            is_guard_mode: data.is_guard_mode || false,
            sn: data.sn,
            state: data.state,
            event: data.event,
            time: data.time,
            device_size: data.device_size,
            scenes: data.scenes,
            actions: data.actions,
            devices: tempDevice,
            ipcDevices: data.favor_ipc_devices || [],
            last_event_id: data.user.user.last_user_event_id,
            temp: data.temp,
            favorDevices: data.favor_devices,
            current_scene: data.current_scene,
            show: true,
            refreshing: false,
            humidity: data.humidity || '',
            widget_data: data.widget_data || [],
          });

          // 更新首页名称
          changeHomeName(data.home.name);
          HelperMemo.ipc_is_local_play = data.home.ipc_is_local_play;

          // 处理 widget 数据
          const temp: any = [];
          _.forEach(data.widget_data, (s: any) => {
            let icon = 'icons8-home-page-96';
            if (s.icon) {
              icon = s.icon.split('.png')[0];
            }
            temp.push({
              name: s.name,
              uuid: s.uuid,
              icon,
              isEnabled: s.is_enabled === true,
              type: s.widget_type,
            });
          });

          // 保存 widget 数据
          if (Platform.OS === 'android') {
            SharedStorageData.setData(JSON.stringify(temp));
          }
          AsyncStorage.setItem('widgetData', JSON.stringify(temp));
          AsyncStorage.setItem('androidWidgetData', JSON.stringify(temp));
          if (Platform.OS === 'ios') {
            saveUserDataToSharedStorage({data: temp});
          }

          // 处理地理位置数据
          if (!_.isEmpty(data.gps_routines)) {
            data.gps_routines.map((routine: any) => {
              settingGeofence(routine);
            });
          }

          // 更新用户数据
          const sn_data = {
            home_id: data.user.home_id,
            role: data.user.role,
            user: data.user.user,
            sn: data.sn,
          };

          AsyncStorage.mergeItem('user_data', JSON.stringify(sn_data), () => {
            HelperMemo.user_data.home_id = data.user.home_id;
            HelperMemo.user_data.role = data.user.role;
            HelperMemo.user_data.user = data.user.user;
            HelperMemo.user_data.sn = data.sn;
          });
        },
        ensure: () => {
          if (type === 'refresh') {
          } else if (type !== 'initial') {
            hideLoader();
          }
          setHomeData(prev => ({...prev, show: true}));
        },
        error: (error: any, errorType: string | undefined) => {
          if (errorType === 'no_sn') {
            if (noSnPush) {
              setNoSnPush(false);
              HelperMemo.select_home = HelperMemo.user_data.home_id;
              navigation.push('WifiScreen', {
                title: I18n.t('home.add_controller'),
              });
            } else {
              PubSub.publish(
                PubSubEvent.SHOW_ERROR,
                _.flatten([error]).join('\n'),
              );
            }
          } else if (errorType !== 'force_login') {
            PubSub.publish(
              PubSubEvent.SHOW_ERROR,
              _.flatten([error]).join('\n'),
            );
          }
        },
      });
    },
    [
      navigation,
      showLoader,
      hideLoader,
      changeHomeName,
      noSnPush,
      setNoSnPush,
      doUnifyData,
      settingGeofence,
    ],
  );

  // 返回关键数据和方法
  return {
    homeData,
    showEmpty,
    setShowEmpty,
    doFetchData,
    saveUserDataToSharedStorage,
    settingGeofence,
  };
};
