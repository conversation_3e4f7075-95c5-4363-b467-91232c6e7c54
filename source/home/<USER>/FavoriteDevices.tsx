import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {Tme} from '../../ThemeStyle';
import I18n from '../../I18n';
import DeviceItemCCSpecs from '../../device_spec/DeviceItemCCSpecs';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

interface Device {
  uuid: string;
  display_name: string;
  [key: string]: any; // 用于其他可能的设备属性
}

interface FavoriteDevicesProps {
  devices: Device[];
  navigation: NativeStackNavigationProp<any>;
  useBackgroundImage: boolean;
}

const FavoriteDevices: React.FC<FavoriteDevicesProps> = ({
  devices,
  navigation,
  useBackgroundImage,
}) => {
  return (
    <View>
      <View
        style={{
          paddingHorizontal: 20,
          marginBottom: 10,
          marginTop: 8,
        }}>
        <Text
          style={{
            fontSize: 12,
            // fontWeight: '500',
            marginLeft: 10,
            color: useBackgroundImage
              ? Tme('cardTextColor', 'D')
              : Tme('cardTextColor'),
          }}>
          {I18n.t('home.qiock_control')}
        </Text>
      </View>
      <View>
        {devices.map((v: Device, k: number) => (
          <View key={k}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => {
                navigation.push('deviceShow', {
                  data: {uuid: v.uuid},
                });
              }}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 20,
                marginLeft: 10,
              }}>
              <Text
                style={[
                  {
                    color: useBackgroundImage
                      ? Tme('cardTextColor', 'D')
                      : Tme('cardTextColor'),
                    fontSize: 16,
                    fontWeight: '600',
                  },
                ]}>
                {v.display_name}
              </Text>
            </TouchableOpacity>
            <DeviceItemCCSpecs
              parent={this}
              navigation={navigation}
              device={v}
              from="home"
            />
          </View>
        ))}
      </View>
    </View>
  );
};

export default FavoriteDevices;
