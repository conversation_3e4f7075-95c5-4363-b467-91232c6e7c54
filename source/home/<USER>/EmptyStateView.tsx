import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Tme } from '../../ThemeStyle';
import { mainRadius } from '../../Tools';
import I18n from '../../I18n';
import ShadowView from '../../share/ShadowView';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface EmptyStateViewProps {
  onAddController: () => void;
  onHide: () => void;
}

const EmptyStateView: React.FC<EmptyStateViewProps> = ({ onAddController, onHide }) => {
  return (
    <View style={{ paddingHorizontal: 20, marginBottom: 25 }}>
      <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
        <View
          style={{
            backgroundColor: Tme('cardColor2'),
            borderRadius: mainRadius(),
            padding: 20,
            position: 'relative',
          }}>
          <View style={{ paddingRight: 10 }}>
            <Text
              style={{
                color: Tme('cardTextColor'),
                fontSize: 12,
              }}>
              {I18n.t('home.no_controller')}
            </Text>
          </View>
          <TouchableOpacity
            onPress={onAddController}
            activeOpacity={0.8}
            style={{
              marginTop: 20,
              paddingVertical: 8,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: mainRadius(),
              borderColor: Tme('cardTextColor'),
              borderWidth: 1,
            }}>
            <Text style={{ color: Tme('cardTextColor'), fontSize: 12 }}>
              {I18n.t('home.add_controller')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            hitSlop={{ top: 20, right: 20, bottom: 20, left: 20 }}
            activeOpacity={0.8}
            onPress={onHide}
            style={{
              position: 'absolute',
              right: 10,
              top: 6,
            }}>
            <Ionicons name="close" color={Tme('cardTextColor')} size={20} />
          </TouchableOpacity>
        </View>
      </ShadowView>
    </View>
  );
};

export default EmptyStateView;
