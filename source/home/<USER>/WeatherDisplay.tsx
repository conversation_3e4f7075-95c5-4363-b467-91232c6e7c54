import React from 'react';
import {View, Text} from 'react-native';
import {Tme} from '../../ThemeStyle';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';

interface TimeData {
  icon: string;
  value: string;
}

interface WeatherDisplayProps {
  time: TimeData;
  userName?: string;
  useBackgroundImage?: boolean;
}

// 天气显示组件
const WeatherDisplay: React.FC<WeatherDisplayProps> = ({
  time,
  userName,
  useBackgroundImage,
}) => (
  <View
    style={{
      backgroundColor: 'transparent',
      paddingTop: 10,
      paddingHorizontal: 20,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    }}>
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
      }}>
      <MCIcons
        name={time.icon}
        size={28}
        color={
          useBackgroundImage ? Tme('cardTextColor', 'D') : Tme('cardTextColor')
        }
        style={{marginRight: 8}}
      />
      <Text
        style={{
          color: useBackgroundImage
            ? Tme('cardTextColor', 'D')
            : Tme('cardTextColor'),
          fontSize: 21,
          fontWeight: '600',
        }}>
        {time.value}
        {userName ? `, ${userName.substring(0, 5)}` : ''}
      </Text>
    </View>
  </View>
);

export default WeatherDisplay;
