import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Image,
  Text,
  useWindowDimensions,
} from 'react-native';
import IpcHtmlShow from '../device/ipc/ipc_html/IpcHtmlShow';
import I18n from '../I18n';
import AlertModal from '../share/AlertModal';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {Tme} from '../ThemeStyle';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useNavigation} from '@react-navigation/native';
import {getDeviceHost, getViewFrom} from '../device/ipc/IpcHelper';
import {IpcSource} from '../device/ipc/ipcTypes';
import {Device} from '../types/home';
import {mainRadius} from '../Tools';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

const aspectRatio = 16 / 9;

// 定义组件 Props 类型
interface IpcItemProps {
  item: Device;
  onBlur: boolean;
  from: 'home' | 'ipc'; // 视图来源
  onFullscreen?: () => void; // 添加全屏回调函数属性
}

type RootStackParamList = {
  LocalCameraShow: {
    host: string | null;
    clientid: string | undefined;
    node: Device;
    title: string;
  };
  IpcCameraShow: {
    title: string;
    node: Device;
  };
  IpcList: {
    title: string;
  };
};

// 简化后的 IpcItem 组件，只负责视频渲染和播放控制
const IpcItem: React.FC<IpcItemProps> = function IpcItem(props) {
  const {width: windowWidth} = useWindowDimensions();
  const videoWidth = windowWidth - 40;
  const videoHeight = videoWidth / aspectRatio;

  // 先解构 props 获取必要的数据，给默认值避免访问 undefined
  const {item} = props || {};
  const {from} = props;

  const hasMissingData = !props || !item || !item.ipc;
  // 检查设备是否处于活跃状态
  const isDeviceAlive = item?.is_alive === true;

  // 获取正确的 webrtc_uuid，只从 ipc 对象中获取
  const clientId = item?.ipc?.webrtc_uuid;

  const [host, setHost] = useState<string | null>(null); // 主机地址
  const [showIpc, setShowIpc] = useState<boolean>(false); // 是否显示IPC视图
  const [viewFrom, setViewFrom] = useState<IpcSource>(IpcSource.Cloud); // 视图来源，默认为云端

  // 使用 useRef 替代 useState 来追踪 ready 事件是否已处理
  const readyHandledRef = useRef<boolean>(false);

  // 内部引用，确保始终有一个可用的引用
  const localIpcRef = useRef<any>(null);
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  // 初始化主机地址
  useEffect(() => {
    // 获取并设置主机地址
    const deviceHost = getDeviceHost(item);
    setHost(deviceHost);
    setViewFrom(getViewFrom(deviceHost));
    console.log('主机地址:', deviceHost);
  }, [hasMissingData, item]);

  // 初始化连接
  const startConnection = (): void => {
    // 如果设备处于活跃状态，不执行任何操作
    if (!isDeviceAlive) {
      return;
    }
    setShowIpc(true);
  };

  // 处理 WebView 准备就绪
  const handleReady = (): void => {
    // 使用ref来检查是否已处理过ready事件，这是同步的
    if (readyHandledRef.current) {
      return;
    }
    // 立即更新ref，不会有延迟
    readyHandledRef.current = true;
    if (localIpcRef.current && host && clientId) {
      localIpcRef.current.initConnection(host, clientId, viewFrom); // 初始化连接
    }
  };

  // 处理错误
  const handleError = (err: any): void => {
    console.error('IpcHtmlShow 错误:', err);

    AlertModal.alert('', I18n.t('home.network_issue_desp'), [
      {
        text: I18n.t('home.cancel'),
      },
    ]);
  };

  useEffect(() => {
    // 修复：使用与旧版本相同的逻辑
    if (!props.onBlur) {
      // 只在 onBlur 为 false 时执行
      setShowIpc(props.onBlur); // 设置为 false (不显示)
      readyHandledRef.current = false;
    }
  }, [props.onBlur]);

  // 组件卸载时的清理
  useEffect(() => {
    return () => {
      setShowIpc(false);
      setHost(null);
      // 重置ref值，确保下次挂载时能正常工作
      readyHandledRef.current = false;
    };
  }, []);

  // 使用单一返回，根据条件渲染不同内容
  return (
    <View>
      {/* 标题部分移到外部，不包含在黑色背景中 */}
      {from == 'ipc' && (
        <View style={styles.titleContainer}>
          <TouchableOpacity
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
            hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
            activeOpacity={0.8}
            onPress={() => {
              if (!isDeviceAlive) {
                return;
              }
              if (viewFrom === 'local') {
                navigation.push('LocalCameraShow', {
                  host: host,
                  clientid: clientId,
                  node: props.item,
                  title: I18n.t('ipc.local_play'),
                });
              } else {
                navigation.push('IpcCameraShow', {
                  title: props.item?.display_name || '',
                  node: props.item,
                });
              }
            }}>
            <Text
              style={[
                {
                  color: Tme('cardTextColor'),
                  fontSize: 16,
                  fontWeight: '600',
                },
              ]}>
              {item?.display_name || ''}
            </Text>
            <MCIcons
              name="chevron-right"
              size={30}
              color={Tme('cardTextColor')}
            />
          </TouchableOpacity>
        </View>
      )}

      {/* 视频容器部分 */}
      <View
        style={[
          styles.outerContainer,
          {
            borderBottomRightRadius: from === 'ipc' ? mainRadius() : 0,
            borderBottomLeftRadius: from === 'ipc' ? mainRadius() : 0,
          },
        ]}>
        <View
          style={[
            styles.container,
            {width: videoWidth, height: videoHeight},
            {backgroundColor: 'black'},
          ]}>
          {/* 视频区域容器 */}
          <View
            style={[
              styles.videoWrapper,
              {width: videoWidth, height: videoHeight},
            ]}>
            {/* 视频显示组件 */}
            {showIpc && (
              <IpcHtmlShow
                ref={localIpcRef}
                onReady={handleReady}
                onError={handleError}
                clientid={clientId}
                from={viewFrom}
              />
            )}

            {!showIpc && (
              <>
                <Image
                  source={
                    item?.ipc_last_event_image_url
                      ? {uri: item?.ipc_last_event_image_url}
                      : undefined
                  }
                  style={[
                    styles.backgroundImage,
                    {
                      width: videoWidth,
                      height: videoHeight,
                    },
                  ]}
                  resizeMode="cover"
                />
                <View
                  style={[
                    styles.overlay,
                    {
                      backgroundColor: isDeviceAlive
                        ? 'rgba(0, 0, 0, 0.5)'
                        : 'black',
                    },
                    {
                      width: videoWidth,
                      height: videoHeight,
                    },
                  ]}
                />
                {!showIpc && (
                  <View style={styles.playButtonContainer}>
                    <TouchableOpacity
                      style={[
                        styles.playButton,
                        isDeviceAlive ? null : styles.disabledPlayButton,
                      ]}
                      onPress={startConnection}
                      activeOpacity={isDeviceAlive ? 0.7 : 1}
                      disabled={!isDeviceAlive}
                      testID="PlayButton">
                      <Ionicons
                        name={isDeviceAlive ? 'play' : 'cloud-offline-outline'}
                        size={40}
                        color={'white'}
                      />
                    </TouchableOpacity>
                  </View>
                )}
              </>
            )}

            {/* 添加全屏按钮 */}
            {isDeviceAlive && (
              <TouchableOpacity
                style={styles.fullscreenButton}
                onPress={props.onFullscreen}
                testID="FullscreenButton">
                <Ionicons name="expand" size={26} color="white" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
      {from === 'home' && (
        <View style={{marginHorizontal: 20, marginVertical: 10}}>
          <Text
            style={[
              {
                color: Tme('cardTextColor'),
              },
            ]}>
            {item?.display_name || ''}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  outerContainer: {
    overflow: 'hidden',
  },
  titleContainer: {
    paddingHorizontal: 20,
    backgroundColor: 'transparent',
    marginBottom: 10,
    marginTop: 10,
  },
  titleText: {
    // 调整文本颜色，使用系统默认文本颜色
    color: Tme('textPrimaryColor', '#333'),
    fontSize: 16,
    fontWeight: '600',
  },
  container: {
    backgroundColor: 'black',
    position: 'relative',
    overflow: 'hidden',
  },
  videoWrapper: {
    position: 'relative',
    overflow: 'hidden',
    backgroundColor: 'black',
  },
  backgroundImage: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 1,
    // 添加Flexbox属性使子元素可以居中
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButtonContainer: {
    position: 'absolute',
    zIndex: 2,
    // 使用Flexbox属性而不是手动计算位置
    alignSelf: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    alignItems: 'center',
  },
  playButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  disabledPlayButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  fullscreenButton: {
    position: 'absolute',
    top: 15,
    right: 15,
    zIndex: 3,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default IpcItem;
