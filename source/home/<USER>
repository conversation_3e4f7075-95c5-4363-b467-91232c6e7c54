/* eslint-disable react/no-unstable-nested-components */
import React, {useRef, useImperativeHandle, forwardRef, useState} from 'react';
import {BottomSheetBackdrop, BottomSheetModal} from '@gorhom/bottom-sheet';
import {Text, TouchableOpacity, View} from 'react-native';
import {Tme, IsDark} from '../ThemeStyle';
import {mainRadius} from '../Tools';

interface BottomSheetModalViewProps {}

interface BottomTextState {
  title: string;
  desp: string;
  confirm: string;
  cancel: string;
  onChange: () => void;
}

interface BottomSheetModalViewRef {
  openBottomSheet: (
    title: string,
    desp: string,
    confirm: string,
    cancel: string,
    onChange: () => void,
  ) => void;
}

const BottomSheetModalView = forwardRef<
  BottomSheetModalViewRef,
  BottomSheetModalViewProps
>((_props, ref) => {
  useImperativeHandle(ref, () => ({
    openBottomSheet: openBottomSheet,
  }));

  const bottomSheetRef = useRef<BottomSheetModal>(null);

  const [bottomText, setBottomText] = useState<BottomTextState>({
    title: '',
    desp: '',
    confirm: '',
    cancel: '',
    onChange: () => {},
  });

  const openBottomSheet = (
    title: string,
    desp: string,
    confirm: string,
    cancel: string,
    onChange: () => void,
  ): void => {
    setBottomText({
      title: title,
      desp: desp,
      confirm: confirm,
      cancel: cancel,
      onChange: onChange,
    });
    bottomSheetRef.current?.present();
  };

  return (
    <BottomSheetModal
      ref={bottomSheetRef}
      snapPoints={['33%']}
      index={0}
      backgroundStyle={{
        backgroundColor: Tme('cardColor'),
      }}
      backdropComponent={props => (
        <BottomSheetBackdrop
          disappearsOnIndex={-1}
          appearsOnIndex={0}
          {...props}
        />
      )}>
      <View
        style={{
          flex: 1,
          padding: 20,
        }}>
        <Text
          style={{
            color: Tme('cardTextColor'),
            fontSize: 18,
            marginBottom: 20,
            fontWeight: '600',
          }}>
          {bottomText.title}
        </Text>
        <Text
          style={{
            color: Tme('cardTextColor'),
            fontSize: 14,
          }}>
          {bottomText.desp}
        </Text>
        <View style={{marginTop: 30}}>
          <TouchableOpacity
            onPress={() => {
              bottomText.onChange();
              bottomSheetRef.current?.dismiss();
            }}
            activeOpacity={0.8}
            style={{
              alignItems: 'center',
              justifyContent: 'center',
              paddingVertical: 8,
              borderRadius: mainRadius(),
              backgroundColor: Tme('cardColor', IsDark() ? 'L' : 'D'),
            }}>
            <Text style={{color: Tme('cardTextColor', IsDark() ? 'L' : 'D')}}>
              {bottomText.confirm}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              bottomSheetRef.current?.dismiss();
            }}
            activeOpacity={0.8}
            style={{
              marginTop: 15,
              alignItems: 'center',
              justifyContent: 'center',
              paddingVertical: 8,
              borderRadius: mainRadius(),
              borderColor: Tme('cardColor', IsDark() ? 'L' : 'D'),
              borderWidth: 1,
              borderStyle: 'solid',
            }}>
            <Text style={{color: Tme('cardTextColor')}}>
              {bottomText.cancel}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </BottomSheetModal>
  );
});

export default BottomSheetModalView;
