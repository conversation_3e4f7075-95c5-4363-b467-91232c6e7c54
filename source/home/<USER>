import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Platform,
  Linking,
  Animated,
} from 'react-native';

import { Helper, HelperMemo, DEVICE_WIDTH } from '../Helper';
import I18n from '../I18n';
import HomeView from './HomeView';
import SystemNotification from '../SystemNotification';
import { FcmNotification } from '../FcmNotification';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import NetworkHandler from '../NetworkHandler';
import UpdateApp from '../UpdateApp';
import AppStateCenter from '../AppStateCenter';
import {
  NotificationCenter,
  EVENT_HOME_ADD_DEVICE,
} from '../NotificationCenter';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Tme, Colors } from '../ThemeStyle';
import Icons from 'react-native-vector-icons/Ionicons';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import DeviceInfo from 'react-native-device-info';
import UserBackgroundImage from '../share/UserBackgroundImage';
import momentTZ from 'moment-timezone';
import _ from 'lodash';
import BackgroundGeolocation from 'react-native-background-geolocation';
import AlertModal from '../share/AlertModal';

import { SafeAreaView } from 'react-native-safe-area-context';
import { hideLoading, showLoading } from '../../ILoading';
import GetuiPush from '../Getui';
import { PERMISSIONS, request } from 'react-native-permissions';
// import GPSHelper from '../GPSHelper';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
// import BottomSheetModalView from './BottomSheetModalView';
import AppConfig from '../../app_config';
import { StripeProvider } from '@stripe/stripe-react-native';
import SnDetect from './SnDetect';
import PubSub from 'pubsub-js';
import IpcScanner from '../../IpcScanner';
import { PubSubEvent } from '../types/PubSubEvent';

const titleOpacityShowHeight = 100;
const titleOpacityShowAnimDelta = 30;

class HomeScreen extends Component {
  constructor(props) {
    super(props);

    this.state = {
      title: '',
      isDataLoaded: false,
      isFlash: false,
      page: 0,
      isNotify: false,
      showRefresh: false,
      titleOpacity: new Animated.Value(0),
      isGuardMode: false,
      showView: false,
      use_background_image: true,
      homeName: '',
      bottomText: {
        title: I18n.t('home.warning_message'),
        desp: '',
        confirm: I18n.t('home.confirm'),
        cancel: I18n.t('home.cancel'),
        onChange: () => { },
      },
    };
    this.ai = React.createRef();
    this.homeViewRef = React.createRef();

    this.isBackgroundImage();
    this.screenEventListener = null;

    this.onLocation = this.onLocation.bind(this);
    this.onError = this.onError.bind(this);
    this.onActivityChange = this.onActivityChange.bind(this);
    this.onProviderChange = this.onProviderChange.bind(this);
    this.onMotionChange = this.onMotionChange.bind(this);
    // this.bottomSheetRef = React.createRef(null);
  }

  componentDidMount() {
    if (Platform.OS === 'android') {
      DeviceInfo.hasGms()
        .then(hasGms => {
          this.setState({
            notificationType: hasGms ? 'fcm' : 'system',
          });
        })
        .catch(error => {
          console.error('GMS检测失败:', error);
          this.setState({ notificationType: 'system' }); // 出错时默认使用系统通知
        });
    } else {
      this.setState({ notificationType: 'system' }); // iOS 使用系统通知
    }
    // this.removeUser();
    // AsyncStorage.removeItem('user_data', () => {
    //   HelperMemo.user_data = null;
    // PubSub.subscribe(PubSubEvent.RESTART_APP);
    // });
    this.startIpcScanner();

    request(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT).then(result => { });
    this.getUrlAsync();

    this.Linking = Linking.addEventListener('url', ({ url }) => {
      AsyncStorage.removeItem('isGetUrlAsync');
      console.log('linking url', url);
      const linkData = url.split('com.presen://');
      if (linkData[1]) {
        const data = linkData[1].split(',');
        AsyncStorage.getItem('widgetData').then(d => {
          const widgetData = JSON.parse(d);
          const params = _.find(widgetData, w => w.uuid === data[0]);
          if (!_.isEmpty(params)) {
            this.props.navigation.push('LinkOpenAlert', {
              uuid: data[0],
              name: params.name,
              type: data[1],
            });
          }
        });
      }
    });

    Helper.backgroundGeolocationConfig().then(config => {
      BackgroundGeolocation.onLocation(this.onLocation, this.onError);
      BackgroundGeolocation.onMotionChange(this.onMotionChange);
      BackgroundGeolocation.onActivityChange(this.onActivityChange);
      BackgroundGeolocation.onProviderChange(this.onProviderChange);
      BackgroundGeolocation.ready(config, state => {
        BackgroundGeolocation.getCurrentPosition().then(position => {
          // var lat = position.coords.latitude;
          // var lng = position.coords.longitude;
          // const location = GPSHelper.wgs84togcj02(lng, lat);
        });
        BackgroundGeolocation.removeGeofences().then(success => {
          BackgroundGeolocation.stop();
        });
      });
    });

    var that = this;
    PubSub.subscribe(PubSubEvent.CHANGE_BACKGROUND_IMAGE, () => {
      that.isBackgroundImage();
    });
    NotificationCenter.addObserver(this, EVENT_HOME_ADD_DEVICE, event => {
      if (event) {
        if (event.type == 'add') {
          that.showLoader();

          setTimeout(() => {
            that.hideLoader();
            that.props.navigation.push('deviceShow', {
              id: event.node_id,
            });
          }, 1000);
        } else if (event.type == 'remove') {
          AlertModal.alert(I18n.t('home.success'), event.notice);
        }
      }
    });
    PubSub.subscribe(PubSubEvent.EVENT_APP_NOTIFY, (msg, data) => {
      if (HelperMemo.message) {
        that.props.navigation.push('urgentAlertScreen', {
          ...HelperMemo.message,
        });
        AsyncStorage.removeItem('message', () => {
          HelperMemo.message = null;
        });
      }
    });

    if (Platform.OS === 'android') {
      GetuiPush.getWidgetData(data => {
        if (data) {
          const pa = JSON.parse(data);
          this.props.navigation.push('LinkOpenAlert', {
            uuid: pa.uuid,
            name: pa.name,
            type: pa.type,
          });
        }
      });
    }

    PubSub.subscribe(PubSubEvent.DASHBOARD_EVENT, (msg, data) => {
      if (data.type === 'DashboardSignUp') {
        this.props.navigation.push('DashboardSignUp', {
          type: 'set_password',
        });
      }
      if (data.type === 'DashboardScreen') {
        this.props.navigation.push('DashboardScreen', {
          title: I18n.t('global.dashboard'),
        });
      }
    });
  }

  componentWillUnmount() {
    console.log('home screen unmount');
    // 确保在组件卸载时停止扫描
    IpcScanner.stopScan();

    PubSub.unsubscribe(PubSubEvent.DASHBOARD_EVENT);
    HelperMemo.dashboard = null;
    if (this.navigationEventListener) {
      this.navigationEventListener.remove();
    }
    PubSub.unsubscribe(PubSubEvent.EVENT_APP_NOTIFY);
    PubSub.unsubscribe(PubSubEvent.CHANGE_BACKGROUND_IMAGE);
    NotificationCenter.removeObserver(this, EVENT_HOME_ADD_DEVICE);
    BackgroundGeolocation.removeListeners();
    this.Linking.remove();
  }

  startIpcScanner() {
    // 开始新的扫描
    IpcScanner.startScan((data, isComplete) => {
      if (isComplete) {
        IpcScanner.stopScan();
        console.log('扫描完成，最终设备列表:', data);
      } else {
        console.log('实时设备发现:', data);
      }
    }, 3000);
  }

  removeUser() {
    BackgroundGeolocation.stop();
    BackgroundGeolocation.removeGeofences().then(success => {
      console.log('[removeGeofences] all geofences have been destroyed');
    });

    AsyncStorage.removeItem('user_data', () => {
      HelperMemo.user_data = null;
      PubSub.subscribe(PubSubEvent.RESTART_APP);
    });
  }

  onLocation(location) { }

  onError(error) {
    console.warn('[location] ERROR -', error);
  }
  onActivityChange(event) { }
  onProviderChange(provider) { }
  onMotionChange(event) { }

  getUrlAsync = async () => {
    const isGetUrlAsync = await AsyncStorage.getItem('isGetUrlAsync');
    // Get the deep link used to open the app
    const initialUrl = await Linking.getInitialURL();
    console.log('initialUrl', initialUrl);
    if (isGetUrlAsync) {
      // The setTimeout is just for testing purpose
      setTimeout(() => {
        if (initialUrl) {
          AsyncStorage.removeItem('isGetUrlAsync');
          const linkData = initialUrl.split('com.presen://');
          if (linkData[1]) {
            const data = linkData[1].split(',');
            AsyncStorage.getItem('widgetData').then(d => {
              const widgetData = JSON.parse(d);
              const params = _.find(widgetData, w => w.uuid === data[0]);
              if (!_.isEmpty(params)) {
                this.props.navigation.push('LinkOpenAlert', {
                  uuid: data[0],
                  name: params.name,
                  type: data[1],
                });
              }
            });
          }
        }
      }, 1000);
    }
  };

  isBackgroundImage() {
    AsyncStorage.getItem('background_image').then(data => {
      var temp = JSON.parse(data);
      if (temp && temp.check) {
        this.setState({
          use_background_image: temp.check !== 'bg_null',
        });
      } else {
        this.setState({
          use_background_image: false,
        });
      }
    });
  }

  changeHomeName(homeName) {
    this.setState({ homeName });
  }

  _mountNotification() {
    const { notificationType } = this.state;
    if (Platform.OS === 'android') {
      if (!notificationType) {
        return null; // 还未确定使用哪种通知时不渲染任何内容
      }
      if (momentTZ.tz.guess(true) === 'Asia/Shanghai') {
        if (notificationType === 'fcm') {
          return <FcmNotification navigation={this.props.navigation} />;
        }
        if (notificationType === 'system') {
          return <SystemNotification navigation={this.props.navigation} />;
        }
      } else {
        if (notificationType === 'fcm') {
          return <FcmNotification navigation={this.props.navigation} />;
        }
        if (notificationType === 'system') {
          return <SystemNotification navigation={this.props.navigation} />;
        }
      }
    } else {
      // ios
      return <SystemNotification navigation={this.props.navigation} />;
    }
  }

  doFetchData() {
    this.homeViewRef.current.doFetchData();
  }

  render() {
    return (
      <BottomSheetModalProvider>
        <SnDetect navigation={this.props.navigation} />
        <SafeAreaView
          edges={['left', 'right', 'top']}
          style={{
            flex: 1,
          }}>
          <UserBackgroundImage />
          {this._mountNotification()}
          <NetworkHandler />
          <UpdateApp />
          <AppStateCenter navigation={this.props.navigation} />
          <StripeProvider
            publishableKey={AppConfig.stripe_key}
            urlScheme="presen" // required for 3D Secure and bank redirects
            merchantIdentifier="merchant.com.presen" // required for Apple Pay
          >
            <View
              style={{
                backgroundColor: 'transparent',
                height: HelperMemo.NAV_BAR_HEIGHT,
                paddingHorizontal: 16,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <Animated.Text
                style={{
                  color: this.state.use_background_image
                    ? Tme('cardTextColor', 'D')
                    : Tme('cardTextColor'),
                  fontWeight: 'bold',
                  fontSize: 20,
                  opacity: this.state.titleOpacity.interpolate({
                    inputRange: [0, titleOpacityShowAnimDelta],
                    outputRange: [0, 1],
                  }),
                }}>
                {_.isEmpty(this.state.homeName)
                  ? I18n.t('home.home').toUpperCase()
                  : this.state.homeName}
              </Animated.Text>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  flexDirection: 'row',
                }}>
                <TouchableOpacity
                  activeOpacity={0.8}
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: 16,
                  }}
                  hitSlop={{ top: 15, right: 15, bottom: 15, left: 15 }}
                  onPress={this.clickController.bind(this)}>
                  <MaterialIcons
                    name="home"
                    size={24}
                    color={
                      this.state.use_background_image
                        ? Tme('cardTextColor', 'D')
                        : Tme('cardTextColor', 'D')
                    }
                  />
                </TouchableOpacity>
                {this.state.isGuardMode ? (
                  <TouchableOpacity
                    activeOpacity={0.8}
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 16,
                    }}
                    hitSlop={{ top: 15, right: 15, bottom: 15, left: 15 }}
                    onPress={this.guard.bind(this)}>
                    <MCIcons
                      name="shield-home"
                      size={24}
                      color={
                        this.state.use_background_image
                          ? Tme('cardTextColor', 'D')
                          : Tme('cardTextColor', 'D')
                      }
                    />
                  </TouchableOpacity>
                ) : null}
                <TouchableOpacity
                  style={{ alignItems: 'center', justifyContent: 'center' }}
                  activeOpacity={0.8}
                  hitSlop={{ top: 15, right: 15, bottom: 15, left: 15 }}
                  onPress={this.event.bind(this)}>
                  <Icons
                    name="notifications-outline"
                    size={24}
                    color={
                      this.state.use_background_image
                        ? Tme('cardTextColor', 'D')
                        : Tme('cardTextColor', 'D')
                    }
                  />
                  {this.state.isNotify ? (
                    <View
                      style={{
                        width: 6,
                        height: 6,
                        backgroundColor: this.state.use_background_image
                          ? Tme('cardTextColor', 'D')
                          : Tme('cardTextColor', 'D'),
                        borderRadius: 3,
                        position: 'absolute',
                        top: 2,
                        right: 0,
                      }}
                    />
                  ) : null}
                </TouchableOpacity>
              </View>
            </View>
            <View style={{ flex: 1 }}>
              {this.state.showView ? (
                <View
                  style={{
                    backgroundColor: 'transparent',
                    width: '100%',
                    height: 300,
                    position: 'absolute',
                  }}
                />
              ) : null}
              <HomeView
                use_background_image={this.state.use_background_image}
                navigation={this.props.navigation}
                parent={this}
                ref={this.homeViewRef}
                changeGuardMode={(value) => {
                  if (this.state.isGuardMode !== value) {
                    this.setState({
                      isGuardMode: value,
                    });
                  }
                }}
                showNotify={isNotify => {
                  if (this.state.isNotify !== isNotify) {
                    this.setState({
                      isNotify: isNotify,
                    });
                  }
                }}
                // openBottomSheet={this.openBottomSheet.bind(this)}
                changeHomeName={this.changeHomeName.bind(this)}
              />
            </View>
            {this.state.showRefresh ? (
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={this.refresh.bind(this)}
                style={{
                  position: 'absolute',
                  bottom: 0,
                  width: DEVICE_WIDTH,
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  paddingVertical: 16,
                  backgroundColor: Colors.MainColor,
                }}>
                <Text
                  style={{ color: '#ffffff', fontSize: 14, fontWeight: '500' }}>
                  {I18n.t('wifi.retry')}
                </Text>
              </TouchableOpacity>
            ) : null}
          </StripeProvider>
        </SafeAreaView>
        {/* <BottomSheetModalView ref={this.bottomSheetRef} /> */}
      </BottomSheetModalProvider>
    );
  }

  async clickController() {
    this.props.navigation.push('changeController', {
      home_id: HelperMemo.user_data.home_id,
      title: I18n.t('home.your_home'),
    });
  }

  guard() {
    var that = this;
    AlertModal.alert(
      I18n.t('guard.guard_mode'),
      I18n.t('guard.exit_guard_mode'),
      [
        { text: 'Cancel', onPress: () => { } },
        {
          text: 'OK',
          onPress: () => {
            Helper.httpPOST('/guards/delete_guard', {
              success: data => {
                that.setState({
                  isGuardMode: data.is_guard_mode,
                });
              },
              ensure: () => { },
            });
          },
        },
      ],
      { cancelable: false },
    );
  }

  setGuardMode(mode) {
    this.setState({
      isGuardMode: mode,
    });
  }

  showRefresh() {
    this.setState({
      showRefresh: true,
    });
  }
  hideRefresh() {
    this.setState({
      showRefresh: false,
    });
  }

  showTitle(evt) {
    var y = evt.nativeEvent.contentOffset.y;
    if (y > titleOpacityShowHeight) {
      this.state.titleOpacity.setValue(y - titleOpacityShowHeight);
    } else {
      this.state.titleOpacity.setValue(0);
    }
  }

  event() {
    this.props.navigation.push('eventScreen', {
      _from: 'home',
      title: I18n.t('home.event'),
    });
    this.setState({
      isNotify: false,
    });
  }

  showLoader() {
    showLoading();
  }

  hideLoader() {
    hideLoading();
  }

  refresh() {
    var that = this;
    that.showLoader();
    Helper.httpGET('/users/home_data', {
      ensure: () => {
        that.hideLoader();
      },
      cloud: true,
      success: data => {
        var localData = {
          user: data.user,
          uuid: data.user.uuid,
          role: data.role,
          home_id: data.home_id,
          is_demo: data.is_demo,
          sn: data.sn,
        };
        AsyncStorage.setItem('user_data', JSON.stringify(localData), () => {
          HelperMemo.user_data = localData;
          PubSub.publish(PubSubEvent.RESTART_APP);
        });
      },
    });
  }
}
export default HomeScreen;
