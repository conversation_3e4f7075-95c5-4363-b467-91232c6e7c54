import React, {useEffect} from 'react';
import {Helper, HelperMemo} from '../Helper';
import I18n from '../I18n';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AlertModal from '../share/AlertModal';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

interface CtlUpdateAlertProps {
  sn: {
    id: string;
  };
  navigation: NativeStackNavigationProp<any>;
}

interface ResponseData {
  code?: string;
  v?: string | number;
}

const CtlUpdateAlert: React.FC<CtlUpdateAlertProps> = props => {
  useEffect(() => {
    if (props.sn.id) {
      Helper.httpPOST(
        '/partner/settings/upgrade',
        {
          error: () => {},
          success: (data: ResponseData) => {
            if (data.code === 'controller_offline') {
            } else {
              if (
                HelperMemo.user_data.isCtlUpdateNotify !== data.v?.toString()
              ) {
                AsyncStorage.mergeItem(
                  'user_data',
                  JSON.stringify({isCtlUpdateNotify: data.v?.toString()}),
                  () => {
                    HelperMemo.user_data.isCtlUpdateNotify = data.v?.toString();
                  },
                );
                AlertModal.alert(
                  I18n.t('setting.update_desp_one') + data.v,
                  I18n.t('global.ctl_update'),
                  [
                    {
                      text: I18n.t('home.cancel'),
                      onPress: () => {},
                    },
                    {
                      text: 'OK',
                      onPress: () => {
                        props.navigation.push('changeController', {
                          home_id: HelperMemo.user_data.home_id,
                          title: I18n.t('setting.home_setting'),
                        });
                      },
                    },
                  ],
                );
              }
            }
          },
        },
        {updated: 'yes'},
      );
    }
  }, [props.sn.id, props.navigation]); // 添加依赖项，只有当这些值变化时才会重新执行

  return null;
};

export default CtlUpdateAlert;
