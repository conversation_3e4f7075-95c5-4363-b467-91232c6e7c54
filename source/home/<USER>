/* eslint-disable react-hooks/exhaustive-deps */
import React, { useContext, useEffect, useState, useRef } from 'react';
import { View, ScrollView, RefreshControl, AppState, Platform, Text, TouchableOpacity } from 'react-native';
import { useHomeData } from '../hooks/useHomeData';
import { HelperMemo } from '../Helper';
import I18n from '../I18n';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';
import ScreenSizeContext from '../../WindowResizeContext';
import WeatherDisplay from './components/WeatherDisplay';
import StatusCards from './components/StatusCards';
import ScenesList from './components/ScenesList';
import ActionsList from './components/ActionsList';
import DevicesList from './components/DevicesList';
import EmptyStateView from './components/EmptyStateView';
import FavoriteDevices from './components/FavoriteDevices';
import IpcDeviceSection from './components/IpcDeviceSection';
import CtlUpdateAlert from './CtlUpdateAlert';
import Skeletion from '../share/Skeletion';
import AsyncStorage from '@react-native-async-storage/async-storage';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import SharedStorageData from '../share/SharedStorage';
import _ from 'lodash';
import IpcScanner from '../../IpcScanner';
import { Tme } from '../ThemeStyle';
import { mainRadius } from '../Tools';

const HomeView = ({ navigation, changeHomeName, parent, use_background_image, changeGuardMode, showNotify }) => {
  const context = useContext(ScreenSizeContext);
  const [onBlur, setOnBlur] = useState(false);
  const appState = useRef(AppState.currentState);

  const {
    homeData,
    showEmpty,
    setShowEmpty,
    doFetchData,
  } = useHomeData(navigation, {
    showLoader: parent.showLoader,
    hideLoader: parent.hideLoader,
    changeHomeName: changeHomeName,
  });

  useEffect(() => {
    // 确保 homeData 已加载并且 is_guard_mode 不是 undefined
    if (homeData.show && homeData.is_guard_mode !== undefined) {
      changeGuardMode(homeData.is_guard_mode);
    }

  }, [homeData.is_guard_mode]);

  useEffect(() => {
    if (homeData.show) {
      if (homeData.event) {
        console.log('homeData.event', homeData.event.id, homeData.last_event_id);
        if (homeData.event.id !== homeData.last_event_id) {
          showNotify(true);
        } else {
          showNotify(false);
        }
      }
    }
  }, [homeData.last_event_id, homeData.event]);

  // 获取当前时间对应的问候语
  const getTimeInfo = () => {
    const hour = new Date().getHours();
    if (hour < 12) {
      return { icon: 'weather-sunny', value: I18n.t('home.good_morning') };
    } else if (hour < 17) {
      return { icon: 'weather-sunny', value: I18n.t('home.good_afternoon') };
    } else {
      return { icon: 'weather-night', value: I18n.t('home.good_evening') };
    }
  };

  // 处理设备点击
  const handleDevicePress = (device) => {
    if (device.is_tuya_camera) {
      navigation.push('TuyaCameraShow', {
        title: device.display_name,
        node: device,
      });
    } else if (device.dv_type === 'ipc') {
      const scan = IpcScanner.getScanResults();
      let index = 0;
      if (HelperMemo.ipc_is_local_play) {
        index = _.findIndex(scan, function (o) { return o.sn === device.sn; });
      } else {
        index = 0;
      }

      if (index !== -1) {
        navigation.push('LocalCameraShow', {
          host: scan[index].ip,
          clientid: device.ipc.webrtc_uuid,
          node: device,
          title: I18n.t('ipc.local_play'),
        });
      } else {
        navigation.push('IpcCameraShow', {
          title: device.display_name,
          node: device,
        });
      }
    } else {
      navigation.push('deviceShow', {
        data: device,
      });
    }
  };

  // 添加控制器
  const addController = () => {
    HelperMemo.select_home = HelperMemo.user_data.home_id;
    navigation.push('WifiScreen', {
      title: I18n.t('home.add_controller'),
    });
  };

  // 隐藏空状态提示
  const hideEmptyView = () => {
    setShowEmpty('hide');
    AsyncStorage.mergeItem(
      'user_data',
      JSON.stringify({ showEmpty: 'hide' }),
      () => {
        HelperMemo.user_data.showEmpty = 'hide';
      },
    );
  };

  // 处理刷新
  const doRefreshData = () => {
    doFetchData('refresh');
  };

  // 组件挂载与卸载
  useEffect(() => {
    // 初始数据加载，传入'initial'参数表示首次加载
    doFetchData('initial');

    const errorSubscription = PubSub.subscribe(PubSubEvent.ERROR_REFETCH, () => {
      doFetchData();
    });

    // 订阅首页场景更新事件
    const homeSceneSubscription = PubSub.subscribe(PubSubEvent.EVENT_HOME_SCENE, () => {
      doFetchData();
    });

    // 通知中心订阅
    PubSub.subscribe(PubSubEvent.SELECT_WIDGET_DATA, () => {
      doFetchData();
    });

    PubSub.subscribe(PubSubEvent.LOGOUT_WIDGET, () => {
      AsyncStorage.removeItem('androidWidgetData');
      if (Platform.OS === 'android') {
        SharedStorageData.setData('');
      }
    });

    // 导航监听
    const blurListener = navigation.addListener('blur', () => {
      setOnBlur(true);
    });

    const focusListener = navigation.addListener('focus', () => {
      setOnBlur(false);
    });

    // AppState 监听
    const appStateListener = AppState.addEventListener('change', nextAppState => {
      if (nextAppState.match(/inactive|background/) && appState.current === 'active') {
        setOnBlur(true);
      } else if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        setOnBlur(false);
      }
      appState.current = nextAppState;
    });

    // 清理函数
    return () => {
      PubSub.unsubscribe(errorSubscription);
      PubSub.unsubscribe(homeSceneSubscription);
      PubSub.unsubscribe(PubSubEvent.SELECT_WIDGET_DATA);
      PubSub.unsubscribe(PubSubEvent.LOGOUT_WIDGET);

      if (blurListener) {
        blurListener();
      }
      if (focusListener) {
        focusListener();
      }
      if (appStateListener) {
        appStateListener.remove();
      }
    };

  }, []);

  // 如果数据未加载，显示骨架屏
  if (!homeData.show) {
    return <Skeletion />;
  }

  const timeInfo = getTimeInfo();

  return (
    <ScrollView
      contentInsetAdjustmentBehavior="automatic"
      onScroll={evt => parent.showTitle(evt)}
      scrollEventThrottle={16}
      style={{ flex: 1, backgroundColor: 'transparent' }}
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={homeData.refreshing}
          onRefresh={doRefreshData}
        />
      }>

      {homeData.sn && (
        <CtlUpdateAlert navigation={navigation} sn={homeData.sn} />
      )}

      {/* 天气问候组件 */}
      <WeatherDisplay
        time={timeInfo}
        userName={HelperMemo.user_data.user.name}
        useBackgroundImage={use_background_image}
      />

      {/* 状态卡片组件 */}
      <StatusCards
        state={homeData.state}
        temp={homeData.temp}
        currentScene={homeData.current_scene}
        context={context}
        useBackgroundImage={use_background_image}
      />

      {/* 内容区域 */}
      <View style={{ flex: 1, backgroundColor: 'transparent' }}>
        {/* 空状态提示 */}
        {homeData.devices.length === 0 && !homeData.sn &&
          showEmpty !== 'hide' && (
            <EmptyStateView
              onAddController={addController}
              onHide={hideEmptyView}
            />
          )
        }

        {/* 场景列表 */}
        <ScenesList
          scenes={homeData.scenes}
          context={context}
          useBackgroundImage={use_background_image}
        />

        {/* 动作列表 */}
        <ActionsList
          actions={homeData.actions}
          context={context}
          useBackgroundImage={use_background_image}
        />

        {/* IPC摄像设备部分 */}
        {homeData.ipcDevices.length > 0 && (
          <>
            <View
              style={{
                marginTop: 20,
                marginHorizontal: 20,
                backgroundColor: Tme('cardColor'),
                borderTopLeftRadius: mainRadius(),
                borderTopRightRadius: mainRadius(),
              }}>
              <TouchableOpacity
                onPress={() => {
                  navigation.push('IpcList', {
                    title: I18n.t('ipc.ipc_title'),
                  });
                }}
                activeOpacity={0.8}
                style={{
                  paddingLeft: 10,
                  paddingRight: 16,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginVertical: 10,
                }}>
                <Text
                  style={{
                    // fontSize: 1,
                    // fontWeight: '600',
                    color: Tme('cardTextColor'),
                  }}>
                  {I18n.t('ipc.ipc_title')}
                </Text>
                <MCIcons name="chevron-right" size={30} color={Tme('cardTextColor')} />
              </TouchableOpacity>
            </View>
            <View style={{ marginHorizontal: 20 }}>
              {homeData.ipcDevices.map((item, index) => (
                <IpcDeviceSection
                  key={index}
                  item={item}
                  navigation={navigation}
                  onBlur={onBlur}
                  fullBorder={index !== 0}
                />
              ))}
            </View>
          </>
        )}
        {/* 设备列表 */}
        <DevicesList
          devices={homeData.devices}
          onDevicePress={handleDevicePress}
          context={context}
          useBackgroundImage={use_background_image}
        />

        {/* 收藏设备部分 */}
        {homeData.favorDevices.length > 0 && (
          <FavoriteDevices
            devices={homeData.favorDevices}
            navigation={navigation}
            useBackgroundImage={use_background_image}
          />
        )}
      </View>
    </ScrollView>
  );
};

export default HomeView;
