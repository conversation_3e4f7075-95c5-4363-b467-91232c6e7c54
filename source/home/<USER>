import React from 'react';
import { useEffect, useRef, useState } from 'react';
import Zeroconf from 'react-native-zeroconf';
import _ from 'lodash';
import { HelperMemo } from '../Helper';
import { AppState, AppStateStatus } from 'react-native';

interface ServiceData {
  txt: {
    sn: string;
  };
  addresses: string[];
}

const BackgroundStateRe = /inactive|background/i;
const zeroconf = new Zeroconf();

export default function SnDetect(): React.ReactElement | null {
  const [isScanning, setIsScanning] = useState<boolean>(false);
  const [appState, setAppState] = useState<AppStateStatus>(AppState.currentState);
  const appStateEvent = useRef<{ remove: () => void } | null>(null);

  useEffect(() => {
    zeroconf.scan('presenctl', 'tcp', 'local.');
    startListening();
    appStateEvent.current = AppState.addEventListener('change', handleAppStateChange);
    return () => {
      zeroconf.stop();
      if (appStateEvent.current) {
        appStateEvent.current.remove();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  function handleAppStateChange(nextAppState: AppStateStatus): void {
    if (appState.match(BackgroundStateRe) && nextAppState === 'active') {
      HelperMemo.dashboard = undefined;
      if (!isScanning) {
        zeroconf.scan('presenctl', 'tcp', 'local.');
      }
    } else if (
      nextAppState.match(BackgroundStateRe) &&
      appState === 'active'
    ) {
      if (isScanning) {
        zeroconf.stop();
      }
    }
    setAppState(nextAppState);
  }

  function startListening(): void {
    zeroconf.on('start', () => {
      setIsScanning(true);
    });
    zeroconf.on('stop', () => {
      setIsScanning(false);
    });
    zeroconf.on('resolved', (service: ServiceData) => {
      const pattern = /((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}/g;
      let ip = '';
      _.forEach(service.addresses, (v) => {
        if (pattern.test(v)) {
          ip = v;
          return false;
        }
      });
      if (HelperMemo.user_data && HelperMemo.user_data.sn) {
        if (service.txt.sn === HelperMemo.user_data.sn.sn) {
          zeroconf.stop();
          console.log('stop', service.txt.sn, ip);
          HelperMemo.dashboard = {
            sn: service.txt.sn,
            ip: ip,
          };
        }
      }
    });
  }

  return null;
}
