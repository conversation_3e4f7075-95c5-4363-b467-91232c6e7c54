import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useStripe} from '@stripe/stripe-react-native';
import {useCallback, useEffect} from 'react';
import {Linking} from 'react-native';

type RootStackParamList = {
  PaymentResultScreen: {
    url: string;
  };
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'PaymentResultScreen'>;

const StripeCallBack: React.FC = () => {
  const {handleURLCallback} = useStripe();
  const navigation = useNavigation<NavigationProp>();

  const handleDeepLink = useCallback(
    async (url: string | null) => {
      if (url) {
        const stripeHandled = await handleURLCallback(url);
        if (stripeHandled) {
          navigation.push('PaymentResultScreen', {
            url: url,
          });
          // This was a Stripe URL - you can return or add extra handling here as you see fit
        } else {
          // This was NOT a Stripe URL – handle as you normally would
        }
      }
    },
    [handleURLCallback, navigation],
  );

  useEffect(() => {
    const getUrlAsync = async () => {
      const initialUrl = await Linking.getInitialURL();
      handleDeepLink(initialUrl);
    };

    getUrlAsync();

    const deepLinkListener = Linking.addEventListener('url', (event: { url: string }) => {
      handleDeepLink(event.url);
    });

    return () => deepLinkListener.remove();
  }, [handleDeepLink]);

  return null;
};

export default StripeCallBack;
