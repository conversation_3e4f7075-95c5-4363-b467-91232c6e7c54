import React, {useState} from 'react';
import {View, Text} from 'react-native';
import {Tme} from '../../ThemeStyle';
import {getImageFromKey, mainRadius, mainTitle} from '../../Tools';
import I18n from '../../I18n';
import CardView from '../../share/CardView';
import GrayscaledImage from '../../share/GrayscaledImage';
import EmptyView from '../../share/EmptyView';
import ActionBottomDrawer from '../../share/ActionBottomDrawer';
import {hideLoading, showLoading} from '../../../ILoading';
import {Helper} from '../../Helper';
import {Toast} from '../../Toast';

interface Action {
  icon: string;
  name: string;
  [key: string]: any; // 支持可能的其他属性
}

interface ActionsListProps {
  actions: Action[];
  context: {
    winWidth: number;
    [key: string]: any; // 支持可能的其他属性
  };
  useBackgroundImage?: boolean;
}

const ActionsList: React.FC<ActionsListProps> = ({
  actions,
  context,
  useBackgroundImage,
}) => {
  const [selectedAction, setSelectedAction] = useState<Action | null>(null);

  const handleActionConfirm = () => {
    if (!selectedAction) {
      return;
    }

    showLoading();
    Helper.httpPOST(
      '/partner/actions/run',
      {
        ensure: () => {
          hideLoading();
        },
        success: () => {
          Toast.show();
        },
      },
      {uuid: selectedAction.uuid},
    );
  };

  return (
    <View>
      <View style={{paddingHorizontal: 20, marginBottom: 10, marginTop: 8}}>
        <Text
          style={{
            fontSize: 12,
            // fontWeight: '500',
            marginLeft: 10,
            color: useBackgroundImage
              ? Tme('cardTextColor', 'D')
              : Tme('cardTextColor'),
          }}>
          {I18n.t('action.favorite')}
        </Text>
      </View>
      <View style={{paddingHorizontal: 16}}>
        <View
          style={{
            justifyContent: 'space-between',
            flexDirection: 'row',
            flexWrap: 'wrap',
          }}>
          {actions.length > 0 ? (
            actions.map((item, index) => (
              <View key={index} style={{marginBottom: 12, marginHorizontal: 4}}>
                <CardView
                  onChange={() => {
                    setSelectedAction(item);
                  }}
                  styles={{
                    width: context.winWidth / 2 - 26,
                    padding: 12,
                    borderRadius: mainRadius(),
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <GrayscaledImage
                    source={getImageFromKey(item.icon).value}
                    style={{width: 30, height: 30, marginRight: 8}}
                  />
                  <Text
                    numberOfLines={1}
                    style={[
                      {
                        color: Tme('cardTextColor'),
                        fontSize: mainTitle(),
                        fontWeight: '500',
                        width: context.winWidth / 2 - 100,
                      },
                    ]}>
                    {item.name}
                  </Text>
                </CardView>
              </View>
            ))
          ) : (
            <EmptyView />
          )}
        </View>
      </View>
      {selectedAction && (
        <ActionBottomDrawer
          viewHeight={250}
          title={selectedAction.name}
          desp={I18n.t('global.activate_action')}
          confirm={I18n.t('home.confirm')}
          cancel={I18n.t('home.cancel')}
          action="action"
          uuid={selectedAction.uuid}
          type="home"
          onClose={() => setSelectedAction(null)}
          onConfirm={handleActionConfirm}
        />
      )}
    </View>
  );
};

export default ActionsList;
