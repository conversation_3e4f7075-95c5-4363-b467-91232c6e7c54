import React from 'react';
import { View, Text } from 'react-native';
import { Tme } from '../../ThemeStyle';
import { getDeviceIcon, mainRadius, mainTitle } from '../../Tools';
import I18n from '../../I18n';
import CardView from '../../share/CardView';
import EmptyView from '../../share/EmptyView';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { Device } from '../../types/home';

interface Context {
  winWidth: number;
  [key: string]: any; // 允许上下文对象有其他属性
}

interface DevicesListProps {
  devices: Device[];
  onDevicePress: (device: Device) => void;
  context: Context;
  useBackgroundImage?: boolean;
}

const DevicesList: React.FC<DevicesListProps> = ({ devices, onDevicePress, context, useBackgroundImage }) => {
  return (
    <View>
      <View style={{paddingHorizontal: 16, marginBottom: 10, marginTop: 8}}>
        <Text
          style={{
            fontSize: 12,
            marginLeft: 12,
            // fontWeight: '500',
            color: useBackgroundImage
              ? Tme('cardTextColor', 'D')
              : Tme('cardTextColor'),
          }}>
          {I18n.t('home.recently_devices')}
        </Text>
      </View>
      <View style={{paddingHorizontal: 16}}>
        <View
          style={{
            justifyContent: 'space-between',
            flexDirection: 'row',
            flexWrap: 'wrap',
          }}>
          {devices.length > 0 ? (
            devices.map((item: Device, index: number) => {
              const icon = getDeviceIcon(item);
              return (
                <View
                  key={index}
                  style={{marginBottom: 12, marginHorizontal: 4}}>
                  <CardView
                    withWaveBg={true}
                    onChange={() => onDevicePress(item)}
                    styles={{
                      padding: 12,
                      borderRadius: mainRadius(),
                      height: 100,
                      width: context.winWidth / 2 - 26,
                    }}>
                    {icon}
                    <Text
                      numberOfLines={1}
                      style={[
                        {
                          color: Tme('cardTextColor'),
                          fontSize: mainTitle(),
                          marginTop: 12,
                          fontWeight: '500',
                          width: context.winWidth / 2 - 60,
                        },
                      ]}>
                      {item.display_name}
                    </Text>
                    <View style={{position: 'absolute', right: 10, bottom: 10}}>
                      {item.is_alive ? null : (
                        <Ionicons
                          name="information-circle-outline"
                          size={16}
                          color={Tme('cardTextColor')}
                        />
                      )}
                    </View>
                  </CardView>
                </View>
              );
            })
          ) : (
            <EmptyView />
          )}
        </View>
      </View>
    </View>
  );
};

export default DevicesList;
