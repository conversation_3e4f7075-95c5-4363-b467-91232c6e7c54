import React, {useState} from 'react';
import {View, Text, ViewStyle} from 'react-native';
import {Tme, Colors} from '../../ThemeStyle';
import {
  getImageFromKey,
  getSceneName,
  mainRadius,
  mainTitle,
} from '../../Tools';
import I18n from '../../I18n';
import CardView from '../../share/CardView';
import GrayscaledImage from '../../share/GrayscaledImage';
import EmptyView from '../../share/EmptyView';
import ActionBottomDrawer from '../../share/ActionBottomDrawer';
import { Toast } from '../../Toast';
import { hideLoading, showLoading } from '../../../ILoading';
import { Helper } from '../../Helper';
import { PubSubEvent } from '../../types/PubSubEvent';

interface Scene {
  icon: string;
  name: string;
  is_enabled: boolean;
  uuid: string;
}

interface Context {
  winWidth: number;
}

interface ScenesListProps {
  scenes: Scene[];
  context: Context;
  useBackgroundImage?: boolean;
}

const ScenesList: React.FC<ScenesListProps> = ({
  scenes,
  context,
  useBackgroundImage,
}) => {
  const [selectedScene, setSelectedScene] = useState<Scene | null>(null);

  const handleScenePress = (scene: Scene) => {
    setSelectedScene(scene);
  };

  const handleClose = () => {
    setSelectedScene(null);
  };

  return (
    <View>
      <View style={{paddingHorizontal: 20, marginBottom: 10}}>
        <Text
          style={{
            fontSize: 12,
            marginLeft: 10,
            color: useBackgroundImage
              ? Tme('cardTextColor', 'D')
              : Tme('cardTextColor'),
          }}>
          {I18n.t('home.favorite')}
        </Text>
      </View>
      <View style={{paddingHorizontal: 16}}>
        <View
          style={{
            justifyContent: 'space-between',
            flexDirection: 'row',
            flexWrap: 'wrap',
          }}>
          {scenes.length > 0 ? (
            scenes.map((item, index) => (
              <View key={index} style={{marginBottom: 12, marginHorizontal: 4}}>
                <CardView
                  onChange={() => handleScenePress(item)}
                  styles={[
                    {
                      width: context.winWidth / 2 - 26,
                      padding: 12,
                      borderRadius: mainRadius(),
                      flexDirection: 'row',
                      alignItems: 'center',
                      backgroundColor: Tme('cardColor2'),
                    } as ViewStyle,
                  ]}>
                  <GrayscaledImage
                    source={getImageFromKey(item.icon).value}
                    style={{width: 30, height: 30}}
                  />
                  <View style={{marginLeft: 10, position: 'relative'}}>
                    <Text
                      testID="SceenItem"
                      numberOfLines={1}
                      style={[
                        {
                          width: context.winWidth / 2 - 100,
                          color: Tme('cardTextColor'),
                          fontSize: mainTitle(),
                          fontWeight: '500',
                        },
                      ]}>
                      {getSceneName(item.name)}
                    </Text>
                    {item.is_enabled && (
                      <View
                        style={{
                          width: 6,
                          height: 6,
                          borderRadius: 4,
                          backgroundColor: Colors.MainColor,
                          position: 'absolute',
                          top: 5,
                          left: -8,
                        }}
                      />
                    )}
                  </View>
                </CardView>
              </View>
            ))
          ) : (
            <EmptyView />
          )}
        </View>
      </View>

      {selectedScene && (
        <ActionBottomDrawer
          viewHeight={250}
          title={getSceneName(selectedScene.name)}
          desp={I18n.t('global.activate_scene')}
          confirm={I18n.t('home.confirm')}
          cancel={I18n.t('home.cancel')}
          action="scene"
          uuid={selectedScene.uuid}
          type="home"
          onClose={handleClose}
          onConfirm={() => {
            // 处理场景运行
            showLoading();
            Helper.httpPOST(
              '/partner/scenes/run',
              {
                ensure: () => {
                  hideLoading();
                },
                success: () => {
                  Toast.show();
                  PubSub.publish(PubSubEvent.EVENT_HOME_SCENE);
                },
              },
              {uuid: selectedScene.uuid, type: 'home'},
            );
            handleClose();
          }}
        />
      )}
    </View>
  );
};

export default ScenesList;
