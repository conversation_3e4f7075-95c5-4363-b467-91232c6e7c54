import React, {ReactElement} from 'react';
import {View, Text, ScrollView} from 'react-native';
import {Tme, Colors} from '../../ThemeStyle';
import {getImageFromKey, getSceneName, mainRadius} from '../../Tools';
import I18n from '../../I18n';
import ShadowView from '../../share/ShadowView';
import GrayscaledImage from '../../share/GrayscaledImage';

interface Scene {
  icon: string;
  name: string;
}

interface Context {
  winWidth: number;
}

interface StatusCardsProps {
  state: boolean;
  temp?: string | number;
  currentScene?: Scene;
  context: Context;
  useBackgroundImage?: boolean;
}

const StatusCards: React.FC<StatusCardsProps> = ({
  state,
  temp,
  currentScene,
  context,
}) => {
  const renderCards = (): ReactElement[] => {
    const cards: ReactElement[] = [];

    // 控制器状态卡片
    cards.push(
      <ShadowView viewStyle={{alignSelf: 'stretch'}} key="controller_state">
        <View
          style={{
            width: context.winWidth / 3,
            paddingVertical: 6,
            paddingHorizontal: 12,
            backgroundColor: Tme('cardColor2'),
            borderRadius: mainRadius(),
            marginRight: 8,
          }}>
          <View style={{flex: 1, alignItems: 'flex-start'}}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              {state ? (
                <GrayscaledImage
                  style={{width: 30, height: 30}}
                  source={require('../../../img/home/<USER>')}
                />
              ) : (
                <GrayscaledImage
                  style={{width: 30, height: 30}}
                  source={require('../../../img/home/<USER>')}
                />
              )}
              <View style={{marginLeft: 10}}>
                <Text
                  style={[
                    {
                      fontSize: 16,
                      fontWeight: '600',
                    },
                    {
                      color: state ? Tme('cardTextColor') : Colors.GoldenColor,
                    },
                  ]}>
                  {state ? I18n.t('home.on_line') : I18n.t('home.off_line')}
                </Text>
                <Text
                  style={{
                    color: Tme('smallTextColor'),
                    marginTop: 4,
                    fontSize: 12,
                  }}>
                  {I18n.t('home.controller')}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </ShadowView>,
    );

    // 场景状态卡片
    if (currentScene) {
      cards.push(
        <ShadowView viewStyle={{alignSelf: 'stretch'}} key="current_scene">
          <View
            style={{
              width: context.winWidth / 3,
              paddingVertical: 6,
              paddingHorizontal: 12,
              backgroundColor: Tme('cardColor2'),
              borderRadius: mainRadius(),
              marginRight: 8,
            }}>
            <View
              style={{
                flex: 1,
                alignItems: 'flex-start',
              }}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <GrayscaledImage
                  source={getImageFromKey(currentScene.icon).value}
                  style={{width: 30, height: 30}}
                />
                <View style={{marginLeft: 10}}>
                  <Text
                    numberOfLines={1}
                    style={{
                      width: context.winWidth / 3 - 60,
                      fontSize: 16,
                      fontWeight: '600',
                      color: Tme('cardTextColor'),
                    }}>
                    {getSceneName(currentScene.name)}
                  </Text>
                  <Text
                    style={{
                      color: Tme('smallTextColor'),
                      marginTop: 4,
                      fontSize: 12,
                    }}>
                    {I18n.t('global.scene')}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </ShadowView>,
      );
    }

    // 温度卡片
    if (temp) {
      cards.push(
        <ShadowView viewStyle={{alignSelf: 'stretch'}} key="temperature">
          <View
            style={{
              width: context.winWidth / 3,
              paddingVertical: 6,
              paddingHorizontal: 12,
              backgroundColor: Tme('cardColor2'),
              borderRadius: mainRadius(),
              marginRight: 8,
            }}>
            <View style={{flex: 1, alignItems: 'flex-start'}}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <GrayscaledImage
                  source={require('../../../img/home/<USER>')}
                  style={{width: 30, height: 30}}
                />
                <View style={{marginLeft: 10}}>
                  <Text
                    style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: Tme('cardTextColor'),
                    }}>
                    {temp}
                  </Text>
                  <Text
                    style={{
                      color: Tme('smallTextColor'),
                      marginTop: 4,
                      fontSize: 12,
                    }}>
                    {I18n.t('spec.temperature')}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </ShadowView>,
      );
    }

    return cards;
  };

  return (
    <View
      style={{
        marginTop: 25,
        paddingHorizontal: 20,
        marginBottom: 25,
      }}>
      <ScrollView
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          flexDirection: 'row',
          alignItems: 'center',
        }}
        style={[
          {
            flex: 1,
          },
        ]}>
        {renderCards()}
      </ScrollView>
    </View>
  );
};

export default StatusCards;
