import React from 'react';
import IpcItem from '../IpcItem';
import {ParamListBase} from '@react-navigation/native';
import {getDeviceHost, getViewFrom} from '../../device/ipc/IpcHelper';
import {Device} from '../../types/home';
import {View} from 'react-native';
import {Tme} from '../../ThemeStyle';
import {mainRadius} from '../../Tools';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

interface IpcDeviceSectionProps {
  item: Device;
  navigation: NativeStackNavigationProp<ParamListBase>;
  onBlur: boolean;
  fullBorder: boolean;
  useBackgroundImage?: boolean;
}

const IpcDeviceSection: React.FC<IpcDeviceSectionProps> = ({
  item,
  navigation,
  onBlur,
  fullBorder,
}) => {
  // 获取摄像头连接数据并跳转到全屏页面
  const goToFullscreen = (data: Device): void => {
    const host = getDeviceHost(data);
    const from = getViewFrom(host);

    // 导航到全屏页面，直接传递 host 和 clientid
    navigation.push('IpcFullscreenView', {
      title: data.display_name || '',
      host: host,
      clientid: data.ipc?.webrtc_uuid,
      from: from, // cloud or local
    });
  };

  return (
    <View
      style={[
        {
          backgroundColor: Tme('cardColor'),
          overflow: 'hidden',
          marginBottom: 10,
        },
        fullBorder
          ? {
              borderRadius: mainRadius(),
            }
          : {
              borderBottomLeftRadius: mainRadius(),
              borderBottomRightRadius: mainRadius(),
            },
      ]}>
      <IpcItem
        item={item}
        from="home"
        onBlur={onBlur}
        onFullscreen={() => {
          // 导航到新的全屏页面
          goToFullscreen(item);
        }}
      />
    </View>
  );
};

export default IpcDeviceSection;
