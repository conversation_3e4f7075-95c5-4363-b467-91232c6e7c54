declare const NotificationCenter: {
  _events: Array<any>;
  init(): void;
  addObserver(object: any, eventName: number | string, callback: (argsObj?: any) => void): void;
  removeObserver(object: any, eventName: number | string): void;
  dispatchEvent(eventName: number | string, argsObj?: any): void;
};

// 事件常量
declare const EVENT_ROOM: number;
declare const EVENT_DEVICE: number;
declare const EVENT_SCENE_ELECT: number;
declare const EVENT_HOME_ADD_DEVICE: number;
declare const EVENT_DEVICE_REFRESH_VIA_HOME: number;
declare const EVENT_TIMEZONE: number;
declare const EVENT_MAP: number;
declare const EVENT_SCENE_KEY: number;
declare const WIFI_SETTING: number;
declare const EVENT_GRPC_NODE_CHANGE: number;
declare const SCHEME_EVENT: number;
declare const HOME_TOP_BAR: number;
declare const HOME_TOP_BAR_SCROLL: number;
declare const ALEXA_CALLBACK_EVENT: number;
declare const SUN_SELECT_ADD: number;
declare const TO_BAR_HOME_EVENT: number;
declare const DASHBOARD_REFRESH: number;
declare const BOTTOM_DRAWER_CLOSE: number;
declare const DISMISS_OVERLAY: number;
declare const SELECT_DEVICE_EVENT: number;
declare const EVENT_ADD_CONTROLLER: number;
declare const D433_SELECT_DEVICE: number;
declare const D433_SELECT_TYPE: number;
declare const EVENT_SELECT_DATE: number;
declare const DEVICE_NOTIFY_DATE: number;
declare const BACKGROUND_IMAGE: number;
declare const FILTER_DEVICE_EVENT: number;
declare const SELECT_LONG: number;
declare const SMART_LIST_ADD: number;
declare const CLOSE_USER: number;

export {
  NotificationCenter,
  EVENT_ROOM,
  EVENT_DEVICE,
  EVENT_SCENE_ELECT,
  EVENT_HOME_ADD_DEVICE,
  EVENT_DEVICE_REFRESH_VIA_HOME,
  EVENT_TIMEZONE,
  EVENT_MAP,
  EVENT_SCENE_KEY,
  WIFI_SETTING,
  EVENT_GRPC_NODE_CHANGE,
  SCHEME_EVENT,
  HOME_TOP_BAR,
  HOME_TOP_BAR_SCROLL,
  ALEXA_CALLBACK_EVENT,
  SUN_SELECT_ADD,
  TO_BAR_HOME_EVENT,
  DASHBOARD_REFRESH,
  DISMISS_OVERLAY,
  SELECT_DEVICE_EVENT,
  EVENT_ADD_CONTROLLER,
  D433_SELECT_DEVICE,
  D433_SELECT_TYPE,
  EVENT_SELECT_DATE,
  DEVICE_NOTIFY_DATE,
  BACKGROUND_IMAGE,
  FILTER_DEVICE_EVENT,
  SELECT_LONG,
  SMART_LIST_ADD,
  CLOSE_USER,
};
