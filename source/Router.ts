import {HelperMemo} from './Helper';

interface NavigationProps {
  push: (screen: string, params: any) => void;
}

interface PassProps {
  [key: string]: any;
}

export const Router = {
  push: (navigation: NavigationProps, passProps: PassProps, screen: string) => {
    navigation.push(screen, {
      ...passProps,
    });
  },
  pushDeviceShow: (navigation: NavigationProps, passProps: PassProps) => {
    Router.push(navigation, passProps, 'deviceShow');
  },
};

export const isOwnerOrAdmin = (): boolean => {
  if (HelperMemo.user_data) {
    return (
      HelperMemo.user_data.role === 'owner' ||
      HelperMemo.user_data.role === 'admin'
    );
  }
  return false;
};

export const isOwner = (): boolean => {
  if (HelperMemo.user_data) {
    return HelperMemo.user_data.role === 'owner';
  }
  return false;
};
