import {NativeStackNavigationProp} from '@react-navigation/native-stack';

export interface BaseItem {
  uuid: string;
  name: string;
  type?: string;
  is_favor?: boolean;
  is_enabled?: boolean;
  target_length?: number;
  icon?: string;
}

export interface SceneItem extends BaseItem {
  system?: boolean;
}

export interface ActionItem extends BaseItem {
  target_type?: string;
  targets?: any[];
}

export interface AutomationItem extends BaseItem {
  type: string;
  is_cycle?: boolean;
  is_returned?: boolean;
  location?: string;
  week_day?: number[];
  begin_at?: string;
  end_at?: string;
  target_type?: string;
  target_name?: string;
  targets?: any[];
  app_url?: string;
  is_sunrise?: boolean;
}

export interface SmartScreenProps {
  navigation: NativeStackNavigationProp<any>;
}

export interface TabRoute {
  key: string;
  title: string;
}

export interface ScreenSizeContextType {
  width: number;
  height: number;
}

export interface CardViewProps {
  withWaveBg?: boolean;
  showMenu?: boolean;
  onChange?: () => void;
  styles?: any[];
  children?: React.ReactNode;
}

export interface ActionSheetProps {
  ref: React.RefObject<any>;
  options: string[];
  cancelButtonIndex: number;
  destructiveButtonIndex: number;
  onPress: (index: number) => void;
  userInterfaceStyle?: 'light' | 'dark';
  theme?: string;
  styles?: any;
}
