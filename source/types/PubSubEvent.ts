export enum PubSubEvent {
  EVENT_LOGGED_IN = 'EVENT_LOGGED_IN',
  EVENT_MEMBER_CATE_SUCCESS = 'EVENT_MEMBER_CATE_SUCCESS',
  EVENT_ADD_SETTING_ROOM_SUCCESS = 'EVENT_ADD_SETTING_ROOM_SUCCESS',
  EVENT_SCENE = 'EVENT_SCENE',
  EVENT_ACTION = 'EVENT_ACTION',
  EVENT_USER = 'EVENT_USER',
  EVENT_ROOM = 'EVENT_ROOM',
  EVENT_DEVICE = 'EVENT_DEVICE',
  EVENT_SCENE_ELECT = 'EVENT_SCENE_ELECT',
  EVENT_HOME_ADD_DEVICE = 'EVENT_HOME_ADD_DEVICE',
  EVENT_DEVICE_REFRESH_VIA_HOME = 'EVENT_DEVICE_REFRESH_VIA_HOME',
  EVENT_TIMEZONE = 'EVENT_TIMEZONE',
  EVENT_ROUTINE = 'EVENT_ROUTINE',
  EVENT_MAP = 'EVENT_MAP',
  EVENT_HOME_SCENE = 'EVENT_HOME_SCENE',
  EVENT_SCENE_KEY = 'EVENT_SCENE_KEY',
  WIFI_SETTING = 'WIFI_SETTING',
  EVENT_GRPC_NODE_CHANGE = 'EVENT_GRPC_NODE_CHANGE',
  EVENT_GRPC_CONTROLLER_CHANGE = 'EVENT_GRPC_CONTROLLER_CHANGE',
  SCHEME_EVENT = 'SCHEME_EVENT',
  HOME_TOP_BAR = 'HOME_TOP_BAR',
  HOME_TOP_BAR_SCROLL = 'HOME_TOP_BAR_SCROLL',
  ALEXA_CALLBACK_EVENT = 'ALEXA_CALLBACK_EVENT',
  SUN_SELECT_ADD = 'SUN_SELECT_ADD',
  EVENT_GUARD = 'EVENT_GUARD',
  SMART_SELECT_EVENT = 'SMART_SELECT_EVENT',
  TO_BAR_HOME_EVENT = 'TO_BAR_HOME_EVENT',
  DASHBOARD_REFRESH = 'DASHBOARD_REFRESH',
  BOTTOM_DRAWER_CLOSE = 'BOTTOM_DRAWER_CLOSE',
  DISMISS_OVERLAY = 'DISMISS_OVERLAY',
  SELECT_DEVICE_EVENT = 'SELECT_DEVICE_EVENT',
  EVENT_ADD_CONTROLLER = 'EVENT_ADD_CONTROLLER',
  D433_SELECT_DEVICE = 'D433_SELECT_DEVICE',
  D433_SELECT_TYPE = 'D433_SELECT_TYPE',
  EVENT_SELECT_DATE = 'EVENT_SELECT_DATE',
  DEVICE_NOTIFY_DATE = 'DEVICE_NOTIFY_DATE',
  BACKGROUND_IMAGE = 'BACKGROUND_IMAGE',
  CHANGE_BACKGROUND_IMAGE = 'CHANGE_BACKGROUND_IMAGE',
  FILTER_DEVICE_EVENT = 'FILTER_DEVICE_EVENT',
  EVENT_ADD_CONTROLLER_SCAN = 'EVENT_ADD_CONTROLLER_SCAN',
  SELECT_SCENE_ICON = 'SELECT_SCENE_ICON',
  SELECT_LONG = 'SELECT_LONG',
  SELECT_WIDGET_DATA = 'SELECT_WIDGET_DATA',
  LOGOUT_WIDGET = 'LOGOUT_WIDGET',
  SMART_LIST_ADD = 'SMART_LIST_ADD',
  SELECT_COLOR_DONE = 'SELECT_COLOR_DONE',
  CLOSE_USER = 'CLOSE_USER',
  SCAN_CODE = 'SCAN_CODE',
  RESTART_APP = 'RESTART_APP',
  SHOW_LOADING = 'SHOW_LOADING',
  ERROR_REFETCH = 'ERROR_REFETCH',
  DASHBOARD_EVENT = 'DASHBOARD_EVENT',
  EVENT_APP_STATE_ACTIVE = 'EVENT_APP_STATE_ACTIVE',
  EVENT_APP_STATE_BACKGROUND = 'EVENT_APP_STATE_BACKGROUND',
  EVENT_APP_NOTIFY = 'EVENT_APP_NOTIFY',
  ipc_scene = 'ipc_scene',
  ipc_wday = 'ipc_wday',
  ios_speak = 'ios_speak',
  ios_sound = 'ios_sound',
  ios_picture = 'ios_picture',
  playRemotePause = 'playRemotePause',
  ios_record = 'ios_record',
  getFileList = 'getFileList',
  playRemoteFile = 'playRemoteFile',
  PUBSUB_SELECT_DEVICE_EVENT = 'PUBSUB_SELECT_DEVICE_EVENT',
  ERROR_RE = 'ERROR_RE',
  SHOW_ERROR = 'SHOW_ERROR',
  HIDE_ERROR = 'HIDE_ERROR',
}

// 直接导出各个枚举成员，方便单独导入使用
export const EVENT_LOGGED_IN = PubSubEvent.EVENT_LOGGED_IN;
export const EVENT_MEMBER_CATE_SUCCESS = PubSubEvent.EVENT_MEMBER_CATE_SUCCESS;
export const EVENT_ADD_SETTING_ROOM_SUCCESS = PubSubEvent.EVENT_ADD_SETTING_ROOM_SUCCESS;
export const EVENT_SCENE = PubSubEvent.EVENT_SCENE;
export const EVENT_ACTION = PubSubEvent.EVENT_ACTION;
export const EVENT_USER = PubSubEvent.EVENT_USER;
export const EVENT_ROOM = PubSubEvent.EVENT_ROOM;
export const EVENT_DEVICE = PubSubEvent.EVENT_DEVICE;
export const EVENT_SCENE_ELECT = PubSubEvent.EVENT_SCENE_ELECT;
export const EVENT_HOME_ADD_DEVICE = PubSubEvent.EVENT_HOME_ADD_DEVICE;
export const EVENT_DEVICE_REFRESH_VIA_HOME = PubSubEvent.EVENT_DEVICE_REFRESH_VIA_HOME;
export const EVENT_TIMEZONE = PubSubEvent.EVENT_TIMEZONE;
export const EVENT_ROUTINE = PubSubEvent.EVENT_ROUTINE;
export const EVENT_MAP = PubSubEvent.EVENT_MAP;
export const EVENT_HOME_SCENE = PubSubEvent.EVENT_HOME_SCENE;
export const EVENT_SCENE_KEY = PubSubEvent.EVENT_SCENE_KEY;
export const WIFI_SETTING = PubSubEvent.WIFI_SETTING;
export const EVENT_GRPC_NODE_CHANGE = PubSubEvent.EVENT_GRPC_NODE_CHANGE;
export const EVENT_GRPC_CONTROLLER_CHANGE = PubSubEvent.EVENT_GRPC_CONTROLLER_CHANGE;
export const SCHEME_EVENT = PubSubEvent.SCHEME_EVENT;
export const HOME_TOP_BAR = PubSubEvent.HOME_TOP_BAR;
export const HOME_TOP_BAR_SCROLL = PubSubEvent.HOME_TOP_BAR_SCROLL;
export const ALEXA_CALLBACK_EVENT = PubSubEvent.ALEXA_CALLBACK_EVENT;
export const SUN_SELECT_ADD = PubSubEvent.SUN_SELECT_ADD;
export const EVENT_GUARD = PubSubEvent.EVENT_GUARD;
export const SMART_SELECT_EVENT = PubSubEvent.SMART_SELECT_EVENT;
export const TO_BAR_HOME_EVENT = PubSubEvent.TO_BAR_HOME_EVENT;
export const DASHBOARD_REFRESH = PubSubEvent.DASHBOARD_REFRESH;
export const BOTTOM_DRAWER_CLOSE = PubSubEvent.BOTTOM_DRAWER_CLOSE;
export const DISMISS_OVERLAY = PubSubEvent.DISMISS_OVERLAY;
export const SELECT_DEVICE_EVENT = PubSubEvent.SELECT_DEVICE_EVENT;
export const EVENT_ADD_CONTROLLER = PubSubEvent.EVENT_ADD_CONTROLLER;
export const D433_SELECT_DEVICE = PubSubEvent.D433_SELECT_DEVICE;
export const D433_SELECT_TYPE = PubSubEvent.D433_SELECT_TYPE;
export const EVENT_SELECT_DATE = PubSubEvent.EVENT_SELECT_DATE;
export const DEVICE_NOTIFY_DATE = PubSubEvent.DEVICE_NOTIFY_DATE;
export const BACKGROUND_IMAGE = PubSubEvent.BACKGROUND_IMAGE;
export const CHANGE_BACKGROUND_IMAGE = PubSubEvent.CHANGE_BACKGROUND_IMAGE;
export const FILTER_DEVICE_EVENT = PubSubEvent.FILTER_DEVICE_EVENT;
export const EVENT_ADD_CONTROLLER_SCAN = PubSubEvent.EVENT_ADD_CONTROLLER_SCAN;
export const SELECT_SCENE_ICON = PubSubEvent.SELECT_SCENE_ICON;
export const SELECT_LONG = PubSubEvent.SELECT_LONG;
export const SELECT_WIDGET_DATA = PubSubEvent.SELECT_WIDGET_DATA;
export const LOGOUT_WIDGET = PubSubEvent.LOGOUT_WIDGET;
export const SMART_LIST_ADD = PubSubEvent.SMART_LIST_ADD;
export const SELECT_COLOR_DONE = PubSubEvent.SELECT_COLOR_DONE;
export const CLOSE_USER = PubSubEvent.CLOSE_USER;
export const SCAN_CODE = PubSubEvent.SCAN_CODE;
export const RESTART_APP = PubSubEvent.RESTART_APP;
export const SHOW_LOADING = PubSubEvent.SHOW_LOADING;
export const ERROR_REFETCH = PubSubEvent.ERROR_REFETCH;
export const DASHBOARD_EVENT = PubSubEvent.DASHBOARD_EVENT;
export const EVENT_APP_STATE_ACTIVE = PubSubEvent.EVENT_APP_STATE_ACTIVE;
export const EVENT_APP_STATE_BACKGROUND = PubSubEvent.EVENT_APP_STATE_BACKGROUND;
export const EVENT_APP_NOTIFY = PubSubEvent.EVENT_APP_NOTIFY;
export const ipc_scene = PubSubEvent.ipc_scene;
export const ipc_wday = PubSubEvent.ipc_wday;
export const ios_speak = PubSubEvent.ios_speak;
export const ios_sound = PubSubEvent.ios_sound;
export const ios_picture = PubSubEvent.ios_picture;
export const playRemotePause = PubSubEvent.playRemotePause;
export const ios_record = PubSubEvent.ios_record;
export const getFileList = PubSubEvent.getFileList;
export const playRemoteFile = PubSubEvent.playRemoteFile;
export const PUBSUB_SELECT_DEVICE_EVENT = PubSubEvent.PUBSUB_SELECT_DEVICE_EVENT;
export const ERROR_RE = PubSubEvent.ERROR_RE;
export const SHOW_ERROR = PubSubEvent.SHOW_ERROR;
export const HIDE_ERROR = PubSubEvent.HIDE_ERROR;
