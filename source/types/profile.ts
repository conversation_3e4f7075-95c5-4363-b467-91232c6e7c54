import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RouteProp} from '@react-navigation/native';
import {User} from './home';

/**
 * Profile 相关路由参数列表
 */
export type ProfileRouteParamList = {
  ChangeUserName: {
    title: string;
  };
  profileScreen: {
    title: string;
  };
  ProfileList: undefined;
  ChangeDatacenter: {
    title: string;
  };
  ChangeScale: {
    title: string;
  };
  TimezoneScreen: {
    title: string;
  };
  ThemeScreen: {
    title: string;
    theme: string;
  };
};

/**
 * Profile 导航属性类型
 */
export type ProfileNavigationProp = NativeStackNavigationProp<ProfileRouteParamList>;

/**
 * ChangeUserName 组件的路由属性
 */
export type ChangeUserNameRouteProp = RouteProp<ProfileRouteParamList, 'ChangeUserName'>;

/**
 * ProfileScreen 组件的路由属性
 */
export type ProfileScreenRouteProp = RouteProp<ProfileRouteParamList, 'profileScreen'>;

/**
 * ChangeUserName 组件的 Props 接口
 */
export interface ChangeUserNameProps {
  navigation: ProfileNavigationProp;
  route: ChangeUserNameRouteProp;
}

/**
 * ProfileScreen 组件的 Props 接口
 */
export interface ProfileScreenProps {
  navigation: ProfileNavigationProp;
  route: ProfileScreenRouteProp;
}

/**
 * ChangeUserName 组件的状态接口
 */
export interface ChangeUserNameState {
  name: string;
}

/**
 * ProfileScreen 组件的状态接口
 */
export interface ProfileScreenState {
  name: string;
  old_password: string;
  new_password: string;
  password_confirmation: string;
  cloud: boolean;
}

/**
 * API 响应数据接口
 */
export interface ProfileApiResponse {
  user: User;
}

/**
 * 修改用户名请求体接口
 */
export interface ChangeNameRequestBody {
  name: string;
}

/**
 * 修改密码请求体接口
 */
export interface ChangePasswordRequestBody {
  old: string;
  new: string;
  conf: string;
}

/**
 * 表单验证错误类型
 */
export type ValidationError = string;

/**
 * 密码验证规则
 */
export const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/;

/**
 * 表单验证函数类型
 */
export type ValidationFunction<T> = (value: T) => ValidationError[];
