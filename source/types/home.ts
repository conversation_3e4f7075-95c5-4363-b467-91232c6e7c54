// 设备类型定义
export interface Device {
  id: string;
  sn_id: string | null;
  uuid: string;
  home_id: string;
  index: string;
  dv_type: string;
  is_failed: boolean;
  long_id: string | null;
  security_caps: string[];
  last_received_update_time: number;
  ipc_last_event_image_url?: string;
  last_view_at: number;
  config_commands: any[];
  vendor: string | null;
  display_name: string;
  cc_specs: CCSpec[];
  is_alive: boolean;
  is_tuya_camera: boolean;
  icon_bg_color: string;
  favors: string[];
  sn: string;
  ipc?: {
    webrtc_host: string;
    webrtc_uuid: string;
  };
}

export interface CCSpec {
  device_id: string;
  instance_id: string;
  cc: string;
  value_id: string;
  long_id: string;
  name: string;
  value: string;
  value_hash: Record<string, any>;
  real_value: number;
  spec_type: number;
  dv_type: string;
  u_t: number;
  version: string;
  options: Array<{
    name: string;
    val: number;
  }>;
  max: number;
  min: number;
  battery_charge_text?: string;
  scale?: string;
}

// 场景类型定义
export interface Scene {
  id: string;
  name: string;
  uuid: string;
  is_favor: boolean;
  icon: string | null;
  favor_updated_timestamp: number;
  created_at: string;
  updated_at: string;
  target_length: string;
  is_enabled: boolean;
  targets: Target[];
  widget_type: string;
}

// 动作类型定义
export interface Action {
  id: string;
  name: string;
  uuid: string;
  is_favor: boolean;
  icon: string | null;
  favor_updated_timestamp: number;
  created_at: string;
  updated_at: string;
  target_length: string;
  targets: Target[];
  widget_type: string;
}

// 用户类型定义
export interface User {
  id: string;
  email: string;
  cellphone: string | null;
  zone: string;
  name: string;
  uuid: string;
  locale: string;
  timezone: string;
  dc_name: string;
  scale: string;
  dc_config: {
    cloud: {
      api_path: string;
      host: string;
    };
  };
  last_user_event_id: string;
  identity: string;
  stripe_customer_id: string | null;
  customer_name: string | null;
  customer_email: string | null;
  sub_type: string;
  sub_expire_ts: number;
  sub_file_days: number;
  is_trailed: boolean;
  sub_status: string;
  sub_expire_at: string;
}

// 主数据类型定义
export interface HomeData {
  is_guard_mode: boolean;
  state: boolean | null;
  event: Event | null;
  time: string;
  device_size: string;
  scenes: Scene[];
  actions: Action[];
  devices: Device[];
  ipcDevices: Device[];
  favorDevices: Device[];
  temp: string;
  humidity: string;
  show: boolean;
  sn: Sn | null;
  current_scene: Scene | null;
  refreshing: boolean;
  widget_data: WidgetData[];
  last_event_id?: string;
  user?: UserData;
  home?: Home;
  gps_routines?: GpsRoutine[];
}

// 导航类型定义
export interface HomeNavigation {
  push: (screen: string, params: any) => void;
}

// 加载器类型定义
export interface LoaderProps {
  showLoader: () => void;
  hideLoader: () => void;
  changeHomeName: (name: string) => void;
}

interface Sn {
  id: string;
  sn: string;
  salt: string;
  version: string | null;
  sn_tags: string[];
  name: string;
  state: boolean;
  display_name: string;
}

interface UserData {
  sn: Sn;
  user: User;
  is_demo: boolean;
  role: string;
  home_id: string;
}

interface Home {
  id: string;
  name: string;
}

interface WidgetData {
  uuid: string;
  icon: string | null;
  is_enabled: boolean;
  name: string;
  widget_type: string;
}

interface Event {
  id: string;
  event_type: string;
  event_sub_type: string | null;
  event_uuid: string;
  event_name: string;
  value_id: string;
  spec_name: string;
  spec_scale: string | null;
  spec_value: string;
  data: any;
  u_t: number;
  date: string;
  time: string;
  time_ago: string;
  unit_index: string | null;
  ipc_screenshot_url?: string;
  ipc_video_success?: boolean;
  ipc_video_url?: string;
}

interface Target {
  id: string;
  uuid: string | null;
  device_uuid: string;
  value_id: string;
  node_id: string;
  spec_cc: string;
  params: string;
  instance_id: string;
  spec_name: string;
  spec_value: string;
  logic: any;
  commands: Array<{
    type: string;
    cmd: string;
    params1: string;
    params2: string;
    sn: string | null;
    sn_id: string | null;
    home_id: string;
  }>;
  delay: number;
  desp: string;
  app_url: string;
  target_type: string;
  home_id: string;
  sn_id: string | null;
  created_at: string | null;
  updated_at: string | null;
}

interface GpsRoutine {
  id: string;
  uuid: string;
  type: string;
  is_cycle: boolean;
  is_sunrise: boolean;
  week_day: number[];
  begin_at: string;
  end_at: string;
  location: string;
  location_info: string;
  is_returned: boolean;
  scene_ids: string[];
  target_type: string;
  target_uuid: string;
  name: string;
  is_enabled: boolean;
  is_triggered: boolean;
  created_at: string;
  updated_at: string;
  home_id: string;
  target_name: string;
  targets: any[];
  conditions: any[];
}
