import React, {useEffect} from 'react';
import DeviceInfo from 'react-native-device-info';
import {Linking} from 'react-native';
import I18n from 'react-native-i18n';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {HelperMemo} from './Helper';
import axios from 'axios';
import AlertModal from './share/AlertModal';

const UpdateApp: React.FC = () => {
  const skipVersion = async (v: string) => {
    await AsyncStorage.setItem('ios_skip_version', v);
  };

  const getVersion = async () => {
    try {
      const response = await axios.post('https://itunes.apple.com/lookup?id=1171646098');
      const responseJson = response.data;
      const appleVersion = responseJson.results[0].version;
      console.log(appleVersion, '苹果应用商店版本');

      const v = DeviceInfo.getVersion();
      const skippedVersion = await AsyncStorage.getItem('ios_skip_version');

      if (skippedVersion !== appleVersion) {
        if (v.localeCompare(appleVersion) === -1) {
          if (HelperMemo.user_data.appUpdateNotify !== appleVersion) {
            AsyncStorage.mergeItem(
              'user_data',
              JSON.stringify({appUpdateNotify: appleVersion}),
              () => {
                HelperMemo.user_data.appUpdateNotify = appleVersion;
              },
            );

            AlertModal.alert(
              I18n.t('home.have_version'),
              I18n.t('home.vs_update'),
              [
                {
                  text: I18n.t('home.confirm'),
                  onPress: () => {
                    Linking.openURL(responseJson.results[0].trackViewUrl)
                      .catch(err => console.error('An error occurred', err));
                  },
                },
                {text: I18n.t('home.cancel')},
                {
                  text: I18n.t('home.skip_vs'),
                  onPress: () => skipVersion(appleVersion),
                },
              ],
            );
          }
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getVersion();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return null;
};

export default UpdateApp;
