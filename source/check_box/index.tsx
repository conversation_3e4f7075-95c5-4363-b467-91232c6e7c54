import React, {useCallback} from 'react';
import {StyleSheet, View, Image, Text, TouchableHighlight, ViewStyle, TextStyle} from 'react-native';

interface CheckBoxProps {
  leftTextView?: React.ReactElement;
  leftText?: string;
  rightTextView?: React.ReactElement;
  rightText?: string;
  leftTextStyle?: TextStyle;
  rightTextStyle?: TextStyle;
  checkedImage?: React.ReactElement;
  unCheckedImage?: React.ReactElement;
  indeterminateImage?: React.ReactElement;
  isChecked?: boolean;
  isIndeterminate?: boolean;
  checkedCheckBoxColor?: string;
  uncheckedCheckBoxColor?: string;
  checkBoxColor?: string;
  style?: ViewStyle;
  disabled?: boolean;
  onClick: () => void;
}

const CheckBox: React.FC<CheckBoxProps> = ({
  leftTextView,
  leftText,
  rightTextView,
  rightText,
  leftTextStyle = {},
  rightTextStyle = {},
  checkedImage,
  unCheckedImage,
  indeterminateImage,
  isChecked = false,
  isIndeterminate = false,
  checkedCheckBoxColor,
  uncheckedCheckBoxColor,
  checkBoxColor,
  style,
  disabled,
  onClick,
}) => {
  const getCheckedCheckBoxColor = useCallback((): string => {
    return checkedCheckBoxColor ? checkedCheckBoxColor : checkBoxColor ?? '';
  }, [checkedCheckBoxColor, checkBoxColor]);

  const getUncheckedCheckBoxColor = useCallback((): string => {
    return uncheckedCheckBoxColor ? uncheckedCheckBoxColor : checkBoxColor ?? '';
  }, [uncheckedCheckBoxColor, checkBoxColor]);

  const getTintColor = useCallback((): string => {
    return isChecked ? getCheckedCheckBoxColor() : getUncheckedCheckBoxColor();
  }, [isChecked, getCheckedCheckBoxColor, getUncheckedCheckBoxColor]);

  const renderLeft = useCallback((): React.ReactElement | null => {
    if (leftTextView) {
      return leftTextView;
    }
    if (!leftText) {
      return null;
    }
    return (
      <Text style={[styles.leftText, leftTextStyle]}>
        {leftText}
      </Text>
    );
  }, [leftTextView, leftText, leftTextStyle]);

  const renderRight = useCallback((): React.ReactElement | null => {
    if (rightTextView) {
      return rightTextView;
    }
    if (!rightText) {
      return null;
    }
    return (
      <Text style={[styles.rightText, rightTextStyle]}>
        {rightText}
      </Text>
    );
  }, [rightTextView, rightText, rightTextStyle]);

  const genCheckedImage = useCallback((): React.ReactElement => {
    let source;
    if (isIndeterminate) {
      source = require('../../img/check_box/ic_indeterminate_check_box.png');
    } else {
      source = isChecked
        ? require('../../img/check_box/ic_check_box.png')
        : require('../../img/check_box/ic_check_box_outline_blank.png');
    }

    return <Image source={source} style={{tintColor: getTintColor()}} />;
  }, [isIndeterminate, isChecked, getTintColor]);

  const renderImage = useCallback((): React.ReactElement => {
    if (isIndeterminate) {
      return indeterminateImage ? indeterminateImage : genCheckedImage();
    }
    if (isChecked) {
      return checkedImage ? checkedImage : genCheckedImage();
    } else {
      return unCheckedImage ? unCheckedImage : genCheckedImage();
    }
  }, [isIndeterminate, indeterminateImage, isChecked, checkedImage, unCheckedImage, genCheckedImage]);

  return (
    <TouchableHighlight
      style={style}
      onPress={onClick}
      underlayColor="transparent"
      disabled={disabled}>
      <View style={styles.container}>
        {renderLeft()}
        {renderImage()}
        {renderRight()}
      </View>
    </TouchableHighlight>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftText: {
    flex: 1,
  },
  rightText: {
    flex: 1,
    marginLeft: 10,
  },
});

export default CheckBox;
