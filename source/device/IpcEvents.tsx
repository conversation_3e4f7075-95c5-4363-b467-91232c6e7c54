import React, {useEffect, useRef, useState, useMemo, useCallback} from 'react';
import {
  Text,
  View,
  StyleSheet,
  SectionList,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
  Linking,
  ScrollView,
  RefreshControl,
  Alert,
  ViewStyle,
  StyleProp,
  Platform,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {Helper} from '../Helper';
import I18n from '../I18n';
import {Tme, Colors} from '../ThemeStyle';
import {hideLoading, showLoading} from '../../ILoading';
import {useNavigation} from '@react-navigation/native';
import ImageViewer from 'react-native-image-zoom-viewer';
import AlertModal from '../share/AlertModal';
import {hasSavePermission} from '../Util';
import {DownloadImage} from '../event/DownLoadImage';
import {Chip} from 'react-native-paper';
import {IpcNavigationProp} from './ipc/types/navigation';
import BgAnimateView from '../share/BgAnimateView';
import BottomUpAnimateView from '../share/BottomUpAnimateView';
import {mainRadius} from '../Tools';
import ImagePreviewView from './ImagePreviewViewProps';

const HIT_SLOP = {top: 16, left: 16, bottom: 16, right: 16};

interface Event {
  id: string;
  date: string;
  event_name: string;
  event_sub_type: string | null;
  event_type: string;
  event_uuid: string;
  ipc_screenshot_url: string;
  ipc_video_success: boolean;
  ipc_video_url: string;
  spec_name: string;
  spec_scale: string | null;
  spec_value: string;
  time: string;
  time_ago: string;
  u_t: number;
  unit_index: number | null;
  value_id: string;
  created_at: string;
  rekognition_labels: string[];
}

// 添加EventSection接口用于分组数据
interface EventSection {
  title: string; // 日期标题
  data: Event[]; // 该日期下的事件列表
}

// interface ImageLoadingProps {
//   height: number;
//   uri: string;
// }

interface ImageShowProps {
  images: Array<{url: string}>;
  open: boolean;
  onRequestClose: () => void;
  onSaveImage: (uri: string) => void;
}

interface FilterOption {
  id: string;
  label: string;
  value: string;
}

interface FilterPanelProps {
  visible: boolean;
  devices: Array<{uuid: string; name: string}>;
  onClose: () => void;
  // 移除 onTypeSelect 和 onDeviceSelect，因为子组件内部处理临时状态
  // onTypeSelect: (id: string) => void;
  // onDeviceSelect: (id: string) => void;
  onApplyFilter: (types: string[], device: string) => void; // 修改 onApplyFilter 签名
  onReset: () => void;
  selectedTypes: string[];
  selectedDevice: string;
}

// 类型筛选项
const typeFilterOptions: FilterOption[] = [
  {id: 'package', label: 'package', value: 'package'},
  {id: 'person', label: 'person', value: 'person'},
  {id: 'car', label: 'car', value: 'car'},
  {id: 'animal', label: 'animal', value: 'animal'},
];

interface LabelChipProps {
  label: string;
  backgroundColor: string;
  textColor: string;
  style?: StyleProp<ViewStyle>;
}

const LabelChip: React.FC<LabelChipProps> = React.memo(
  ({label, backgroundColor, textColor, style}) => {
    return (
      <View
        style={[
          {
            backgroundColor,
            paddingHorizontal: 12,
            paddingVertical: 4,
            borderRadius: mainRadius(),
            alignItems: 'center',
            justifyContent: 'center',
          },
          style,
        ]}>
        <Text
          style={{
            color: textColor,
            fontSize: 10,
            includeFontPadding: false,
          }}>
          {label}
        </Text>
      </View>
    );
  },
);

// 添加图片预览组件
const ImageShow = React.memo(
  ({images, open, onRequestClose, onSaveImage}: ImageShowProps) => {
    return (
      <Modal
        visible={open}
        transparent={true}
        animationType="fade"
        presentationStyle="overFullScreen"
        statusBarTranslucent
        navigationBarTranslucent
        onRequestClose={() => {
          onRequestClose();
        }}>
        <ImageViewer
          // eslint-disable-next-line react/no-unstable-nested-components
          loadingRender={() => (
            <ActivityIndicator size="small" color={Colors.MainColor} />
          )}
          imageUrls={images}
          saveToLocalByLongPress={false}
          renderIndicator={() => <View />}
        />
        <View
          style={{position: 'absolute', width: Dimensions.get('window').width}}>
          <View
            style={{
              marginTop: Platform.OS === 'ios' ? 58 : 40,
              paddingHorizontal: 20,
              alignItems: 'center',
              justifyContent: 'space-between',
              flexDirection: 'row',
            }}>
            <TouchableOpacity
              activeOpacity={0.8}
              style={{
                width: 45,
                height: 45,
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onPress={() => {
                onRequestClose();
              }}
              hitSlop={HIT_SLOP}>
              <Text
                style={{
                  lineHeight: 25,
                  fontSize: 25,
                  includeFontPadding: false,
                  color: Colors.MainColor,
                }}>
                ✕
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.8}
              style={{
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onPress={() => {
                AlertModal.alert(I18n.t('event.save_image'), '', [
                  {
                    text: I18n.t('home.cancel'),
                    onPress: () => {},
                    style: 'cancel',
                  },
                  {
                    text: I18n.t('home.confirm'),
                    onPress: () => {
                      onSaveImage(images[0].url);
                    },
                  },
                ]);
              }}
              hitSlop={HIT_SLOP}>
              <Text
                style={{
                  lineHeight: 25,
                  includeFontPadding: false,
                  color: Colors.MainColor,
                }}>
                {I18n.t('event.save_image')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  },
);

// 筛选面板组件
const FilterPanel = React.memo(
  ({
    visible,
    onClose,
    onApplyFilter, // 使用修改后的签名
    onReset,
    selectedTypes,
    selectedDevice,
    devices,
  }: FilterPanelProps) => {
    const bgAnimateRef = useRef<any>(null);
    const upAnimateRef = useRef<any>(null);
    // 添加临时状态来存储选择，只有点击确认时才应用
    const [tempSelectedTypes, setTempSelectedTypes] = useState<string[]>([]);
    const [tempSelectedDevice, setTempSelectedDevice] = useState<string>('');

    useEffect(() => {
      if (visible) {
        // 每次显示时使用父组件的当前状态初始化临时状态
        setTempSelectedTypes([...selectedTypes]);
        setTempSelectedDevice(selectedDevice);
        upAnimateRef.current?.start();
        bgAnimateRef.current?.start();
      }
    }, [visible, selectedTypes, selectedDevice]);

    const handleClose = () => {
      upAnimateRef.current?.close();
      bgAnimateRef.current?.close();
      // 给动画一点时间完成
      setTimeout(() => {
        onClose();
      }, 200);
    };

    // 处理应用筛选器点击
    const handleApplyFilter = () => {
      // 调用父组件传入的 onApplyFilter，并传递当前的临时状态
      // 即使没有选择任何筛选条件，也要正确传递当前状态
      onApplyFilter(tempSelectedTypes, tempSelectedDevice);
      handleClose();
    };

    // 处理重置筛选器点击
    const handleReset = () => {
      // 清空临时状态
      setTempSelectedTypes([]);
      setTempSelectedDevice('');
      // 调用父组件的 onReset
      onReset();
      handleClose(); // 关闭面板
    };

    // 处理类型选择 - 更新临时状态
    const handleTypeSelect = (id: string) => {
      setTempSelectedTypes(prev =>
        prev.includes(id) ? prev.filter(type => type !== id) : [...prev, id],
      );
    };

    // 处理设备选择 - 更新临时状态
    const handleDeviceSelect = (id: string) => {
      setTempSelectedDevice(prev => (prev === id ? '' : id));
    };

    return (
      <Modal visible={visible} transparent={true} onRequestClose={handleClose}>
        <View style={StyleSheet.absoluteFill}>
          <BgAnimateView
            ref={bgAnimateRef}
            style={StyleSheet.absoluteFillObject}>
            <TouchableOpacity
              style={StyleSheet.absoluteFill}
              onPress={handleClose}
              activeOpacity={1}
            />
          </BgAnimateView>
          <BottomUpAnimateView
            ref={upAnimateRef}
            style={{
              position: 'absolute',
              width: '100%',
              bottom: 0,
              borderTopLeftRadius: 16,
              borderTopRightRadius: 16,
              backgroundColor: Tme('cardColor'),
            }}
            viewHeight={400}>
            <View
              style={{
                flex: 1,
                flexDirection: 'column',
                height: '100%',
              }}>
              {/* 标题部分 */}
              <View style={[styles.filterModalHeader, {padding: 16}]}>
                <Text
                  style={[
                    styles.filterModalTitle,
                    {color: Tme('cardTextColor')},
                  ]}>
                  {I18n.t('event.filter_events')}
                </Text>
              </View>

              <ScrollView
                style={{flex: 1}}
                contentContainerStyle={{padding: 16, paddingTop: 0}}
                showsVerticalScrollIndicator={true}>
                <View style={styles.filterSection}>
                  <Text
                    style={[
                      styles.filterSectionTitle,
                      {color: Tme('cardTextColor')},
                    ]}>
                    {I18n.t('ipc.ipc_alarm_type')}
                  </Text>
                  <View style={styles.filterChipContainer}>
                    {typeFilterOptions.map(option => (
                      <Chip
                        key={option.id}
                        selected={tempSelectedTypes.includes(option.id)} // 使用 tempSelectedTypes
                        onPress={() => handleTypeSelect(option.id)} // 使用 handleTypeSelect
                        style={[
                          styles.filterOptionChip,
                          {backgroundColor: Tme('bgColor')},
                        ]}
                        selectedColor={Colors.MainColor}
                        textStyle={{
                          color: Tme('cardTextColor'),
                        }}>
                        {I18n.t('ipc.' + option.label)}
                      </Chip>
                    ))}
                  </View>
                </View>

                <View style={styles.filterSection}>
                  <Text
                    style={[
                      styles.filterSectionTitle,
                      {color: Tme('cardTextColor')},
                    ]}>
                    {I18n.t('device.device')}
                  </Text>
                  <View style={styles.filterChipContainer}>
                    {devices.map(device => (
                      <Chip
                        key={device.uuid}
                        selected={tempSelectedDevice === device.uuid} // 使用 tempSelectedDevice
                        onPress={() => handleDeviceSelect(device.uuid)} // 使用 handleDeviceSelect
                        style={[
                          styles.filterOptionChip,
                          {backgroundColor: Tme('bgColor')},
                        ]}
                        selectedColor={Colors.MainColor}
                        textStyle={{
                          color: Tme('cardTextColor'),
                        }}>
                        {device.name}
                      </Chip>
                    ))}
                  </View>
                </View>
              </ScrollView>

              <View
                style={[
                  styles.filterButtonContainer,
                  {
                    padding: 16,
                    borderTopWidth: StyleSheet.hairlineWidth,
                    borderTopColor: Tme('inputBorderColor'),
                  },
                ]}>
                <TouchableOpacity
                  style={[
                    styles.filterActionButton,
                    {backgroundColor: Tme('bgColor'), marginRight: 20},
                  ]}
                  onPress={handleReset}>
                  <Text style={{color: Tme('cardTextColor')}}>
                    {I18n.t('home.reset')}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.filterActionButton,
                    {backgroundColor: Colors.MainColor},
                  ]}
                  onPress={handleApplyFilter}>
                  <Text style={styles.filterActionButtonText}>
                    {I18n.t('home.confirm')}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </BottomUpAnimateView>
        </View>
      </Modal>
    );
  },
);

// 将这些函数移到组件外部
const valueScale = (spec: Event): string => {
  switch (spec.event_type) {
    case 'ipc':
      let labels = '';
      if (spec.rekognition_labels.includes('person')) {
        labels = I18n.t('spec.person_detected');
      } else if (spec.rekognition_labels.includes('car')) {
        labels = I18n.t('spec.car_detected');
      } else if (spec.rekognition_labels.includes('animal')) {
        labels = I18n.t('spec.animal_detected');
      } else if (spec.rekognition_labels.includes('package')) {
        labels = I18n.t('spec.package_detected');
      }
      return `${I18n.t('spec.camera')} ${spec.event_name} ${labels}`;
    default:
      return `${I18n.t('device.device')} ${spec.event_name} ${Helper.i(
        spec.spec_name,
      )} ${Helper.i(spec.spec_value)}`;
  }
};

const getLabelColor = (label: string): string => {
  switch (label) {
    case 'person':
      return 'rgba(66, 133, 244, 0.2)';
    case 'car':
      return 'rgba(52, 168, 83, 0.2)';
    case 'animal':
      return 'rgba(251, 140, 0, 0.2)';
    case 'package':
      return 'rgba(156, 39, 176, 0.2)';
    default:
      return 'rgba(128, 128, 128, 0.2)';
  }
};

const getLabelTextColor = (label: string): string => {
  switch (label) {
    case 'person':
      return '#4285F4';
    case 'car':
      return '#34A853';
    case 'animal':
      return '#FB8C00';
    case 'package':
      return '#9C27B0';
    default:
      return '#808080';
  }
};

// 添加 EventItem 组件
const EventItem = React.memo<{
  item: Event;
  onImagePress: (event: Event) => void;
  onVideoPress: (event: Event) => void;
}>(({item, onImagePress, onVideoPress}) => {
  return (
    <View style={[styles.eventItem, {backgroundColor: Tme('cardColor')}]}>
      <View style={styles.contentContainer}>
        <View style={styles.timeRow}>
          <Text
            numberOfLines={2}
            style={[styles.eventTitle, {color: Tme('cardTextColor')}]}>
            {valueScale(item)}
          </Text>
          <View style={styles.timeContainer}>
            <Icon name="access-time" size={12} color={Tme('smallTextColor')} />
            <Text
              style={{
                fontSize: 12,
                marginLeft: 4,
                color: Tme('smallTextColor'),
              }}>
              {item.time}
            </Text>
          </View>
        </View>
        {item.rekognition_labels && item.rekognition_labels.length > 0 && (
          <View style={styles.labelsContainer}>
            {item.rekognition_labels.map((label, index) => (
              <LabelChip
                key={index}
                label={I18n.t('ipc.' + label)}
                backgroundColor={getLabelColor(label)}
                textColor={getLabelTextColor(label)}
                style={styles.labelChip}
              />
            ))}
          </View>
        )}
      </View>
      {item.ipc_screenshot_url && (
        <ImagePreviewView
          imageUrl={item.ipc_screenshot_url}
          onImagePress={() => onImagePress(item)}
          onVideoPress={() => onVideoPress(item)}
          hasVideo={item.ipc_video_success}
        />
      )}
    </View>
  );
});

interface IpcEventsProps {
  initialDeviceUUID?: string; // 可选的初始设备 UUID
}

const IpcEvents: React.FC<IpcEventsProps> = ({initialDeviceUUID}) => {
  // 将事件状态改为分组格式
  const [eventSections, setEventSections] = useState<EventSection[]>([]);
  const navigation = useNavigation<IpcNavigationProp>();
  const nextPage = useRef<number>(1);
  const [showImages, setShowImages] = useState<Array<{url: string}>>([]);
  const [imageVisible, setImageVisible] = useState<boolean>(false);
  // 添加刷新状态
  const [refreshing, setRefreshing] = useState<boolean>(false);
  // 添加加载状态 ref
  const isLoadingRef = useRef<boolean>(false);

  // 将筛选状态改为 useRef
  const selectedTypesRef = useRef<string[]>([]);
  const selectedDeviceRef = useRef<string>(initialDeviceUUID || '');
  // 保留这些状态用于UI显示
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<string>(
    initialDeviceUUID || '',
  );
  const [filterModalVisible, setFilterModalVisible] = useState<boolean>(false);
  const [devices, setDevices] = useState<Array<{uuid: string; name: string}>>(
    [],
  );

  // 添加设备 Map 缓存
  const deviceMap = useMemo(() => {
    const map = new Map<string, {uuid: string; name: string}>();
    devices.forEach(device => map.set(device.uuid, device));
    return map;
  }, [devices]);

  // 使用 useMemo 优化 getFilterText
  const filterText = useMemo(() => {
    const parts: string[] = [];

    // 添加设备名称 - 使用 Map 查找替代 find
    if (selectedDevice) {
      const device = deviceMap.get(selectedDevice);
      if (device) {
        parts.push(device.name);
      }
    }

    // 添加类型名称
    if (selectedTypes.length > 0) {
      const typeLabels = selectedTypes.map(typeId => {
        const typeOption = typeFilterOptions.find(t => t.id === typeId);
        return typeOption ? I18n.t('ipc.' + typeOption.label) : '';
      });
      parts.push(typeLabels.join(', '));
    }

    return parts.join(' | ');
  }, [selectedDevice, selectedTypes, deviceMap]);

  useEffect(() => {
    doFetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 添加下拉刷新函数
  const handleRefresh = () => {
    setRefreshing(true);
    // 重置页码
    nextPage.current = 1;
    // 调用现有的 doFetchData 函数，但不显示全局 loading
    doFetchData(true);
  };

  // 将事件数据按时间戳分组的函数，使用u_t*1000作为时间戳
  const groupEventsByTimestamp = useCallback(
    (events: Event[]): EventSection[] => {
      // 创建一个Map用于存储分组数据
      const groupedData = new Map<string, EventSection>();

      // 遍历事件数组，按日期分组
      events.forEach(event => {
        const timestamp = event.u_t * 1000;
        const date = new Date(timestamp);

        // 格式化日期为YYYY-MM-DD格式作为分组键
        const dateKey = `${date.getFullYear()}-${String(
          date.getMonth() + 1,
        ).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

        // 格式化为显示格式 DD/MM/YYYY
        const dateTitle = `${String(date.getDate()).padStart(2, '0')}/${String(
          date.getMonth() + 1,
        ).padStart(2, '0')}/${date.getFullYear()}`;

        if (!groupedData.has(dateKey)) {
          groupedData.set(dateKey, {
            title: dateTitle,
            data: [],
          });
        }

        // 将事件添加到对应日期组的data数组中
        groupedData.get(dateKey)!.data.push(event);
      });

      // 将Map转换为数组格式
      const sections: EventSection[] = Array.from(groupedData.values());

      // 按日期降序排序
      sections.sort((a, b) => {
        const dateA = new Date(a.title.split('/').reverse().join('-'));
        const dateB = new Date(b.title.split('/').reverse().join('-'));
        return dateB.getTime() - dateA.getTime();
      });

      return sections;
    },
    [],
  ); // 没有依赖项因为这是一个纯函数

  // 修改 doFetchData 的依赖项，移除 groupEventsByTimestamp
  const doFetchData = useCallback(
    (isRefreshing = false) => {
      if (nextPage.current == -1 || isLoadingRef.current) {
        return;
      }

      // 只有在非刷新模式下才显示全局加载
      if (!isRefreshing) {
        showLoading();
      }

      isLoadingRef.current = true;

      var data: any = {
        page: nextPage.current,
      };

      if (selectedDeviceRef.current) {
        data.device_uuid = selectedDeviceRef.current;
      }

      if (selectedTypesRef.current.length > 0) {
        data.ipc_alarm_types = selectedTypesRef.current;
      }

      Helper.httpGET(Helper.urlWithQuery('/events/ipcs', data), {
        ensure: () => {
          // 根据模式决定结束哪种加载状态
          if (isRefreshing) {
            setRefreshing(false);
          } else {
            hideLoading();
          }
          isLoadingRef.current = false;
        },
        success: (resp: any) => {
          if (nextPage.current === 1) {
            setDevices(resp.devices);
          }

          // 获取事件数据并处理
          if (resp.events && Array.isArray(resp.events)) {
            // 如果是第一页，直接设置数据
            if (nextPage.current === 1) {
              const sections = groupEventsByTimestamp(resp.events);
              setEventSections(sections);
            } else {
              // 如果不是第一页，将新数据与现有数据合并
              setEventSections(prevSections => {
                // 合并新的事件数据
                const existingEvents = prevSections.flatMap(
                  section => section.data,
                );
                // 合并所有事件
                const allEvents = [...existingEvents, ...resp.events];

                // 重新按日期分组
                const newSections = groupEventsByTimestamp(allEvents);
                return newSections;
              });
            }
          } else {
            console.warn('No events data in response or invalid format');
          }

          // 更新 nextPage，确保 resp.next_page 存在且有效
          nextPage.current =
            resp.next_page && resp.next_page > 0 ? resp.next_page : -1;
        },
      });
    },
    [setDevices, setEventSections, setRefreshing, groupEventsByTimestamp],
  );

  // 使用 useCallback 优化回调函数
  const handleVideoPress = useCallback(
    (item: Event): void => {
      showLoading();
      Helper.httpGET(
        Helper.urlWithQuery('/events/ipc_video_url', {id: item.id}),
        {
          success: (data: {url: string}) => {
            if (data.url) {
              navigation.push('LookVideo', {
                file: data.url,
              });
            } else {
              Alert.alert(
                I18n.t('home.warning_message'),
                I18n.t('ipc.ipc_video_null'),
                [
                  {
                    text: I18n.t('home.cancel'),
                    onPress: () => {},
                    style: 'cancel',
                  },
                ],
              );
            }
          },
          ensure: () => {
            hideLoading();
          },
        },
      );
    },
    [navigation],
  );

  const handleImagePress = useCallback((event: Event): void => {
    if (event.ipc_screenshot_url) {
      setShowImages([{url: event.ipc_screenshot_url}]);
      setImageVisible(true);
    }
  }, []);

  // 使用 useCallback 优化筛选相关函数
  const handleFilterButtonPress = useCallback(() => {
    setFilterModalVisible(true);
  }, []);

  const applyFilters = useCallback(
    (types: string[], device: string) => {
      selectedTypesRef.current = types;
      selectedDeviceRef.current = device;
      setSelectedTypes(types);
      setSelectedDevice(device);
      nextPage.current = 1;
      setEventSections([]);
      doFetchData();
    },
    [doFetchData],
  );

  const resetFilters = useCallback(() => {
    selectedTypesRef.current = [];
    selectedDeviceRef.current = '';
    setSelectedTypes([]);
    setSelectedDevice('');
    nextPage.current = 1;
    setEventSections([]);
    doFetchData();
  }, [doFetchData]);

  // 使用 useCallback 优化保存图片函数
  const saveImage = useCallback((uri: string): void => {
    hasSavePermission()
      .then(hasPermission => {
        showLoading();
        if (hasPermission) {
          DownloadImage(uri, 'image')!
            .then(() => {
              hideLoading();
            })
            .catch(error => {
              hideLoading();
              const errorMessage =
                error && typeof error === 'object'
                  ? error.message || JSON.stringify(error)
                  : String(error);
              AlertModal.alert(I18n.t('home.error'), errorMessage);
            });
        } else {
          hideLoading();
          AlertModal.alert(
            I18n.t('home.warning_message'),
            I18n.t('permissions.media_title'),
            [
              {
                text: I18n.t('home.cancel'),
                onPress: () => {},
                style: 'cancel',
              },
              {
                text: I18n.t('home.confirm'),
                onPress: () => {
                  Linking.openSettings();
                },
              },
            ],
          );
        }
      })
      .catch(error => {
        const errorMessage =
          error && typeof error === 'object'
            ? error.message || JSON.stringify(error)
            : String(error);
        AlertModal.alert(I18n.t('home.error'), errorMessage);
      });
  }, []);

  return (
    <View style={{backgroundColor: Tme('bgColor'), flex: 1}}>
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={styles.filterButton}
          onPress={handleFilterButtonPress}>
          <View style={styles.filterButtonInner}>
            <Icon name="filter-list" size={20} color={Colors.MainColor} />
            <Text style={styles.filterButtonText}>
              {I18n.t('event.filter_events')}
            </Text>
          </View>
        </TouchableOpacity>

        {(selectedTypes.length > 0 || selectedDevice) && (
          <View style={styles.selectedFiltersContainer}>
            <Text
              style={styles.selectedFiltersText}
              numberOfLines={1}
              ellipsizeMode="tail">
              {filterText}
            </Text>
          </View>
        )}
      </View>

      <FilterPanel
        devices={devices}
        visible={filterModalVisible}
        onClose={() => setFilterModalVisible(false)}
        onApplyFilter={(types, device) => applyFilters(types, device)}
        onReset={resetFilters}
        selectedTypes={selectedTypes}
        selectedDevice={selectedDevice}
      />
      <ImageShow
        images={showImages}
        open={imageVisible}
        onSaveImage={saveImage}
        onRequestClose={() => setImageVisible(false)}
      />
      <SectionList
        sections={eventSections}
        keyExtractor={item => item.id}
        stickySectionHeadersEnabled={true}
        windowSize={10}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={50}
        initialNumToRender={10}
        getItemLayout={(data, index) => ({
          length: 262,
          offset: 262 * index,
          index,
        })}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.MainColor]}
            tintColor={Colors.MainColor}
          />
        }
        renderSectionHeader={({section}) => (
          <View
            style={[
              styles.sectionHeader,
              {
                backgroundColor: Tme('bgColor'),
                borderBottomColor: Tme('bgColor'),
              },
            ]}>
            <Text
              style={[
                styles.sectionHeaderText,
                {color: Tme('smallTextColor')},
              ]}>
              {section.title}
            </Text>
          </View>
        )}
        renderItem={({item}) => (
          <EventItem
            item={item}
            onImagePress={handleImagePress}
            onVideoPress={handleVideoPress}
          />
        )}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        onEndReached={() => {
          if (nextPage.current > 1 && !isLoadingRef.current) {
            doFetchData();
          }
        }}
        onEndReachedThreshold={0.1}
      />
    </View>
  );
};

export default IpcEvents;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContent: {
    // padding: 16,
    paddingHorizontal: 16,
  },
  eventItem: {
    borderRadius: 12,
    marginBottom: 12,
    overflow: 'hidden',
    height: 250, // 设置固定高度
  },
  contentContainer: {
    // padding: 16,
    paddingTop: 16,
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  timeRow: {
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  eventTitle: {
    flex: 1,
    marginRight: 8,
    fontWeight: 'bold',
  },
  eventTime: {
    fontSize: 12,
    marginBottom: 10,
  },
  imageContainer: {
    width: '100%',
    borderRadius: 0,
    overflow: 'hidden',
  },
  actionButtonsContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
    gap: 20,
  },
  actionButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(64,64,64,0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 20,
    alignItems: 'center',
  },
  filterButton: {
    borderWidth: 1,
    borderColor: Colors.MainColor,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  filterButtonInner: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterButtonText: {
    color: Colors.MainColor,
    marginLeft: 4,
    fontSize: 14,
  },
  selectedFiltersContainer: {
    flex: 1,
    marginLeft: 10,
  },
  selectedFiltersText: {
    color: Colors.MainColor,
    fontSize: 12,
  },
  filterModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  filterModalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    maxHeight: '80%',
  },
  filterModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  filterModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  filterScrollView: {
    flexGrow: 0,
  },
  filterSection: {
    marginBottom: 16,
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  filterChipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterOptionChip: {
    marginBottom: 8,
  },
  filterButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  filterActionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  filterResetButton: {
    backgroundColor: Colors.MainColor,
    marginRight: 8,
  },
  filterApplyButton: {
    backgroundColor: Colors.MainColor,
  },
  filterActionButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  sectionHeader: {
    paddingVertical: 8,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    marginBottom: 8,
    // marginTop: 8,
  },
  sectionHeaderText: {
    fontSize: 16,
    // fontWeight: 'bold',
  },
  labelsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  labelChip: {
    marginRight: 8,
  },
});
