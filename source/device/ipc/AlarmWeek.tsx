/* eslint-disable no-bitwise */
import React, {useState, useEffect, useCallback} from 'react';
import {View, ScrollView} from 'react-native';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import I18n from '../../I18n';
import {Tme} from '../../ThemeStyle';
import NavBarView from '../../share/NavBarView';
import HeaderRightBtn from '../../share/HeaderRightBtn';
import {getDpsCmd} from './ipcUtils';
import {IpcNavigationProp, IpcRouteParamList} from './types/navigation';
import CheckBox from '../../share/CheckBox';
import {hideLoading, showLoading} from '../../../ILoading';
import { Toast } from '../../Toast';

// 星期配置项定义
interface WeekItem {
  id: number;
  name: string;
  selected: boolean;
}

const AlarmWeek: React.FC = () => {
  const navigation = useNavigation<IpcNavigationProp>();
  const route = useRoute<RouteProp<IpcRouteParamList, 'AlarmWeek'>>();
  const {sn, from, alarmDay} = route.params;

  // 定义星期选择状态
  const [weekSelections, setWeekSelections] = useState<WeekItem[]>([
    {id: 0, name: I18n.t('setting.sunday'), selected: false},
    {id: 1, name: I18n.t('setting.monday'), selected: false},
    {id: 2, name: I18n.t('setting.tuesday'), selected: false},
    {id: 3, name: I18n.t('setting.wednesday'), selected: false},
    {id: 4, name: I18n.t('setting.thursday'), selected: false},
    {id: 5, name: I18n.t('setting.friday'), selected: false},
    {id: 6, name: I18n.t('setting.saturday'), selected: false},
  ]);

  // 将十进制 alarmDay 转为二进制字符串，然后解析出选中的星期
  const parseAlarmDay = useCallback((dayValue: number): number[] => {
    const binaryStr = dayValue.toString(2).padStart(7, '0');
    const selectedDays: number[] = [];

    // 从二进制字符串中解析选中的天数
    for (let i = 0; i < binaryStr.length; i++) {
      if (binaryStr[binaryStr.length - 1 - i] === '1') {
        selectedDays.push(i);
      }
    }

    return selectedDays;
  }, []);

  // 初始化已选中的星期
  useEffect(() => {
    if (alarmDay !== undefined && alarmDay !== null) {
      const selectedDays = parseAlarmDay(Number(alarmDay));

      setWeekSelections(prev =>
        prev.map(item => ({
          ...item,
          selected: selectedDays.includes(item.id),
        })),
      );
    }
  }, [alarmDay, parseAlarmDay]);

  // 设置导航栏右侧按钮
  useEffect(() => {
    navigation.setOptions({
      // eslint-disable-next-line react/no-unstable-nested-components
      headerRight: () => (
        <HeaderRightBtn text={I18n.t('home.save')} rightClick={handleSave} />
      ),
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigation]);

  // 保存配置
  const handleSave = useCallback(() => {
    showLoading();

    // 只获取被选中的天数
    const selectedDays = weekSelections
      .filter(item => item.selected)
      .map(item => item.id);

    // 将选中的天数转换为二进制表示，然后转为十进制
    let binaryValue = 0;
    selectedDays.forEach(day => {
      // 设置对应位的二进制值（2^day）
      binaryValue |= 1 << day;
    });

    getDpsCmd(sn, [
      {ipc_cmd_key: from, value: binaryValue.toString()},
      {
        ipc_cmd_key: 'get_alarm_info',
        value: 'get',
      },
    ])
      .then(() => {
        setTimeout(() => {
          hideLoading();
          Toast.show();
          navigation.goBack();
        }, 1000);
      })
      .catch(err => {
        hideLoading();
        console.error('Error saving alarm week:', err);
      });
  }, [weekSelections, sn, from, navigation]);

  // 处理复选框点击事件
  const handleCheckboxClick = (index: number) => {
    setWeekSelections(prev => {
      const updated = [...prev];
      updated[index].selected = !updated[index].selected;
      return updated;
    });
  };

  // 渲染星期选择框
  const renderCheckboxes = () => {
    return weekSelections.map((item, index) => (
      <View key={index} style={{backgroundColor: Tme('cardColor')}}>
        <View style={{paddingHorizontal: 20}}>
          <CheckBox
            isLast={index === weekSelections.length - 1}
            value={item.name}
            index={item.id}
            isChecked={item.selected}
            onClick={() => handleCheckboxClick(index)}
          />
        </View>
      </View>
    ));
  };

  return (
    <NavBarView>
      <ScrollView showsVerticalScrollIndicator={false} style={{flex: 1}}>
        <View style={{backgroundColor: Tme('cardColor')}}>
          <View
            style={{
              height: 20,
              backgroundColor: Tme('bgColor'),
            }}
          />
          {renderCheckboxes()}
        </View>
      </ScrollView>
    </NavBarView>
  );
};

export default AlarmWeek;
