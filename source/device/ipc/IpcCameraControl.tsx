import React from 'react';
import { View, ScrollView, TouchableOpacity } from 'react-native';
import { Tme, Colors } from '../../ThemeStyle';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import I18n from '../../I18n';
import { useNavigation } from '@react-navigation/native';
import { IpcNavigationProp } from './types/navigation';
import { useIpcControl } from './hooks/useIpcControl';
import { ControlButton } from './components/ControlButton';
import { Device } from '../../types/home';

interface Props {
  node: Device;
  from?: string;
  host?: string;
  clientid?: string;
  need_update_firmware?: boolean;
}

export const IpcCameraControl: React.FC<Props> = ({
  node,
  from,
  host,
  clientid,
  need_update_firmware,
}) => {
  const navigation = useNavigation<IpcNavigationProp>();
  const {
    isMute,
    speak,
    controlView,
    muteClick,
    speakClick,
    onControl,
    snapShotClick,
    onCloseControl,
  } = useIpcControl(node);

  const getPlayBackData = () => {
    if (from === 'local') {
      navigation.push('LocalPlayBack', {
        title: I18n.t('device.playback'),
        host,
        clientid,
        node,
      });
    } else {
      navigation.push('IpcPlayBackScreen', {
        title: I18n.t('device.playback'),
        node,
      });
    }
  };

  const onSettingsClick = () => {
    navigation.push('IpcListSetting', {
      title: I18n.t('home.setting'),
      // devices: [node], // Convert single device to an array
    });
  };

  // 控制视图
  if (controlView) {
    return (
      <View style={{ flex: 1, backgroundColor: Tme('bgColor') }}>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={onCloseControl}
          style={{
            height: 50,
            backgroundColor: Tme('cardColor'),
            width: '100%',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <MCIcons
            name="chevron-double-down"
            size={20}
            color={Tme('textColor')}
          />
        </TouchableOpacity>
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: 40,
            flex: 1,
          }}>
          <View style={{ alignItems: 'center', justifyContent: 'center' }}>
            <TouchableOpacity
              onPressIn={() => onControl('up')}
              style={{ alignItems: 'center', justifyContent: 'center' }}>
              <MCIcons
                name="arrow-up-bold-circle"
                size={50}
                color={Colors.MainColor}
              />
            </TouchableOpacity>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', width: 150 }}>
              <TouchableOpacity onPressIn={() => onControl('left')} style={{ alignItems: 'center', justifyContent: 'center' }}>
                <MCIcons name="arrow-left-bold-circle" size={50} color={Colors.MainColor} />
              </TouchableOpacity>
              <TouchableOpacity onPressIn={() => onControl('right')} style={{ alignItems: 'center', justifyContent: 'center' }}>
                <MCIcons name="arrow-right-bold-circle" size={50} color={Colors.MainColor} />
              </TouchableOpacity>
            </View>
            <TouchableOpacity onPressIn={() => onControl('down')} style={{ alignItems: 'center', justifyContent: 'center' }}>
              <MCIcons name="arrow-down-bold-circle" size={50} color={Colors.MainColor} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  // 常规控制视图
  return (
    <View style={{ flex: 1, backgroundColor: Tme('bgColor') }}>
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ flexDirection: 'row', flexWrap: 'wrap' }}>
        <ControlButton
          onPress={muteClick}
          icon={isMute ? 'microphone' : 'microphone-off'}
          label={I18n.t('tuya.audio')}
        />

        <ControlButton
          onPress={speakClick}
          icon={speak ? 'text-to-speech' : 'text-to-speech-off'}
          label={I18n.t('tuya.speak')}
          style={{ marginHorizontal: 1 }}
        />

        <ControlButton
          onPress={snapShotClick}
          icon="camera"
          label={I18n.t('tuya.screenshot')}
        />

        <ControlButton
          onPress={getPlayBackData}
          icon="settings-backup-restore"
          iconType="mi"
          label={I18n.t('device.playback')}
        />

        <ControlButton
          onPress={() => {
            navigation.push('IpcList', {
              initialTab: 'events',
              initialDeviceUUID: node.uuid,
              title: I18n.t('home.event'),
            });
          }}
          icon="event"
          iconType="mi"
          label={I18n.t('home.event')}
          style={{ marginHorizontal: 1 }}
        />

        {typeof from === 'string' && from !== 's' && (
          <ControlButton
            onPress={onSettingsClick}
            icon="settings-outline"
            iconType="ion"
            label={I18n.t('home.setting')}
            badge={need_update_firmware}
          />
        )}
      </ScrollView>
    </View>
  );
};
