import React, {useEffect, useRef, forwardRef, useImperativeHandle} from 'react';
import {Platform, View} from 'react-native';
import WebView from 'react-native-webview';
import {PERMISSIONS, request} from 'react-native-permissions';
import RNFS from 'react-native-fs';
import {CameraRoll} from '@react-native-camera-roll/camera-roll';
import PubSub from 'pubsub-js';

import AlertModal from '../../../share/AlertModal';
import I18n from '../../../I18n';
import {hideLoading, showLoading} from '../../../../ILoading';
import {webrtc} from '../../webrtc';
import {IpcSource} from '../ipcTypes';
import {PubSubEvent} from '../../../types/PubSubEvent';

// 定义组件的属性类型
interface IpcHtmlShowProps {
  onReady?: () => void;
  onError?: (error: any) => void;
  onWebLoadEnd?: () => void;
  onOffline?: () => void;
  onFileData?: (data: any) => void;
  from?: IpcSource;
  host?: string;
  clientid?: string;
}

// 定义组件的引用类型
interface IpcHtmlShowRef {
  initConnection: (host: string, clientid: string, fromType: IpcSource) => void;
  downloadImage: (base64: string) => void;
}

const IpcHtmlShow = forwardRef<IpcHtmlShowRef, IpcHtmlShowProps>(
  (props, ref) => {
    // 解构属性
    const {onReady, onError, onWebLoadEnd, onOffline, onFileData, from} = props;

    // 创建 WebView 引用
    const cameraControlRef = useRef<WebView>(null);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      initConnection: (host, clientid, fromType) => {
        if (cameraControlRef.current) {
          console.log('host', host, clientid, fromType);
          // 如果 fromType 是local 则判断host 是否有:9445 如果没有host加9445
          if (fromType === 'local' && !host.includes(':9445')) {
            host = `${host}:9445`;
          }
          cameraControlRef.current.postMessage(
            JSON.stringify({
              type: 'init',
              data: {
                serverHost:
                  fromType === 'cloud'
                    ? `wss://${host}/wswebclient/`
                    : `ws://${host}/wswebclient/`,
                deviceId: clientid,
              },
            }),
          );
        }
      },
      downloadImage,
    }));

    useEffect(() => {
      // 订阅录制事件
      const recordSubscription = PubSub.subscribe(
        PubSubEvent.ios_record,
        async (msg, data) => {
          if (Platform.OS === 'android') {
            await request(PERMISSIONS.ANDROID.RECORD_AUDIO);
          }
          if (cameraControlRef.current) {
            cameraControlRef.current.postMessage(
              JSON.stringify({type: 'record', data: data}),
            );
          }
        },
      );

      // 订阅拍照事件
      const pictureSubscription = PubSub.subscribe(
        PubSubEvent.ios_picture,
        (msg, data) => {
          showLoading();
          if (cameraControlRef.current) {
            cameraControlRef.current.postMessage(
              JSON.stringify({type: 'ios_picture', data: data}),
            );
          }
        },
      );

      // 订阅声音事件
      const soundSubscription = PubSub.subscribe(
        PubSubEvent.ios_sound,
        (msg, data) => {
          if (cameraControlRef.current) {
            cameraControlRef.current.postMessage(
              JSON.stringify({type: 'ios_sound', data: data}),
            );
          }
        },
      );

      // 订阅对讲事件
      const speakSubscription = PubSub.subscribe(
        PubSubEvent.ios_speak,
        async (msg, data) => {
          if (Platform.OS === 'ios') {
            await request(PERMISSIONS.IOS.MICROPHONE);
          }
          if (cameraControlRef.current) {
            cameraControlRef.current.postMessage(
              JSON.stringify({type: 'ios_speak', data: data}),
            );
          }
        },
      );

      const fileListSubscription = PubSub.subscribe(
        PubSubEvent.getFileList,
        (msg, data) => {
          if (cameraControlRef.current) {
            cameraControlRef.current.postMessage(
              JSON.stringify({type: 'getFileList', data: data}),
            );
          }
        },
      );

      const playRemoteFileSubscription = PubSub.subscribe(
        PubSubEvent.playRemoteFile,
        (msg, data) => {
          if (cameraControlRef.current) {
            cameraControlRef.current.postMessage(
              JSON.stringify({type: 'playRemoteFile', data: data}),
            );
          }
        },
      );

      const playRemotePauseSubscription = PubSub.subscribe(
        PubSubEvent.playRemotePause,
        (_msg, _data) => {
          if (cameraControlRef.current) {
            cameraControlRef.current.postMessage(
              JSON.stringify({type: 'pause'}),
            );
          }
        },
      );

      // 清除订阅
      return () => {
        PubSub.unsubscribe(recordSubscription);
        PubSub.unsubscribe(pictureSubscription);
        PubSub.unsubscribe(soundSubscription);
        PubSub.unsubscribe(speakSubscription);
        PubSub.unsubscribe(fileListSubscription);
        PubSub.unsubscribe(playRemoteFileSubscription);
        PubSub.unsubscribe(playRemotePauseSubscription);
      };
    }, []);

    const downloadImage = async (base64: string) => {
      //data:image/png;base64,iVBORw0KGgo
      const base64Data = base64.split(',')[1];
      let dirs =
        Platform.OS === 'ios'
          ? RNFS.LibraryDirectoryPath
          : RNFS.ExternalDirectoryPath;
      const path = `${dirs}/${new Date().getTime()}.png`;

      try {
        await RNFS.writeFile(path, base64Data, 'base64');

        // 在实际需要访问相册时请求权限
        if (Platform.OS === 'android') {
          await request(PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE);
        } else if (Platform.OS === 'ios') {
          await request(PERMISSIONS.IOS.PHOTO_LIBRARY);
        }

        await CameraRoll.save(path, {type: 'auto'});
        hideLoading();
        AlertModal.alert(I18n.t('home.successful'), '', [
          {
            text: 'OK',
          },
        ]);
      } catch (error) {
        hideLoading();
        console.error('保存图片失败:', error);
      }
    };

    return (
      <View style={{flex: 1, backgroundColor: 'black'}}>
        <WebView
          ref={cameraControlRef}
          originWhitelist={['*']}
          style={{flex: 1, backgroundColor: 'black', opacity: 0.99}}
          source={{
            html: webrtc,
            baseUrl:
              from === 'cloud'
                ? 'https://www.presensmarthome.com/'
                : 'http://localhost',
          }}
          mediaPlaybackRequiresUserAction={false}
          domStorageEnabled={true}
          webviewDebuggingEnabled={true}
          allowsInlineMediaPlayback={true}
          startInLoadingState={false}
          allowUniversalAccessFromFileURLs={true}
          javaScriptEnabled={true}
          onMessage={({nativeEvent}) => {
            if (nativeEvent.data) {
              try {
                const eventData = JSON.parse(nativeEvent.data);
                if (eventData.type === 'ready') {
                  // 当WebView准备好时，通知父组件
                  if (onReady) {
                    onReady();
                  }
                }
                if (eventData.type === 'picture') {
                  downloadImage(eventData.data);
                }
                if (eventData.type === 'getFileList') {
                  if (onFileData) {
                    onFileData(eventData.data);
                  }
                }
                if (eventData.type === 'openDataChannel') {
                  if (onWebLoadEnd) {
                    onWebLoadEnd();
                  }
                }
                if (eventData.type === 'offline') {
                  if (onOffline) {
                    onOffline();
                  }
                }
                if (eventData.type === 'error') {
                  if (onError) {
                    onError(eventData.data);
                  }
                }
              } catch (err) {
                console.error('解析WebView消息错误:', err);
              }
            }
          }}
        />
      </View>
    );
  },
);

export default IpcHtmlShow;
