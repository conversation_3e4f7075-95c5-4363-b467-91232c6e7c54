import React, {useState, useEffect, useRef} from 'react';
import {View, Text, ActivityIndicator, ScrollView} from 'react-native';
import {Helper, HelperMemo} from '../../Helper';
import NavBarView from '../../share/NavBarView';
import CardView from '../../share/CardView';
import I18n from '../../I18n';
import {Tme, Colors} from '../../ThemeStyle';
import QRCode from 'react-native-qrcode-svg';
import AlertModal from '../../share/AlertModal';
import { useNavigation } from '@react-navigation/native';
import { IpcNavigationProp } from './types/navigation';

interface RouteParams {
  ssid: string;
  password: string;
}

interface AddIPCDeviceProps {
  route: {
    params: RouteParams;
  };
}

const AddIPCDevice: React.FC<AddIPCDeviceProps> = ({route}) => {
  const [qr, setQr] = useState<string>('');
  const timeOutEventRef = useRef<NodeJS.Timeout | null>(null);
  const timeoutRef = useRef<number>(30);
  const isMounted = useRef<boolean>(true);
  const navigation = useNavigation<IpcNavigationProp>();

  useEffect(() => {
    isMounted.current = true;
    doFetchData();

    return () => {
      isMounted.current = false;
      if (timeOutEventRef.current) {
        clearTimeout(timeOutEventRef.current);
        timeOutEventRef.current = null;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const doFetchData = () => {
    Helper.httpPOST(
      '/ipcs/create_token',
      {
        success: (data: string) => {
          if (isMounted.current) {
            setQr(`${route.params.ssid},${route.params.password},${data}`);
            setTimeout(() => {
              if (isMounted.current) {
                checkAdd(data);
              }
            }, 2000);
          }
        },
      },
      {
        home_id: HelperMemo.user_data.home_id,
      },
    );
  };

  const checkAdd = (key: string) => {
    if (!isMounted.current) {
      return;
    }

    Helper.httpGET(
      Helper.urlWithQuery('/ipcs/check', {
        ipc_track_key: key,
      }),
      {
        success: (data: {success: boolean; index: string}) => {
          if (!isMounted.current) {
            return;
          }

          if (data.success) {
            if (timeOutEventRef.current) {
              clearTimeout(timeOutEventRef.current);
              timeOutEventRef.current = null;
            }
            navigation.replace('AddSuccess', {
              uuid: data.index,
              sn_id: '',
              type: 'new',
              from: 'ipc',
            });
          } else {
            if (timeoutRef.current <= 0) {
              AlertModal.alert(I18n.t('global.time_out'), '', [
                {
                  text: I18n.t('home.confirm'),
                  onPress: () => {
                    navigation.goBack();
                  },
                },
              ]);
            } else {
              timeoutRef.current--;
              if (isMounted.current) {
                timeOutEventRef.current = setTimeout(() => {
                  if (isMounted.current) {
                    checkAdd(key);
                  }
                  if (timeOutEventRef.current) {
                    clearTimeout(timeOutEventRef.current);
                    timeOutEventRef.current = null;
                  }
                }, 2000);
              }
            }
          }
        },
      },
    );
  };

  return (
    <NavBarView>
      <ScrollView style={{flex: 1}}>
        <View
          style={{
            flex: 1,
            backgroundColor: Tme('bgColor'),
            alignItems: 'center',
            marginTop: 20,
            padding: 20,
          }}>
          <CardView
            styles={{
              height: 480,
              padding: 20,
              alignItems: 'center',
              borderRadius: 8,
            }}>
            <View style={{padding: 16, alignItems: 'center'}}>
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: '600',
                  textAlign: 'center',
                  color: Tme('cardTextColor'),
                }}>
                {I18n.t('global.add_device_s2_title')}
              </Text>
              <ActivityIndicator
                size="small"
                color={Colors.MainColor}
                style={{marginTop: 8}}
              />
              <View style={{height: 8}} />
              {qr === '' ? null : <QRCode size={280} value={qr} />}
            </View>
          </CardView>
        </View>
      </ScrollView>
    </NavBarView>
  );
};

export default AddIPCDevice;
