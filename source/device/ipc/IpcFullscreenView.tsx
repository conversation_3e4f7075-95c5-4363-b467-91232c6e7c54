import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  TouchableOpacity,
  BackHandler,
  Dimensions,
  Platform,
  StyleSheet,
} from 'react-native';
import I18n from '../../I18n';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Orientation from 'react-native-orientation-locker';
import AlertModal from '../../share/AlertModal';
import {PERMISSIONS, request} from 'react-native-permissions';
import IpcHtmlShow from './ipc_html/IpcHtmlShow';
import {useLifecycleEvents} from './hooks/useLifecycleEvents';
import {IpcSource} from './ipcTypes';
import {useNavigation} from '@react-navigation/native';

interface IpcFullscreenViewProps {
  route: {
    params: {
      title?: string;
      host: string;
      clientid: string;
      from: IpcSource;
    };
  };
}

const ASPECT_RATIO = 16 / 9;

const IpcFullscreenView: React.FC<IpcFullscreenViewProps> = ({route}) => {
  const navigation = useNavigation();
  const {host, clientid, from} = route.params;

  // 状态管理
  const [mountWebView, setMountWebView] = useState(false);
  const [windowWidth] = useState(Dimensions.get('window').width);
  const [videoWidth, setVideoWidth] = useState(windowWidth * ASPECT_RATIO);
  const [videoHeight, setVideoHeight] = useState(windowWidth);

  // 引用
  const localIpcRef = useRef<any>(null);
  const readyHandledRef = useRef<boolean>(false);
  const mountedRef = useRef<boolean>(false);

  // 生命周期管理
  const isActive = useLifecycleEvents({navigation});

  // 处理 WebView 准备就绪
  const handleReady = () => {
    if (readyHandledRef.current || !mountedRef.current) {
      return;
    }

    readyHandledRef.current = true;

    if (localIpcRef.current && host && clientid) {
      localIpcRef.current.initConnection(host, clientid, from);
    }
  };

  // 处理错误
  const handleError = () => {
    if (!mountedRef.current) {
      return;
    }

    AlertModal.alert('', I18n.t('home.network_issue_desp'), [
      {
        text: I18n.t('home.cancel'),
        onDismiss: () => navigation.goBack(),
      },
    ]);
  };

  // 返回处理
  const handleBack = () => {
    resetOrientation();
    navigation.goBack();
    return true;
  };

  // 重置方向
  const resetOrientation = () => {
    Orientation.unlockAllOrientations();
    Orientation.lockToPortrait();
    navigation.setOptions({
      headerShown: true,
      orientation: 'portrait',
    });
  };

  // 设置横屏模式
  const setLandscapeMode = () => {
    setVideoHeight(windowWidth);
    setVideoWidth(windowWidth * ASPECT_RATIO);

    Orientation.unlockAllOrientations();
    Orientation.lockToLandscapeLeft();

    navigation.setOptions({
      headerShown: false,
      orientation: 'landscape',
    });
  };

  // 生命周期管理
  useEffect(() => {
    mountedRef.current = true;
    setLandscapeMode();
    setMountWebView(true);

    let backHandler: any;
    if (Platform.OS === 'android') {
      request(PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE);
      backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        handleBack,
      );
    }

    return () => {
      mountedRef.current = false;
      if (backHandler) {
        backHandler.remove();
      }
      readyHandledRef.current = false;
      resetOrientation();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 活动状态管理
  useEffect(() => {
    if (isActive) {
      setMountWebView(true);
      readyHandledRef.current = false;
    } else {
      setMountWebView(false);
    }
  }, [isActive]);

  return (
    <View style={styles.container}>
      <TouchableOpacity
        hitSlop={{top: 10, left: 10, bottom: 10, right: 10}}
        activeOpacity={0.8}
        onPress={handleBack}
        style={styles.closeButton}>
        <Ionicons name="close-outline" size={26} color="white" />
      </TouchableOpacity>

      <View
        style={[
          styles.videoContainer,
          {width: videoWidth, height: videoHeight},
        ]}>
        {mountWebView && host && (
          <IpcHtmlShow
            ref={localIpcRef}
            onReady={handleReady}
            onError={handleError}
            host={host}
            clientid={clientid}
            from={from}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
    alignItems: 'center',
  },
  closeButton: {
    height: 40,
    width: 40,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.2)',
    position: 'absolute',
    right: 40,
    top: 40,
    zIndex: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  videoContainer: {
    backgroundColor: 'black',
  },
});

export default IpcFullscreenView;
