import React, { useState, useEffect, useContext, useRef } from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import * as Progress from 'react-native-progress';
import I18n from '../../I18n';
import WifiManager from 'react-native-wifi-reborn';
import { Tme, Colors } from '../../ThemeStyle';
import { Helper } from '../../Helper';
import IdleTimerManager from 'react-native-idle-timer';
import NavBarView from '../../share/NavBarView';
import CardView from '../../share/CardView';
import axios from 'axios';
import AlertModal from '../../share/AlertModal';
import ScreenSizeContext from '../../../WindowResizeContext';
import { sha256 } from '../../sha256';
import { hideLoading, showLoading } from '../../../ILoading';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

interface RouteParams {
  ssid: string;
  password: string;
}

interface Props {
  navigation: NativeStackNavigationProp<any>;
  route: {
    params: RouteParams;
  };
}

const AddIPCAPDevice: React.FC<Props> = ({ navigation, route }) => {
  const [progress, setProgress] = useState<number>(0);
  const [indeterminate, setIndeterminate] = useState<boolean>(true);
  const [showLinkError, setShowLinkError] = useState<boolean>(false);
  const [showConnectError, setShowConnectError] = useState<boolean>(false);
  const context = useContext(ScreenSizeContext);
  const timeOutEventRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    linkWifi();
    IdleTimerManager.setIdleTimerDisabled(true);

    return () => {
      IdleTimerManager.setIdleTimerDisabled(false);
      if (timeOutEventRef.current) {
        clearTimeout(timeOutEventRef.current);
        timeOutEventRef.current = null;
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const linkWifi = (): void => {
    WifiManager.getCurrentWifiSSID().then((ssid: string) => {
      if (ssid === 'newway') {
        setProgress(0.5);
        setIndeterminate(false);
        setLink();
      } else {
        WifiManager.connectToProtectedWifiSSID({
          ssid: 'newway',
          password: null,
        }).then(
          () => {
            setProgress(0.5);
            setIndeterminate(false);
            setLink();
          },
          () => {
            setProgress(0);
            setIndeterminate(true);
            setShowLinkError(true);
          },
        );
      }
    });
  };

  const retry = (): void => {
    setShowLinkError(false);
    setShowConnectError(false);

    WifiManager.getCurrentWifiSSID().then((SSID: string) => {
      if (SSID === 'newway') {
        setProgress(0.5);
        setIndeterminate(false);
        setLink();
      } else {
        setProgress(0);
        setIndeterminate(true);
        setShowLinkError(true);
      }
    });
  };

  const checkAdd = (key: string): void => {
    showLoading();
    setTimeout(() => {
      Helper.httpPOST(
        '/ipcs/device_added',
        {
          success: (data: { success: boolean; index: string }) => {
            if (data.success) {
              navigation.replace('AddSuccess', {
                uuid: data.index,
                sn_id: '',
                type: 'new',
                from: 'ipc',
              });
            }
          },
          ensure: () => {
            hideLoading();
          },
        },
        {
          sn: key,
        },
      );
    }, 2000);
  };

  const setLink = (): void => {
    const timestamp = new Date().getTime();
    const code = `newway::timestamp=${timestamp}&nonceStr=${timestamp}`;

    sha256(code).then((sign: string) => {
      axios({
        url: 'http://***********:9080/auth',
        method: 'POST',
        data: {
          timestamp: timestamp,
          nonceStr: `${timestamp}`,
          sign: sign,
        },
      })
        .then((response: { status: number; data: { code: number; data: { token: string; uuid: string } } }) => {
          if (response.status === 200) {
            const data = response.data;
            if (data.code === 200) {
              axios({
                url: 'http://***********:9080/network/config',
                method: 'POST',
                data: {
                  ssid: route.params.ssid,
                  password: route.params.password,
                },
                headers: {
                  token: `${data.data.token}`,
                },
              })
                .then((res: { data: { code: number } }) => {
                  if (res.data.code === 200) {
                    setTimeout(() => {
                      setProgress(1);
                      setTimeout(() => {
                        AlertModal.alert(I18n.t('wifi.add_title'), '', [
                          {
                            text: I18n.t('home.confirm'),
                            onPress: () => {
                              checkAdd(data.data.uuid);
                            },
                          },
                        ]);
                      }, 2000);
                    }, 5000);
                  } else {
                    setProgress(0);
                    setIndeterminate(true);
                    setShowLinkError(false);
                    setShowConnectError(true);
                  }
                })
                .catch((_error: Error) => {
                  setProgress(0);
                  setIndeterminate(true);
                  setShowLinkError(false);
                  setShowConnectError(true);
                });
            }
          } else {
            setProgress(0);
            setIndeterminate(true);
            setShowLinkError(false);
            setShowConnectError(true);
          }
        })
        .catch((_error: Error) => {
          setProgress(0);
          setIndeterminate(true);
          setShowLinkError(false);
          setShowConnectError(true);
        });
    });
  };

  let desp = I18n.t('wifi.ipc_setting_desp');
  if (showLinkError) {
    desp = I18n.t('wifi.ipc_setting_error');
  }
  if (showConnectError) {
    desp = I18n.t('wifi.wifi_link_error');
  }

  return (
    <NavBarView>
      <ScrollView style={{ flex: 1 }}>
        <View style={{ alignItems: 'center', marginTop: 20, padding: 20 }}>
          <CardView
            styles={{
              width: context.winWidth - 40,
              height: 380,
              padding: 20,
              borderRadius: 8,
              alignItems: 'center',
            }}>
            <View style={{ marginTop: 60, marginBottom: 20 }}>
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: '600',
                  textAlign: 'center',
                  color: Tme('cardTextColor'),
                }}>
                {desp}
              </Text>
            </View>
            {showLinkError || showConnectError ? null : (
              <Progress.Circle
                size={140}
                color={Colors.MainColor}
                progress={progress}
                indeterminate={indeterminate}
                showsText={true}
                formatText={() => {
                  return `${parseInt(String(progress * 100), 10)}%`;
                }}
              />
            )}
            {showLinkError || showConnectError ? (
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={retry}
                style={{
                  height: 40,
                  borderRadius: 20,
                  width: 120,
                  backgroundColor: Colors.MainColor,
                  borderColor: Colors.MainColor,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    fontSize: 14,
                    color: '#fff',
                  }}>
                  {I18n.t('wifi.retry')}
                </Text>
              </TouchableOpacity>
            ) : null}
          </CardView>
        </View>
      </ScrollView>
    </NavBarView>
  );
};

export default AddIPCAPDevice;
