import React, {useEffect, useState, useCallback} from 'react';
import {View, TouchableOpacity, StyleSheet} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Orientation from 'react-native-orientation-locker';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import ResizableDraggableView from '../../event/ResizableDraggableView';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import {sendSimpleCommand} from './ipcUtils';
import IpcHtmlShow from './ipc_html/IpcHtmlShow';
import {SafeAreaView} from 'react-native-safe-area-context';
import {IpcNavigationProp, IpcRouteParamList} from './types/navigation';
import {IpcSource} from './ipcTypes';
import {hideLoading, showLoading} from '../../../ILoading';
import { Toast } from '../../Toast';

interface Corner {
  x: number;
  y: number;
}

interface Region {
  show: boolean;
  coordinates: string;
  corners: Corner[];
}
//0, 0, 1000, 0, 0, 1000, 1000, 1000; x1,y1,x2,y2,x3,y3,x4,y4
const DEFAULT_REGION = '0,0,1000,0,0,1000,1000,1000';

export default function IpcRegion(): React.ReactElement {
  const navigation = useNavigation<IpcNavigationProp>();
  const route = useRoute<RouteProp<IpcRouteParamList, 'IpcRegion'>>();
  const [videoWidth] = useState<number>(route.params.width || 500);
  const [videoHeight] = useState<number>(route.params.height || 300);
  const [show, setShow] = useState<boolean>(false);

  const [region, setRegion] = useState<Region>({
    show: false,
    coordinates: DEFAULT_REGION,
    corners: [],
  });

  const parseRegionString = useCallback(
    (regionStr: string): Region => {
      const coords = regionStr.split(',').map(Number);
      // 直接按照正确的顺序映射坐标点
      // 假设坐标顺序为：左上x,左上y,右上x,右上y,右下x,右下y,左下x,左下y
      const corners: Corner[] = [
        {
          // 左上
          x: (coords[0] / 1000) * videoWidth,
          y: (coords[1] / 1000) * videoHeight,
        },
        {
          // 右上
          x: (coords[2] / 1000) * videoWidth,
          y: (coords[3] / 1000) * videoHeight,
        },
        {
          // 右下
          x: (coords[4] / 1000) * videoWidth,
          y: (coords[5] / 1000) * videoHeight,
        },
        {
          // 左下
          x: (coords[6] / 1000) * videoWidth,
          y: (coords[7] / 1000) * videoHeight,
        },
      ];

      return {
        corners: corners, // 不需要重新排序
        coordinates: regionStr,
        show: true,
      };
    },
    [videoWidth, videoHeight],
  );

  useEffect(() => {
    Orientation.lockToLandscapeLeft();

    navigation.setOptions({
      headerShown: false,
      navigationBarHidden: true,
    });

    setTimeout(() => {
      const regionData = parseRegionString(
        route.params.region || DEFAULT_REGION,
      );
      setRegion({
        ...regionData,
        show: true,
      });
      setShow(true);
    }, 100);

    return () => {
      Orientation.unlockAllOrientations();
      Orientation.lockToPortrait();
    };
  }, [navigation, route.params.region, parseRegionString]);

  const onRegionChange = (data: string): void => {
    setRegion(prev => ({
      ...prev,
      coordinates: data,
    }));
  };

  const onSave = (): void => {
    showLoading();
    sendSimpleCommand(
      route.params.sn,
      {
        ipc_cmd_key: route.params.from,
        value: region.coordinates,
      },
      () => {
        setTimeout(() => {
          hideLoading();
          Toast.show();
          navigation.goBack();
        }, 1000);
      },
    ).then(() => {});
  };

  const onReload = (): void => {
    setRegion(prev => ({
      ...prev,
      show: false,
    }));

    setTimeout(() => {
      const defaultRegion = parseRegionString(DEFAULT_REGION);
      setRegion({
        ...defaultRegion,
        show: true,
      });
    }, 50);
  };

  const videoContainerStyle = {
    width: videoWidth,
    height: videoHeight,
    backgroundColor: '#000',
  };
  const readyHandledRef = React.useRef<boolean>(false);
  const localIpcRef = React.useRef<any>(null);
  const handleReady = (): void => {
    // 使用ref来检查是否已处理过ready事件，这是同步的
    if (readyHandledRef.current) {
      return;
    }
    // 立即更新ref，不会有延迟
    readyHandledRef.current = true;
    if (localIpcRef.current && route.params.host && route.params.clientid) {
      localIpcRef.current.initConnection(
        route.params.host,
        route.params.clientid,
        'cloud',
      ); // 初始化连接
    }
  };

  return (
    <GestureHandlerRootView style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={() => {
            navigation.goBack();
          }}
          style={styles.closeButton}>
          <Ionicons name="close-outline" size={24} color="white" />
        </TouchableOpacity>

        <TouchableOpacity
          activeOpacity={0.8}
          onPress={onSave}
          style={styles.saveButton}>
          <Ionicons name="save-outline" size={24} color="white" />
        </TouchableOpacity>

        <TouchableOpacity
          activeOpacity={0.8}
          onPress={onReload}
          style={styles.reloadButton}>
          <Ionicons name="reload" size={24} color="white" />
        </TouchableOpacity>

        {show && (
          <View style={videoContainerStyle}>
            <IpcHtmlShow
              ref={localIpcRef}
              onReady={handleReady}
              onError={(err: Error) => console.log(err)}
              clientid={route.params.clientid}
              host={route.params.host}
              from={IpcSource.Cloud}
            />
            {region.show && (
              <ResizableDraggableView
                corners={region.corners}
                width={videoWidth}
                height={videoHeight}
                onRegionChange={onRegionChange}
              />
            )}
          </View>
        )}
      </SafeAreaView>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
    backgroundColor: '#000',
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    height: 30,
    width: 30,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.2)',
    position: 'absolute',
    left: 40,
    top: 40,
    zIndex: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButton: {
    height: 30,
    width: 30,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.2)',
    position: 'absolute',
    right: 40,
    top: 40,
    zIndex: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  reloadButton: {
    height: 30,
    width: 30,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.2)',
    position: 'absolute',
    right: 100,
    top: 40,
    zIndex: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
