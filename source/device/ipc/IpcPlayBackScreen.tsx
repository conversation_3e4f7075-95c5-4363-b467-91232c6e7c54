import React from 'react';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Helper } from '../../Helper';
import AlertModal from '../../share/AlertModal';
import _ from 'lodash';
import PlaybackBase from './components/PlaybackBase';
import { IpcNavigationProp, IpcRouteParamList } from './types/navigation';
import { RouteProp } from '@react-navigation/native';

/**
 * IPC设备回放屏幕组件
 * 封装了PlaybackBase组件，用于支持IPC设备的云端视频回放
 */
const IpcPlayBackScreen: React.FC = () => {
  const route = useRoute<RouteProp<IpcRouteParamList, 'IpcPlayBackScreen'>>();
  const navigation = useNavigation<IpcNavigationProp>();

  const deviceNode = route.params?.node;

  /**
   * 处理错误信息
   * @param errorMessage 错误信息
   */
  const handleError = (errorMessage: string): void => {
    AlertModal.alert('', _.isArray(errorMessage) ? _.uniq(errorMessage).join('\n') : errorMessage, [
      {
        text: 'cancel',
        onPress: () => navigation.goBack(),
        style: 'cancel',
      },
    ]);
  };

  /**
   * 加载客户端连接数据
   */
  const loadClientData = async (): Promise<{ clientId: string; host: string }> => {
    return new Promise((resolve, reject) => {
      if (!deviceNode || !deviceNode.id) {
        reject('找不到设备信息');
        return;
      }

      Helper.httpGET(
        Helper.urlWithQuery('/ipcs/info', {
          node_id: deviceNode.id,
        }),
        {
          success: (data: { webrtc_uuid: string; webrtc_host: string }) => {
            resolve({
              clientId: data.webrtc_uuid,
              host: data.webrtc_host,
            });
          },
          error: (data: string[]) => {
            reject(_.uniq(data).join('\n'));
          },
        },
      );
    });
  };

  return (
    <PlaybackBase
      playbackSource="online"
      navigation={navigation}
      deviceId={deviceNode?.id}
      loadClientData={loadClientData}
      onError={handleError}
    />
  );
};

export default IpcPlayBackScreen;
