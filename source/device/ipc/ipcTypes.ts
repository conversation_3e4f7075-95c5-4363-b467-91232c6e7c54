import {Device} from '../../types/home';

export enum IpcSource {
  Local = 'local',
  Cloud = 'cloud',
  S = 's',
  Ios = 'ios',
}

export interface IpcCameraControlProps {
  node: Device;
  from?: IpcSource;
  host?: string;
  clientid?: string;
  need_update_firmware?: boolean;
}

/**
 * IPC回放屏幕组件参数接口
 */
export interface IpcPlayBackScreenProps {
  /** 设备节点数据 */
  node: Device;
  [key: string]: any;
}

/**
 * WebRTC消息数据接口
 */
export interface WebRtcMessageData {
  sessionId: string;
  sessionType: string;
  messageId: string;
  from: string;
  to: string;
  [key: string]: any;
}

/**
 * 视频信息接口
 */
export interface VideoInfo {
  /** 视频宽度 */
  width: number;
  /** 视频高度 */
  height: number;
  /** 视频比特率 */
  bitrate: number;
  /** 视频编码器 */
  codec: string;
}

/**
 * 回放文件信息接口
 */
export interface PlaybackFileInfo {
  /** 文件名 */
  filename: string;
  /** 开始时间 */
  startTime: string;
  /** 结束时间 */
  endTime: string;
  /** 文件大小 */
  size: number;
  /** 文件类型 */
  type: string;
}
