/* eslint-disable react/no-unstable-nested-components */
import React, {useState, useEffect, useRef} from 'react';
import {View, ScrollView} from 'react-native';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import I18n from '../../I18n';
import {Tme} from '../../ThemeStyle';
import NavBarView from '../../share/NavBarView';
import {SelectDate} from '../../share/SelectDateView';
import AlertModal from '../../share/AlertModal';
import moment from 'moment';
import HeaderRightBtn from '../../share/HeaderRightBtn';
import {getDpsCmd} from './ipcUtils';
import {IpcNavigationProp, IpcRouteParamList} from './types/navigation';
import {hideLoading, showLoading} from '../../../ILoading';
import { Toast } from '../../Toast';

const AlarmTime: React.FC = () => {
  const navigation = useNavigation<IpcNavigationProp>();
  const route = useRoute<RouteProp<IpcRouteParamList, 'AlarmTime'>>();
  const {startTime, endTime, sn, from} = route.params;

  const [begin_at, setBeginAt] = useState<Date | null>(null);
  const [end_at, setEndAt] = useState<Date | null>(null);

  const upBeginTime = useRef<Date | null>(null);
  const upEndTime = useRef<Date | null>(null);

  useEffect(() => {
    // 相当于 componentDidMount
    initDate();

    // 设置标题栏右侧按钮
    navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn text={I18n.t('home.save')} rightClick={handleSave} />
      ),
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const initDate = (): void => {
    setBeginAt(moment(startTime, 'HHmm').toDate());
    setEndAt(moment(endTime, 'HHmm').toDate());
    upBeginTime.current = moment(startTime, 'HHmm').toDate();
    upEndTime.current = moment(endTime, 'HHmm').toDate();
  };

  const handleSave = (): void => {
    handleNext();
  };

  const handleTimeChange = (date: Date, fr: string): void => {
    if (fr === 'start_at') {
      setBeginAt(date);
      upBeginTime.current = date;
    }
  };

  const handleEndTimeChange = (date: Date, f: string): void => {
    if (f === 'end_at') {
      setEndAt(date);
      upEndTime.current = date;
    }
  };

  const handleNext = (): void => {
    const errors: string[] = [];

    if (upBeginTime.current === null && upEndTime.current !== null) {
      errors.push(I18n.t('setting.place_end_time'));
    }
    if (upBeginTime.current !== null && upEndTime.current === null) {
      errors.push(I18n.t('setting.place_start_time'));
    }
    if (errors.length > 0) {
      AlertModal.alert(I18n.t('home.warning_message'), errors.join('\n'));
      return;
    }
    if (upBeginTime.current && upEndTime.current) {
      showLoading();
      getDpsCmd(sn, [
        {
          ipc_cmd_key: from,
          value: `${moment(upBeginTime.current).format('HH:mm')}-${moment(upEndTime.current).format(
            'HH:mm',
          )}`,
        },
        {
          ipc_cmd_key: 'get_alarm_info',
          value: 'get',
        },
      ]).then(() => {
        setTimeout(() => {
          hideLoading();
          Toast.show();
          navigation.goBack();
        }, 1000);
      });
    }
  };

  return (
    <NavBarView>
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={{
          backgroundColor: Tme('bgColor'),
          flex: 1,
        }}>
        <View style={{height: 20}} />
        {begin_at && (
          <SelectDate
            from="start_at"
            title={I18n.t('setting.starting_time')}
            value={begin_at}
            onChange={handleTimeChange}
          />
        )}
        {end_at && (
          <SelectDate
            from="end_at"
            title={I18n.t('setting.end_time')}
            value={end_at}
            onChange={handleEndTimeChange}
          />
        )}
      </ScrollView>
    </NavBarView>
  );
};

export default AlarmTime;
