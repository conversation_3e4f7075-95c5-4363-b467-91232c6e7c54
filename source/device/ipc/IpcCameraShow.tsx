/* eslint-disable react/no-unstable-nested-components */
import React, {useState, useRef, useEffect, useCallback} from 'react';
import ActionSheet from '@alessiocancian/react-native-actionsheet';
import {Helper} from '../../Helper';
import {IsDark} from '../../ThemeStyle';
import I18n from '../../I18n';
import AlertModal from '../../share/AlertModal';
import {hideLoading, showLoading} from '../../../ILoading';
import _ from 'lodash';
import {dcodeIpcData, getDpsCmd} from './ipcUtils';
import {IpcSource} from './ipcTypes';
import {Toast} from '../../Toast';
import {Device} from '../../types/home';
import {BaseCameraShow} from './BaseCameraShow';
import HeaderRightBtn from '../../share/HeaderRightBtn';

interface RouteParams {
  node: Device;
}

interface IpcCameraShowProps {
  navigation: any;
  route: {
    params: RouteParams;
  };
}

interface DcDataType {
  devInfo?: {
    ip?: string;
    // 其他可能的属性
  };
  // 可能有更多属性...
}

interface InfoResponseType {
  webrtc_uuid: string;
  webrtc_host: string;
  need_update_firmware: boolean;
}

const IpcCameraShow: React.FC<IpcCameraShowProps> = props => {
  const {navigation, route} = props;
  const {node} = route.params;

  // 状态定义
  const [dcData, setDcData] = useState<DcDataType | null>(null);
  const [clientid, setClientid] = useState<string>('');
  const [host, setHost] = useState<string>('');
  const [needUpdateFirmware, setNeedUpdateFirmware] = useState<boolean>(false);
  const [dataReady, setDataReady] = useState<boolean>(false);

  // Refs
  const actionSheetRef = useRef<ActionSheet | null>(null);

  // 获取设备数据
  const doFetchData = useCallback(async () => {
    showLoading();
    Helper.httpGET(
      Helper.urlWithQuery('/ipcs/info', {
        node_id: node.id,
      }),
      {
        success: (data: InfoResponseType) => {
          setClientid(data.webrtc_uuid);
          setHost(data.webrtc_host);
          setDataReady(true);
          setNeedUpdateFirmware(data.need_update_firmware);
        },
        error: (data: string[]) => {
          AlertModal.alert('', _.uniq(data).join('\n'), [
            {
              text: 'cancel',
              onPress: () => navigation.goBack(),
              style: 'cancel',
            },
          ]);
        },
      },
    );
  }, [navigation, node.id]);

  // 获取 DPS 数据
  const getDps = useCallback(() => {
    Helper.httpGET(
      Helper.urlWithQuery('/sns/ipc_dp_values', {
        sn: node.sn,
      }),
      {
        success: (data: any) => {
          const decodedData = dcodeIpcData(data);
          setDcData(decodedData);
          hideLoading(true);
        },
      },
    );
  }, [node.sn]);

  // 初始化数据
  useEffect(() => {
    // 获取数据
    doFetchData();

    // 获取 DPS 数据
    getDpsCmd(node.sn, [
      {
        ipc_cmd_key: 'get_basic_cfg',
        value: 'get',
      },
    ]).then(() => {
      setTimeout(getDps, 500);
    });
  }, [doFetchData, getDps, node.sn]);

  // 显示菜单
  const showMenu = useCallback(() => {
    actionSheetRef.current?.show();
  }, []);

  // 菜单选项处理
  const handleMenu = useCallback(
    (index: number) => {
      switch (index) {
        case 0:
          navigation.push('AddSuccess', {
            uuid: node.uuid,
            name: node.display_name,
            type: 'edit',
            title: I18n.t('home.edit_device'),
          });
          break;
        case 1:
          if (dcData?.devInfo?.ip) {
            navigation.replace('LocalCameraShow', {
              host: dcData.devInfo.ip,
              clientid: clientid,
              node: node,
              title: I18n.t('ipc.local_play'),
            });
          }
          break;
        case 2:
          showLoading();
          Helper.httpPOST(
            '/partner/devices/ipc_favor',
            {
              ensure: () => {
                hideLoading();
              },
              success: (_data: any) => {
                Toast.show(I18n.t('global.successful'));
              },
            },
            {uuid: node.uuid},
          );
          break;
        case 3:
          AlertModal.alert(
            I18n.t('home.remove_device'),
            I18n.t('global.activate_sure'),
            [
              {
                text: I18n.t('home.cancel'),
                onPress: () => console.log('Cancel Pressed'),
                style: 'cancel',
              },
              {
                text: I18n.t('home.confirm'),
                onPress: () => {
                  showLoading();
                  Helper.httpPOST(
                    '/partner/devices/delete',
                    {
                      ensure: () => {
                        hideLoading();
                      },
                      success: (_data: any) => {
                        navigation.goBack();
                      },
                    },
                    {uuid: node.uuid},
                  );
                },
              },
            ],
          );
          break;
      }
    },
    [navigation, node, dcData, clientid],
  );

  // 设置标题右侧菜单按钮
  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          rightClick={showMenu}
          icon={{name: 'menu', icon: 'MCIcons'}}
        />
      ),
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigation]);

  return dataReady ? (
    <>
      <BaseCameraShow
        navigation={navigation}
        clientid={clientid}
        host={host}
        node={node}
        ipcSource={IpcSource.Cloud}
        needUpdateFirmware={needUpdateFirmware}
      />
      <ActionSheet
        ref={actionSheetRef}
        options={[
          I18n.t('home.cancel'),
          I18n.t('home.edit'),
          I18n.t('ipc.local_play'),
          I18n.t('ipc.add_home'),
          I18n.t('home.remove_device'),
        ]}
        cancelButtonIndex={0}
        destructiveButtonIndex={4}
        userInterfaceStyle={IsDark() ? 'dark' : 'light'}
        theme="ios"
        styles={{
          cancelButtonBox: {
            height: 50,
            marginTop: 6,
            alignItems: 'center',
            justifyContent: 'center',
          },
          buttonBox: {
            height: 50,
            alignItems: 'center',
            justifyContent: 'center',
          },
        }}
        onPress={(index: number) => {
          handleMenu(index - 1);
        }}
      />
    </>
  ) : null;
};

export default IpcCameraShow;
