import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { IpcSource } from '../ipcTypes';
import { Device } from '../../../types/home';

/**
 * @deprecated 请使用 IpcRouteParamList
 */
export type IpcRootStackParamList = {
  LocalPlayBack: {
    title: string;
    host: string | undefined;
    clientid: string | undefined;
    node: any;
  };
  IpcPlayBackScreen: {
    title: string;
    node: any;
  };
  IpcCameraSetting: {
    node: any;
    need_update_firmware: boolean | undefined;
    title: string;
    host: string | undefined;
    clientid: string | undefined;
  };
  eventScreen: {
    device_name: string;
    device_uuid: string;
    title: string;
  };
  LookVideo: {
    file: string;
  };
  AddSuccess: {
    uuid: string;
    sn_id: string;
    type: string;
    from: string;
  };
  IpcCameraShow: {
    title: string;
    node: any;
  };
  LocalCameraShow: {
    clientid: string;
    host: string;
    node: any;
    title?: string;
  };
  LookImage: {
    file: string;
  };
};

/**
 * @deprecated 请使用 IpcNavigationProp
 */
export type OldIpcNavigationProp = NativeStackNavigationProp<IpcRootStackParamList>;

/**
 * IPC路由参数列表
 * 整合了所有IPC相关组件的路由参数
 */
export type IpcRouteParamList = {
  // 相机相关
  IpcList: {
    initialTab?: 'devices' | 'events';
    initialDeviceUUID?: string;
    title: string;
  };
  IpcCameraShow: {
    title: string;
    node: Device;
  };
  LocalCameraShow: {
    host: string | null;
    clientid: string | undefined;
    node: Device;
    title?: string;
  };
  IpcFullscreenView: {
    title: string;
    host: string | null;
    clientid: string;
    from: IpcSource;
  };

  // 回放相关
  IpcPlayBackScreen: {
    title: string;
    node: Device;
  };
  LocalPlayBack: {
    title: string;
    host?: string;
    clientid?: string;
    node: Device;
  };

  // 设置相关
  IpcCameraSetting: {
    node: Device;
    need_update_firmware?: boolean;
    title: string;
    host?: string;
    clientid?: string;
  };
  IpcListSetting: {
    title: string;
    // devices: Device[];
  };
  IpcImageSetting: {
    node: Device;
    title: string;
  };
  IpcAlarmSetting: {
    node: Device;
    title: string;
    host?: string;
    clientid?: string;
  };

  // 区域设置
  IpcRegion: {
    sn: string;
    region: string;
    type?: string;
    host?: string;
    clientid?: string;
    width?: number;
    height?: number;
    from: string;
    title?: string;
  };

  // 告警相关
  AlarmTime: {
    startTime: string;
    endTime: string;
    sn: string;
    from: string;
    title?: string;
  };
  AlarmWeek: {
    sn: string;
    from: string;
    alarmDay: number; // 添加 alarmDay 作为可选参数
    title: string;
  };

  // 事件相关
  eventScreen: {
    device_name: string;
    device_uuid: string;
    title: string;
  };

  // 添加设备相关
  AddIPCDevice: {
    ssid: string;
    password: string;
  };
  AddIPCAPDevice: {
    ssid: string;
    password: string;
    sn: string;
  };
  AddWlanDevice: {
    ssid: string;
    password: string;
  };
  AddSuccess: {
    uuid: string;
    sn_id: string;
    type: string;
    from: string;
  };

  // 媒体查看
  LookVideo: {
    file: string;
  };
  LookImage: {
    file: string;
  };

  // MultipleButton 路由参数
  MultipleButton: {
    title: string;
    data: Array<{
      key: number | string;
      label: string;
      [key: string]: any;
    }>;
    sn: string;
    defaultSelected?: (number | string)[];
  };

  // 场景和自动化相关
  SmartSceneView: {
    title: string;
    scene_ids: string[];
    scenes: any[];
    from: 'ipc' | 'other';
  };
  SmartTimeView: {
    title: string;
    cycle: boolean;
    from: 'ipc' | 'other';
    wday: any[];
  };
};

/**
 * IPC导航属性类型
 */
export type IpcNavigationProp = NativeStackNavigationProp<IpcRouteParamList>;

/**
 * 相机控制组件参数接口
 */
export interface IpcCameraControlParams {
  node: Device;
  from?: IpcSource;
  host?: string;
  clientid?: string;
  need_update_firmware?: boolean;
}

/**
 * 获取路由参数或默认值
 * @param params 路由参数
 * @param defaultValue 默认值
 * @returns 参数值或默认值
 */
export function getRouteParamOrDefault<T>(params: T | undefined, defaultValue: T): T {
  return params || defaultValue;
}

