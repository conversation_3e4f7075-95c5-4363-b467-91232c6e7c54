/* eslint-disable react/no-unstable-nested-components */
import React, {useRef, useCallback, useEffect} from 'react';
import ActionSheet from '@alessiocancian/react-native-actionsheet';
import AlertModal from '../../share/AlertModal';
import I18n from '../../I18n';
import {IsDark} from '../../ThemeStyle';
import {IpcNavigationProp, IpcRouteParamList} from './types/navigation';
import {RouteProp} from '@react-navigation/native';
import {IpcSource} from './ipcTypes';
import {BaseCameraShow} from './BaseCameraShow';
import {hideLoading, showLoading} from '../../../ILoading';
import {Helper} from '../../Helper';
import {Toast} from '../../Toast';
import HeaderRightBtn from '../../share/HeaderRightBtn';

// 定义组件的属性类型
interface LocalCameraShowProps {
  navigation: IpcNavigationProp;
  route: RouteProp<IpcRouteParamList, 'LocalCameraShow'>;
}

const LocalCameraShow: React.FC<LocalCameraShowProps> = props => {
  const {navigation, route} = props;
  const {node} = route.params;
  // 确保host和clientid总是字符串类型
  const clientid = route.params.clientid || '';
  const host = String(route.params.host || '');

  // 创建ActionSheet引用
  const actionSheetRef = useRef<ActionSheet>(null);

  // 显示菜单
  const showMenu = useCallback(() => {
    actionSheetRef.current?.show();
  }, []);

  // 处理菜单选择
  const handleMenuPress = useCallback(
    (index: number) => {
      switch (index) {
        case 0:
          navigation.replace('IpcCameraShow', {
            title: node.display_name,
            node: node,
          });
          break;
        case 1:
          showLoading();
          Helper.httpPOST(
            '/partner/devices/ipc_favor',
            {
              ensure: () => {
                hideLoading();
              },
              success: (_data: any) => {
                Toast.show(I18n.t('global.successful'));
              },
            },
            {uuid: node.uuid},
          );

          break;
      }
    },
    [navigation, node],
  );

  // 处理错误，增加err参数的使用
  const handleError = useCallback(
    (_err: any) => {
      AlertModal.alert('', I18n.t('home.network_issue_desp'), [
        {
          text: I18n.t('home.cancel'),
          onDismiss: () => {
            navigation.goBack();
          },
        },
        {
          text: I18n.t('ipc.cloud_play'),
          onPress: () => {
            navigation.replace('IpcCameraShow', {
              title: node.display_name,
              node: node,
            });
          },
        },
      ]);
    },
    [navigation, node],
  );

  // 设置标题右侧菜单按钮
  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          rightClick={showMenu}
          icon={{name: 'menu', icon: 'MCIcons'}}
        />
      ),
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigation]);

  return (
    <>
      <BaseCameraShow
        navigation={navigation}
        clientid={clientid}
        host={host}
        node={node}
        ipcSource={IpcSource.Local}
        onError={handleError}
        needUpdateFirmware={false}
      />

      <ActionSheet
        ref={actionSheetRef}
        options={[
          I18n.t('home.cancel'),
          I18n.t('ipc.cloud_play'),
          I18n.t('ipc.add_home'),
        ]}
        cancelButtonIndex={0}
        userInterfaceStyle={IsDark() ? 'dark' : 'light'}
        theme="ios"
        styles={{
          cancelButtonBox: {
            height: 50,
            marginTop: 6,
            alignItems: 'center',
            justifyContent: 'center',
          },
          buttonBox: {
            height: 50,
            alignItems: 'center',
            justifyContent: 'center',
          },
        }}
        onPress={index => {
          handleMenuPress(index - 1);
        }}
      />
    </>
  );
};

export default LocalCameraShow;
