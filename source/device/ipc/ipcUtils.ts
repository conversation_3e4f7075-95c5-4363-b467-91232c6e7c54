import {hideLoading} from '../../../ILoading';
import {Helper} from '../../Helper';

  //run_ipc_cmd data
  /** {
    "UpgradeProgress": {
        "value": {
            "dpId": 602,
            "dpName": "UpgradeProgress",
            "dpValue": "100"
        },
        "t": 1745488033
    },
    "keepalive": {
        "value": {
            "dpId": 1300,
            "dpName": "keepalive",
            "dpValue": "ok"
        },
        "t": 1745556432
    },
    "devInfo": {
        "value": {
            "dpName": "devInfo",
            "dpValue": "get",
            "uuid": "RC14W012919289312",
            "Model": "RC14W01-RSM",
            "FwVersion": "PLS-M10-V2.2.2-c1-build-250424",
            "WiFi_Info": {
                "MAC": "04:BC:87:40:36:02",
                "ssid": "343_1",
                "signal": 4,
                "IP": "*************"
            },
            "dpId": 101
        },
        "t": 1745556431
    },
    "volume": {
        "value": {
            "dpId": 102,
            "type": 1,
            "dpName": "volume",
            "dpValue": "80",
            "dpResults": 0
        },
        "t": 1745553737
    },
    "BasicCfg": {
        "t": 1745556441,
        "value": {
            "resolution": 1,
            "PTZ": 1,
            "volume": 80,
            "callType": 1,
            "dpId": 199,
            "dpName": "BasicCfg",
            "call": 0,
            "dpValue": "get",
            "time": "2025-4-25 12:47:20",
            "zoom": 10,
            "led": 1,
            "mic_volume": 80,
            "zone": "+8:00",
            "dual": 0,
            "sleep": 0
        }
    },
    "alarmInfo": {
        "value": {
            "motionArea": "0,0,625,0,584,996,0,1000",
            "humanArea": "1,2,997,2,997,996,1,996",
            "alarmDay": 127,
            "alarmTime": "00:00-00:00",
            "pdSen": 1,
            "tracker": 0,
            "humanAutoZoom": 0,
            "msgPush": 0,
            "dpName": "alarmInfo",
            "dpValue": "get",
            "mdSen": 0,
            "md": 1,
            "timeInterval": 30,
            "otherAlarm": "[{\"type\":0,\"enable\":0},{\"type\":1,\"enable\":0},{\"type\":2,\"enable\":0},{\"type\":3,\"enable\":0},{\"type\":4,\"enable\":0},{\"type\":5,\"enable\":0},{\"type\":6,\"enable\":0}]",
            "dpId": 500,
            "pd": 0
        },
        "t": 1745556431
    },
    "SDInfo": {
        "t": 1745490071,
        "value": {
            "free": 101376,
            "sdRecordType": 0,
            "used": 60967936,
            "sdRecordStat": 1,
            "dpId": 301,
            "dpName": "SDInfo",
            "dpValue": "get",
            "status": 1,
            "total": 67108864
        }
    },
    "motionInfoGet": {
        "value": {
            "motion_sensitivity": 1,
            "motion_region": "0,0,625,0,584,996,0,1000",
            "dpId": 521,
            "dpName": "motionInfoGet",
            "dpValue": "get",
            "motion_switch": 1
        },
        "t": 1745556432
    },
    "humanVLInfoGet": {
        "value": {
            "dpName": "humanVLInfoGet",
            "voice_switch": 1,
            "voice_url": "0",
            "voice_type": 0,
            "led_switch": 0,
            "led_time": "00:00-23:00",
            "dpId": 525,
            "dpValue": "get",
            "voice_time": "00:00-22:00"
        },
        "t": 1743575747
    },
    "humanInfoGet": {
        "value": {
            "dpValue": "get",
            "human_switch": 1,
            "human_sensitivity": 1,
            "human_region": "0,0,1000,0,1000,996,0,996",
            "dpId": 524,
            "dpName": "humanInfoGet"
        },
        "t": 1743575746
    },
    "alarmTime": {
        "t": 1741312592,
        "value": {
            "type": 1,
            "dpName": "alarmTime",
            "dpValue": "00:00-00:00",
            "dpResults": 0,
            "dpId": 501
        }
    },
    "alarmDay": {
        "t": 1713839171,
        "value": {
            "dpId": 506,
            "type": 1,
            "dpName": "alarmDay",
            "dpValue": "31",
            "dpResults": 0
        }
    },
    "msgPush": {
        "value": {
            "type": 1,
            "dpName": "msgPush",
            "dpValue": "7",
            "dpResults": 0,
            "dpId": 505
        },
        "t": 1744598618
    },
    "motionEn": {
        "value": {
            "dpValue": "0",
            "dpResults": 0,
            "dpId": 502,
            "type": 1,
            "dpName": "motionEn"
        },
        "t": 1744598619
    },
    "tracker": {
        "value": {
            "dpValue": "2",
            "dpResults": 0,
            "dpId": 504,
            "type": 1,
            "dpName": "tracker"
        },
        "t": 1715751019
    },
    "motionSenSet": {
        "value": {
            "dpName": "motionSenSet",
            "dpValue": "2",
            "dpResults": 0,
            "dpId": 522,
            "type": 1
        },
        "t": 1745549416
    },
    "humanArea": {
        "t": 1743416353,
        "value": {
            "dpId": 518,
            "type": 1,
            "dpName": "humanArea",
            "dpValue": "0,0,1000,0,1000,996,0,996",
            "dpResults": 0
        }
    },
    "timeInterval": {
        "t": 1745483338,
        "value": {
            "dpId": 519,
            "type": 1,
            "dpName": "timeInterval",
            "dpValue": "180",
            "dpResults": 0
        }
    },
    "MvoiceAlarm": {
        "value": {
            "dpId": 513,
            "type": 1,
            "dpName": "MvoiceAlarm",
            "dpValue": "1",
            "dpResults": 0
        },
        "t": 1743508915
    },
    "HlightAlarm": {
        "value": {
            "dpResults": 0,
            "dpId": 516,
            "type": 1,
            "dpName": "HlightAlarm",
            "dpValue": "0"
        },
        "t": 1743130047
    },
    "humanEn": {
        "value": {
            "dpId": 503,
            "type": 1,
            "dpName": "humanEn",
            "dpValue": "1",
            "dpResults": 0
        },
        "t": 1742895230
    },
    "mic_volume": {
        "value": {
            "dpResults": -1,
            "dpId": 120,
            "type": 1,
            "dpName": "mic_volume",
            "dpValue": "100"
        },
        "t": 1744608022
    },
    "sdRecode": {
        "value": {
            "type": 1,
            "dpName": "sdRecode",
            "dpValue": "0",
            "dpResults": 0,
            "dpId": 303
        },
        "t": 1745555198
    },
    "sdRecordType": {
        "value": {
            "dpId": 304,
            "type": 1,
            "dpName": "sdRecordType",
            "dpValue": "1",
            "dpResults": -1
        },
        "t": 1743509042
    },
    "format": {
        "t": 1743129179,
        "value": {
            "dpId": 302,
            "type": 1,
            "dpName": "format",
            "dpValue": "",
            "dpResults": 0
        }
    },
    "resolution": {
        "value": {
            "dpId": 103,
            "type": 1,
            "dpName": "resolution",
            "dpValue": "0",
            "dpResults": 0
        },
        "t": 1744609651
    },
    "motionArea": {
        "value": {
            "dpValue": "0,0,625,0,584,996,0,1000",
            "dpResults": 0,
            "dpId": 512,
            "type": 1,
            "dpName": "motionArea"
        },
        "t": 1745552178
    },
    "playbackList": {
        "t": 1715743671,
        "value": {
            "count": 0,
            "complete": 1,
            "List": [],
            "dpId": 401,
            "dpName": "playbackList",
            "dpValue": "1715529600,1715743163,IPLS-00-YFC4-FQD5-00000002"
        }
    },
    "UpgradeFirmware": {
        "value": {
            "dpId": 10001,
            "type": 1,
            "dpName": "UpgradeFirmware",
            "dpValue": "https://gcs-us.smarthomesdk.com:50055/ipc/download_firmware",
            "dpResults": 0
        },
        "t": 1741224870
    },
    "reboot": {
        "value": {
            "dpId": 112,
            "type": 1,
            "dpName": "reboot",
            "dpValue": "set",
            "dpResults": 0
        },
        "t": 1745475592
    },
    "online": {
        "value": {
            "dpId": 10002,
            "dpName": "online",
            "dpValue": "ok"
        },
        "t": 1745550302
    },
    "repel": {
        "value": {
            "dpId": 116,
            "type": 1,
            "dpName": "repel",
            "dpValue": "1",
            "dpResults": 0
        },
        "t": 1744608346
    },
    "humanSenSet": {
        "t": 1743416374,
        "value": {
            "dpId": 523,
            "type": 1,
            "dpName": "humanSenSet",
            "dpValue": "1",
            "dpResults": 0
        }
    },
    "DNmodeGet": {
        "t": 1745556441,
        "value": {
            "dpName": "DNmodeGet",
            "dpValue": "get",
            "DNmode": 1,
            "DNmodeTime": "00:00-00:00",
            "WlightEn": 1,
            "WlightType": 0,
            "Wbrightness": 100,
            "dpId": 720
        }
    },
    "WlightSet": {
        "value": {
            "dpId": 722,
            "type": 1,
            "dpName": "WlightSet",
            "dpValue": "1",
            "dpResults": 0
        },
        "t": 1740970571
    },
    "WlightEn": {
        "value": {
            "dpId": 721,
            "type": 1,
            "dpName": "WlightEn",
            "dpValue": "1",
            "dpResults": 0
        },
        "t": 1745157455
    },
    "DNmode": {
        "value": {
            "dpValue": "2",
            "dpResults": 0,
            "dpId": 710,
            "type": 1,
            "dpName": "DNmode"
        },
        "t": 1745157457
    },
    "HvoiceTime": {
        "value": {
            "dpName": "HvoiceTime",
            "dpValue": "00:00-22:00",
            "dpResults": 0,
            "dpId": 515,
            "type": 1
        },
        "t": 1743129254
    },
    "HlightTime": {
        "value": {
            "dpValue": "00:00-23:00",
            "dpResults": 0,
            "dpId": 517,
            "type": 1,
            "dpName": "HlightTime"
        },
        "t": 1743129279
    },
    "imgInfo": {
        "value": {
            "dpValue": "get",
            "FlipV": 0,
            "contrast": 150,
            "sharpness": 135,
            "TwoDnr": -1,
            "sensorExpt": 10000,
            "dpId": 700,
            "dpName": "imgInfo",
            "FlipH": 0,
            "lum": 127,
            "saturation": 135,
            "ThreeDnr": 65,
            "WDR": 1
        },
        "t": 1745556441
    },
    "plsUpload": {
        "value": {
            "dpId": 10003,
            "type": 1,
            "dpName": "plsUpload",
            "dpValue": "4",
            "dpResults": 0
        },
        "t": 1745487895
    },
    "led": {
        "value": {
            "dpId": 107,
            "type": 1,
            "dpName": "led",
            "dpValue": "0",
            "dpResults": 0
        },
        "t": 1744608450
    },
    "zoom": {
        "t": 1745476678,
        "value": {
            "type": 1,
            "dpName": "zoom",
            "dpValue": "10",
            "dpResults": 0,
            "dpId": 113
        }
    },
    "callType": {
        "value": {
            "dpValue": "2",
            "dpResults": 0,
            "dpId": 111,
            "type": 1,
            "dpName": "callType"
        },
        "t": 1744705042
    },
    "2Dnr": {
        "value": {
            "dpId": 707,
            "type": 1,
            "dpName": "2Dnr",
            "dpValue": "255",
            "dpResults": 0
        },
        "t": 1745476709
    },
    "3Dnr": {
        "value": {
            "dpId": 708,
            "type": 1,
            "dpName": "3Dnr",
            "dpValue": "255",
            "dpResults": 0
        },
        "t": 1745476718
    },
    "imgDefault": {
        "value": {
            "FlipV": 0,
            "FlipH": 0,
            "saturation": 127,
            "sharpness": 127,
            "TwoDnr": 255,
            "ThreeDnr": 127,
            "dpId": 709,
            "dpValue": "",
            "sensorExpt": 10000,
            "contrast": 127,
            "WDR": 1,
            "dpName": "imgDefault",
            "lum": 127
        },
        "t": 1745476807
    },
    "factory": {
        "value": {
            "dpName": "factory",
            "dpValue": "",
            "dpResults": 0,
            "dpId": 119,
            "type": 1
        },
        "t": 1745483162
    },
    "ipc_is_local_play": true,
    "ipc_alarm_types": [
        "person"
    ]
}*/

interface IpcResponse {
  success: (data: any) => void;
  error: (err: any) => void;
  ignore_error?: boolean;
}

interface IpcCommandParams {
  sn: string;
  commands: Array<{
    ipc_cmd_key: string;
    value: any;
  }>;
}

interface AlarmInfoValue {
  alarmTime: string;
  alarmDay: number;
  timeInterval: number;
  md: number;
  mdSen: number;
  motionArea: string;
  pd: number;
  pdSen: number;
  HvoiceAlarm: number;
  HvoiceType: number;
  otherAlarm: string;
  tracker: number;
  msgPush: number;
}

interface DNModeValue {
  DNmode: number;
  DNmodeTime: string;
  Wbrightness: number;
  WlightEn: number;
  WlightType: number;
}

interface DeviceInfoValue {
  FwVersion: string;
  Model: string;
  WiFi_Info: {
    ssid: string;
    signal: number;
    IP: string;
  };
}

interface SDInfoValue {
  sdRecordStat: number;
  sdRecordType: number;
  total: number;
  status: number;
  used: number;
  free: number;
}

interface BasicCfgValue {
  PTZ: any;
  call: any;
  mic_volume: number;
  volume: number;
  resolution: number;
  zone: string;
  led: any;
  callType: number;
  zoom: any;
  dual: any;
  sleep: any;
}

interface imgInfo {
  FlipV: number;
  FlipH: number;
  lum: number;
  contrast: number;
  saturation: number;
  sharpness: number;
  WDR: number;
  ThreeDnr: number;
}

interface MotionInfoValue {
  motion_region: string;
  motion_sensitivity: number;
  motion_switch: number;
}

interface HumanVLInfoValue {
  led_switch: number;
  led_time: string;
  voice_switch: number;
  voice_time: string;
  voice_type: number;
  voice_url: string;
}

interface HumanInfoValue {
  human_switch: number;
  human_sensitivity: number;
  human_region: string;
}

interface IpcData {
  [key: string]:
    | {
        value: any;
        t?: number;
      }
    | undefined;

  keepalive?: {
    value: {
      dpValue: any;
    };
    t?: number;
  };
  UpgradeProgress?: {
    value: {
      dpValue: number;
    };
    t?: number;
  };
  BasicCfg?: {
    value: BasicCfgValue;
    t?: number;
  };
  motionInfoGet?: {
    value: MotionInfoValue;
    t?: number;
  };
  humanVLInfoGet?: {
    value: HumanVLInfoValue;
    t?: number;
  };
  humanInfoGet?: {
    value: HumanInfoValue;
    t?: number;
  };
  devInfo?: {
    value: DeviceInfoValue;
    t?: number;
  };
  volume?: {
    value: {
      dpValue: number;
    };
    t?: number;
  };
  SDInfo?: {
    value: SDInfoValue;
    t?: number;
  };
  alarmInfo?: {
    value: AlarmInfoValue;
    t?: number;
  };
  DNmodeGet?: {
    value: DNModeValue;
    t?: number;
  };
  imgInfo?: {
    value: imgInfo;
    t?: number;
  };
}

export interface DecodedData {
  timestamps: {
    [key: string]: number;
  };
  ipc_alarm_types?: string[];
  keepalive?: any;
  upgradeProgress?: number;
  BasicCfg?: {
    ptz: any;
    call: any;
    mic_volume: number;
    volume: number;
    resolution: number;
    zone: string;
    led: any;
    callType: number;
    zoom: any;
    dual: any;
    sleep: any;
  };
  motionInfo?: {
    motion_region: string;
    motion_sensitivity: number;
    motion_switch: number;
  };
  humanVLInfo?: {
    led_switch: number;
    led_time: string[];
    voice_switch: number;
    voice_time: string[];
    voice_type: number;
    voice_url: string;
  };
  humanInfo?: {
    human_switch: number;
    human_sensitivity: number;
    human_region: string;
  };
  devInfo?: {
    version: string;
    ssid?: string;
    signal?: number;
    ip?: string;
    model: string;
  };
  volume?: number;
  sdInfo?: {
    sdRecordStat: number;
    sdRecordType: number;
    sdTotal: number;
    sdStatus: number;
    sdUsed: number;
    sdFree: number;
  };
  alarmInfo?: {
    alarmTime: string[];
    alarmDay: number;
    timeInterval: number;
    md: number;
    mdSen: number;
    motionArea: string;
    pd: number;
    pdSen: number;
    HvoiceAlarm: number;
    HvoiceType: number;
    otherAlarm: string;
    tracker: number;
    msgPush: number;
  };
  dnmode?: {
    DNmode: number;
    DNmodeTime: string;
    Wbrightness: number;
    WlightEn: number;
    WlightType: number;
  };
  imgInfo?: imgInfo;
}

export function getDpsCmd(
  sn: string,
  commands: Array<{ipc_cmd_key: string; value: any}>,
): Promise<string> {
  return new Promise((resolve, reject) => {
    Helper.httpPOST(
      '/sns/run_ipc_cmd',
      {
        ignore_error: true,
        success: () => {
          resolve('ok');
        },
        error: () => {
          hideLoading();
          reject('error');
        },
      } as IpcResponse,
      {
        sn: sn,
        commands: commands,
      } as IpcCommandParams,
    );
  });
}

export function dcodeIpcData(data: IpcData): DecodedData {

  const temp: DecodedData = {
    timestamps: {}, // 初始化时间戳对象
  };

  try {
    // 存储每个字段的时间戳
    Object.keys(data).forEach(key => {
      const value = data[key];
      // 确保 value 是一个对象并且包含 't' 属性
      if (value && typeof value === 'object' && !Array.isArray(value) && 't' in value && value.t !== undefined) {
        temp.timestamps[key] = value.t as number;
      }
    });

    // 处理图像信息
    if (data.imgInfo && data.imgInfo.value) {

      const imgValue = data.imgInfo.value;
      temp.imgInfo = {
        FlipV: imgValue.FlipV,
        FlipH: imgValue.FlipH,
        lum: imgValue.lum,
        contrast: imgValue.contrast,
        saturation: imgValue.saturation,
        sharpness: imgValue.sharpness,
        ThreeDnr: imgValue.ThreeDnr,
        WDR: imgValue.WDR,
      };

    }

    // 处理设备信息
    if (data.devInfo && data.devInfo.value) {

      temp.devInfo = {
        version: data.devInfo.value.FwVersion || '',
        model: data.devInfo.value.Model || '',
      };

      // 处理WiFi信息
      if (data.devInfo.value.WiFi_Info) {
        temp.devInfo.ssid = data.devInfo.value.WiFi_Info.ssid;
        temp.devInfo.signal = data.devInfo.value.WiFi_Info.signal;
        temp.devInfo.ip = data.devInfo.value.WiFi_Info.IP;
      }
    }

    // 处理基本配置
    if (data.BasicCfg && data.BasicCfg.value) {

      temp.BasicCfg = {
        ptz: data.BasicCfg.value.PTZ,
        call: data.BasicCfg.value.call,
        mic_volume: data.BasicCfg.value.mic_volume,
        volume: data.BasicCfg.value.volume,
        resolution: data.BasicCfg.value.resolution,
        zone: data.BasicCfg.value.zone,
        led: data.BasicCfg.value.led,
        callType: data.BasicCfg.value.callType,
        zoom: data.BasicCfg.value.zoom,
        dual: data.BasicCfg.value.dual,
        sleep: data.BasicCfg.value.sleep,
      };
    }

    // 处理音量
    if (data.volume && data.volume.value && data.volume.value.dpValue !== undefined) {
      temp.volume = Number(data.volume.value.dpValue);
    }

    // 处理SD卡信息
    if (data.SDInfo && data.SDInfo.value) {

      temp.sdInfo = {
        sdRecordStat: data.SDInfo.value.sdRecordStat,
        sdRecordType: data.SDInfo.value.sdRecordType,
        sdTotal: data.SDInfo.value.total,
        sdStatus: data.SDInfo.value.status,
        sdUsed: data.SDInfo.value.used,
        sdFree: data.SDInfo.value.free,
      };
    }

    // 处理告警信息
    if (data.alarmInfo && data.alarmInfo.value) {

      // 分割时间字符串为数组
      const alarmTimeStr = data.alarmInfo.value.alarmTime || '';
      const alarmTimeParts = alarmTimeStr.includes('-')
        ? alarmTimeStr.split('-')
        : ['00:00', '00:00'];

      temp.alarmInfo = {
        alarmTime: alarmTimeParts,
        alarmDay: data.alarmInfo.value.alarmDay,
        timeInterval: data.alarmInfo.value.timeInterval,
        md: data.alarmInfo.value.md,
        mdSen: data.alarmInfo.value.mdSen,
        motionArea: data.alarmInfo.value.motionArea,
        pd: data.alarmInfo.value.pd,
        pdSen: data.alarmInfo.value.pdSen,
        HvoiceAlarm: data.alarmInfo.value.HvoiceAlarm,
        HvoiceType: data.alarmInfo.value.HvoiceType,
        otherAlarm: data.alarmInfo.value.otherAlarm,
        tracker: data.alarmInfo.value.tracker,
        msgPush: data.alarmInfo.value.msgPush,
      };
    }

    // 处理夜视模式
    if (data.DNmodeGet && data.DNmodeGet.value) {

      temp.dnmode = {
        DNmode: data.DNmodeGet.value.DNmode,
        DNmodeTime: data.DNmodeGet.value.DNmodeTime,
        Wbrightness: data.DNmodeGet.value.Wbrightness,
        WlightEn: data.DNmodeGet.value.WlightEn,
        WlightType: data.DNmodeGet.value.WlightType,
      };
    }

    // 处理移动侦测信息
    if (data.motionInfoGet && data.motionInfoGet.value) {

      temp.motionInfo = {
        motion_region: data.motionInfoGet.value.motion_region,
        motion_sensitivity: data.motionInfoGet.value.motion_sensitivity,
        motion_switch: data.motionInfoGet.value.motion_switch,
      };
    }

    // 处理人体语音和灯光信息
    if (data.humanVLInfoGet && data.humanVLInfoGet.value) {

      // 安全地分割led_time字符串
      const ledTimeStr = data.humanVLInfoGet.value.led_time || '';
      const ledTimeParts = ledTimeStr.includes('-')
        ? ledTimeStr.split('-')
        : ['00:00', '23:00'];

      // 安全地分割voice_time字符串
      const voiceTimeStr = data.humanVLInfoGet.value.voice_time || '';
      const voiceTimeParts = voiceTimeStr.includes('-')
        ? voiceTimeStr.split('-')
        : ['00:00', '22:00'];

      temp.humanVLInfo = {
        led_switch: data.humanVLInfoGet.value.led_switch,
        led_time: ledTimeParts,
        voice_switch: data.humanVLInfoGet.value.voice_switch,
        voice_time: voiceTimeParts,
        voice_type: data.humanVLInfoGet.value.voice_type,
        voice_url: data.humanVLInfoGet.value.voice_url,
      };
    }

    // 处理人体侦测信息
    if (data.humanInfoGet && data.humanInfoGet.value) {

      temp.humanInfo = {
        human_switch: data.humanInfoGet.value.human_switch,
        human_sensitivity: data.humanInfoGet.value.human_sensitivity,
        human_region: data.humanInfoGet.value.human_region,
      };
    }

    // 处理告警类型
    if (data.ipc_alarm_types && Array.isArray(data.ipc_alarm_types)) {
      temp.ipc_alarm_types = data.ipc_alarm_types;
    }

    return temp;
  } catch (error) {

    return {
      timestamps: temp.timestamps || {},
      imgInfo: temp.imgInfo,
      devInfo: temp.devInfo,
      BasicCfg: temp.BasicCfg,
      volume: temp.volume,
      sdInfo: temp.sdInfo,
      alarmInfo: temp.alarmInfo,
      dnmode: temp.dnmode,
      motionInfo: temp.motionInfo,
      humanVLInfo: temp.humanVLInfo,
      humanInfo: temp.humanInfo,
      ipc_alarm_types: temp.ipc_alarm_types,
    };
  }
}

// 定义轮询配置接口
interface PollingConfig {
  maxAttempts?: number;
  interval?: number;
  timeout?: number;
}

// 定义命令与数据字段的映射关系
const COMMAND_FIELD_MAP: Record<string, string[]> = {
  set_motion_en: ['motionInfoGet', 'alarmInfo'],
  set_motion_sen: ['motionInfoGet', 'alarmInfo'],
  set_volume: ['volume', 'BasicCfg'],
  set_alarm_time: ['alarmInfo'],
  set_time_interval: ['alarmInfo'],
  set_motion_area: ['motionInfoGet', 'alarmInfo'],
  set_msg_push: ['alarmInfo'],
  get_basic_cfg: ['BasicCfg'],
  get_alarm_info: ['alarmInfo'],
  get_device_info: ['devInfo'],
  get_dn_mode_info: ['DNmodeGet'],
  get_img_info: ['imgInfo'],
  get_motion_info: ['motionInfoGet'],
  switch_resolution: ['BasicCfg'],
  set_img_default: ['imgInfo'],
  set_zoom: ['BasicCfg'],
  set_FlipV: ['imgInfo'],
  set_FlipH: ['imgInfo'],
  set_3Dnr: ['imgInfo'],
  set_WDR: ['imgInfo'],
  set_lum: ['imgInfo'],
  set_contrast: ['imgInfo'],
  set_saturation: ['imgInfo'],
  set_sharpness: ['imgInfo'],
  set_sd_record_type: ['SDInfo'],
  set_sd_format: ['SDInfo'],
  switch_sd_recode: ['SDInfo'],
  get_sd_info: ['SDInfo'],
  set_dn_mode: ['DNmodeGet'],
};

// 定义不同命令需要的额外获取命令
const COMMANDS_EXTRA_GET: Record<
  string,
  Array<{ipc_cmd_key: string; value: string}>
> = {
  set_motion_en: [
    {ipc_cmd_key: 'get_motion_info', value: 'get'},
    {ipc_cmd_key: 'get_basic_cfg', value: 'get'},
  ],
  set_motion_sen: [{ipc_cmd_key: 'get_motion_info', value: 'get'}],
  set_volume: [{ipc_cmd_key: 'get_basic_cfg', value: 'get'}],
  set_alarm_time: [{ipc_cmd_key: 'get_alarm_info', value: 'get'}],
  set_time_interval: [{ipc_cmd_key: 'get_alarm_info', value: 'get'}],
  set_motion_area: [{ipc_cmd_key: 'get_motion_info', value: 'get'}],
  set_msg_push: [{ipc_cmd_key: 'get_alarm_info', value: 'get'}],
  get_basic_cfg: [{ipc_cmd_key: 'get_basic_cfg', value: 'get'}],
  get_alarm_info: [{ipc_cmd_key: 'get_alarm_info', value: 'get'}],
  get_device_info: [{ipc_cmd_key: 'get_device_info', value: 'get'}],
  get_dn_mode_info: [{ipc_cmd_key: 'get_dn_mode_info', value: 'get'}],
  get_img_info: [{ipc_cmd_key: 'get_img_info', value: 'get'}],
  get_motion_info: [{ipc_cmd_key: 'get_motion_info', value: 'get'}],
  switch_resolution: [{ipc_cmd_key: 'get_basic_cfg', value: 'get'}],
  set_img_default: [{ipc_cmd_key: 'get_img_info', value: 'get'}],
  set_zoom: [{ipc_cmd_key: 'get_basic_cfg', value: 'get'}],
  set_FlipV: [{ipc_cmd_key: 'get_img_info', value: 'get'}],
  set_FlipH: [{ipc_cmd_key: 'get_img_info', value: 'get'}],
  set_3Dnr: [{ipc_cmd_key: 'get_img_info', value: 'get'}],
  set_WDR: [{ipc_cmd_key: 'get_img_info', value: 'get'}],
  set_lum: [{ipc_cmd_key: 'get_img_info', value: 'get'}],
  set_contrast: [{ipc_cmd_key: 'get_img_info', value: 'get'}],
  set_saturation: [{ipc_cmd_key: 'get_img_info', value: 'get'}],
  set_sharpness: [{ipc_cmd_key: 'get_img_info', value: 'get'}],
  set_sd_record_type: [{ipc_cmd_key: 'get_sd_info', value: 'get'}],
  set_sd_format: [{ipc_cmd_key: 'get_sd_info', value: 'get'}],
  switch_sd_recode: [{ipc_cmd_key: 'get_sd_info', value: 'get'}],
  set_dn_mode: [{ipc_cmd_key: 'get_dn_mode_info', value: 'get'}],
  switch_white_light: [{ipc_cmd_key: 'get_dn_mode_info', value: 'get'}],
};

// 默认轮询配置
const DEFAULT_POLLING_CONFIG: PollingConfig = {
  maxAttempts: 10,
  interval: 1500,
  timeout: 15000,
};

// 验证数据是否更新的函数
const isDataUpdated = (
  data: IpcData,
  commandTime: number,
  commandType: string,
): boolean => {
  const fieldsToCheck = COMMAND_FIELD_MAP[commandType] || [];
  return fieldsToCheck.every(field => {
    const fieldData = data[field];
    return fieldData && fieldData.t && fieldData.t > commandTime;
  });
};

// 轮询获取数据的函数
const fetchDataWithPolling = async (
  sn: string,
  commandTime: number,
  commandType: string,
  config: PollingConfig = {},
): Promise<IpcData> => {
  const finalConfig = {...DEFAULT_POLLING_CONFIG, ...config};
  let attempts = 0;
  const startTime = Date.now();
  let lastData: IpcData | null = null;

  while (attempts < finalConfig.maxAttempts!) {
    if (Date.now() - startTime > finalConfig.timeout!) {

      return lastData || {};
    }

    try {
      const response = await new Promise<IpcData>((resolve, reject) => {
        Helper.httpGET(Helper.urlWithQuery('/sns/ipc_dp_values', {sn}), {
          cloud: true,
          success: (data: any) => {
            if (!data || typeof data !== 'object') {
              reject(new Error('无效的响应数据格式'));
              return;
            }
            // 保存最后一次请求的数据
            lastData = data;
            resolve(data);
          },
          error: (err: any) => {
            reject(err);
          },
        });
      });

      if (isDataUpdated(response, commandTime, commandType)) {
        return response;
      }

      await new Promise(resolve => setTimeout(resolve, finalConfig.interval));
      attempts++;
    } catch (error) {
      console.error('轮询获取数据失败:', error);
      // 如果发生错误，返回最后一次请求的数据
      if (lastData) {

        return lastData;
      }
      throw error;
    }
  }
  return lastData || {};
};

// 发送命令并获取最新数据的通用函数
export async function sendCommandAndGetData(
  sn: string,
  commands: Array<{ipc_cmd_key: string; value: any}>,
  onDataUpdate: (data: DecodedData) => void,
  config: PollingConfig = {},
): Promise<void> {
  try {
    // 记录发送命令的时间
    const commandTime = Math.floor(Date.now() / 1000);

    // 发送主命令
    await getDpsCmd(sn, commands);

    // 检查是否需要获取额外信息
    const mainCommand = commands[0].ipc_cmd_key;
    const extraCommands = COMMANDS_EXTRA_GET[mainCommand];

    // 如果需要获取额外信息，发送额外的获取命令
    if (extraCommands) {
      await getDpsCmd(sn, extraCommands);
    }

    // 获取最新数据
    const newData = await fetchDataWithPolling(
      sn,
      commandTime,
      commands[0].ipc_cmd_key,
      config,
    );

    // 解码数据并回调
    const decodedData = dcodeIpcData(newData);
    onDataUpdate(decodedData);
  } catch (error) {
    console.error('发送命令并获取数据失败:', error);
    throw error;
  }
}

// 简化的命令发送函数
export async function sendSimpleCommand(
  sn: string,
  command: {ipc_cmd_key: string; value: any},
  onDataUpdate: (data: DecodedData) => void,
  config: PollingConfig = {},
): Promise<void> {
  return sendCommandAndGetData(sn, [command], onDataUpdate, config);
}
