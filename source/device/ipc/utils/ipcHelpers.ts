import I18n from '../../../I18n';
import IpcScanner from '../../../../IpcScanner';
import { IpcSource } from '../ipcTypes';
import _ from 'lodash';
import { ScanResult } from '../IpcHelper';
import { Device } from '../../../types/home';

/**
 * 获取设备主机地址
 * @param device 设备信息
 * @returns 设备主机地址或null
 */
export function getDeviceHost(device: Device): string | null {
  if (!device?.ipc) {
    return null;
  }

  const scan = IpcScanner.getScanResults() as ScanResult[] | null;
  if (scan && Array.isArray(scan) && scan.length > 0) {
    const snu = device.ipc.webrtc_uuid;
    if (!snu) {
      return device.ipc.webrtc_host || null;
    }

    // 统一使用 uuid 进行匹配
    const matchedDevice = scan.find(o => o.sn === device.sn);
    if (matchedDevice) {
      return matchedDevice.ip + ':9445'; // 使用局域网IP
    }
  }
  return device.ipc.webrtc_host || null;
}

/**
 * 判断设备是使用本地连接还是云端连接
 * @param host 主机地址
 * @returns 连接源类型
 */
export function getViewFrom(host: string | null): IpcSource {
  // 如果 host 为空，返回 'cloud'
  if (!host) {
    return IpcSource.Cloud;
  }

  // 判断是否为本地连接
  const ipPattern = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(:\d+)?$/;
  return host.includes('localhost') || ipPattern.test(host)
    ? IpcSource.Local
    : IpcSource.Cloud;
}

/**
 * 获取设备连接信息
 * @param device 设备对象
 * @returns 设备连接信息
 */
export function getDeviceConnectionInfo(device: Device) {
  const host = getDeviceHost(device);
  const connectType = getViewFrom(host);

  return {
    host,
    connectType,
    isConnected: !!host,
  };
}

/**
 * 检查设备是否在线
 * @param device 设备对象
 * @returns 是否在线
 */
export function isDeviceOnline(device: Device): boolean {
  return device?.is_alive === true;
}

/**
 * 格式化存储空间 (KB -> GB)
 * @param sizeInKB 以KB为单位的大小
 * @param decimals 小数位数，默认为2
 * @returns 格式化后的GB字符串
 */
export function formatStorageSize(sizeInKB: number, decimals: number = 2): string {
  const sizeInGB = sizeInKB / 1024 / 1024;
  return sizeInGB.toFixed(decimals);
}

/**
 * 从SSID信息解析网络类型
 * @param ssid SSID信息
 * @returns 网络类型字符串
 */
export function getNetworkTypeFromSSID(ssid: string | undefined | null): string {
  const hasWifi = !!(ssid && ssid.length > 0);
  return hasWifi ? 'Wi-Fi' : I18n.t('ipc.wired');
}

/**
 * 显示星期几的字符串
 * @param notifyWeek 星期值（二进制位表示）
 * @returns 星期显示字符串
 */
export function showWeekDays(notifyWeek: number): string {
  if (notifyWeek === 0) {
    return I18n.t('ipc.alarm_no_week');
  }
  if (notifyWeek === 127) {
    return I18n.t('global.every_day');
  }

  const week = [
    {key: 0, value: I18n.t('setting.sunday')},
    {key: 6, value: I18n.t('setting.saturday')},
    {key: 5, value: I18n.t('setting.friday')},
    {key: 4, value: I18n.t('setting.thursday')},
    {key: 3, value: I18n.t('setting.wednesday')},
    {key: 2, value: I18n.t('setting.tuesday')},
    {key: 1, value: I18n.t('setting.monday')},
  ];

  // 将十进制数转换为二进制字符串
  const weekDay = decimalToBinary(notifyWeek);
  const selectedDays: string[] = [];

  _.forEach(weekDay, (day, key) => {
    if (day === '1') {
      selectedDays.push(week[key].value);
    }
  });

  if (selectedDays.length === 7) {
    return I18n.t('global.every_day');
  } else {
    return selectedDays.length === 0
      ? I18n.t('global.every_day')
      : selectedDays.reverse().join(' ');
  }
}

/**
 * 十进制转二进制字符串
 * @param decimalNumber 十进制数
 * @returns 二进制字符串
 */
export function decimalToBinary(decimalNumber: string | number): string {
  if (typeof decimalNumber === 'string') {
    decimalNumber = parseInt(decimalNumber, 10);
  }

  return decimalNumber
    .toString(2)
    .padStart(7, '0')
    .split('')
    .reverse()
    .join('');
}

/**
 * 二进制字符串转十进制数
 * @param binary 二进制字符串
 * @returns 十进制数
 */
export function binaryToDecimal(binary: string): number {
  return parseInt(binary, 2);
}

/**
 * 生成唯一标识符
 * @returns UUID字符串
 */
export function generateUUID(): string {
  const s: string[] = [];
  const hexDigits = '0123456789abcdef';
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.charAt(Math.floor(Math.random() * 0x10));
  }
  s[14] = '4';
  // eslint-disable-next-line no-bitwise
  s[19] = hexDigits.charAt((parseInt(s[19], 16) & 0x3) | 0x8);
  s[8] = s[13] = s[18] = s[23] = '-';
  return s.join('');
}
