import React from 'react';
import { View, Text, TouchableOpacity, StyleProp, ViewStyle } from 'react-native';
import { Tme, Colors } from '../../../ThemeStyle';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import MIcons from 'react-native-vector-icons/MaterialIcons';
import IonicIcons from 'react-native-vector-icons/Ionicons';

interface ControlButtonProps {
  onPress: () => void;
  icon: string;
  label: string;
  badge?: boolean;
  iconType?: 'mc' | 'mi' | 'ion';
  style?: StyleProp<ViewStyle>;
}

/**
 * 通用控制按钮组件
 * 用于IPC摄像头控制界面的功能按钮
 */
export const ControlButton: React.FC<ControlButtonProps> = ({
  onPress,
  icon,
  label,
  badge = false,
  iconType = 'mc',
  style,
}) => {
  // 根据iconType选择图标组件
  const renderIcon = () => {
    const iconColor = Tme('textColor');
    const iconSize = 28;

    switch (iconType) {
      case 'mi':
        return <MIcons name={icon} size={iconSize} color={iconColor} />;
      case 'ion':
        return <IonicIcons name={icon} size={iconSize} color={iconColor} />;
      default:
        return <MCIcons name={icon} size={iconSize} color={iconColor} />;
    }
  };

  return (
    <TouchableOpacity
      style={[
        {
          width: '33%',
          height: 100,
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: Tme('cardColor'),
          position: 'relative',
          marginBottom: 1,
        },
        style,
      ]}
      onPress={onPress}>
      {badge && (
        <View
          style={{
            position: 'absolute',
            right: 10,
            top: 10,
            backgroundColor: Colors.MainColor,
            width: 6,
            height: 6,
            borderRadius: 3,
          }}
        />
      )}
      <View style={{ alignItems: 'center', justifyContent: 'center' }}>
        {renderIcon()}
        <Text
          style={{
            fontSize: 12,
            color: Tme('smallTextColor'),
            marginTop: 5,
          }}>
          {label}
        </Text>
      </View>
    </TouchableOpacity>
  );
};
