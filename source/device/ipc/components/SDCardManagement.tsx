import React from 'react';
import {View, Text, TouchableOpacity, Alert} from 'react-native';
import {ProgressBar} from 'react-native-paper';
import I18n from '../../../I18n';
import {Tme, Colors} from '../../../ThemeStyle';
import {mainRadius} from '../../../Tools';
import {DecodedData, getDpsCmd, sendSimpleCommand} from '../ipcUtils';
import SwitchBtn from '../../../share/SwitchBtn';
import {hideLoading, showLoading} from '../../../../ILoading';
import {Toast} from '../../../Toast';

interface SDCardManagementProps {
  dcData: DecodedData | null;
  sdState: string;
  storage: string[];
  navigation?: any;
  nodeSn: string;
  refresh: () => void;
  setDcData: (data: DecodedData) => void;
}

const SDCardManagement: React.FC<SDCardManagementProps> = ({
  dcData,
  sdState,
  storage,
  nodeSn,
  refresh,
  setDcData,
}) => {
  // SD卡录制类型设置
  const setSdRecordType = (newType: number): void => {
    if (!dcData || !dcData.sdInfo) {
      return;
    }

    showLoading();
    sendSimpleCommand(
      nodeSn,
      {
        ipc_cmd_key: 'set_sd_record_type',
        value: `${newType}`,
      },
      (decodedData: DecodedData) => {
        setDcData(decodedData);
        Toast.show();
        refresh();
      },
    )
      .catch(() => {
        Toast.show(I18n.t('ipc.operation_failed'));
      })
      .finally(() => {
        hideLoading();
      });
  };

  // 格式化SD卡
  const sdReset = (): void => {
    Alert.alert(I18n.t('home.warning_message'), I18n.t('ipc.sd_reset_desp'), [
      {
        text: I18n.t('home.cancel'),
        onPress: () => {},
      },
      {
        text: I18n.t('home.confirm'),
        onPress: () => {
          showLoading();
          getDpsCmd(nodeSn, [
            {
              ipc_cmd_key: 'set_sd_format',
              value: '',
            },
          ])
            .then(() => {
              Toast.show();
            })
            .catch(() => {
              Toast.show(I18n.t('ipc.operation_failed'));
            })
            .finally(() => {
              hideLoading();
            });
        },
      },
    ]);
  };

  // SD卡开关切换处理
  const handleSdRecordStatChange = (): void => {
    if (!dcData || !dcData.sdInfo) {
      return;
    }
    showLoading();
    sendSimpleCommand(
      nodeSn,
      {
        ipc_cmd_key: 'switch_sd_recode',
        value: `${dcData.sdInfo.sdRecordStat === 1 ? 0 : 1}`,
      },
      (decodedData: DecodedData) => {
        setDcData(decodedData);
        refresh();
        Toast.show();
      },
    )
      .catch(() => {
        Toast.show(I18n.t('ipc.operation_failed'));
      })
      .finally(() => {
        hideLoading();
      });
  };

  return (
    <View style={{marginBottom: 20, marginHorizontal: 15}}>
      <View
        style={{
          overflow: 'hidden',
          marginBottom: 2,
          borderRadius: mainRadius(),
        }}>
        {/* SD卡状态标题 */}
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            marginBottom: 1,
            paddingVertical: 16,
            paddingHorizontal: 16,
          }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <Text
              style={{
                color: Tme('cardTextColor'),
              }}>
              {I18n.t('tuya.sd_state')}
            </Text>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <View
                style={{
                  width: 10,
                  height: 10,
                  borderRadius: 5,
                  backgroundColor:
                    dcData?.sdInfo?.sdStatus === 1
                      ? '#4CAF50'
                      : dcData?.sdInfo?.sdStatus === 0
                      ? '#F44336'
                      : '#FF9800',
                  marginRight: 8,
                }}
              />
              <Text style={{color: Tme('cardTextColor')}}>{sdState}</Text>
            </View>
          </View>
        </View>

        {/* 存储空间信息 */}
        {dcData?.sdInfo?.sdStatus === 1 && (
          <View
            style={{
              backgroundColor: Tme('cardColor'),
              marginBottom: 1,
              paddingVertical: 16,
              paddingHorizontal: 16,
            }}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginBottom: 4,
              }}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('tuya.sd_storage')}
              </Text>
              <Text style={{color: Tme('cardTextColor')}}>
                {storage[1]} GB / {storage[0]} GB
              </Text>
            </View>

            <ProgressBar
              progress={
                parseFloat(storage[1]) && parseFloat(storage[0])
                  ? Math.min(parseFloat(storage[1]) / parseFloat(storage[0]), 1)
                  : 0
              }
              color={Colors.MainColor}
              style={{height: 8, borderRadius: 4, marginTop: 8}}
            />

            <View style={{marginTop: 8, alignItems: 'flex-end'}}>
              <Text style={{color: Tme('smallTextColor'), fontSize: 12}}>
                {(() => {
                  const total = parseFloat(storage[0]) || 0;
                  const used = parseFloat(storage[1]) || 0;
                  const remaining = Math.max(total - used, 0).toFixed(2);
                  return I18n.t('ipc.sd_storage_remaining', {
                    remaining: remaining,
                  });
                })()}
              </Text>
            </View>
          </View>
        )}

        {/* SD卡录像开关 */}
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            marginBottom: 1,
            paddingVertical: 16,
            paddingHorizontal: 16,
          }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <Text style={{color: Tme('cardTextColor')}}>
              {I18n.t('ipc.sd_switch')}
            </Text>
            {dcData?.sdInfo && (
              <SwitchBtn
                cusStyle={{marginLeft: -3}}
                trackColor={{true: Colors.MainColor}}
                value={dcData.sdInfo.sdRecordStat === 1}
                change={handleSdRecordStatChange}
              />
            )}
          </View>
        </View>

        {/* SD卡录像管理 */}
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            marginBottom: 1,
            paddingVertical: 16,
            paddingHorizontal: 16,
          }}>
          <Text
            style={{
              color: Tme('cardTextColor'),
              marginBottom: 12,
            }}>
            {I18n.t('ipc.sd_setting')}
          </Text>
          <View style={{flexDirection: 'row', marginTop: 8, width: '100%'}}>
            <TouchableOpacity
              onPress={() => setSdRecordType(0)}
              style={{
                backgroundColor:
                  dcData?.sdInfo?.sdRecordType === 0
                    ? Colors.MainColor
                    : Tme('bgColor'),
                padding: 10,
                borderRadius: mainRadius(),
                flex: 1,
                alignItems: 'center',
                marginRight: 6,
              }}>
              <Text
                style={{
                  color:
                    dcData?.sdInfo?.sdRecordType === 0
                      ? '#fff'
                      : Tme('cardTextColor'),
                }}>
                {I18n.t('ipc.all_day_video')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => setSdRecordType(1)}
              style={{
                backgroundColor:
                  dcData?.sdInfo?.sdRecordType === 1
                    ? Colors.MainColor
                    : Tme('bgColor'),
                padding: 10,
                borderRadius: mainRadius(),
                flex: 1,
                alignItems: 'center',
                marginLeft: 6,
              }}>
              <Text
                style={{
                  color:
                    dcData?.sdInfo?.sdRecordType === 1
                      ? '#fff'
                      : Tme('cardTextColor'),
                }}>
                {I18n.t('ipc.motion_video')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 格式化SD卡 */}
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={sdReset}
          style={{backgroundColor: Tme('cardColor')}}>
          <View
            style={{
              paddingVertical: 16,
              paddingHorizontal: 16,
              alignItems: 'center',
            }}>
            <Text
              style={{
                color: Colors.MainColor,
                fontWeight: '500',
                fontSize: 15,
              }}>
              {I18n.t('ipc.sd_reset')}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default SDCardManagement;
