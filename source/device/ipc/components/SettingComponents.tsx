import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Tme, Colors } from '../../../ThemeStyle';
import Slider from '@react-native-community/slider';
import { mainRadius } from '../../../Tools';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

/**
 * 设置页面通用行组件
 */
export const SettingRow: React.FC<{
  title: string;
  rightTitle?: string;
  rightView?: React.ReactNode;
  onPress?: () => void;
}> = ({ title, rightTitle, rightView, onPress }) => {
  const content = (
    <View
      style={{
        paddingHorizontal: 20,
        flexDirection: 'row',
        paddingVertical: 16,
        justifyContent: 'space-between',
      }}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Text style={{ color: Tme('cardTextColor') }}>{title}</Text>
      </View>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          flex: 1,
          marginLeft: 20,
          justifyContent: 'flex-end',
        }}>
        {rightTitle && <Text style={{ color: Tme('cardTextColor') }}>{rightTitle}</Text>}
        {rightView}
        {!rightView && (
          <MaterialIcons name="keyboard-arrow-right" size={16} color={Tme('textColor')} />
        )}
      </View>
    </View>
  );

  if (onPress) {
    return (
      <TouchableOpacity activeOpacity={0.8} onPress={onPress}>
        {content}
      </TouchableOpacity>
    );
  }

  return content;
};

/**
 * 滑块配置组件
 */
export const SliderRow: React.FC<{
  title: string;
  value: number;
  onChange?: (value: number) => void;
  minimumValue?: number;
  maximumValue?: number;
  onSlidingComplete?: (value: number) => void;
}> = ({
  title,
  value,
  minimumValue = 0,
  maximumValue = 255,
  onSlidingComplete,
}) => {
  // 添加本地状态以跟踪显示值，初始值为传入的value
  const [displayValue, setDisplayValue] = React.useState(value);

  // 当传入的value变化时更新displayValue
  React.useEffect(() => {
    setDisplayValue(value);
  }, [value]);

  return (
    <>
      <View
        style={{
          paddingHorizontal: 20,
          flexDirection: 'row',
          paddingVertical: 16,
          justifyContent: 'space-between',
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Text style={{ color: Tme('cardTextColor') }}>{title}</Text>
        </View>
      </View>

      <View
        style={{
          paddingHorizontal: 20,
          paddingBottom: 16,
        }}>
        <View style={{ marginTop: 8 }}>
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 8,
            }}>
            <Text
              style={{
                fontSize: 22,
                fontWeight: '500',
                color: Tme('cardTextColor'),
              }}>
              {displayValue}
            </Text>
          </View>
          <Slider
            minimumValue={minimumValue}
            maximumValue={maximumValue}
            step={1}
            minimumTrackTintColor={Colors.MainColor}
            value={value}
            // 滑动过程中仅更新显示值
            onValueChange={val => setDisplayValue(Math.round(val))}
            // 滑动完成时执行onSlidingComplete回调
            onSlidingComplete={val => {
              const roundedVal = Math.round(val);
              setDisplayValue(roundedVal);
              if (onSlidingComplete) {
                onSlidingComplete(roundedVal);
              }
            }}
          />
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <Text style={{ color: Tme('cardTextColor') }}>{minimumValue}</Text>
            <Text style={{ color: Tme('cardTextColor') }}>{maximumValue}</Text>
          </View>
        </View>
      </View>
    </>
  );
};

/**
 * 切换按钮组件
 */
export const SwitchButtonRow: React.FC<{
  title: string;
  value: boolean;
  btnsTitle: string[];
  onChange: (value: boolean) => void;
}> = ({ title, value, btnsTitle, onChange }) => {
  return (
    <View>
      <View
        style={{
          paddingHorizontal: 20,
          flexDirection: 'row',
          paddingVertical: 16,
          justifyContent: 'space-between',
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Text style={{ color: Tme('cardTextColor') }}>{title}</Text>
        </View>
      </View>

      <View
        style={{
          flexDirection: 'row',
          paddingHorizontal: 20,
          paddingBottom: 16,
          justifyContent: 'space-between',
        }}>
        <TouchableOpacity
          onPress={() => onChange(false)}
          style={{
            backgroundColor: !value ? Colors.MainColor : Tme('bgColor'),
            padding: 10,
            borderRadius: mainRadius(),
            flex: 1,
            alignItems: 'center',
            marginRight: 6,
          }}>
          <Text
            style={{
              color: !value ? '#fff' : Tme('cardTextColor'),
            }}>
            {btnsTitle[0]}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => onChange(true)}
          style={{
            backgroundColor: value ? Colors.MainColor : Tme('bgColor'),
            padding: 10,
            borderRadius: mainRadius(),
            flex: 1,
            alignItems: 'center',
            marginLeft: 6,
          }}>
          <Text
            style={{
              color: value ? '#fff' : Tme('cardTextColor'),
            }}>
            {btnsTitle[1]}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

/**
 * 多选按钮组
 */
export const MultiButtonGroup: React.FC<{
  items: Array<{ key: number | string; label: string }>;
  selectedKey: number | string;
  onChange: (key: number | string) => void;
  style?: any;
}> = ({ items, selectedKey, onChange, style = {} }) => {
  return (
    <View
      style={{
        flexDirection: 'row',
        paddingHorizontal: 20,
        paddingBottom: 16,
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        ...style,
      }}>
      {items.map((item, index) => (
        <TouchableOpacity
          key={index}
          onPress={() => onChange(item.key)}
          style={{
            backgroundColor: item.key === selectedKey ? Colors.MainColor : Tme('bgColor'),
            padding: 10,
            borderRadius: mainRadius(),
            flex: 1,
            alignItems: 'center',
            marginHorizontal: 3,
            marginBottom: 6,
          }}>
          <Text
            style={{
              color: item.key === selectedKey ? '#fff' : Tme('cardTextColor'),
            }}>
            {item.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

// 创建不包含Tme的静态样式
export const styles = StyleSheet.create({
  sectionContainer: {
    marginVertical: 10,
    borderRadius: mainRadius(),
  },
  sectionHeader: {
    borderTopRightRadius: mainRadius(),
    borderTopLeftRadius: mainRadius(),
  },
  resetButton: {
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  resetButtonText: {
    color: Colors.MainColor,
    fontWeight: '600',
    fontSize: 16,
  },
});

// 用于需要使用styles的组件
export const SectionContainer: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  return (
    <View
      style={[
        styles.sectionContainer,
        { backgroundColor: Tme('cardColor') },
      ]}
    >
      {children}
    </View>
  );
};

export const SectionHeader: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  return (
    <View
      style={[
        styles.sectionHeader,
        { backgroundColor: Tme('cardColor') },
      ]}
    >
      {children}
    </View>
  );
};

export const ResetButton: React.FC<{
  onPress: () => void;
  title: string;
}> = ({ onPress, title }) => {
  return (
    <TouchableOpacity
      style={[
        styles.resetButton,
        { backgroundColor: Tme('cardColor') },
      ]}
      onPress={onPress}
    >
      <Text style={styles.resetButtonText}>{title}</Text>
    </TouchableOpacity>
  );
};
