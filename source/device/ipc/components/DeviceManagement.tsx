import React from 'react';
import {View, Text, TouchableOpacity, Alert} from 'react-native';
import I18n from '../../../I18n';
import {Tme, Colors} from '../../../ThemeStyle';
import {getDpsCmd} from '../ipcUtils';
import {hideLoading, showLoading} from '../../../../ILoading';
import {Toast} from '../../../Toast';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {mainRadius} from '../../../Tools';
import {Helper} from '../../../Helper';

interface DeviceManagementProps {
  navigation: any;
  nodeSn: string;
  node: any;
  needUpdate: boolean;
}

const DeviceManagement: React.FC<DeviceManagementProps> = ({
  navigation,
  nodeSn,
  node,
  needUpdate,
}) => {
  // 更新固件
  const onUpdate = (): void => {
    if (needUpdate) {
      Alert.alert(I18n.t('ipc.update_title'), I18n.t('ipc.update_desp'), [
        {
          text: I18n.t('home.cancel'),
          style: 'cancel',
        },
        {
          text: I18n.t('home.confirm'),
          onPress: () => {
            getDpsCmd(nodeSn, [
              {
                ipc_cmd_key: 'upgrade_firmware',
                value: '',
              },
            ]).then(() => {
              Toast.show();
            });
          },
        },
      ]);
    }
  };

  // 重启设备
  const reboot = (): void => {
    Alert.alert(I18n.t('home.warning_message'), I18n.t('ipc.reboot_desp'), [
      {
        text: I18n.t('home.cancel'),
        onPress: () => {},
      },
      {
        text: I18n.t('home.confirm'),
        onPress: () => {
          showLoading();
          getDpsCmd(nodeSn, [
            {
              ipc_cmd_key: 'reboot',
              value: 'set',
            },
          ]).then(() => {
            hideLoading();
            Toast.show();
          });
        },
      },
    ]);
  };

  // 恢复出厂设置
  const factory = (): void => {
    Alert.alert(I18n.t('home.warning_message'), I18n.t('ipc.factory_desp'), [
      {
        text: I18n.t('home.cancel'),
        onPress: () => {},
      },
      {
        text: I18n.t('home.confirm'),
        onPress: () => {
          showLoading();
          getDpsCmd(nodeSn, [
            {
              ipc_cmd_key: 'set_factory',
              value: '',
            },
          ]).then(() => {
            hideLoading();
            Toast.show();
          });
        },
      },
    ]);
  };

  const removeDevice = (): void => {
    Alert.alert(
      I18n.t('home.remove_device'),
      I18n.t('global.activate_sure'),
      [
        {
          text: I18n.t('home.cancel'),
          onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
        {
          text: I18n.t('home.confirm'),
          onPress: () => {
            showLoading();
            Helper.httpPOST(
              '/partner/devices/delete',
              {
                ensure: () => {
                  hideLoading();
                },
                success: (_data: any) => {
                  navigation.goBack();
                },
              },
              {uuid: node.uuid},
            );
          },
        },
      ],
      {cancelable: false},
    );
  };

  return (
    <View style={{marginBottom: 20, marginHorizontal: 15}}>
      <View
        style={{
          overflow: 'hidden',
          marginBottom: 2,
          borderRadius: mainRadius(),
        }}>
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            marginBottom: 1,
            paddingVertical: 16,
            paddingHorizontal: 16,
          }}>
          <Text
            style={{
              color: Tme('cardTextColor'),
              // fontSize: 16,
              // fontWeight: '500',
            }}>
            {I18n.t('home.device_management')}
          </Text>
        </View>

        <TouchableOpacity
          activeOpacity={0.8}
          onPress={onUpdate}
          style={{
            backgroundColor: Tme('cardColor'),
            marginBottom: 1,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 16,
              paddingVertical: 16,
            }}>
            <View
              style={{
                width: 36,
                height: 36,
                borderRadius: 18,
                backgroundColor: Colors.MainColor + '15',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 12,
              }}>
              <MaterialIcons
                name="system-update"
                size={20}
                color={Colors.MainColor}
              />
            </View>
            <View style={{flex: 1}}>
              <Text
                style={{
                  color: Tme('cardTextColor'),
                  fontWeight: '500',
                  fontSize: 15,
                }}>
                {I18n.t('ipc.update')}
              </Text>
            </View>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              {needUpdate && (
                <View
                  style={{
                    height: 8,
                    width: 8,
                    borderRadius: 4,
                    backgroundColor: '#F44336',
                    marginRight: 6,
                  }}
                />
              )}
              <MaterialIcons
                name="keyboard-arrow-right"
                size={16}
                color={Tme('cardTextColor')}
              />
            </View>
          </View>
        </TouchableOpacity>

        {/* 重启设备按钮 */}
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={reboot}
          style={{
            backgroundColor: Tme('cardColor'),
            marginBottom: 1,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 16,
              paddingVertical: 16,
            }}>
            <View
              style={{
                width: 36,
                height: 36,
                borderRadius: 18,
                backgroundColor: '#4CAF5015',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 12,
              }}>
              <MaterialIcons name="refresh" size={20} color="#4CAF50" />
            </View>
            <View style={{flex: 1}}>
              <Text
                style={{
                  color: Tme('cardTextColor'),
                  fontWeight: '500',
                  fontSize: 15,
                }}>
                {I18n.t('ipc.reboot')}
              </Text>
            </View>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={16}
              color={Tme('cardTextColor')}
            />
          </View>
        </TouchableOpacity>

        {/* 恢复出厂设置按钮 */}
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={factory}
          style={{
            backgroundColor: Tme('cardColor'),
            marginBottom: 1,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 16,
              paddingVertical: 16,
            }}>
            <View
              style={{
                width: 36,
                height: 36,
                borderRadius: 18,
                backgroundColor: '#F4433615',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 12,
              }}>
              <MaterialIcons name="restore" size={20} color="#F44336" />
            </View>
            <View style={{flex: 1}}>
              <Text
                style={{
                  color: Tme('cardTextColor'),
                  fontWeight: '500',
                  fontSize: 15,
                }}>
                {I18n.t('ipc.factory')}
              </Text>

            </View>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={16}
              color={Tme('cardTextColor')}
            />
          </View>
        </TouchableOpacity>

        {/* 删除设备按钮 */}
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={removeDevice}
          style={{
            backgroundColor: Tme('cardColor'),
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingHorizontal: 16,
              paddingVertical: 16,
            }}>
            <View
              style={{
                width: 36,
                height: 36,
                borderRadius: 18,
                backgroundColor: '#F4433615',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 12,
              }}>
              <MaterialIcons name="delete" size={20} color="#F44336" />
            </View>
            <View style={{flex: 1}}>
              <Text
                style={{
                  color: '#F44336',
                  fontWeight: '500',
                  fontSize: 15,
                }}>
                {I18n.t('home.remove_device')}
              </Text>
            </View>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={16}
              color={Tme('cardTextColor')}
            />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default DeviceManagement;
