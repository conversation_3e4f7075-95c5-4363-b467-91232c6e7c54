import React from 'react';
import {View, Text} from 'react-native';
import I18n from '../../../I18n';
import {Tme, Colors} from '../../../ThemeStyle';
import {mainRadius} from '../../../Tools';
import {DecodedData} from '../ipcUtils';

interface NetworkInfoCardProps {
  dcData: DecodedData | null;
  networkType: string;
  signal: number;
}

const NetworkInfoCard: React.FC<NetworkInfoCardProps> = ({
  dcData,
  networkType,
  signal,
}) => {
  // 将信号强度转换为0-4的区间显示
  const getSignalBars = (s: number) => {
    return Math.min(Math.max(Math.floor(s), 0), 4);
  };

  return (
    <View style={{marginBottom: 20, marginHorizontal: 15}}>
      <View
        style={{
          overflow: 'hidden',
          marginBottom: 2,
          borderRadius: mainRadius(),
        }}>
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            marginBottom: 1,
            paddingVertical: 16,
            paddingHorizontal: 16,
          }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <Text
              style={{
                color: Tme('cardTextColor'),
              }}>
              {I18n.t('ipc.network_info')}
            </Text>
            <Text style={{color: Tme('cardTextColor'), fontWeight: '500'}}>
              {networkType}
            </Text>
          </View>
        </View>

        <View
          style={{
            backgroundColor: Tme('cardColor'),
            paddingHorizontal: 16,
            paddingVertical: 16,
          }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              marginBottom: 12,
            }}>
            <Text style={{color: Tme('cardTextColor')}}>
              {I18n.t('ipc.ip_address')}
            </Text>
            <Text style={{color: Tme('cardTextColor'), fontWeight: '500'}}>
              {dcData?.devInfo?.ip || '-'}
            </Text>
          </View>

          {dcData?.devInfo?.ssid && (
            <>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginBottom: 12,
                }}>
                <Text style={{color: Tme('cardTextColor')}}>SSID</Text>
                <Text style={{color: Tme('cardTextColor'), fontWeight: '500'}}>
                  {dcData.devInfo.ssid}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <Text style={{color: Tme('cardTextColor')}}>
                  {I18n.t('tuya.wifi_signal')}
                </Text>
                <View style={{flexDirection: 'row', alignItems: 'flex-end'}}>
                  {[1, 2, 3, 4].map(i => (
                    <View
                      key={i}
                      style={{
                        width: 3,
                        height: i * 3,
                        backgroundColor:
                          getSignalBars(signal) >= i
                            ? Colors.MainColor
                            : '#D0D0D0',
                        marginHorizontal: 1,
                      }}
                    />
                  ))}
                  <Text
                    style={{
                      color: Tme('cardTextColor'),
                      marginLeft: 6,
                      fontWeight: '500',
                    }}>
                    {Math.round((signal / 5) * 100)}%
                  </Text>
                </View>
              </View>
            </>
          )}
        </View>
      </View>
    </View>
  );
};

export default NetworkInfoCard;
