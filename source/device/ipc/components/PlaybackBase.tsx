import React, {useEffect, useRef, useState, useCallback} from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  BackHandler,
  Platform,
} from 'react-native';
import moment from 'moment';
import {Colors, Tme} from '../../../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Slider from '@react-native-community/slider';
import {SelectDate} from '../../../share/SelectDateView';
import I18n from '../../../I18n';
import WebRtcLocalPlayBack, {
  WebRtcLocalPlayBackRef,
} from '../WebRtcLocalPlayBack';
import {showLoading, hideLoading} from '../../../../ILoading';

// 导入自定义Hook
import useVideoOrientation from '../hooks/useVideoOrientation';
import usePlaybackControl from '../hooks/usePlaybackControl';
import usePlaybackFileList, {
  PlaybackFileData,
} from '../hooks/usePlaybackFileList';
import EmptyView from '../../../share/EmptyView';

/**
 * 回放组件属性接口
 */
export interface PlaybackBaseProps {
  /** 播放源类型: "local" 本地回放, "online" 在线回放 */
  playbackSource: 'local' | 'online';
  /** 客户端ID */
  clientId?: string;
  /** 主机地址 */
  host?: string;
  /** 导航对象 */
  navigation: any;
  /** 设备ID */
  deviceId?: string;
  /** 载入客户端ID和主机地址数据 */
  loadClientData?: () => Promise<{
    clientId: string;
    host: string;
  }>;
  /** 错误处理函数 */
  onError?: (error: string) => void;
}

/**
 * 基础回放组件
 * @param props 组件属性
 * @returns 回放组件
 */
const PlaybackBase: React.FC<PlaybackBaseProps> = ({
  playbackSource,
  clientId: initialClientId,
  host: initialHost,
  navigation,
  loadClientData,
  onError,
}) => {
  // 状态管理
  const [clientId, setClientId] = useState<string>(initialClientId || '');
  const [host, setHost] = useState<string>(initialHost || '');
  const [isReady, setIsReady] = useState<boolean>(false);

  // 引用
  const webViewRef = useRef<WebRtcLocalPlayBackRef>(null);

  // 使用自定义Hooks
  const {
    videoIcon,
    videoWidth,
    videoHeight,
    controllerShow,
    bgColor,
    toggleOrientation,
    changePortrait,
  } = useVideoOrientation({navigation});

  const {
    sliderValue,
    getMaxTime,
    selectVideoFile,
    seekToPosition,
    cleanup: cleanupPlaybackControl,
  } = usePlaybackControl({webViewRef});

  const {fileList, currentDate, updateFileList, changeDate, clearFileList} =
    usePlaybackFileList({webViewRef, onError});

  /**
   * 加载客户端数据
   */
  const initializeClientData = useCallback(async () => {
    try {
      // 如果已经有初始数据，直接设置为就绪状态
      if (initialClientId && initialHost) {
        setIsReady(true);
        return;
      }

      // 如果提供了加载函数，使用它来获取数据
      if (loadClientData) {
        showLoading();
        const data = await loadClientData();
        setClientId(data.clientId);
        setHost(data.host);
        setIsReady(true);
      } else {
        // 如果既没有初始数据也没有加载函数，显示错误
        throw new Error('No client data provided');
      }
    } catch (error) {
      if (onError) {
        onError(error instanceof Error ? error.message : '加载失败');
      }
    } finally {
      hideLoading();
    }
  }, [initialClientId, initialHost, loadClientData, onError]);

  /**
   * 文件列表项渲染
   */
  const renderFileItem = useCallback(
    ({item}: {item: PlaybackFileData}) => {
      return (
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={() => selectVideoFile(item.filename)}
          style={{
            borderRadius: 4,
            backgroundColor: Tme('cardColor'),
            paddingHorizontal: 20,
            paddingVertical: 20,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: 1,
          }}>
          <View>
            <Text style={{color: Tme('cardTextColor')}}>
              {`${moment(item.startTime * 1000).format(
                'YYYY-MM-DD HH:mm:ss',
              )} - ${moment(item.endTime * 1000).format(
                'YYYY-MM-DD HH:mm:ss',
              )}`}
            </Text>
          </View>
          <MaterialIcons
            name="keyboard-arrow-right"
            size={20}
            color={Tme('textColor')}
          />
        </TouchableOpacity>
      );
    },
    [selectVideoFile],
  );

  // 初始化数据
  useEffect(() => {
    initializeClientData();
  }, [initializeClientData]);

  // 处理Android返回键
  useEffect(() => {
    if (Platform.OS === 'android') {
      const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        () => {
          if (videoIcon === 'landscape') {
            changePortrait();
            return true; // 事件已处理
          }
          // 例如：可以添加返回到上一个界面的逻辑
          if (navigation && navigation.canGoBack()) {
            navigation.goBack();
            return true; // 事件已处理
          }
          return false; // 未处理，交由系统处理
        },
      );

      return () => backHandler.remove();
    }
  }, [navigation, videoIcon, changePortrait]);

  // 组件卸载时的清理
  useEffect(() => {
    return () => {
      hideLoading();
      cleanupPlaybackControl();
      clearFileList();
    };
  }, [cleanupPlaybackControl, clearFileList]);

  // 如果未就绪，不渲染内容
  if (!isReady) {
    return null;
  }

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: bgColor,
        position: 'relative',
        alignItems: videoIcon === 'portrait' ? undefined : 'center',
      }}>
      {/* 全屏/退出全屏按钮 */}
      {videoIcon === 'portrait' ? (
        <TouchableOpacity
          hitSlop={{top: 10, left: 10, bottom: 10, right: 10}}
          activeOpacity={0.8}
          onPress={toggleOrientation}
          style={{
            height: 40,
            width: 40,
            borderRadius: 8,
            zIndex: 999,
            backgroundColor: 'rgba(0,0,0,0.2)',
            position: 'absolute',
            right: 20,
            top: 20,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Ionicons name="expand-sharp" size={26} color="white" />
        </TouchableOpacity>
      ) : (
        <TouchableOpacity
          hitSlop={{top: 10, left: 10, bottom: 10, right: 10}}
          activeOpacity={0.8}
          onPress={toggleOrientation}
          style={{
            height: 40,
            width: 40,
            borderRadius: 8,
            backgroundColor: 'rgba(0,0,0,0.2)',
            position: 'absolute',
            right: 40,
            top: 40,
            zIndex: 100,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Ionicons name="close-outline" size={26} color="white" />
        </TouchableOpacity>
      )}

      {/* 视频播放区域 */}
      <View
        style={{
          width: videoWidth,
          height: videoHeight,
          backgroundColor: 'black',
        }}>
        <WebRtcLocalPlayBack
          from={playbackSource}
          ref={webViewRef}
          clientId={clientId}
          host={host}
          openDataChannel={() => {
            showLoading();
            webViewRef.current?.getfilelist([
              moment(currentDate).format('YYYY-MM-DD'),
              moment(currentDate).add(1, 'days').format('YYYY-MM-DD'),
            ]);
          }}
          getFileList={updateFileList}
        />
      </View>

      {/* 进度条 */}
      {getMaxTime() > 0 && (
        <View
          style={{
            position: 'absolute',
            top: videoHeight - 40,
            left: 10,
            right: 10,
            zIndex: 10,
          }}>
          <Slider
            minimumValue={0}
            maximumValue={getMaxTime()}
            step={1}
            minimumTrackTintColor={Colors.MainColor}
            value={sliderValue}
            onSlidingComplete={seekToPosition}
          />
        </View>
      )}

      {/* 日期选择器和文件列表 - 仅在非全屏模式下显示 */}
      {controllerShow && (
        <>
          <SelectDate
            from="date"
            title={I18n.t('setting.starting_time')}
            value={currentDate}
            onChange={changeDate}
          />
          <FlatList
            data={fileList}
            style={{paddingTop: 20}}
            renderItem={renderFileItem}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item, index) => index.toString()}
            // eslint-disable-next-line react/no-unstable-nested-components
            ItemSeparatorComponent={() => <View style={{height: 1}} />}
            ListEmptyComponent={<EmptyView />}
            ListFooterComponent={<View style={{height: 50}} />}
          />
        </>
      )}
    </View>
  );
};

export default PlaybackBase;
