import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import I18n from '../../../I18n';
import {Tme} from '../../../ThemeStyle';
import {DecodedData} from '../ipcUtils';
import {mainRadius} from '../../../Tools';

interface FunctionalMenuItemsProps {
  navigation: any;
  node: any;
  dcData: DecodedData;
  host?: string;
  clientid?: string;
}

const FunctionalMenuItems: React.FC<FunctionalMenuItemsProps> = ({
  navigation,
  node,
  dcData,
  host,
  clientid,
}) => {
  // 导航到告警设置页面
  const onAlertClik = (): void => {
    navigation.push('IpcAlarmSetting', {
      title: I18n.t('ipc.alarm_setting'),
      node: node,
      host: host,
      clientid: clientid,
    });
  };

  // // 导航到夜视设置页面

  return (
    <View style={{marginBottom: 20, marginHorizontal: 15}}>
      <View
        style={{
          overflow: 'hidden',
          borderRadius: mainRadius(),
        }}>
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            marginBottom: 1,
            paddingVertical: 16,
            paddingHorizontal: 16,
          }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <Text
              style={{
                color: Tme('cardTextColor'),
              }}>
              {I18n.t('ipc.camera_setting')}
            </Text>
          </View>
        </View>
        <TouchableOpacity
          onPress={()=> {
            navigation.push('IpcImageSetting', {
              title: I18n.t('ipc.video_setting'),
              dcData: dcData,
              node: node,
            });
          }}
          activeOpacity={0.8}
          style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
          <View
            style={{
              flexDirection: 'row',
              paddingLeft: 20,
              paddingRight: 16,
              paddingVertical: 16,
              justifyContent: 'space-between',
            }}>
            <Text style={{color: Tme('cardTextColor')}}>
              {I18n.t('ipc.video_setting')}
            </Text>
            <View style={{flexDirection: 'row'}}>
              <MaterialIcons
                name="keyboard-arrow-right"
                size={16}
                color={Tme('cardTextColor')}
              />
            </View>
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={onAlertClik}
          activeOpacity={0.8}
          style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
          <View
            style={{
              flexDirection: 'row',
              paddingLeft: 20,
              paddingRight: 16,
              paddingVertical: 16,
              justifyContent: 'space-between',
            }}>
            <Text style={{color: Tme('cardTextColor')}}>
              {I18n.t('ipc.alarm_setting')}
            </Text>
            <View style={{flexDirection: 'row'}}>
              <MaterialIcons
                name="keyboard-arrow-right"
                size={16}
                color={Tme('cardTextColor')}
              />
            </View>
          </View>
        </TouchableOpacity>

        {/* <TouchableOpacity
          onPress={onNightClik}
          activeOpacity={0.8}
          style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
          <View
            style={{
              flexDirection: 'row',
              paddingLeft: 20,
              paddingRight: 16,
              paddingVertical: 16,
              justifyContent: 'space-between',
            }}>
            <Text style={{color: Tme('cardTextColor')}}>
              {I18n.t('ipc.night_light')}
            </Text>
            <View style={{flexDirection: 'row'}}>
              <MaterialIcons
                name="keyboard-arrow-right"
                size={16}
                color={Tme('cardTextColor')}
              />
            </View>
          </View>
        </TouchableOpacity> */}
        {/* <TouchableOpacity
        activeOpacity={0.8}
        onPress={setMicVolue}
        style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
        <View
          style={{
            flexDirection: 'row',
            paddingLeft: 20,
            paddingRight: 16,
            paddingVertical: 16,
            justifyContent: 'space-between',
          }}>
          <Text style={{color: Tme('cardTextColor')}}>
            {I18n.t('ipc.mic_volume')}
          </Text>
          <View
            style={{
              flexDirection: 'row',
              flex: 1,
              marginLeft: 20,
              justifyContent: 'flex-end',
            }}>
            <Text style={{color: Tme('cardTextColor')}}>
              {dcData.BasicCfg?.mic_volume || 0}
            </Text>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={16}
              color={Tme('textColor')}
            />
          </View>
        </View>
      </TouchableOpacity> */}
      </View>
    </View>
  );
};

export default FunctionalMenuItems;
