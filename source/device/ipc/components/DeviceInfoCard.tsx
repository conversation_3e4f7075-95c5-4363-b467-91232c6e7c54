import React from 'react';
import {View, Text} from 'react-native';
import {Tme} from '../../../ThemeStyle';
import {mainRadius} from '../../../Tools';
import {DecodedData} from '../ipcUtils';
import {Device} from '../../../types/home';
import I18n from '../../../I18n';

interface DeviceInfoCardProps {
  node: Device;
  dcData: DecodedData | null;
}

const DeviceInfoCard: React.FC<DeviceInfoCardProps> = ({node, dcData}) => {
  return (
    <View style={{marginBottom: 20, marginHorizontal: 15, marginTop: 10}}>
      <View
        style={{
          overflow: 'hidden',
          borderRadius: mainRadius(),
        }}>
        {/* 标题区域 */}
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            marginBottom: 1,
            paddingVertical: 16,
            paddingHorizontal: 16,
          }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <Text
              style={{
                color: Tme('cardTextColor'),
              }}>
              {node.display_name}
            </Text>
            <View
              style={{
                width: 8,
                height: 8,
                borderRadius: 4,
                backgroundColor: node.is_alive ? '#4CAF50' : '#F44336',
              }}
            />
          </View>
        </View>

        {/* 内容区域 */}
        <View
          style={{
            backgroundColor: Tme('cardColor'),
            paddingVertical: 16,
            paddingHorizontal: 16,
          }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              marginBottom: 12,
            }}>
            <Text style={{color: Tme('cardTextColor')}}>SN</Text>
            <Text style={{color: Tme('cardTextColor'), fontWeight: '500'}}>
              {node.sn}
            </Text>
          </View>

          {dcData?.devInfo && (
            <>
              {dcData.devInfo.model && (
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    marginBottom: 12,
                  }}>
                  <Text style={{color: Tme('cardTextColor')}}>
                    {I18n.t('ipc.type')}
                  </Text>
                  <Text
                    style={{color: Tme('cardTextColor'), fontWeight: '500'}}>
                    {dcData.devInfo.model}
                  </Text>
                </View>
              )}
            </>
          )}

          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            <Text style={{color: Tme('cardTextColor')}}>
              {I18n.t('ipc.last_time')}
            </Text>
            <Text
              style={{
                color: Tme('cardTextColor'),
                fontWeight: '500',
              }}>
              {new Date(node.last_received_update_time * 1000).toLocaleString()}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default DeviceInfoCard;
