import IpcScanner from '../../../IpcScanner';
import {HelperMemo} from '../../Helper';
import {Device} from '../../types/home';
import {IpcSource} from './ipcTypes';

/**
 * 定义扫描结果类型接口
 */
export interface ScanResult {
  sn: string;
  ip: string;
  [key: string]: any;
}

/**
 * 定义设备连接状态接口
 */
export interface DeviceConnectionInfo {
  host: string | null;
  connectType: IpcSource;
  isConnected: boolean;
}

/**
 * 获取设备的主机地址
 * 优先使用局域网IP，如果找不到再使用 webrtc_host
 *
 * @param item 设备数据对象
 * @returns 设备主机地址或null
 */
export function getDeviceHost(item: Device): string | null {
  if (!item?.ipc) {
    return null;
  }

  if (!HelperMemo.ipc_is_local_play) {
    return item.ipc.webrtc_host || null;
  }

  const scan = IpcScanner.getScanResults() as ScanResult[] | null;

  if (scan && Array.isArray(scan) && scan.length > 0) {
    const snu = item.ipc.webrtc_uuid;
    if (!snu) {
      return item.ipc.webrtc_host || null;
    }

    // 统一使用 uuid 进行匹配
    const matchedDevice = scan.find(o => o.sn === item.sn);
    if (matchedDevice) {
      return matchedDevice.ip + ':9445'; // 使用局域网IP
    }
  }
  return item.ipc.webrtc_host || null;
}

/**
 * 判断设备是使用本地连接还是云端连接
 *
 * @param host 主机地址
 * @returns 'local' 或 'cloud'
 */
export function getViewFrom(host: string | null): IpcSource {
  // 如果 host 为空，返回 'cloud'
  if (!host) {
    return IpcSource.Cloud;
  }

  if (!HelperMemo.ipc_is_local_play) {
    return IpcSource.Cloud;
  }

  // 判断是否为本地连接
  // 1. 包含 localhost
  // 2. 匹配 IP 地址模式：xxx.xxx.xxx.xxx 或 xxx.xxx.xxx.xxx:port
  const ipPattern = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(:\d+)?$/;

  return host.includes('localhost') || ipPattern.test(host)
    ? IpcSource.Local
    : IpcSource.Cloud;
}

/**
 * 获取设备连接信息
 *
 * @param device 设备对象
 * @returns 设备连接信息
 */
export function getDeviceConnectionInfo(device: Device): DeviceConnectionInfo {
  const host = getDeviceHost(device);
  const connectType = getViewFrom(host);

  return {
    host,
    connectType,
    isConnected: !!host,
  };
}

/**
 * 检查设备是否在线
 *
 * @param device 设备对象
 * @returns 是否在线
 */
export function isDeviceOnline(device: Device): boolean {
  return device?.is_alive === true;
}

/**
 * 格式化存储空间 (KB -> GB)
 *
 * @param sizeInKB 以KB为单位的大小
 * @param decimals 小数位数，默认为2
 * @returns 格式化后的GB字符串
 */
export function formatStorageSize(
  sizeInKB: number,
  decimals: number = 2,
): string {
  const sizeInGB = sizeInKB / 1024 / 1024;
  return sizeInGB.toFixed(decimals);
}

/**
 * 解析设备网络类型
 *
 * @param ssid SSID信息
 * @returns 网络类型字符串
 */
export function getNetworkTypeFromSSID(
  ssid: string | undefined | null,
): string {
  const hasWifi = !!(ssid && ssid.length > 0);
  return hasWifi ? 'Wi-Fi' : 'wired';
}

export function uuid(): string {
  const s: string[] = [];
  const hexDigits = '0123456789abcdef';
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.charAt(Math.floor(Math.random() * 0x10));
  }
  s[14] = '4';
  // eslint-disable-next-line no-bitwise
  s[19] = hexDigits.charAt((parseInt(s[19], 16) & 0x3) | 0x8);
  s[8] = s[13] = s[18] = s[23] = '-';
  return s.join('');
}
