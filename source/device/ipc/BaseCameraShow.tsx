import React, {useState, useEffect, useRef, useCallback} from 'react';
import {
  View,
  TouchableOpacity,
  BackHandler,
  Platform,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import IpcHtmlShow from './ipc_html/IpcHtmlShow';
import {IpcCameraControl} from './IpcCameraControl';
import AlertModal from '../../share/AlertModal';
import I18n from '../../I18n';
import {IpcSource} from './ipcTypes';
import {useLifecycleEvents} from './hooks/useLifecycleEvents';
import {useVideoOrientation} from './hooks/useVideoOrientation';
import {Device} from '../../types/home';
import {PubSubEvent} from '../../types/PubSubEvent';

// 定义基础组件的属性类型
export interface BaseCameraShowProps {
  navigation: any;
  clientid: string;
  host: string;
  node: Device;
  ipcSource: IpcSource;
  onInitConnection?: () => void;
  onError?: (err: any) => void;
  needUpdateFirmware?: boolean;
}

export const BaseCameraShow: React.FC<BaseCameraShowProps> = props => {
  const {
    navigation,
    clientid,
    host,
    node,
    ipcSource,
    onError,
    needUpdateFirmware = false,
  } = props;

  // 使用自定义钩子处理视频方向
  const {
    videoIcon,
    videoWidth,
    videoHeight,
    controllerShow,
    bgColor,
    changePortrait,
    toggleOrientation,
  } = useVideoOrientation({
    aspectRatio: 16 / 9,
    navigation,
  });

  // 状态定义
  const [mountWebView, setMountWebView] = useState<boolean>(false);
  const [dataReady, setDataReady] = useState<boolean>(false);

  // 引用
  const localIpcRef = useRef<any>(null);
  const readyHandledRef = useRef<boolean>(false);

  // 处理生命周期事件
  const isActive = useLifecycleEvents({navigation});

  // 当活动状态改变时更新 mountWebView
  useEffect(() => {
    if (isActive && dataReady) {
      setMountWebView(true);
      // 当页面重新获得焦点时，重置标志并重新初始化连接
      if (localIpcRef.current) {
        readyHandledRef.current = false;
        const sourceType = ipcSource === IpcSource.Local ? 'local' : 'cloud';
        localIpcRef.current.initConnection(host, clientid, sourceType);
      }
    } else {
      setMountWebView(false);
      // 页面失去焦点时重置标志
      readyHandledRef.current = false;
    }
  }, [isActive, dataReady, host, clientid, ipcSource]);

  // 监听对讲事件，重置readyHandledRef
  useEffect(() => {
    const speakSubscription = PubSub.subscribe(PubSubEvent.ios_speak, () => {
      readyHandledRef.current = false;
    });

    return () => {
      PubSub.unsubscribe(speakSubscription);
    };
  }, []);

  // 使用useCallback包装onBackAndroid函数
  const onBackAndroid = useCallback((): boolean => {
    if (videoIcon === 'landscape') {
      // 如果是全屏状态，先切换到竖屏
      changePortrait();
      return true; // 拦截返回事件
    }
    // 如果已经是竖屏状态，则允许返回操作
    return false;
  }, [videoIcon, changePortrait]);

  // 使用 Android 返回按钮处理
  useEffect(() => {
    let backHandler: any;
    if (Platform.OS === 'android') {
      backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        onBackAndroid,
      );
    }

    setDataReady(true);

    return () => {
      if (backHandler) {
        backHandler.remove();
      }
    };
  }, [onBackAndroid]);

  // 处理 WebView 准备就绪
  const handleReady = (): void => {
    if (readyHandledRef.current) {
      // console.log('IpcHtmlShow ready事件已经处理过，忽略重复事件');
      return;
    }

    readyHandledRef.current = true;
    const sourceType = ipcSource === IpcSource.Local ? 'local' : 'cloud';
    localIpcRef.current?.initConnection(host, clientid, sourceType);
  };

  // 处理视频播放错误
  const handleError = (err: any): void => {
    if (onError) {
      onError(err);
    } else {
      AlertModal.alert('', I18n.t('home.network_issue_desp'), [
        {
          text: I18n.t('home.cancel'),
          onDismiss: () => {
            navigation.goBack();
          },
        },
      ]);
    }
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: bgColor,
        alignItems: 'center',
      }}>
      <View style={{flexDirection: 'row'}}>
        {videoIcon === 'portrait' ? (
          <TouchableOpacity
            hitSlop={{top: 10, left: 10, bottom: 10, right: 10}}
            activeOpacity={0.8}
            onPress={toggleOrientation}
            style={{
              height: 40,
              width: 40,
              borderRadius: 8,
              zIndex: 999,
              backgroundColor: 'rgba(0,0,0,0.2)',
              position: 'absolute',
              right: 20,
              top: 20,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Ionicons name="expand-sharp" size={26} color="white" />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            hitSlop={{top: 10, left: 10, bottom: 10, right: 10}}
            activeOpacity={0.8}
            onPress={toggleOrientation}
            style={{
              height: 40,
              width: 40,
              borderRadius: 8,
              backgroundColor: 'rgba(0,0,0,0.2)',
              position: 'absolute',
              right: 40,
              top: 40,
              zIndex: 100,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Ionicons name="close-outline" size={26} color="white" />
          </TouchableOpacity>
        )}

        <View
          style={{
            width: videoWidth,
            height: videoHeight,
            backgroundColor: 'black',
          }}>
          {mountWebView && host && (
            <IpcHtmlShow
              ref={localIpcRef}
              onReady={handleReady}
              onError={handleError}
              host={host}
              clientid={clientid}
              from={ipcSource}
            />
          )}
        </View>
      </View>

      {controllerShow && (
        <IpcCameraControl
          from={ipcSource}
          clientid={clientid}
          host={host}
          need_update_firmware={needUpdateFirmware}
          node={node}
        />
      )}
    </View>
  );
};
