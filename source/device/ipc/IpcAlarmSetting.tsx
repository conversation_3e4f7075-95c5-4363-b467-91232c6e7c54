import React, {useEffect, useRef, useState} from 'react';
import {
  Dimensions,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {hideLoading, showLoading} from '../../../ILoading';
import AlertModal from '../../share/AlertModal';
import _ from 'lodash';
import {Tme, Colors} from '../../ThemeStyle';
import I18n from '../../I18n';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import SwitchBtn from '../../share/SwitchBtn';
import {mainRadius} from '../../Tools';
import {
  DecodedData,
  sendSimpleCommand,
  sendCommandAndGetData,
} from './ipcUtils';
import {useGetState} from 'ahooks';
import {IpcNavigationProp, IpcRouteParamList} from './types/navigation';
// 引入通用UI组件和助手函数
import {
  SettingRow,
  SliderRow,
  MultiButtonGroup,
} from './components/SettingComponents';
import {showWeekDays} from './utils/ipcHelpers';
// 引入 EventEmitter 以设置最大监听器数量
import {EventEmitter} from 'events';
import { Toast } from '../../Toast';

// 增加 EventEmitter 默认最大监听器数量限制
EventEmitter.defaultMaxListeners = 20;

const times = [
  {label: '3', value: 180},
  {label: '5', value: 300},
  {label: '10', value: 600},
  {label: '15', value: 900},
  {label: '30', value: 1800},
];

const ipcScanType = [
  {key: 'package', label: 'package'},
  {key: 'person', label: 'person'},
  {key: 'animal', label: 'animal'},
  {key: 'car', label: 'car'},
];

const ipcMotionSene = [
  {key: 0, label: 'normal'},
  {key: 1, label: 'high'},
];

export default function IpcAlarmSetting() {
  const navigation = useNavigation<IpcNavigationProp>();
  const route = useRoute<RouteProp<IpcRouteParamList, 'IpcAlarmSetting'>>();

  const [dcData, setDcData] = useState<DecodedData | null>(null);
  const [volume, setVolume] = useGetState<number>(0);
  const [motionSensitivity, setMotionSensitivity] = useState<number>(0);
  const [motionTypes, setMotionTypes] = useState<string[]>([]);
  const dcDataRef = useRef<DecodedData | null>(null);

  useEffect(() => {
    const focusEvent = navigation.addListener('focus', () => {
      doFetchData();
    });
    return () => {
      focusEvent();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const doFetchData = async (): Promise<void> => {
    showLoading();
    try {
      await sendCommandAndGetData(
        route.params.node.sn,
        [
          {ipc_cmd_key: 'get_basic_cfg', value: ''},
          {ipc_cmd_key: 'get_alarm_info', value: ''},
        ],
        (decodedData: DecodedData) => {
          setMotionTypes(decodedData.ipc_alarm_types || []);
          setDcData(decodedData);
          if (decodedData.BasicCfg) {
            setVolume(decodedData.BasicCfg.volume);
          }
          if (decodedData.motionInfo) {
            setMotionSensitivity(decodedData.motionInfo.motion_sensitivity);
          }
          dcDataRef.current = decodedData;
          console.log('获取设备数据成功:', decodedData);
        },
      );
    } catch (error: unknown) {
      AlertModal.alert(
        '',
        _.uniq([error instanceof Error ? error.message : String(error)]).join(
          '\n',
        ),
        [
          {
            text: I18n.t('home.cancel'),
            onPress: () => navigation.goBack(),
            style: 'cancel',
          },
        ],
      );
    } finally {
      hideLoading();
    }
  };

  const alarmTime = (): void => {
    if (!dcData?.alarmInfo) {
      return;
    }
    navigation.push('AlarmTime', {
      title: I18n.t('ipc.alarm_time'),
      startTime: dcData.alarmInfo.alarmTime[0],
      endTime: dcData.alarmInfo.alarmTime[1],
      sn: route.params.node.sn,
      from: 'set_alarm_time',
    });
  };

  const alarmWeek = (): void => {
    if (!dcData?.alarmInfo) {
      return;
    }
    navigation.push('AlarmWeek', {
      title: I18n.t('ipc.alarm_day'),
      alarmDay: dcData.alarmInfo.alarmDay,
      sn: route.params.node.sn,
      from: 'week',
    });
  };

  const handleMotionSwitch = async () => {
    showLoading();
    try {
      let msgPush = 0;
      if (dcData?.alarmInfo?.md === 1) {
        msgPush = 0;
      } else {
        msgPush = 7;
      }
      if (dcData?.alarmInfo?.pd === 1) {
        msgPush = 7;
      } else {
        msgPush = msgPush === 0 ? 0 : 7;
      }

      await sendCommandAndGetData(route.params.node.sn, [
        {
          ipc_cmd_key: 'set_msg_push',
          value: `${msgPush}`,
        },
        {
          ipc_cmd_key: 'set_motion_en',
          value: dcData?.alarmInfo?.md === 1 ? '0' : '1',
        },
      ],(decodedData) => {
        Toast.show();
        setDcData(decodedData);
        setMotionTypes(decodedData.ipc_alarm_types || []);
        setDcData(decodedData);
        if (decodedData.BasicCfg) {
          setVolume(decodedData.BasicCfg.volume);
        }
        if (decodedData.motionInfo) {
          setMotionSensitivity(decodedData.motionInfo.motion_sensitivity);
        }
      });

    } catch (error: unknown) {
      AlertModal.alert(
        '',
        _.uniq([error instanceof Error ? error.message : String(error)]).join(
          '\n',
        ),
        [
          {
            text: I18n.t('home.cancel'),
            onPress: () => navigation.goBack(),
            style: 'cancel',
          },
        ],
      );
    } finally {
      hideLoading();
    }
  };

  // 音量调节处理函数
  const handleVolumeChange = (value: number): void => {
    setVolume(value);
  };

  // 音量调节完成后保存设置
  const saveVolumeChange = (value: number): void => {
    showLoading();

    sendSimpleCommand(
      route.params.node.sn,
      {
        ipc_cmd_key: 'set_volume',
        value: `${value}`,
      },
      (decodedData: DecodedData) => {
        Toast.show();
        setDcData(decodedData);
        if (decodedData.BasicCfg) {
          setVolume(decodedData.BasicCfg.volume);
        }
      },
    )
      .catch((error: unknown) => {
        AlertModal.alert(
          '',
          _.uniq([error instanceof Error ? error.message : String(error)]).join(
            '\n',
          ),
          [
            {
              text: I18n.t('home.cancel'),
              onPress: () => navigation.goBack(),
              style: 'cancel',
            },
          ],
        );
      })
      .finally(() => {
        hideLoading();
      });
  };

  // 处理修改后的灵敏度选择功能
  const handleMotionSensitivityChange = (value: string | number): void => {
    const numValue = typeof value === 'string' ? parseInt(value, 10) : value;
    showLoading();
    sendSimpleCommand(
      route.params.node.sn,
      {
        ipc_cmd_key: 'set_motion_sen',
        value: `${numValue}`,
      },
      (decodedData: DecodedData) => {
        Toast.show();
        setDcData(decodedData);
        if (decodedData.motionInfo) {
          setMotionSensitivity(decodedData.motionInfo.motion_sensitivity);
        }
      },
    )
      .catch((error: unknown) => {
        AlertModal.alert(
          '',
          _.uniq([error instanceof Error ? error.message : String(error)]).join(
            '\n',
          ),
          [
            {
              text: I18n.t('home.cancel'),
              onPress: () => navigation.goBack(),
              style: 'cancel',
            },
          ],
        );
      })
      .finally(() => {
        hideLoading();
      });
  };

  const handleTypeSelect = (value: number): void => {
    showLoading();
    sendSimpleCommand(
      route.params.node.sn,
      {
        ipc_cmd_key: 'set_time_interval',
        value: `${value}`,
      },
      (decodedData: DecodedData) => {
        Toast.show();
        setDcData(decodedData);
      },
    )
      .catch((error: unknown) => {
        AlertModal.alert(
          '',
          _.uniq([error instanceof Error ? error.message : String(error)]).join(
            '\n',
          ),
          [
            {
              text: I18n.t('home.cancel'),
              onPress: () => navigation.goBack(),
              style: 'cancel',
            },
          ],
        );
      })
      .finally(() => {
        hideLoading();
      });
  };

  const motionRegion = (): void => {
    if (!dcDataRef.current?.motionInfo) {
      return;
    }
    const {videoWidth, videoHeight} = getVideoRegion();

    navigation.push('IpcRegion', {
      title: I18n.t('ipc.motion_area'),
      region: dcDataRef.current.motionInfo.motion_region,
      sn: route.params.node.sn,
      host: route.params.host,
      clientid: route.params.clientid,
      width: videoWidth,
      height: videoHeight,
      from: 'set_motion_area',
    });
  };

  const getVideoRegion = (): {videoWidth: number; videoHeight: number} => {
    // 获取屏幕的宽度和高度
    const { height} = Dimensions.get('screen');

    const videoWidth = height * 0.58;
    const videoHeight = videoWidth * (9 / 16);

    return {
      videoWidth: Math.floor(videoWidth),
      videoHeight: Math.floor(videoHeight),
    };
  };

  const showMotionTypes = (types: string[]): string => {
    const selectedTypes = types.map(type => {
      const found = ipcScanType.find(item => item.key === type);
      return found ? I18n.t('ipc.' + found.label) : '';
    });
    return selectedTypes.join(', ');
  };

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      style={{flex: 1, padding: 20, backgroundColor: Tme('bgColor')}}>
      {dcData?.alarmInfo && (
        <>
          <View
            style={{
              backgroundColor: Tme('cardColor'),
              borderTopRightRadius: mainRadius(),
              borderTopLeftRadius: mainRadius(),
            }}>
            <SettingRow
              title={I18n.t('ipc.motion_switch')}
              rightView={
                <SwitchBtn
                  cusStyle={{marginLeft: -3}}
                  trackColor={{true: Colors.MainColor}}
                  value={dcData.alarmInfo.md === 1}
                  change={handleMotionSwitch}
                />
              }
            />
          </View>

          <View style={{height: 2}} />

          {/* 移动侦测灵敏度 */}
          <View style={{backgroundColor: Tme('cardColor')}}>
            <View
              style={{
                paddingHorizontal: 20,
                flexDirection: 'row',
                paddingVertical: 16,
                justifyContent: 'space-between',
              }}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('ipc.motion_sensitivity')}
              </Text>
            </View>
            <MultiButtonGroup
              items={ipcMotionSene.map(item => ({
                key: item.key,
                label: I18n.t('tuya.' + item.label),
              }))}
              selectedKey={motionSensitivity}
              onChange={handleMotionSensitivityChange}
            />
          </View>

          <View style={{height: 2}} />

          {/* 移动侦测类型 */}
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => {
              navigation.push('MultipleButton', {
                title: I18n.t('ipc.motion_type'),
                data: ipcScanType,
                defaultSelected: motionTypes, // 直接传递整个数组
                sn: route.params.node.sn,
              });
            }}
            style={{backgroundColor: Tme('cardColor')}}>
            <SettingRow
              title={I18n.t('ipc.motion_type')}
              rightTitle={showMotionTypes(motionTypes)}
            />
          </TouchableOpacity>

          <View style={{height: 2}} />

          {/* 告警时间和天数设置 */}
          <TouchableOpacity
            onPress={alarmTime}
            activeOpacity={0.8}
            style={{backgroundColor: Tme('cardColor')}}>
            <SettingRow
              title={I18n.t('ipc.alarm_time')}
              rightTitle={dcData.alarmInfo.alarmTime.join('-')}
            />
          </TouchableOpacity>

          <View style={{height: 2}} />

          <TouchableOpacity
            activeOpacity={0.8}
            onPress={alarmWeek}
            style={{backgroundColor: Tme('cardColor')}}>
            <SettingRow
              title={I18n.t('ipc.alarm_day')}
              rightTitle={showWeekDays(dcData.alarmInfo.alarmDay)}
            />
          </TouchableOpacity>

          <View style={{height: 2}} />

          {/* 告警时间间隔 */}
          <View style={{backgroundColor: Tme('cardColor')}}>
            <View
              style={{
                paddingHorizontal: 20,
                flexDirection: 'row',
                paddingVertical: 16,
                justifyContent: 'space-between',
              }}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('ipc.alarm_timeInterval')}
              </Text>
            </View>
            <View
              style={{
                flexDirection: 'row',
                paddingHorizontal: 20,
                paddingBottom: 16,
                alignItems: 'center',
                flexWrap: 'wrap',
                flex: 1,
              }}>
              {times.map((item, index) => {
                return (
                  <TouchableOpacity
                    key={index}
                    onPress={() => handleTypeSelect(item.value)}
                    style={{
                      backgroundColor:
                        item.value === Number(dcData.alarmInfo?.timeInterval)
                          ? Colors.MainColor
                          : Tme('bgColor'),
                      paddingVertical: 8,
                      paddingHorizontal: 16,
                      borderRadius: mainRadius(),
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginLeft: 6,
                      marginBottom: 12,
                    }}>
                    <Text
                      style={{
                        color:
                          item.value === Number(dcData.alarmInfo?.timeInterval)
                            ? '#fff'
                            : Tme('cardTextColor'),
                      }}>
                      {I18n.t('ipc.interval_time', {time: item.label})}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>

          <View style={{height: 2}} />

          {/* 移动侦测区域 */}
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={motionRegion}
            style={{backgroundColor: Tme('cardColor')}}>
            <SettingRow title={I18n.t('ipc.motion_area')} />
          </TouchableOpacity>

          <View style={{height: 2}} />

          {/* 音量设置 */}
          <View
            style={{
              backgroundColor: Tme('cardColor'),
              borderBottomRightRadius: mainRadius(),
              borderBottomLeftRadius: mainRadius(),
            }}>
            <SliderRow
              title={I18n.t('ipc.volume')}
              value={volume}
              onChange={handleVolumeChange}
              minimumValue={0}
              maximumValue={100}
              onSlidingComplete={(value)=> {
                saveVolumeChange(value);
              }}
            />
          </View>
        </>
      )}
      <View style={{height: 80}} />
    </ScrollView>
  );
}

interface SensitivityItem {
  key: number;
  value: string;
}

export function getSensitivity(key: string | number): string | undefined {

  const data: SensitivityItem[] = [
    {key: 0, value: I18n.t('tuya.low')},
    {key: 1, value: I18n.t('tuya.middle')},
    {key: 2, value: I18n.t('tuya.high')},
  ];
  const found = _.find(data, {key: Number(key)});
  return found ? found.value : undefined;
}
