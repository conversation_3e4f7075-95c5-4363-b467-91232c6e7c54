import React from 'react';
import {useRoute, useNavigation, RouteProp} from '@react-navigation/native';
import PlaybackBase from './components/PlaybackBase';
import AlertModal from '../../share/AlertModal';
import _ from 'lodash';

// 添加常量
const DEFAULT_PORT = '9445';

// 定义路由参数接口
interface RouteParams {
  clientid: string;
  host: string;
  [key: string]: any;
}

// 定义路由参数列表类型
type RootStackParamList = {
  LocalPlayBack: RouteParams;
};

// 定义路由类型
type LocalPlayBackRouteProp = RouteProp<RootStackParamList, 'LocalPlayBack'>;

/**
 * 本地回放组件
 * 封装了PlaybackBase组件，用于支持本地视频文件回放
 */
const LocalPlayBack: React.FC = () => {
  const route = useRoute<LocalPlayBackRouteProp>();
  const navigation = useNavigation();

  // 从路由参数中获取客户端ID和主机地址
  const clientId = route.params?.clientid || '';
  let host = route.params?.host || '';

  // 处理 host 端口
  if (host) {
    const portRegex = /:\d+$/;
    if (!portRegex.test(host)) {
      host += `:${DEFAULT_PORT}`;
    }
  }

  /**
   * 处理错误信息
   * @param errorMessage 错误信息
   */
  const handleError = (errorMessage: string): void => {
    AlertModal.alert(
      '',
      _.isArray(errorMessage) ? errorMessage.join('\n') : errorMessage,
      [
        {
          text: 'cancel',
          onPress: () => navigation.goBack(),
          style: 'cancel',
        },
      ],
    );
  };

  return (
    <PlaybackBase
      playbackSource="local"
      clientId={clientId}
      host={host}
      navigation={navigation}
      onError={handleError}
    />
  );
};

export default LocalPlayBack;
