import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import {Tme, Colors} from '../../ThemeStyle';
import I18n from '../../I18n';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {mainRadius} from '../../Tools';
import {
  DecodedData,
  sendCommandAndGetData,
  sendSimpleCommand,
} from './ipcUtils';
import AlertModal from '../../share/AlertModal';
import SwitchBtn from '../../share/SwitchBtn';
import {
  SliderRow,
  SwitchButtonRow,
  styles as commonStyles,
} from './components/SettingComponents';
import {Device} from '../../types/home';
import {Helper, HelperMemo} from '../../Helper';
import {hideLoading, showLoading} from '../../../ILoading';
import {Toast} from '../../Toast';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

// 定义参数类型
type RootStackParamList = {
  IpcImageSetting: {
    node: Device;
    title: string;
  };
};

type IpcImageSettingRouteProp = RouteProp<
  RootStackParamList,
  'IpcImageSetting'
>;
type NavigationProps = NativeStackNavigationProp<RootStackParamList>;

// 定义图像设置的类型
interface imgInfo {
  FlipV: number;
  FlipH: number;
  lum: number;
  contrast: number;
  saturation: number;
  sharpness: number;
  ThreeDnr: number;
  WDR: number;
  resolution?: number;
  zoom: string;
}

export default function IpcImageSetting(): React.ReactElement {
  const route = useRoute<IpcImageSettingRouteProp>();
  const navigation = useNavigation<NavigationProps>();
  const [isUpdating, setIsUpdating] = useState(false);
  const isMounted = useRef(true);

  const nightMode = useRef([
    {key: 0, value: I18n.t('ipc.night_auto')},
    {key: 1, value: I18n.t('ipc.night_color')},
    {key: 2, value: I18n.t('ipc.night_black')},
  ]);

  // 状态管理
  const [imageSettings, setImageSettings] = useState<imgInfo>({
    FlipV: 0,
    FlipH: 0,
    lum: 100,
    contrast: 100,
    saturation: 100,
    sharpness: 100,
    ThreeDnr: 100,
    WDR: 0,
    resolution: 1,
    zoom: '1',
  });
  const [decodedData, setDecodedData] = useState<DecodedData>();
  const [isLocal, setIsLocal] = useState(HelperMemo.ipc_is_local_play);

  // 获取设备数据的函数
  const fetchDeviceData = async () => {
    if (!isMounted.current) {
      return;
    }

    try {
      showLoading();
      // 获取基础配置
      await sendCommandAndGetData(
        route.params.node.sn,
        [
          {ipc_cmd_key: 'get_basic_cfg', value: 'get'},
          {ipc_cmd_key: 'get_img_info', value: 'get'},
        ],
        (data: DecodedData) => {
          // 立即检查组件是否仍然挂载
          if (!isMounted.current) {
            return;
          }
          setDecodedData(prev => {
            // 检查数据有效性
            if (!data) {
              return prev;
            }
            return {...prev, ...data};
          });

          // 更新图像设置，同时处理 BasicCfg 和 imgInfo
          setImageSettings(prev => {
            const newSettings = {...prev};

            // 处理基础配置
            if (data?.BasicCfg?.resolution !== undefined) {
              newSettings.resolution = data.BasicCfg.resolution;
            }

            // 处理图像信息
            if (data?.imgInfo) {
              Object.assign(newSettings, data.imgInfo);
            }

            return newSettings;
          });
        },
      );
    } catch (error) {
      if (isMounted.current) {
        Toast.show(I18n.t('global.failed'));
      }
    } finally {
      if (isMounted.current) {
        hideLoading();
      }
    }
  };

  // 发送命令的函数
  const sendCommand = async (command: string, value: any) => {
    if (isUpdating) {
      return;
    }
    setIsUpdating(true);
    showLoading();

    try {
      await sendSimpleCommand(
        route.params.node.sn,
        {ipc_cmd_key: command, value: value.toString()},
        (data: DecodedData) => {
          if (isMounted.current) {
            setDecodedData(prev => ({...prev, ...data}));

            // 更新图像设置
            if (data.imgInfo) {
              setImageSettings(prev => ({
                ...prev,
                ...data.imgInfo,
              }));
            }

            // 更新分辨率设置
            if (data.BasicCfg?.resolution !== undefined) {
              setImageSettings(prev => ({
                ...prev,
                resolution: data.BasicCfg?.resolution,
              }));
            }
          }
        },
      );
      Toast.show();
    } catch (error) {
      Toast.show(I18n.t('global.failed'));
    } finally {
      setIsUpdating(false);
      hideLoading();
    }
  };

  useEffect(() => {
    fetchDeviceData();
    return () => {
      isMounted.current = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 图像设置变更处理函数
  const handleRangeSetting = async (setting: keyof imgInfo, value: number) => {
    if (isUpdating) {
      return;
    }

    // 先更新本地状态
    setImageSettings(prev => ({
      ...prev,
      [setting]: value,
    }));

    const commandMap: {[key: string]: string} = {
      FlipV: 'set_FlipV',
      FlipH: 'set_FlipH',
      ThreeDnr: 'set_3Dnr',
      WDR: 'set_WDR',
      lum: 'set_lum',
      contrast: 'set_contrast',
      saturation: 'set_saturation',
      sharpness: 'set_sharpness',
      resolution: 'switch_resolution',
      zoom: 'set_zoom',
    };

    const command = commandMap[setting];
    if (!command) {
      return;
    }

    let commandValue = value;
    if (setting === 'zoom') {
      commandValue = value * 10;
    }

    // 发送命令并更新状态
    await sendCommand(command, commandValue);
  };

  // 恢复默认设置
  const resetToDefault = () => {
    AlertModal.alert(
      I18n.t('ipc.reset_defaults'),
      I18n.t('ipc.reset_defaults_desp'),
      [
        {
          text: I18n.t('home.cancel'),
          style: 'cancel',
        },
        {
          text: I18n.t('home.confirm'),
          onPress: async () => {
            if (isUpdating) {
              return;
            }
            await sendCommand('set_img_default', '');
            Toast.show(I18n.t('global.successful'));
            navigation.goBack();
          },
        },
      ],
    );
  };

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      style={{flex: 1, padding: 20, backgroundColor: Tme('bgColor')}}>
      {decodedData?.dnmode && (
        <>
          <View
            style={{
              marginTop: 20,
              backgroundColor: Tme('cardColor'),
              borderTopRightRadius: mainRadius(),
              borderTopLeftRadius: mainRadius(),
            }}>
            <View
              style={{
                paddingHorizontal: 20,
                flexDirection: 'row',
                paddingVertical: 16,
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text style={{color: Tme('cardTextColor')}}>
                  {I18n.t('ipc.resolution')}
                </Text>
              </View>
            </View>
            <View
              style={{
                flexDirection: 'row',
                paddingHorizontal: 20,
                paddingBottom: 16,
                justifyContent: 'space-between',
              }}>
              <TouchableOpacity
                disabled={isUpdating}
                style={[
                  styles.resolutionButton,
                  {backgroundColor: Tme('bgColor')},
                  imageSettings.resolution?.toString() === '1' &&
                    styles.resolutionButtonActive,
                ]}
                onPress={() => handleRangeSetting('resolution', 1)}>
                <Text
                  style={[
                    {color: Tme('cardTextColor')},
                    imageSettings.resolution?.toString() === '1' &&
                      styles.resolutionTextActive,
                  ]}>
                  {I18n.t('ipc.resolution_hd')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                disabled={isUpdating}
                style={[
                  styles.resolutionButton,
                  {backgroundColor: Tme('bgColor')},
                  imageSettings.resolution?.toString() === '0' &&
                    styles.resolutionButtonActive,
                ]}
                onPress={() => handleRangeSetting('resolution', 0)}>
                <Text
                  style={[
                    {color: Tme('cardTextColor')},
                    imageSettings.resolution?.toString() === '0' &&
                      styles.resolutionTextActive,
                  ]}>
                  {I18n.t('ipc.resolution_sd')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          <View style={{height: 2}} />
          <View
            style={{
              backgroundColor: Tme('cardColor'),
              borderBottomLeftRadius: mainRadius(),
              borderBottomRightRadius: mainRadius(),
            }}>
            <SliderRow
              title={I18n.t('ipc.zoom')}
              value={parseFloat(imageSettings.zoom) || 1}
              onChange={() => {}}
              minimumValue={1}
              maximumValue={8}
              onSlidingComplete={value => handleRangeSetting('zoom', value)}
            />
          </View>
          <View style={{height: 20}} />
          {/* 夜视 */}
          <View
            style={{
              backgroundColor: Tme('cardColor'),
              borderTopRightRadius: mainRadius(),
              borderTopLeftRadius: mainRadius(),
            }}>
            <View
              style={{
                paddingHorizontal: 20,
                paddingVertical: 16,
              }}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('ipc.night_light')}
              </Text>
            </View>
          </View>
          <View
            style={{
              backgroundColor: Tme('cardColor'),
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              paddingBottom: 16,
              paddingHorizontal: 16,
            }}>
            {nightMode.current.map((item, index) => {
              return (
                <TouchableOpacity
                  disabled={isUpdating}
                  onPress={() => {
                    sendCommand('set_dn_mode', item.key);
                  }}
                  key={index}
                  style={{
                    paddingVertical: 10,
                    borderRadius: mainRadius(),
                    flex: 1,
                    alignItems: 'center',
                    marginLeft: 6,
                    backgroundColor:
                      item.key === decodedData?.dnmode?.DNmode
                        ? Colors.MainColor
                        : Tme('bgColor'),
                  }}>
                  <Text
                    style={{
                      color:
                        item.key === decodedData?.dnmode?.DNmode
                          ? '#fff'
                          : Tme('cardTextColor'),
                    }}>
                    {item.value}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
          <View style={{height: 2}} />
          <View
            style={{
              backgroundColor: Tme('cardColor'),
              borderBottomLeftRadius: mainRadius(),
              borderBottomRightRadius: mainRadius(),
            }}>
            <View
              style={{
                paddingHorizontal: 20,
                paddingVertical: 16,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('ipc.night_switch')}
              </Text>
              <SwitchBtn
                cusStyle={{marginLeft: -3}}
                trackColor={{true: Colors.MainColor}}
                value={decodedData.dnmode.WlightEn === 1}
                change={() => {
                  sendCommand(
                    'switch_white_light',
                    decodedData.dnmode!.WlightEn === 1 ? 0 : 1,
                  );
                }}
              />
            </View>
          </View>
          {/* 图像翻转设置 */}
          <View
            style={{
              marginTop: 20,
              backgroundColor: Tme('cardColor'),
              borderTopRightRadius: mainRadius(),
              borderTopLeftRadius: mainRadius(),
            }}>
            <SwitchButtonRow
              title={I18n.t('ipc.vertical_flip')}
              btnsTitle={[I18n.t('ipc.no_flip'), I18n.t('ipc.flip')]}
              value={imageSettings.FlipV === 1}
              onChange={value => handleRangeSetting('FlipV', value ? 1 : 0)}
            />
          </View>

          <View style={{height: 2}} />
          <View style={{backgroundColor: Tme('cardColor')}}>
            <SwitchButtonRow
              title={I18n.t('ipc.horizontal_flip')}
              btnsTitle={[I18n.t('ipc.no_flip'), I18n.t('ipc.flip')]}
              value={imageSettings.FlipH === 1}
              onChange={value => handleRangeSetting('FlipH', value ? 1 : 0)}
            />
          </View>

          {/* 图像参数设置 */}
          <View style={{height: 2}} />
          <View style={{backgroundColor: Tme('cardColor')}}>
            <SliderRow
              title={I18n.t('ipc.brightness')}
              value={imageSettings.lum}
              onChange={() => {}}
              onSlidingComplete={value => handleRangeSetting('lum', value)}
            />
          </View>

          <View style={{height: 2}} />
          <View style={{backgroundColor: Tme('cardColor')}}>
            <SliderRow
              title={I18n.t('ipc.contrast')}
              value={imageSettings.contrast}
              onChange={() => {}}
              onSlidingComplete={value => handleRangeSetting('contrast', value)}
            />
          </View>

          <View style={{height: 2}} />
          <View style={{backgroundColor: Tme('cardColor')}}>
            <SliderRow
              title={I18n.t('ipc.saturation')}
              value={imageSettings.saturation}
              onChange={() => {}}
              onSlidingComplete={value =>
                handleRangeSetting('saturation', value)
              }
            />
          </View>

          <View style={{height: 2}} />
          <View style={{backgroundColor: Tme('cardColor')}}>
            <SliderRow
              title={I18n.t('ipc.sharpness')}
              value={imageSettings.sharpness}
              onChange={() => {}}
              onSlidingComplete={value =>
                handleRangeSetting('sharpness', value)
              }
            />
          </View>

          <View style={{height: 2}} />
          <View style={{backgroundColor: Tme('cardColor')}}>
            <SliderRow
              title={I18n.t('ipc.noise_3d')}
              value={imageSettings.ThreeDnr}
              onChange={() => {}}
              onSlidingComplete={value => handleRangeSetting('ThreeDnr', value)}
            />
          </View>

          <View style={{height: 2}} />
          {/* 恢复默认设置按钮 */}
          <TouchableOpacity
            style={[
              commonStyles.resetButton,
              {
                backgroundColor: Tme('cardColor'),
                borderBottomLeftRadius: mainRadius(),
                borderBottomRightRadius: mainRadius(),
              },
            ]}
            onPress={resetToDefault}>
            <Text style={commonStyles.resetButtonText}>
              {I18n.t('ipc.reset_defaults')}
            </Text>
          </TouchableOpacity>
          <View style={{height: 20}} />
          <View
            style={{
              backgroundColor: Tme('cardColor'),
              borderRadius: mainRadius(),
            }}>
            <View
              style={{
                paddingHorizontal: 20,
                paddingTop: 16,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('ipc.speed_up_connection')}
              </Text>
              <SwitchBtn
                cusStyle={{marginLeft: -3}}
                trackColor={{true: Colors.MainColor}}
                value={isLocal}
                change={() => {
                  showLoading();
                  Helper.httpPOST(
                    '/home/<USER>',
                    {
                      ignore_error: true,
                      success: () => {
                        hideLoading();
                        setIsLocal(!isLocal);
                        HelperMemo.ipc_is_local_play = !isLocal;
                        Toast.show(I18n.t('global.successful'));
                      },
                    },
                    {
                      sn: route.params.node.sn,
                      ipc_is_local_play: !isLocal,
                    },
                  );
                }}
              />
            </View>
            <Text
              style={{
                color: Tme('smallTextColor'),
                paddingHorizontal: 20,
                paddingBottom: 16,
                fontSize: 12,
              }}>
              {I18n.t('ipc.speed_up_connection_desp')}
            </Text>
          </View>
        </>
      )}
      <View style={{height: 80}} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 20,
  },
  resolutionButton: {
    padding: 10,
    borderRadius: mainRadius(),
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 6,
  },
  resolutionButtonActive: {
    backgroundColor: Colors.MainColor,
  },
  resolutionText: {
    // 移除 Tme 调用，将通过行内样式添加
  },
  resolutionTextActive: {
    color: '#FFFFFF',
  },
});
