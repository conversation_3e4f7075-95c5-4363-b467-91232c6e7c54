/* eslint-disable react/no-unstable-nested-components */
import React, {useEffect, useState, useCallback} from 'react';
import {View, TouchableOpacity, Text, Alert} from 'react-native';
import {mainRadius} from '../../Tools';
import {Colors, Tme} from '../../ThemeStyle';
import {useNavigation, useRoute} from '@react-navigation/native';
import I18n from '../../I18n';
import HeaderRightBtn from '../../share/HeaderRightBtn';
import {Toast} from '../../Toast';
import {Helper} from '../../Helper';
import { hideLoading, showLoading } from '../../../ILoading';

// 增强类型定义，使其更灵活
type DataItem = {
  key: number | string;
  label: string;
  [key: string]: any; // 允许其他属性
};

type RouteParams = {
  data: DataItem[];
  defaultSelected?: (number | string)[]; // 改为数组类型支持多选
  title?: string;
  sn: string;
};

const MultipleButton: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const {data, defaultSelected = [], sn} = route.params as RouteParams;

  const [selectedKeys, setSelectedKeys] =
    useState<(number | string)[]>(defaultSelected);

  const handleSave = useCallback(() => {
    if (selectedKeys.length === 0) {
      Alert.alert(I18n.t('home.warning_message'), I18n.t('ipc.select_one'), [
        {
          text: I18n.t('home.cancel'),
          onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
      ]);
      return;
    }
    showLoading();
    Helper.httpPOST(
      '/sns/ipc_setting',
      {
        ignore_error: true,
        success: () => {
          hideLoading();
          navigation.goBack();
          Toast.show(I18n.t('global.successful'));
        },
      },
      {
        sn: sn,
        ipc_alarm_types: selectedKeys,
      },
    );
  }, [selectedKeys, sn, navigation]);

  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn text={I18n.t('home.save')} rightClick={handleSave} />
      ),
    });
  }, [handleSave, navigation]);

  const handlePress = (item: DataItem) => {
    setSelectedKeys(prev => {
      const index = prev.indexOf(item.key);
      if (index > -1) {
        // 如果已选中，则移除
        return prev.filter(key => key !== item.key);
      } else {
        // 如果未选中，则添加
        return [...prev, item.key];
      }
    });
  };

  return (
    <View
      style={{
        backgroundColor: Tme('cardColor'),
        margin: 20,
        borderRadius: mainRadius(),
      }}>
      <View
        style={{
          flexDirection: 'row',
          padding: 20,
          flexWrap: 'wrap',
        }}>
        {data.map((item, index) => (
          <TouchableOpacity
            key={index}
            activeOpacity={0.8}
            onPress={() => handlePress(item)}
            style={{
              backgroundColor: selectedKeys.includes(item.key)
                ? Colors.MainColor
                : Tme('bgColor'),
              paddingHorizontal: 12,
              paddingVertical: 6,
              borderRadius: mainRadius(),
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 6,
              marginTop: 6,
            }}>
            <Text
              style={{
                color: selectedKeys.includes(item.key)
                  ? '#fff'
                  : Tme('cardTextColor'),
              }}>
              {I18n.t('ipc.' + item.label)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

export default MultipleButton;
