import React, {
  useState,
  useRef,
  useEffect,
  ForwardRefRenderFunction,
} from 'react';
import {Alert, View, Platform} from 'react-native';
import {
  RTCPeerConnection,
  RTCSessionDescription,
  mediaDevices,
  RTCIceCandidate,
  RTCView,
  MediaStream,
} from 'react-native-webrtc';
import {useMount, useUnmount} from 'ahooks';
import InCallManager from 'react-native-incall-manager';
import {hideLoading} from '../../../ILoading';
import AlertModal from '../../share/AlertModal';
import I18n from '../../I18n';
import { uuid } from './IpcHelper';

// 定义类型
export interface WebRtcLocalPlayBackProps {
  from: string;
  clientId: string;
  host: string;
  openDataChannel: (data?: any) => void;
  onMessage?: (data: any) => void;
  getFileList?: (data: any) => void;
}

export interface WebRtcLocalPlayBackRef {
  setlocalaudio: () => void;
  getfilelist: (dates: string[]) => void;
  play: () => void;
  pause: () => void;
  setSeek: (value: string) => void;
  playremotefile: (file: string) => void;
}

// WebSocket 消息类型
interface WebSocketMessage {
  eventName: string;
  data: any;
}

// ICE 候选者类型
interface IceServerConfig {
  urls: string;
  username?: string;
  credential?: string;
}

interface IceConfiguration {
  iceServers: IceServerConfig[];
}

interface IceCandidateData {
  sdpMLineIndex: number;
  sdpMid: string;
  candidate: string;
}

let messagecallback: ((message: any) => void) | null = null;
let peerid = '';
let meid = uuid();
let sessionId = uuid();
const connectmode = 'play';
const connectsource = '';
let remoteMediaStream: MediaStream | null = null;
let webS: WebSocket | null = null;

const IceCandidate: IceCandidateData[] = [];

let IsReconnect = false;

let peerConstraints: IceConfiguration | null = null;

const WebRtcLocalPlayBack: ForwardRefRenderFunction<
  WebRtcLocalPlayBackRef,
  WebRtcLocalPlayBackProps
> = ({from, host, clientId, openDataChannel, getFileList}, ref) => {
  // 使用父组件传入的 host 和 clientId
  const toclientid = clientId;
  const serverHost = host;
  const url =
    from === 'online'
      ? 'wss://' + serverHost + '/wswebclient/' + meid
      : 'ws://' + serverHost + '/wswebclient/' + meid;

  // 添加 flag 来追踪网络错误提示状态
  const [hasShownNetworkError, setHasShownNetworkError] =
    useState<boolean>(false);

  // 添加 rtcPeerConnectionCreated ref
  const rtcPeerConnectionCreatedRef = useRef<boolean>(false);

  const webSocketRef = useRef<any>(null);
  const peerConnectionRef = useRef<any>(null);
  const dataChannelRef = useRef<any>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // 添加基础配置
  const [configuration, setConfiguration] = useState<IceConfiguration>({
    iceServers: [
      {
        urls: 'stun:webrtc.qq-kan.com:3478',
      },
    ],
  });

  useMount(() => {
    RHRTCStart();
  });

  const RHRTCStart = (): void => {
    peerid = toclientid;
    websocketConnect(url);
    const heartCheck: any = {
      timeout: 1000,
      timedelay: 0,
      serverTimeoutObj: null as NodeJS.Timeout | null,
      connectionAttempts: 0,
      maxAttempts: 3,
      reset: function (): typeof heartCheck {
        if (this.serverTimeoutObj) {
          clearInterval(this.serverTimeoutObj);
        }
        this.connectionAttempts = 0;
        hideLoading();
        return this;
      },
      start: function (): void {
        const that = this;
        this.serverTimeoutObj = setInterval(() => {
          that.timedelay++;
          if (!webS) {
            that.connectionAttempts++;
            if (that.connectionAttempts >= that.maxAttempts) {
              hideLoading();
              if (!hasShownNetworkError) {
                AlertModal.alert(
                  I18n.t('home.warning_message'),
                  I18n.t('ipc.local_network_error'),
                );
                setHasShownNetworkError(true);
              }
              that.reset();
              return;
            }
            that.reset();
            return;
          }
          if (webS.readyState === 1) {
            // 当WebSocket连接成功时，重置错误状态
            if (hasShownNetworkError) {
              setHasShownNetworkError(false);
            }

            if (IsReconnect) {
              handleLeave();
              sessionId = uuid();
              Connect();
            } else {
              if (that.timedelay > 30) {
                that.timedelay = 0;
                sendToServer({
                  eventName: '__ping',
                  data: {
                    sessionId: sessionId,
                    sessionType: 'app',
                    messageId: uuid(),
                    from: meid,
                    to: peerid,
                  },
                });
              }
            }
            heartCheck.reset().start();
          } else {
            meid = uuid();
            sessionId = uuid();
            websocketConnect(url);
          }
        }, this.timeout);
      },
    };
    heartCheck.reset().start();
  };

  useUnmount(() => {
    // 先处理所有的媒体流和数据连接
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => {
        track.stop();
      });
      streamRef.current = null;
    }

    if (remoteMediaStream) {
      remoteMediaStream.getTracks().forEach(track => {
        track.stop();
      });
      remoteMediaStream = null;
    }

    // 关闭数据通道
    if (dataChannelRef.current) {
      dataChannelRef.current.close();
      dataChannelRef.current = null;
    }

    // 关闭 RTCPeerConnection
    if (peerConnectionRef.current) {
      peerConnectionRef.current.getSenders().forEach((sender: any) => {
        if (sender.track) {
          sender.track.stop();
        }
      });
      peerConnectionRef.current.close();
      peerConnectionRef.current = null;
    }

    // 先发送断开连接消息
    if (webS && webS.readyState === WebSocket.OPEN) {
      try {
        sendDisconnect();
      } catch (error) {
        console.log('Error sending disconnect:', error);
      }
    }

    // 最后关闭 WebSocket
    if (webSocketRef.current) {
      try {
        webSocketRef.current.onclose = null; // 移除关闭事件处理器
        webSocketRef.current.onerror = null; // 移除错误事件处理器
        webSocketRef.current.close();
      } catch (error) {
        console.log('Error closing websocket:', error);
      }
      webSocketRef.current = null;
      webS = null;
    }

    // 重置所有状态
    rtcPeerConnectionCreatedRef.current = false;
    IsReconnect = false;
    setHasShownNetworkError(false);
  });

  function sendToServer(message: WebSocketMessage): void {
    if (webS != null) {
      webS.send(JSON.stringify(message));
    }
  }

  // 添加远程流状态管理
  const [remoteStream, setRemoteStream] = useState<MediaStream | null>(null);

  // 修改远程流处理函数
  async function handleRemoteTrackAdded(e: any): Promise<void> {
    if (!remoteMediaStream) {
      remoteMediaStream = new MediaStream();
    }
    remoteMediaStream.addTrack(e.track);

    // 更新状态以触发重新渲染
    setRemoteStream(remoteMediaStream);

    // 确保音频初始状态
    if (e.track.kind === 'audio') {
      e.track.enabled = !mute;
    }
  }

  function handleDataChannelOnOpen(): void {
    if (openDataChannel) {
      openDataChannel('ok');
    }
  }

  function handleDataChannelOnClose(_event: Event): void {
    // setShowView(false);
  }

  function handleDataChannelOnMessage(message: MessageEvent): void {
    const data = JSON.parse(message.data);
    const jsonObj = data.data.message.response;

    if (data.eventName === '_play' && jsonObj.length > 0) {
      //判断 jsonObj[0]里面有没有getfilelist
      if (
        jsonObj[0].getfilelist &&
        jsonObj[0].getfilelist.filelists.length > 0
      ) {
        if (getFileList) {
          getFileList(jsonObj[0].getfilelist.filelists[0]);
        }
      }
    }
  }

  function handleDataChannelOnError(err: Event): void {
    console.log('handleDataChannelOnError ', err);
  }

  async function handleSignalingStateChangeEvent(_event: Event): Promise<void> {
    if (peerConnectionRef.current == null) {
      return;
    }

    switch (peerConnectionRef.current.signalingState) {
      case 'closed':
        handleLeave();
        break;
    }
  }

  async function handleIceGatheringStateChangeEvent(
    _event: Event,
  ): Promise<void> {
    if (peerConnectionRef.current == null) {
      return;
    }
  }

  async function handleIceConnectionStateChangeEvent(
    _event: Event,
  ): Promise<void> {
    if (peerConnectionRef.current == null) {
      return;
    }

    switch (peerConnectionRef.current.iceConnectionState) {
      case 'closed':
      case 'failed':
      case 'disconnected':
        sendDisconnect();
        IsReconnect = true;
        break;
      case 'connected':
        break;
    }
  }

  async function handleIceCandidate(event: any): Promise<void> {
    if (event.candidate) {
      sendToServer({
        eventName: '__ice_candidate',
        data: {
          sessionId: sessionId,
          sessionType: 'app',
          messageId: uuid(),
          to: peerid,
          from: meid,
          label: event.candidate.sdpMLineIndex,
          id: event.candidate.sdpMid,
          candidate: event.candidate.candidate,
        },
      });
    }
  }

  const initPeerConnection = (): boolean => {
    try {
      const pc: any = new RTCPeerConnection({
        ...configuration,
      });

      if (!pc) {
        throw new Error('Failed to create PeerConnection');
      }

      // 确保所有事件监听器在连接创建后立即添加
      pc.addEventListener('track', handleRemoteTrackAdded);
      pc.addEventListener('icecandidate', handleIceCandidate);
      pc.addEventListener(
        'iceconnectionstatechange',
        handleIceConnectionStateChangeEvent,
      );
      pc.addEventListener(
        'icegatheringstatechange',
        handleIceGatheringStateChangeEvent,
      );
      pc.addEventListener(
        'signalingstatechange',
        handleSignalingStateChangeEvent,
      );
      pc.addEventListener('connectionstatechange', () => {
        console.log('Connection state:', pc.connectionState);
      });

      // 添加本地流
      if (streamRef.current) {
        const tracks = streamRef.current.getTracks();
        tracks.forEach(track => {
          pc.addTrack(track, streamRef.current!);
        });
      }

      // 创建数据通道
      try {
        const dataChannel: any = pc.createDataChannel('data', {
          ordered: true,
          maxPacketLifeTime: -1,
          maxRetransmits: -1,
          id: 0,
          negotiated: false,
          protocol: 'Subprotocol',
        });

        dataChannelRef.current = dataChannel;

        if (dataChannel) {
          dataChannel.onopen = handleDataChannelOnOpen;
          dataChannel.onclose = handleDataChannelOnClose;
          dataChannel.onmessage = handleDataChannelOnMessage;
          dataChannel.onerror = handleDataChannelOnError;
        }
      } catch (error) {
        console.error('Failed to create data channel:', error);
      }

      // 保存连接引用
      peerConnectionRef.current = pc;
      // 使用 ref 而不是 state
      rtcPeerConnectionCreatedRef.current = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize peer connection:', error);
      rtcPeerConnectionCreatedRef.current = false;
      return false;
    }
  };

  const [callSuccess, setCallSuccess] = useState<boolean>(true);
  const [mute, setMute] = useState<boolean>(true);
  // 添加对讲状态管理

  // 控制接收远程音频（是否能听到摄像头声音）
  const setlocalaudio = (): void => {
    if (peerConnectionRef.current) {
      const newMuteState = !mute;
      setMute(newMuteState);

      // 设置音频输出为扬声器模式
      if (remoteMediaStream) {
        const audioTracks = remoteMediaStream.getAudioTracks();
        audioTracks.forEach(track => {
          track.enabled = !newMuteState;
          // 设置音频输出为扬声器模式
          InCallManager.setSpeakerphoneOn(!newMuteState);
        });
      }

      // 控制远程音频轨道
      peerConnectionRef.current.getReceivers().forEach((receiver: any) => {
        if (receiver.track?.kind === 'audio') {
          receiver.track.enabled = !newMuteState;
        }
      });
    }
  };

  // 通过 useImperativeHandle 暴露方法给父组件
  React.useImperativeHandle(ref, () => ({
    setlocalaudio,
    getfilelist,
    play,
    pause,
    setSeek,
    playremotefile,
  }));

  function Call(): void {
    // 修改：即使初始状态没有开启对讲，也设置为 sendrecv，提前协商音频能力
    const audioenable = 'recvonly'; // 无论对讲状态，都表明我们支持双向音频
    const videoenable = 'recvonly'; // 只接收视频，不发送
    const datachannelenable = 'true';
    sendToServer({
      eventName: '__call',
      data: {
        sessionId: sessionId,
        sessionType: 'app',
        messageId: uuid(),
        from: meid,
        to: peerid,
        mode: connectmode,
        source: connectsource,
        datachannel: datachannelenable,
        audio: audioenable,
        video: videoenable,
        user: 'admin',
        pwd: '123456',
        iceservers: JSON.stringify(peerConstraints),
      },
    });
    setTimeout(() => {
      if (callSuccess === true) {
        setCallSuccess(false);
      }
    }, 5000);
  }

  function handleCreate(data: any): void {
    if (data.state === 'online' || data.state === 'sleep') {
      if (data.iceServers) {
        try {
          const iceConfig =
            typeof data.iceServers === 'string'
              ? JSON.parse(data.iceServers)
              : data.iceServers;
          setConfiguration(iceConfig);
          peerConstraints = iceConfig;
        } catch (error) {
          console.error('Failed to parse ICE config:', error);
        }
      }
      Call();
    } else {
      Alert.alert('WebSocket _create offline');
    }
  }

  function handleDisconnect(data: any): void {
    if (data.sessionId === sessionId) {
      handleLeave();
      IsReconnect = true;
    }
  }

  function handleConnectInfo(_data: any): void {}

  function handleSessionFailed(_data: any): void {
    sendDisconnect();
    IsReconnect = true;
  }

  function handlePostMessage(data: any): void {
    if (messagecallback === undefined || messagecallback === null) {
    } else {
      typeof messagecallback === 'function' && messagecallback(data.message);
      messagecallback = null;
    }
  }

  function handleCandidate(data: any): void {
    const obj = JSON.parse(data.candidate);

    if (
      peerConnectionRef.current != null &&
      rtcPeerConnectionCreatedRef.current === true
    ) {
      const candidate = new RTCIceCandidate({
        sdpMLineIndex: obj.sdpMLineIndex,
        sdpMid: obj.sdpMid,
        candidate: obj.candidate,
      });
      peerConnectionRef.current.addIceCandidate(candidate);
    } else {
      IceCandidate.push(obj);
    }
  }

  async function handleAnswer(answer: any): Promise<void> {
    try {
      if (peerConnectionRef.current) {
        await peerConnectionRef.current.setRemoteDescription(
          new RTCSessionDescription({type: answer.type, sdp: answer.sdp}),
        );
      }
    } catch (err: any) {
      console.log('handleAnswer', err.message);
    }
  }

  async function handleCall(data: any): Promise<void> {
    if (
      data.iceServers !== null ||
      data.iceServers !== undefined ||
      data.iceServers !== ''
    ) {
      if (data.iceServers.constructor === Object) {
        setConfiguration(JSON.parse(JSON.stringify(data.iceServers)));
      } else {
        setConfiguration(JSON.parse(data.iceServers));
      }
    }

    IsReconnect = false;
    if (!rtcPeerConnectionCreatedRef.current) {
      // 使用新的配置初始化连接
      initPeerConnection();
    }

    if (!rtcPeerConnectionCreatedRef.current || !peerConnectionRef.current) {
      sendDisconnect();
      return;
    }

    try {
      const offer = await peerConnectionRef.current.createOffer();
      await peerConnectionRef.current.setLocalDescription(offer);

      sendToServer({
        eventName: '__offer',
        data: {
          sessionId: sessionId,
          sessionType: 'app',
          messageId: uuid(),
          from: meid,
          to: peerid,
          type: offer.type,
          sdp: offer.sdp,
          iceservers: JSON.stringify(configuration),
        },
      });
    } catch (error) {
      sendDisconnect();
      IsReconnect = true;
    }
  }

  async function handleOffer(data: any): Promise<void> {
    setCallSuccess(true);
    IsReconnect = false;
    handleLeave();
    if (!rtcPeerConnectionCreatedRef.current) {
      initPeerConnection();
    }
    if (!rtcPeerConnectionCreatedRef.current || !peerConnectionRef.current) {
      sendDisconnect();
      IsReconnect = true;
      return;
    }

    try {
      await peerConnectionRef.current.setRemoteDescription(
        new RTCSessionDescription({type: 'offer', sdp: data.sdp}),
      );
    } catch (err) {
      IsReconnect = true;
      return;
    }

    try {
      const answer = await peerConnectionRef.current.createAnswer();
      await peerConnectionRef.current.setLocalDescription(answer);

      sendToServer({
        eventName: '__answer',
        data: {
          sessionId: sessionId,
          sessionType: 'app',
          messageId: uuid(),
          from: meid,
          to: peerid,
          type: 'answer',
          sdp: answer.sdp,
        },
      });
    } catch (error) {
      sendDisconnect();
      IsReconnect = true;
    }
  }

  function sendDisconnect(): void {
    sendToServer({
      eventName: '__disconnected',
      data: {
        sessionId: sessionId,
        sessionType: 'app',
        messageId: uuid(),
        from: meid,
        to: peerid,
      },
    });
  }

  function handleLeave(): void {
    if (dataChannelRef.current != null) {
      dataChannelRef.current.close();
      dataChannelRef.current = null;
    }
    if (peerConnectionRef.current != null) {
      peerConnectionRef.current.getSenders().forEach((sender: any) => {
        peerConnectionRef.current?.removeTrack(sender);
      });
      peerConnectionRef.current.close();
      peerConnectionRef.current = null;
    }
    // 更新 ref 而不是 state
    rtcPeerConnectionCreatedRef.current = false;

    IceCandidate.splice(0, IceCandidate.length);
  }

  function handleWebSocketMessage(event: MessageEvent): void {
    try {
      const json = JSON.parse(event.data);

      switch (json.eventName) {
        case '_create':
          handleCreate(json.data);
          break;
        case '_call':
          handleCall(json.data);
          break;
        case '_offer':
          handleOffer(json.data);
          break;
        case '_answer':
          handleAnswer(json.data);
          break;
        case '_ice_candidate':
          handleCandidate(json.data);
          break;
        case '_session_disconnected':
          handleDisconnect(json.data);
          break;
        case '_post_message':
          handlePostMessage(json.data);
          break;
        case '_connectinfo':
          handleConnectInfo(json.data);
          break;
        case '_session_failed':
          handleSessionFailed(json.data);
          break;
        case '_ping':
          break;
        default:
          break;
      }
    } catch (error) {
      console.error('WebSocket message handling error:', error);
    }
  }

  function handleWebSocketClose(_event: any): void {
    webS = null;
  }

  function handleWebSocketOpen(): void {
    webS = webSocketRef.current;
    Connect();
  }

  const websocketConnect = (serverurl: string): void => {
    try {
      webSocketRef.current = new WebSocket(serverurl);

      webSocketRef.current.onmessage = handleWebSocketMessage;
      webSocketRef.current.onerror = () => {
        hideLoading();
        if (!hasShownNetworkError) {
          AlertModal.alert(
            I18n.t('home.warning_message'),
            I18n.t('ipc.local_network_error'),
          );
          setHasShownNetworkError(true);
        }
      };
      webSocketRef.current.onclose = (event: any) => {
        handleWebSocketClose(event);
      };
      webSocketRef.current.onopen = handleWebSocketOpen;
    } catch (error) {
      if (!hasShownNetworkError) {
        AlertModal.alert(
          I18n.t('home.warning_message'),
          I18n.t('ipc.local_network_error'),
        );
        setHasShownNetworkError(true);
      }
    }
  };

  function Connect(): void {
    sendToServer({
      eventName: '__connectto',
      data: {
        sessionId: sessionId,
        sessionType: 'app',
        messageId: uuid(),
        from: meid,
        to: peerid,
      },
    });
  }

  // 添加效果以在组件挂载时设置音频输出到扬声器
  useEffect(() => {
    // 将音频输出路由到扬声器而非听筒
    if (Platform.OS === 'ios') {
      // iOS 需要特别处理
      // mediaDevices.setCategory('PlayAndRecord', 'DefaultToSpeaker');
    } else {
      // Android 可以通过 AudioManager 设置，但需要原生模块支持
      // 这里可以借助媒体流设置
      if (remoteStream) {
        remoteStream.getAudioTracks().forEach(track => {
          if (track.enabled) {
            // 尝试设置扬声器模式
            const constraints = {
              audio: {
                // 强制使用扬声器
                deviceId: 'speaker',
                // 使用扬声器避免听筒模式
                echoCancellation: false,
              },
            };
            mediaDevices
              .getUserMedia(constraints)
              .catch(e => console.log('Error setting speaker mode:', e));
          }
        });
      }
    }
  }, [remoteStream]);

  function getfilelist(time: string[]): void {
    if (dataChannelRef.current) {
      const jsonObj = {
        eventName: '__play',
        data: {
          sessionId: sessionId,
          sessionType: 'IE',
          messageId: uuid(),
          from: meid,
          to: peerid,
          message: {
            request: [
              {
                getfilelist: {
                  starttime: time[0],
                  endtime: time[1],
                },
              },
            ],
          },
        },
      };

      dataChannelRef.current.send(JSON.stringify(jsonObj));
    }
  }

  function playremotefile(file: string): void {
    if (dataChannelRef.current) {
      dataChannelRef.current.send(
        JSON.stringify({
          eventName: '__play',
          data: {
            sessionId: sessionId,
            sessionType: 'IE',
            messageId: uuid(),
            from: meid,
            to: peerid,
            message: {
              request: [
                {
                  open: file,
                },
                {
                  start: 0,
                },
              ],
            },
          },
        }),
      );
    }
  }

  function setSeek(seek: string): void {
    if (dataChannelRef.current) {
      dataChannelRef.current.send(
        JSON.stringify({
          eventName: '__play',
          data: {
            sessionId: sessionId,
            sessionType: 'IE',
            messageId: uuid(),
            from: meid,
            to: peerid,
            message: {
              request: [
                {
                  seek: Number(seek),
                },
              ],
            },
          },
        }),
      );
    }
  }

  function play(): void {
    if (dataChannelRef.current) {
      dataChannelRef.current.send(
        JSON.stringify({
          eventName: '__play',
          data: {
            sessionId: sessionId,
            sessionType: 'IE',
            messageId: uuid(),
            from: meid,
            to: peerid,
            message: {
              request: [
                {
                  start: 0,
                },
              ],
            },
          },
        }),
      );
    }
  }

  function pause(): void {
    if (dataChannelRef.current) {
      dataChannelRef.current.send(
        JSON.stringify({
          eventName: '__play',
          data: {
            sessionId: sessionId,
            sessionType: 'IE',
            messageId: uuid(),
            from: meid,
            to: peerid,
            message: {
              request: [
                {
                  pause: 1,
                },
              ],
            },
          },
        }),
      );
    }
  }

  return (
    <View style={{flex: 1}}>
      <RTCView
        style={{
          flex: 1,
          width: '100%',
          height: '100%',
        }}
        mirror={false} // 通常远程视频不需要镜像
        objectFit={'contain'} // 保持视频比例
        streamURL={remoteStream?.toURL() || ''} // 使用状态管理的流
        zOrder={0}
      />
    </View>
  );
};

export default React.forwardRef(WebRtcLocalPlayBack);
