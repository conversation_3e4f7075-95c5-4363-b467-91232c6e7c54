import React, { useState, useEffect, useCallback } from 'react';
import {
  Text,
  TouchableOpacity,
  SafeAreaView,
  FlatList,
  View,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import I18n from '../../I18n';
import { Tme, Colors } from '../../ThemeStyle';
import { Helper } from '../../Helper';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { decode } from 'base-64';
import _ from 'lodash';
import dgram from 'react-native-udp';
import CardView from '../../share/CardView';
import { hideLoading, showLoading } from '../../../ILoading';
import { IpcNavigationProp } from './types/navigation';

// 设备列表项的接口定义
interface DeviceItem {
  ip: string;
  sn: string;
}

// 组件属性类型
interface AddWlanDeviceProps {
  navigation: IpcNavigationProp;
}

// 分隔线组件
const ItemSeparator = () => (
  <View
    style={{
      backgroundColor: Tme('bgColor'),
      height: 2,
    }}
  />
);

const AddWlanDevice: React.FC<AddWlanDeviceProps> = ({ navigation }) => {
  const [list, setList] = useState<DeviceItem[]>([]);

  useEffect(() => {
    // 创建UDP套接字
    const newSocket = dgram.createSocket({
      type: 'udp4',
      debug: true,
    });

    newSocket.on('close', () => {
      console.log('client closed');
    });

    newSocket.on('error', (err: Error) => {
      console.log('client error' + err);
    });

    newSocket.on('listening', () => {
      console.log('client listening...');
      newSocket.setBroadcast(true);
      newSocket.addMembership('***********');
    });

    newSocket.on('message', (msg: Buffer, rinfo: { address: string }) => {
      const ms = msg.toString();
      if (ms.indexOf('RSKJ') > -1) {
        const data = ms.substring(8);
        const dData = decode(data);
        if (dData) {
          const td = JSON.parse(dData);
          const temp: DeviceItem = {
            ip: rinfo.address,
            sn: td.sn,
          };
          console.log('temp', dData);
          setList(prevList => _.uniqBy([...prevList, temp], 'sn'));
        }
      }
    });

    newSocket.bind(3703);
    // 清理函数
    return () => {
      if (newSocket) {
        newSocket.close();
      }
    };
  }, []);

  const checkAdd = useCallback((key: string) => {
    showLoading();
    Helper.httpPOST(
      '/ipcs/device_added',
      {
        success: (data: { success: boolean; index: string }) => {
          if (data.success) {
            navigation.replace('AddSuccess', {
              uuid: data.index,
              sn_id: '',
              type: 'new',
              from: 'ipc',
            });
          }
        },
        ensure: () => {
          hideLoading();
        },
      },
      {
        sn: key,
      },
    );
  }, [navigation]);

  const renderItem = useCallback((item: DeviceItem) => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => checkAdd(item.sn)}
        style={{
          backgroundColor: Tme('cardColor'),
          padding: 20,
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 2,
        }}>
        <Text style={{ color: Tme('cardTextColor') }}>{item.sn}</Text>
        <MaterialIcons
          name="keyboard-arrow-right"
          size={20}
          color={Tme('textColor')}
        />
      </TouchableOpacity>
    );
  }, [checkAdd]);

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: Tme('bgColor') }}>
      <View
        style={{
          flex: 1,
          alignItems: 'center',
          marginTop: 20,
          padding: 20,
        }}>
        <CardView
          styles={{
            width: Dimensions.get('screen').width - 40,
            height: 580,
            padding: 20,
            alignItems: 'center',
            borderRadius: 8,
          }}>
          <View
            style={{
              marginTop: 79,
              marginBottom: 20,
              width: '100%',
            }}>
            <Text
              style={{
                fontSize: 22,
                fontWeight: '600',
                textAlign: 'center',
                color: Tme('cardTextColor'),
              }}>
              {I18n.t('home.add_device')}
            </Text>
            <ActivityIndicator
              size="small"
              color={Colors.MainColor}
              style={{ marginTop: 8 }}
            />
            <View style={{ height: 8 }} />
            <FlatList
              style={{
                width: '100%',
                height: 380,
              }}
              showsVerticalScrollIndicator={false}
              ItemSeparatorComponent={ItemSeparator}
              data={list}
              keyExtractor={(item: DeviceItem) => item.sn}
              renderItem={({ item }: { item: DeviceItem }) => renderItem(item)}
            />
          </View>
        </CardView>
      </View>
    </SafeAreaView>
  );
};

export default AddWlanDevice;
