import React, {useEffect, useState, useRef} from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {hideLoading, showLoading} from '../../../ILoading';
import AlertModal from '../../share/AlertModal';
import _ from 'lodash';
import {Tme} from '../../ThemeStyle';
import I18n from '../../I18n';
import {dcodeIpcData, DecodedData, getDpsCmd} from './ipcUtils';
import {Helper} from '../../Helper';
import {Toast} from '../../Toast';

// 导入拆分后的组件
import NetworkInfoCard from './components/NetworkInfoCard';
import FunctionalMenuItems from './components/FunctionalMenuItems';
import SDCardManagement from './components/SDCardManagement';
import DeviceManagement from './components/DeviceManagement';
import DeviceInfoCard from './components/DeviceInfoCard';
import {Device} from '../../types/home';

// 使用已有的类型定义，为了兼容现有代码，创建类型别名
type Node = Device;

interface IpcCameraSettingProps {
  navigation: any;
  route: {
    params: {
      node: Node;
      // need_update_firmware?: boolean;
      // host?: string;
      // clientid?: string;
    };
  };
}

const IpcCameraSetting: React.FC<IpcCameraSettingProps> = ({
  navigation,
  route,
}) => {
  const [isDataLoaded, setIsDataLoaded] = useState<boolean>(false);
  const [dcData, setDcData] = useState<DecodedData | null>(null);
  const [sdState, setSdState] = useState<string>(I18n.t('tuya.sd_no'));
  const [storage, setStorage] = useState<string[]>(['0.00', '0.00']);
  const [signal, setSignal] = useState<number>(0);
  const [node] = useState<Node>(route.params.node);
  const [networkType, setNetworkType] = useState<string>('');

  const [needUpdate, setNeedUpdate] = useState<boolean>(false);
  const [host, setHost] = useState<string>('');
  const [clientid, setClientid] = useState<string>('');

  // 版本点击相关状态
  const versionClickCount = useRef<number>(0);
  const lastClickTime = useRef<number>(0);
  const [loadDataDone, setLoadDataDone] = useState<boolean>(false);

  // ComponentDidMount 效果
  useEffect(() => {
    doFetchData('');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 刷新数据
  const refresh = (): void => {
    doFetchData('refresh');
  };

  // 获取设备数据
  const doFetchData = (type: string): void => {
    if (type === 'refresh') {
      setIsDataLoaded(true);
    } else {
      showLoading();
    }

    Helper.httpGET(
      Helper.urlWithQuery('/ipcs/info', {
        node_id: route.params.node.id,
      }),
      {
        success: (data: any) => {
          setNeedUpdate(data.need_update_firmware);
          setHost(data.webrtc_host);
          setClientid(data.webrtc_uuid);
        },
      },
    );

    getDpsCmd(route.params.node.sn, [
      {ipc_cmd_key: 'get_device_info', value: 'get'},
      {ipc_cmd_key: 'get_dn_mode_info', value: 'get'},
      {ipc_cmd_key: 'get_img_info', value: 'get'},
      {ipc_cmd_key: 'get_alarm_info', value: 'get'},
      {ipc_cmd_key: 'get_motion_info', value: 'get'},
    ])
      .then(() => {
        Helper.httpGET(
          Helper.urlWithQuery('/sns/ipc_dp_values', {
            sn: node.sn,
          }),
          {
            success: (data: any) => {
              const decodedData = dcodeIpcData(data);
              setDcData(decodedData);
              // 如果有设备信息，更新信号和网络类型
              if (decodedData.devInfo) {
                if (decodedData.devInfo.signal) {
                  setSignal(decodedData.devInfo.signal);
                }

                const hasWifi = !!(
                  decodedData.devInfo.ssid &&
                  decodedData.devInfo.ssid.length > 0
                );
                setNetworkType(hasWifi ? 'Wi-Fi' : I18n.t('ipc.wired'));
              }

              // 延时处理SD卡信息，确保状态已更新
              if (decodedData.sdInfo) {
                setTimeout(() => {
                  handleSdCardInfo(decodedData);
                }, 100);
              }

              setIsDataLoaded(false);
              setLoadDataDone(true);
              if (type === 'refresh') {
                setIsDataLoaded(false);
              } else {
                hideLoading();
              }
            },
          },
        );
      })
      .catch((error: unknown) => {
        if (type === 'refresh') {
          setIsDataLoaded(false);
        } else {
          hideLoading();
        }
        console.error('获取设备数据失败:', error);
        AlertModal.alert(
          '',
          _.uniq([error instanceof Error ? error.message : String(error)]).join(
            '\n',
          ),
          [
            {
              text: '取消',
              onPress: () => navigation.goBack(),
              style: 'cancel',
            },
          ],
        );
      });
  };

  // 处理SD卡信息
  const handleSdCardInfo = (data: DecodedData): void => {
    if (!data || !data.sdInfo) {
      return;
    }

    // 设置SD卡状态
    if (data.sdInfo.sdStatus === 0) {
      setSdState(I18n.t('tuya.sd_no'));
    } else if (data.sdInfo.sdStatus === 1) {
      setSdState(I18n.t('tuya.ok_s'));
    } else if (data.sdInfo.sdStatus === 2) {
      setSdState(I18n.t('tuya.sd_error'));
    }

    // 只有SD卡状态正常时才计算存储信息
    if (data.sdInfo.sdStatus === 1) {
      // 确保存储值有效
      const sdTotal = data.sdInfo.sdTotal || 0;
      const sdUsed = data.sdInfo.sdUsed || 0;

      // kb -> GB 转换
      const total = sdTotal / 1024 / 1024;
      const used = sdUsed / 1024 / 1024;

      // 确保格式化为两位小数的字符串
      const totalFormatted = total.toFixed(2);
      const usedFormatted = used.toFixed(2);

      setStorage([totalFormatted, usedFormatted]);
    } else {
      setStorage(['0.00', '0.00']);
    }
  };

  // 处理版本号连续点击
  const handleVersionClick = (): void => {
    const now = new Date().getTime();

    // 如果距离上次点击超过3秒，重置计数器
    if (now - lastClickTime.current > 3000) {
      versionClickCount.current = 0;
    }

    lastClickTime.current = now;
    versionClickCount.current += 1;

    // 检查是否点击了10次
    if (versionClickCount.current === 10) {
      activateSecretFunction();
      versionClickCount.current = 0; // 重置计数器
    }
  };

  // 激活秘密功能
  const activateSecretFunction = (): void => {
    AlertModal.alert('开发者模式', '您已进入开发者模式', [
      {
        text: '确定',
        onPress: () => {
          Toast.show('开发者模式已激活');
        },
      },
    ]);
  };

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      style={{flex: 1,backgroundColor: Tme('bgColor')}}
      refreshControl={
        <RefreshControl refreshing={isDataLoaded} onRefresh={refresh} />
      }>
      {loadDataDone && dcData && (
        <>
          <DeviceInfoCard node={node} dcData={dcData} />

          <NetworkInfoCard
            dcData={dcData}
            networkType={networkType}
            signal={signal}
          />

          {/* 功能菜单项 */}
          <FunctionalMenuItems
            navigation={navigation}
            node={node}
            dcData={dcData}
            host={host}
            clientid={clientid}
          />

          {/* SD卡管理 */}
          <SDCardManagement
            dcData={dcData}
            sdState={sdState}
            storage={storage}
            navigation={navigation}
            nodeSn={route.params.node.sn}
            refresh={() => {
              doFetchData('');
            }}
            setDcData={setDcData}
          />

          {/* 设备管理功能 */}
          <DeviceManagement
            navigation={navigation}
            nodeSn={node.sn}
            node={node}
            needUpdate={needUpdate}
          />
        </>
      )}

      {/* 版本号 */}
      <TouchableOpacity
        activeOpacity={1}
        onPress={handleVersionClick}
        style={{
          alignItems: 'center',
          justifyContent: 'center',
          marginTop: 10,
        }}>
        <Text
          style={{
            fontSize: 12,
            color: Tme('smallTextColor'),
          }}>
          {dcData?.devInfo?.version}
        </Text>
      </TouchableOpacity>
      <View style={{height: 40}} />
    </ScrollView>
  );
};

export default IpcCameraSetting;
