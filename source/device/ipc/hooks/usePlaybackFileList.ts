import { useState, useRef, useCallback, useEffect } from 'react';
import moment from 'moment';
import { showLoading, hideLoading } from '../../../../ILoading';
import _ from 'lodash';

/**
 * 回放文件数据接口
 */
export interface PlaybackFileData {
  /** 文件名，格式为"startTime-endTime" */
  filename: string;
  /** 开始时间戳 */
  startTime: number;
  /** 结束时间戳 */
  endTime: number;
}

/**
 * 回放文件列表Hook返回类型
 */
export interface PlaybackFileListResult {
  /** 文件列表 */
  fileList: PlaybackFileData[];
  /** 当前选择的日期 */
  currentDate: Date;
  /** 更新文件列表 */
  updateFileList: (data: {filename?: string} | 'error') => void;
  /** 改变日期并获取文件列表 */
  changeDate: (date: Date) => void;
  /** 清空文件列表 */
  clearFileList: () => void;
}

/**
 * 回放文件列表Hook参数
 */
interface UsePlaybackFileListOptions {
  /** WebView引用 */
  webViewRef: React.RefObject<any>;
  /** 处理错误的回调 */
  onError?: (error: string) => void;
}

/**
 * 回放文件列表Hook
 * @param options 配置选项
 * @returns 回放文件列表接口
 */
export const usePlaybackFileList = ({
  webViewRef,
  onError,
}: UsePlaybackFileListOptions): PlaybackFileListResult => {
  // 状态管理
  const [fileList, setFileList] = useState<PlaybackFileData[]>([]);
  const [currentDate, setCurrentDate] = useState<Date>(moment().toDate());

  // 文件数据缓冲区
  const fileBuffer = useRef<PlaybackFileData[]>([]);

  // 节流处理函数，用于批量更新文件列表数据
  const processBufferedData = useRef(
    _.throttle(() => {
      if (fileBuffer.current.length === 0) {
        return;
      }

      setFileList(prevList => {
        // 合并现有列表和缓冲区数据
        const allItems = [...prevList, ...fileBuffer.current];

        // 对数据进行排序和去重处理
        const uniqueItems = _.uniqBy(allItems, 'filename');
        const sortedItems = _.sortBy(uniqueItems, 'startTime');

        // 清空缓冲区
        fileBuffer.current = [];

        return sortedItems;
      });

      hideLoading();
    }, 300)
  ).current;

  /**
   * 更新文件列表数据
   */
  const updateFileList = useCallback((data: {filename?: string} | 'error') => {
    if (data === 'error') {
      hideLoading();
      if (onError) {
        onError('获取文件列表失败');
      }
      return;
    }

    if (data.filename) {
      const file = data.filename;
      const timePoints = file.split('-');

      if (timePoints.length === 2) {
        // 添加到缓冲区
        fileBuffer.current.push({
          filename: file,
          startTime: Number(timePoints[0]),
          endTime: Number(timePoints[1]),
        });

        // 当缓冲区达到一定大小时，立即处理
        if (fileBuffer.current.length >= 50) {
          processBufferedData();
        }
      }
    }

    // 即使没有达到批处理阈值，也进行节流处理
    processBufferedData();
  }, [processBufferedData, onError]);

  /**
   * 改变日期并获取文件列表
   */
  const changeDate = useCallback((date: Date) => {
    setCurrentDate(date);
    clearFileList();
    showLoading();

    if (webViewRef.current) {
      const startOfDay = moment(date).startOf('day');
      const endOfNextDay = moment(date).add(1, 'days');

      webViewRef.current.getfilelist([
        startOfDay.format('YYYY-MM-DD'),
        endOfNextDay.format('YYYY-MM-DD'),
      ]);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [webViewRef]);

  /**
   * 清空文件列表
   */
  const clearFileList = useCallback(() => {
    setFileList([]);
    fileBuffer.current = [];
  }, []);

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      processBufferedData.cancel();
      hideLoading();
    };
  }, [processBufferedData]);

  return {
    fileList,
    currentDate,
    updateFileList,
    changeDate,
    clearFileList,
  };
};

export default usePlaybackFileList;
