import { useState, useRef, useCallback } from 'react';
import { useGetState } from 'ahooks';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../../../types/PubSubEvent';

/**
 * 回放控制Hook返回类型
 */
export interface PlaybackControlResult {
  /** 当前进度值 */
  sliderValue: number;
  /** 当前选中的文件 */
  selectFile: [string, string];
  /** 最大时间长度 */
  getMaxTime: () => number;
  /** 设置最大时间长度 */
  setMaxTime: (time: number) => void;
  /** 开始播放和进度更新定时器 */
  startProgress: () => void;
  /** 停止进度更新定时器 */
  stopProgress: () => void;
  /** 切换到指定的文件 */
  selectVideoFile: (filename: string) => void;
  /** 跳转到指定进度 */
  seekToPosition: (value: number) => void;
  /** 清理资源 */
  cleanup: () => void;
  /** 重置播放状态 */
  resetPlayback: () => void;
}

/**
 * 回放控制Hook接口
 */
interface UsePlaybackControlOptions {
  /** WebView引用 */
  webViewRef: React.RefObject<any>;
  /** 进度更新回调 */
  onProgressUpdate?: (value: number) => void;
}

/**
 * 回放控制Hook
 * @param options 配置选项
 * @returns 回放控制接口
 */
export const usePlaybackControl = ({
  webViewRef,
  onProgressUpdate,
}: UsePlaybackControlOptions): PlaybackControlResult => {
  // 滑块定时器引用
  const sliderTimer = useRef<NodeJS.Timeout | null>(null);

  // 状态管理
  const [sliderValue, setSliderValue, getSliderValue] = useGetState<number>(0);
  const [selectFile, setSelectFile] = useState<[string, string]>(['0', '0']);
  const [, setMaxTime, getMaxTime] = useGetState<number>(0);

  /**
   * 开始进度更新定时器
   */
  const startProgress = useCallback(() => {
    // 先清除可能已存在的定时器
    if (sliderTimer.current) {
      clearInterval(sliderTimer.current);
    }

    sliderTimer.current = setInterval(() => {
      const newValue = getSliderValue() + 1;
      setSliderValue(newValue);

      // 执行进度更新回调
      if (onProgressUpdate) {
        onProgressUpdate(newValue);
      }

      // 检查是否到达结尾
      if (newValue >= getMaxTime()) {
        PubSub.publish(PubSubEvent.playRemotePause);
        stopProgress();
      }
    }, 1000);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getSliderValue, getMaxTime, setSliderValue, onProgressUpdate]);

  /**
   * 停止进度更新定时器
   */
  const stopProgress = useCallback(() => {
    if (sliderTimer.current) {
      clearInterval(sliderTimer.current);
      sliderTimer.current = null;
    }
  }, []);

  /**
   * 选择视频文件
   */
  const selectVideoFile = useCallback((filename: string) => {
    stopProgress();

    // 解析文件名，获取开始和结束时间
    const timePoints = filename.split('-');
    if (timePoints.length === 2) {
      setSelectFile([timePoints[0], timePoints[1]]);
      setMaxTime(Number(timePoints[1]) - Number(timePoints[0]));
    }

    // 调用WebView引用的播放方法
    if (webViewRef.current) {
      webViewRef.current.playremotefile(filename);
    }

    // 重置进度条并开始计时
    setSliderValue(0);
    setTimeout(() => {
      startProgress();
    }, 500);
  }, [stopProgress, setMaxTime, startProgress, setSliderValue, webViewRef]);

  /**
   * 跳转到指定进度
   */
  const seekToPosition = useCallback((value: number) => {
    stopProgress();

    setSliderValue(value);

    // 获取开始时间
    const start = selectFile[0];

    // 调用WebView引用的设置进度方法
    if (webViewRef.current) {
      webViewRef.current.setSeek((Number(start) + value).toString());
    }

    // 重新开始进度定时器
    setTimeout(() => {
      startProgress();
    }, 500);
  }, [stopProgress, selectFile, startProgress, setSliderValue, webViewRef]);

  /**
   * 重置播放状态
   */
  const resetPlayback = useCallback(() => {
    stopProgress();
    setSliderValue(0);
    setSelectFile(['0', '0']);
    setMaxTime(0);
  }, [stopProgress, setSliderValue, setMaxTime]);

  /**
   * 清理资源
   */
  const cleanup = useCallback(() => {
    stopProgress();
  }, [stopProgress]);

  return {
    sliderValue,
    selectFile,
    getMaxTime,
    setMaxTime,
    startProgress,
    stopProgress,
    selectVideoFile,
    seekToPosition,
    cleanup,
    resetPlayback,
  };
};

export default usePlaybackControl;
