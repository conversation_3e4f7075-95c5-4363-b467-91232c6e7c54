import { useState, useEffect } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';

export const useLifecycleEvents = () => {
  const [isAppActive, setIsAppActive] = useState<boolean>(true);
  const [isScreenFocused, setIsScreenFocused] = useState<boolean>(true);

  // 监听APP活动状态
  useEffect(() => {
    const subscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
      setIsAppActive(nextAppState === 'active');
    });

    return () => {
      subscription.remove();
    };
  }, []);

  // 监听屏幕焦点状态
  useFocusEffect(() => {
    setIsScreenFocused(true);

    return () => {
      setIsScreenFocused(false);
    };
  });

  // 综合判断组件是否处于活跃状态（APP处于前台且屏幕有焦点）
  const isActive = isAppActive && isScreenFocused;

  return isActive;
};
