import { useNavigation } from '@react-navigation/native';
import { useBaseIpcControl } from './useBaseIpcControl';
import { IpcNavigationProp } from '../types/navigation';
import { Device } from '../../../types/home';

export const useIpcControl = (node: Device) => {
  const navigation = useNavigation<IpcNavigationProp>();

  // 使用基础控制钩子
  const baseControls = useBaseIpcControl(node, navigation);

  const onCloseControl = () => {
    baseControls.setControlView(false);
  };

  return {
    ...baseControls,
    onCloseControl,
  };
};
