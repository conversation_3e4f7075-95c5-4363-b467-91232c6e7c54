import { IpcNavigationProp } from '../types/navigation';
import { IpcSource } from '../ipcTypes';
import I18n from '../../../I18n';
import { useBaseIpcControl } from './useBaseIpcControl';
import { Device } from '../../../types/home';
import { Toast } from '../../../Toast';

interface UseIpcCameraProps {
  navigation: IpcNavigationProp;
  node: Device;
  from?: IpcSource;
  host?: string;
  clientid?: string;
  need_update_firmware?: boolean;
}

export const useIpcCamera = ({
  navigation,
  node,
  from,
  host,
  clientid,
  need_update_firmware,
}: UseIpcCameraProps) => {
  // 使用基础控制钩子
  const baseControls = useBaseIpcControl(node, navigation);

  const onCloseControl = () => {
    baseControls.setControlView(false);
  };

  const getPlayBackData = () => {
    if (from === IpcSource.Local) {
      navigation.push('LocalPlayBack', {
        title: I18n.t('device.playback'),
        host,
        clientid,
        node,
      });
    } else {
      navigation.push('IpcPlayBackScreen', {
        title: I18n.t('device.playback'),
        node,
      });
    }
  };

  const onSettingsClick = () => {
    if (node.is_alive === false) {
      Toast.show(I18n.t('ipc.device_offine'));
      return;
    }
    navigation.push('IpcCameraSetting', {
      node,
      need_update_firmware,
      title: I18n.t('device.device_information'),
      host,
      clientid,
    });
  };

  const onEventClick = () => {
    navigation.push('eventScreen', {
      device_name: node.display_name,
      device_uuid: node.uuid,
      title: I18n.t('home.event'),
    });
  };

  return {
    ...baseControls,
    onCloseControl,
    getPlayBackData,
    onSettingsClick,
    onEventClick,
  };
};
