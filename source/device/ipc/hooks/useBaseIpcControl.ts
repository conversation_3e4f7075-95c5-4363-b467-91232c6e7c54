import {useState, useEffect} from 'react';
import {NavigationProp} from '@react-navigation/native';
import PubSub from 'pubsub-js';
import {Device} from '../../../types/home';
import { PubSubEvent } from '../../../types/PubSubEvent';

/**
 * IPC设备基础控制功能的Hook
 * 提供音频、对讲、PTZ控制和截图等基础功能
 */
export const useBaseIpcControl = (
  node: Device,
  navigation: NavigationProp<any>,
) => {
  const [isMute, setIsMute] = useState(false);
  const [speak, setSpeak] = useState(false);
  const [controlView, setControlView] = useState(false);

  // 处理导航离开时的音频状态
  useEffect(() => {
    const navigationBlur = navigation.addListener('blur', () => {
      setIsMute(true);
      setSpeak(true);
    });

    return () => navigationBlur();
  }, [navigation]);

  /**
   * 静音/取消静音切换
   */
  const muteClick = () => {
    if (isMute) {
      PubSub.publish(PubSubEvent.ios_sound, 'ok');
      setIsMute(false);
    } else {
      PubSub.publish(PubSubEvent.ios_sound, 'stop');
      setIsMute(true);
    }
  };

  /**
   * 对讲功能切换
   */
  const speakClick = () => {
    if (speak) {
      PubSub.publish(PubSubEvent.ios_speak, 'stop');
      setSpeak(false);
    } else {
      PubSub.publish(PubSubEvent.ios_speak, 'start');
      setSpeak(true);
    }
  };

  /**
   * PTZ控制
   * @param direction 控制方向
   */
  const onControl = (_direction: 'up' | 'down' | 'right' | 'left') => {
    // const directionMap = {
    //   up: '2',
    //   down: '3',
    //   right: '1',
    //   left: '0',
    // };
    // getDpsCmd(node.sn, [
    //   {
    //     ipc_cmd_key: 'control_ptz',
    //     value: directionMap[direction],
    //   },
    // ]).then(() => {});
  };

  /**
   * 截图功能
   */
  const snapShotClick = () => {
    PubSub.publish(PubSubEvent.ios_picture, 'ok');
  };

  /**
   * 控制视图切换
   */
  const toggleControlView = () => {
    setControlView(!controlView);
  };

  return {
    // 状态
    isMute,
    speak,
    controlView,

    // 方法
    setControlView,
    muteClick,
    speakClick,
    onControl,
    snapShotClick,
    toggleControlView,
  };
};
