import { useEffect, useState } from 'react';
import { AppState } from 'react-native';

interface UseLifecycleEventsProps {
  navigation: any;
}

export const useLifecycleEvents = ({ navigation }: UseLifecycleEventsProps) => {
  const [isActive, setIsActive] = useState<boolean>(true);

  useEffect(() => {
    // Navigation event listeners
    const navigationBlur = navigation.addListener('blur', () => {
      setIsActive(false);
    });

    const navigationFocus = navigation.addListener('focus', () => {
      setIsActive(true);
    });

    // AppState change listener
    const appStateSubscription = AppState.addEventListener('change', nextAppState => {
      if (nextAppState === 'active') {
        setIsActive(true);
      } else if (nextAppState === 'background') {
        setIsActive(false);
      }
    });

    // Cleanup
    return () => {
      navigationBlur();
      navigationFocus();
      appStateSubscription.remove();
    };
  }, [navigation]);

  return isActive;
};
