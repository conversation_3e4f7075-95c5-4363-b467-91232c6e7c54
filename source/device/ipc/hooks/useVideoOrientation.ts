import {useState, useCallback, useEffect} from 'react';
import {Dimensions} from 'react-native';
import Orientation from 'react-native-orientation-locker';
import {Tme} from '../../../ThemeStyle';

/**
 * 视频屏幕方向定义类型
 */
export type VideoOrientation = 'portrait' | 'landscape';

/**
 * 视频方向控制Hook返回类型
 */
export interface VideoOrientationResult {
  /** 当前视频方向 */
  videoIcon: VideoOrientation;
  /** 视频宽度 */
  videoWidth: number;
  /** 视频高度 */
  videoHeight: number;
  /** 是否显示控制器 */
  controllerShow: boolean;
  /** 视频背景色 */
  bgColor: string;
  /** 进入横屏模式 */
  changeLandscape: () => void;
  /** 进入竖屏模式 */
  changePortrait: () => void;
  /** 切换屏幕方向 */
  toggleOrientation: () => void;
}

/**
 * 视频方向控制Hook参数
 */
interface UseVideoOrientationOptions {
  /** 视频宽高比例，默认16:9 */
  aspectRatio?: number;
  /** 导航对象 */
  navigation: any;
}

/**
 * 视频方向控制Hook
 * @param options 配置选项
 * @returns 视频方向控制接口
 */
export const useVideoOrientation = ({
  aspectRatio = 16 / 9,
  navigation,
}: UseVideoOrientationOptions): VideoOrientationResult => {
  // 获取当前窗口尺寸
  const [dimensions, setDimensions] = useState(() => Dimensions.get('window'));

  // 状态管理
  const [videoIcon, setVideoIcon] = useState<VideoOrientation>('portrait');
  const [videoWidth, setVideoWidth] = useState<number>(dimensions.width);
  const [videoHeight, setVideoHeight] = useState<number>(
    dimensions.width / aspectRatio,
  );
  const [controllerShow, setControllerShow] = useState<boolean>(true);
  const [bgColor, setBgColor] = useState<string>(Tme('bgColor'));

  // 监听屏幕尺寸变化
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({window}) => {
      setDimensions(window);
    });

    return () => {
      subscription.remove();
    };
  }, []);

  // 当尺寸变化时，如果是竖屏模式，更新视频尺寸
  useEffect(() => {
    if (videoIcon === 'portrait') {
      setVideoWidth(dimensions.width);
      setVideoHeight(dimensions.width / aspectRatio);
    }
  }, [dimensions, videoIcon, aspectRatio]);

  /**
   * 切换到横屏模式
   */
  const changeLandscape = useCallback(() => {
    const currentDimensions = Dimensions.get('window');
    setVideoHeight(currentDimensions.width);
    setVideoIcon('landscape');
    setBgColor('black');
    setVideoWidth(currentDimensions.width * aspectRatio);
    setControllerShow(false);

    // 设置屏幕方向
    Orientation.unlockAllOrientations();
    Orientation.lockToLandscapeLeft();

    // 隐藏头部导航
    navigation.setOptions({
      headerShown: false,
    });
  }, [aspectRatio, navigation]);

  /**
   * 切换到竖屏模式
   */
  const changePortrait = useCallback(() => {
    // 先设置方向，让系统有时间改变屏幕方向
    Orientation.unlockAllOrientations();
    Orientation.lockToPortrait();

    // 获取最新的窗口尺寸
    const currentDimensions = Dimensions.get('window');

    // 立即更新状态
    setVideoIcon('portrait');
    setBgColor(Tme('bgColor'));
    setControllerShow(true);

    // 设置视频尺寸
    setVideoWidth(currentDimensions.width);
    setVideoHeight(currentDimensions.width / aspectRatio);

    // 显示头部导航
    navigation.setOptions({
      headerShown: true,
    });
  }, [aspectRatio, navigation]);

  /**
   * 切换屏幕方向
   */
  const toggleOrientation = useCallback(() => {
    if (videoIcon === 'portrait') {
      changeLandscape();
    } else {
      changePortrait();
    }
  }, [videoIcon, changeLandscape, changePortrait]);

  return {
    videoIcon,
    videoWidth,
    videoHeight,
    controllerShow,
    bgColor,
    changeLandscape,
    changePortrait,
    toggleOrientation,
  };
};

export default useVideoOrientation;
