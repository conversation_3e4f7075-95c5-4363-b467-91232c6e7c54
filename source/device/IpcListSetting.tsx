import React, {useEffect, useRef, useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, FlatList} from 'react-native';
import {Tme} from '../ThemeStyle';
import {SafeAreaView} from 'react-native-safe-area-context';
import I18n from '../I18n';
import SwitchBtn from '../share/SwitchBtn';
import {
  // NavigationProp,
  // RouteProp,
  useNavigation,
  // useRoute,
} from '@react-navigation/native';
import {IpcNavigationProp} from './ipc/types/navigation';
import {getSceneName, mainRadius} from '../Tools';
import {Helper} from '../Helper';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {SelectDate} from '../share/SelectDateView';
import moment from 'moment';
import PubSub from 'pubsub-js';
import _ from 'lodash';
import {hideLoading, showLoading} from '../../ILoading';
import EmptyView from '../share/EmptyView';
import {Device} from '../types/home';
import {PubSubEvent} from '../types/PubSubEvent';
import {Toast} from '../Toast';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';

const start = moment().startOf('day').toDate();
const end_time = moment().endOf('day').toDate();

export default function IpcListSetting(): React.ReactElement {
  const [deviceData, setDeviceData] = useState<Device[]>([]);

  const navigation = useNavigation<IpcNavigationProp>();

  const [startTime, setStartTime] = useState<Date>(start);
  const [endTime, setEndTime] = useState<Date>(end_time);
  const [scenes, setScenes] = useState<any[]>([]);
  const [sceneNames, setSceneNames] = useState<string[]>([]);
  const [sceneIds, setSceneIds] = useState<string[]>([]);
  const [wday, setWday] = useState<any[]>([]);
  const [notifyEnable, setNotifyEnable] = useState<boolean>(false);
  const [disturbEnable, setDisturbEnable] = useState<boolean>(false);

  const animationHeight = useSharedValue(0);

  const week = useRef([
    {key: 0, value: I18n.t('setting.sunday')},
    {key: 1, value: I18n.t('setting.monday')},
    {key: 2, value: I18n.t('setting.tuesday')},
    {key: 3, value: I18n.t('setting.wednesday')},
    {key: 4, value: I18n.t('setting.thursday')},
    {key: 5, value: I18n.t('setting.friday')},
    {key: 6, value: I18n.t('setting.saturday')},
  ]);

  // useEffect(() => {
  //   setDeviceData(route.params!.devices);
  // }, [route.params]);

  useEffect(() => {
    doFetchData();

    PubSub.subscribe(
      PubSubEvent.SMART_SELECT_EVENT,
      (_msg, data: {ids: string[]; names: string[]}) => {
        setSceneIds(data.ids);
        setSceneNames(initSceneName(scenes, data.ids));
        upNotifyData('scene_ids', data.ids);
        Toast.show();
      },
    );
    PubSub.subscribe(PubSubEvent.ipc_wday, (_msg, data: {wday: any[]}) => {
      setWday(data.wday);
      upNotifyData('wday', data.wday);
      Toast.show();
    });
    return () => {
      PubSub.unsubscribe(PubSubEvent.SMART_SELECT_EVENT);
      PubSub.unsubscribe(PubSubEvent.ipc_wday);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    animationHeight.value = withTiming(disturbEnable ? 1 : 0, {duration: 300});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [disturbEnable]);

  const doFetchData = () => {
    showLoading();
    Helper.httpGET(Helper.urlWithQuery('/notifies/ipc_notifies', {}), {
      ensure: () => {
        hideLoading();
      },
      success: (data: any) => {
        setScenes(data.scenes);
        if (data.ipc_notify) {
          setSceneNames(initSceneName(data.scenes, data.ipc_notify.scene_ids));
          setSceneIds(data.ipc_notify.scene_ids);
          setNotifyEnable(data.ipc_notify.is_enable);
          if (data.ipc_notify.notify_dates.length > 0) {
            const date = data.ipc_notify.notify_dates[0];
            setStartTime(moment(date.begin_at, 'YYYY-MM-DD HH:mm:ss').toDate());
            setEndTime(moment(date.end_at, 'YYYY-MM-DD HH:mm:ss').toDate());
            setWday(date.week_day);
            setDisturbEnable(date.is_enable);
          } else {
            setDisturbEnable(false);
          }
        }

        setDeviceData(data.ipc_nodes);
        // setWday(data.ipc_notify.notify_dates);
      },
    });
  };

  const onItemClick = (item: Device) => {
    if (item.is_alive === false) {
      Toast.show(I18n.t('ipc.device_offine'));
      return;
    }
    navigation.push('IpcCameraSetting', {
      node: item,
      title: item.display_name,
    });
  };

  const initSceneName = (s: any, names: string[]) => {
    var temp: string[] = [];
    _.forEach(names, (uuid, _key) => {
      _.forEach(s, (scene, _k) => {
        if (scene.uuid == uuid) {
          temp.push(getSceneName(scene.name));
        }
      });
    });

    if (temp.length == s.length) {
      return [I18n.t('global.all')];
    } else {
      return temp.length == 0 ? [] : temp;
    }
  };

  const showWday = () => {
    var temp: string[] = [];
    var week_day = _.cloneDeep(wday).sort();
    _.forEach(week_day, (day, _key) => {
      _.forEach(week.current, (w, _k) => {
        if (w.key == day) {
          temp.push(w.value);
        }
      });
    });
    if (temp.length == 7) {
      return I18n.t('global.every_day');
    } else {
      return temp.length == 0 ? '' : temp.join(' ');
    }
  };

  const upNotifyData = (type: string, value: any) => {
    const tempData: any = {};
    if (type === 'enable') {
      tempData.is_enable = `${value}`;
    }
    if (type === 'wday') {
      tempData.week_day = value;
    }
    if (type === 'startTime') {
      tempData.begin_at = moment(value).format('HH:mm');
    }
    if (type === 'endTime') {
      tempData.end_at = moment(value).format('HH:mm');
    }

    if (type === 'scene_ids') {
      tempData.scene_ids = value;
    }
    if (type === 'notify_date_is_enable') {
      tempData.notify_date_is_enable = `${value}`;
    }

    showLoading();
    Helper.httpPOST(
      '/notifies/ipc_update',
      {
        ignore_error: true,
        success: () => {
          doFetchData();
        },
        ensure: () => {
          hideLoading();
        },
      },
      tempData,
    );
  };

  const renderDeviceItem = ({item}: {item: Device}) => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        style={{
          backgroundColor: Tme('cardColor'),
          marginHorizontal: 15,
          marginVertical: 8,
          borderRadius: mainRadius(),
          padding: 16,
        }}
        onPress={() => onItemClick(item)}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          <Text
            style={{
              fontSize: 16,
              fontWeight: '600',
              color: Tme('cardTextColor'),
              marginBottom: 4,
            }}>
            {item.display_name}
          </Text>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <View
              style={{
                width: 10,
                height: 10,
                borderRadius: 5,
                backgroundColor: item.is_alive ? '#4CAF50' : '#F44336',
              }}
            />
            <MaterialIcons
              name="keyboard-arrow-right"
              size={24}
              color={Tme('cardTextColor')}
            />
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      height: animationHeight.value === 0 ? 0 : 'auto',
      opacity: animationHeight.value,
      overflow: 'hidden',
    };
  });

  const headerComponent = () => {
    return (
      <>
        <View
          style={{
            marginHorizontal: 15,
            borderRadius: mainRadius(),
            backgroundColor: Tme('cardColor'),
            padding: 16,
          }}>
          <TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
            activeOpacity={1.0}
            onPress={() => {}}>
            <Text
              numberOfLines={2}
              style={[
                styles.rowTitleText,
                {color: Tme('cardTextColor'), flex: 1},
              ]}>
              {I18n.t('ipc.switch_all')}
            </Text>

            <SwitchBtn
              key={Math.random()}
              value={notifyEnable}
              change={() => {
                setNotifyEnable(!notifyEnable);
                upNotifyData('enable', !notifyEnable);
                Toast.show();
              }}
            />
          </TouchableOpacity>
          <Text
            style={{
              marginTop: 10,
              color: Tme('smallTextColor'),
              fontSize: 12,
            }}>
            {I18n.t('ipc.switch_all_desp')}
          </Text>
        </View>
        {notifyEnable && (
          <>
            <View style={{height: 20}} />
            <View
              style={{
                marginHorizontal: 15,
                backgroundColor: Tme('cardColor'),
                marginBottom: 2,
                padding: 16,
                borderTopLeftRadius: mainRadius(),
                borderTopRightRadius: mainRadius(),
                borderBottomLeftRadius: !disturbEnable ? mainRadius() : 0,
                borderBottomRightRadius: !disturbEnable ? mainRadius() : 0,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <Text style={{color: Tme('cardTextColor'), fontSize: 17}}>
                  {I18n.t('ipc.ipc_alarm_setting')}
                </Text>
                <SwitchBtn
                  key={Math.random()}
                  value={disturbEnable}
                  change={() => {
                    setDisturbEnable(!disturbEnable);
                    upNotifyData('notify_date_is_enable', !disturbEnable);
                    Toast.show();
                  }}
                />
              </View>
            </View>
            <Animated.View style={animatedStyle}>
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={() => {
                  navigation.push('SmartSceneView', {
                    title: I18n.t('global.scene'),
                    scene_ids: sceneIds,
                    scenes: scenes,
                    from: 'ipc',
                  });
                }}
                style={{
                  marginHorizontal: 15,
                  backgroundColor: Tme('cardColor'),
                  padding: 16,
                  marginBottom: 2,
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                  <View>
                    <Text style={{color: Tme('cardTextColor'), fontSize: 17}}>
                      {I18n.t('global.scene')}
                    </Text>
                  </View>
                  <View style={styles.touchRow}>
                    <Text
                      style={{color: Tme('textColor'), fontSize: 17}}
                      numberOfLines={1}>
                      {sceneNames.length > 0 ? sceneNames.join(' ') : null}
                    </Text>
                    <MaterialIcons
                      name="keyboard-arrow-right"
                      size={20}
                      color={Tme('textColor')}
                    />
                  </View>
                </View>

                <Text
                  style={{
                    color: Tme('smallTextColor'),
                    fontSize: 12,
                    marginTop: 10,
                  }}>
                  {I18n.t('ipc.scene_condition')}
                </Text>
              </TouchableOpacity>

              <View
                style={{
                  marginHorizontal: 15,
                  backgroundColor: Tme('cardColor'),
                  marginBottom: 2,
                }}>
                <SelectDate
                  from="start_at"
                  key={1}
                  title={I18n.t('setting.starting_time')}
                  value={startTime}
                  onChange={(data: Date) => {
                    setStartTime(data);
                    upNotifyData('startTime', data);
                    Toast.show();
                  }}
                />
              </View>

              <View
                style={{
                  marginHorizontal: 15,
                  backgroundColor: Tme('cardColor'),
                  marginBottom: 2,
                }}>
                <SelectDate
                  from="end_at"
                  key={1}
                  title={I18n.t('setting.end_time')}
                  value={endTime}
                  onChange={(data: Date) => {
                    setEndTime(data);
                    upNotifyData('endTime', data);
                    Toast.show();
                  }}
                />
              </View>
              <TouchableOpacity
                testID="week"
                activeOpacity={0.8}
                onPress={() => {
                  navigation.push('SmartTimeView', {
                    title: I18n.t('setting.select_date'),
                    from: 'ipc',
                    cycle: true,
                    wday: wday,
                  });
                }}
                style={{
                  marginHorizontal: 15,
                  backgroundColor: Tme('cardColor'),
                  padding: 16,
                  borderBottomLeftRadius: mainRadius(),
                  borderBottomRightRadius: mainRadius(),
                  marginBottom: 2,
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}>
                  <Text
                    style={{
                      color: Tme('cardTextColor'),
                      textAlign: 'center',
                      fontSize: 17,
                    }}>
                    {I18n.t('setting.select_date')}
                  </Text>

                  <View style={[styles.touchRow, {flex: 1}]}>
                    <View
                      style={{
                        flexDirection: 'column',
                        alignItems: 'flex-end',
                      }}>
                      <Text
                        style={{
                          color: Tme('textColor'),
                          marginLeft: 8,
                          flexWrap: 'wrap',
                        }}>
                        {showWday()}
                      </Text>
                    </View>
                    <MaterialIcons
                      name="keyboard-arrow-right"
                      size={20}
                      color={Tme('textColor')}
                    />
                  </View>
                </View>
              </TouchableOpacity>
            </Animated.View>
          </>
        )}
        <View style={{paddingHorizontal: 16, paddingTop: 16, paddingBottom: 4}}>
          <Text style={{color: Tme('cardTextColor')}}>
            {I18n.t('home.device')}
          </Text>
        </View>
      </>
    );
  };

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: Tme('bgColor')}}>
      <FlatList
        data={deviceData}
        ListHeaderComponent={headerComponent}
        renderItem={renderDeviceItem}
        keyExtractor={(Item, index) => index.toString()}
        ListFooterComponent={<View style={{height: 40}} />}
        ListEmptyComponent={<EmptyView />}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  touchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  rowTitleText: {
    fontSize: 17,
  },
  rowRight: {
    marginLeft: 38,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
