import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Keyboard,
} from 'react-native';
import { NotificationCenter, SMART_LIST_ADD } from '../../NotificationCenter';
import { Tme, Colors } from '../../ThemeStyle';
import { CameraScreen } from 'react-native-camera-kit';
import IdleTimerManager from 'react-native-idle-timer';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import NavBarView from '../../share/NavBarView';
import I18n from '../../I18n';
import AlertModal from '../../share/AlertModal';
import CardView from '../../share/CardView';
import { ssQrcode } from '../../Tools';
import ScreenSizeContext from '../../../WindowResizeContext';

class ScanQrScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      input_show: this.props.route.params.type == 'input',
      scan_show: this.props.route.params.type == 'scan',
    };
    this.rq = true;
  }

  static contextType = ScreenSizeContext;

  componentDidMount() {
    IdleTimerManager.setIdleTimerDisabled(true);
  }

  componentWillUnmount() {
    Keyboard.dismiss();
    IdleTimerManager.setIdleTimerDisabled(false);
  }

  render() {
    return (

      <NavBarView>
        <KeyboardAwareScrollView
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="never"
          keyboardDismissMode="on-drag"
          innerRef={ref => {
            this.scroll = ref;
          }}
          style={{
            flex: 1,
            backgroundColor: Tme('bgColor'),
          }}>
          {this.state.scan_show ? (
            <CameraScreen
              style={{ height: this.context.winHeight }}
              scanBarcode={true}
              showFrame={true}
              laserColor={'blue'}
              frameColor={Colors.MainColor}
              onReadCode={event => this.readCode(event)}
              hideControls={false}
              colorForScannerFrame={'red'} //(default white) optional, change colot of the scanner frame
            />
          ) : (
            <View style={{ margin: 20 }}>
              <CardView
                showMenu={false}
                styles={[
                  {
                    width: this.context.winWidth - 40,
                    height: 480,
                    borderRadius: 8,
                  },
                ]}>
                <View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <View style={{ marginTop: 79 }}>
                    <Text
                      style={{
                        fontSize: 22,
                        fontWeight: '600',
                        textAlign: 'center',
                        color: Tme('cardTextColor'),
                      }}>
                      Smart Start
                    </Text>
                  </View>
                  {this.state.input_show ? (
                    <View>
                      <View style={{ padding: 16 }}>
                        <Text
                          style={{ fontSize: 14, color: Tme('cardTextColor') }}>
                          {I18n.t('global.add_smart_desp')}
                        </Text>
                      </View>
                      <View
                        style={{
                          padding: 16,
                        }}>
                        <Text
                          style={{
                            marginBottom: 16,
                            fontSize: 16,
                            fontWeight: '600',
                            color: Tme('cardTextColor'),
                          }}>
                          DSK
                        </Text>
                        <View
                          style={[
                            styles.account_view,
                            { borderColor: Tme('inputBorderColor') },
                          ]}>
                          <TextInput
                            returnKeyType="go"
                            autoCapitalize="none"
                            keyboardType="number-pad"
                            underlineColorAndroid="transparent"
                            autoCorrect={false}
                            value={this.state.dsk}
                            multiline={true}
                            numberOfLines={2}
                            maxLength={40}
                            onChangeText={dsk => this.setState({ dsk })}
                            onSubmitEditing={this._save.bind(this)}
                            style={{
                              height: 66,
                              marginLeft: 6,
                              marginRight: 6,
                              fontSize: 15,
                              fontWeight: '600',
                              letterSpacing: 1,
                              color: Tme('cardTextColor'),
                            }}
                          />
                        </View>
                      </View>
                      <View style={{ marginTop: 20, alignItems: 'center' }}>
                        <TouchableOpacity
                          activeOpacity={0.8}
                          onPress={this._save.bind(this)}
                          style={{
                            width: 120,
                            height: 40,
                            borderRadius: 20,
                            backgroundColor: Colors.MainColor,
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}>
                          <Text
                            style={{
                              fontSize: 16,
                              fontWeight: '600',
                              color: '#ffffff',
                              textAlign: 'center',
                            }}>
                            {I18n.t('home.save')}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  ) : null}
                </View>
              </CardView>
            </View>
          )}
        </KeyboardAwareScrollView>
      </NavBarView>
    );
  }

  readCode(event) {
    var that = this;
    if (this.rq) {
      that.rq = false;
      const qr = ssQrcode(event.nativeEvent.codeStringValue);

      NotificationCenter.dispatchEvent(SMART_LIST_ADD, qr.dsk);
      that.props.navigation.goBack();
    }
  }

  _save() {
    Keyboard.dismiss();
    var value = '';
    if (this.state.dsk) {
      if (/^\d{40}$/.test(this.state.dsk)) {
        var temp = this.state.dsk;
        var dsk = temp.replace(/(.{5})/g, '$1-');
        value = dsk.substring(0, dsk.length - 1);
      } else {
        AlertModal.alert(I18n.t('global.dsk_input'));
        return;
      }
    } else {
      AlertModal.alert(I18n.t('global.dsk_input'));
      return;
    }

    if (value !== '') {
      NotificationCenter.dispatchEvent(SMART_LIST_ADD, value);
      this.props.navigation.goBack();
    }
  }
}
const styles = StyleSheet.create({
  account_view: {
    borderWidth: 1,
    borderRadius: 16,
    height: 66,
  },
});

export default ScanQrScreen;
