import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  LayoutAnimation,
} from 'react-native';
import { Helper } from '../Helper';
import {
  NotificationCenter,
  DEVICE_NOTIFY_DATE,
} from '../NotificationCenter';
import { observer } from 'mobx-react/native';
import { observable } from 'mobx';
import SwitchBtn from '../share/SwitchBtn';
import NavBarView from '../share/NavBarView';
import { Tme } from '../ThemeStyle';
import I18n from '../I18n';
import _ from 'lodash';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { Toast } from '../Toast';
import { convertDateUTCForIos, formatDate, getSceneName } from '../Tools';
import moment from 'moment';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { hideLoading, showLoading } from '../../ILoading';
import { PubSubEvent } from '../types/PubSubEvent';
import PubSub from 'pubsub-js';
const nowTimeStamp = Date.now();
const nowTime = new Date(nowTimeStamp);

class Notify {
  @observable index = '';
  @observable value_id = '';
  @observable sn_id = '';
  @observable spec_name = '';
  @observable is_enable = false;
  @observable is_urgent = false;
  @observable scene_ids = [];
}
class Nd {
  @observable notifys = [];
}

@observer
class DeviceNotifyScreen extends Component {
  Nd = new Nd();

  constructor(props) {
    super(props);

    this.state = {
      scenes: [],
      device: {},
      notifys: [],
      show: false,
      clickRow: '',
      device_state: true,
      low_battery: true,
      show_battery: false,
      from: '',
      key: '',
      notifyDate: {},
      sn_tags: [],
      deviceType: '',
      expanded: false,
    };

    this.props.navigation.setOptions({
      // eslint-disable-next-line react/no-unstable-nested-components
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  componentDidMount() {
    this.doFetchData();
    PubSub.subscribe(PubSubEvent.SMART_SELECT_EVENT, (msg, data) => {
      if (data.type == 'scene') {
        this.editNotify(data.data);
      }
    });

    NotificationCenter.addObserver(this, DEVICE_NOTIFY_DATE, data => {
      LayoutAnimation.configureNext(LayoutAnimation.Presets.linear);
      this.setState({
        expanded: !this.state.expanded,
        notifyDate: data,
      });
    });
  }

  rightClick() {
    this._save();
  }
  componentWillUnmount() {
    PubSub.unsubscribe(PubSubEvent.SMART_SELECT_EVENT);
    NotificationCenter.removeObserver(this, DEVICE_NOTIFY_DATE);
  }

  _save() {
    var that = this;
    var data = this.Nd.notifys;
    showLoading();
    var notifyDate = {};
    if (!_.isEmpty(this.state.notifyDate)) {
      notifyDate = {
        startTime: formatDate(this.state.notifyDate.begin_at, 'hh:mm'),
        endTime: formatDate(this.state.notifyDate.end_at, 'hh:mm'),
        wday: this.state.notifyDate.week_day,
      };
    }

    Helper.httpPOST(
      '/notifies/notify_device',
      {
        success: () => {
          this.props.navigation.goBack();
          Toast.show();
        },
        ensure: () => {
          hideLoading();
        },
      },
      {
        data: data,
        device_state: that.state.device_state,
        uuid: that.state.device.uuid,
        notify_date: notifyDate,
        low_battery: that.state.low_battery,
      },
    );
  }

  doFetchData() {
    var that = this;
    showLoading();
    Helper.httpGET(
      Helper.urlWithQuery('/notifies/' + this.props.route.params.uuid),
      {
        cloud: true,
        success: data => {
          var item = data.device;
          if (item.dv_type !== 'camera') {
            if (data.urgent_devices) {
              if (data.urgent_devices.length > 0) {
                _.forEach(data.urgent_devices, urgent => {
                  var notify = new Notify();
                  notify.scene_ids = urgent.scene_ids;
                  notify.is_enable = urgent.is_enable;
                  notify.is_urgent = urgent.is_urgent;

                  notify.value_id = urgent.devices[0].value_id;
                  notify.index = urgent.devices[0].index;
                  notify.spec_name = urgent.devices[0].spec_name;
                  notify.sn_id = urgent.devices[0].sn_id;

                  that.Nd.notifys.push(notify);
                });
              }
            }
            var battery = _.find(data.device.cc_specs, o => {
              return o.name.toLowerCase() == 'battery';
            });
            that.setState({
              sn_tags: data.sn_tags,
              scenes: data.scenes,
              device: data.device,
              notifyDate: that.initDate(data.notify_date),
              show: true,
              device_state: data.state == false ? false : true,
              low_battery: data.battery == false ? false : true,
              show_battery: battery ? true : false,
            });
          } else {
            that.setState({
              show: true,
              deviceType: 'camera',
              device: data.device,
              notifyDate: that.initDate(data.notify_date),
            });
          }
        },
        ensure: () => {
          hideLoading();
        },
      },
    );
  }

  initDate(notifyDate) {
    var date = {};
    if (!_.isEmpty(notifyDate)) {
      var week = [];
      _.forEach(notifyDate[0].week_day, day => {
        week.push(day);
      });
      date = {
        week_day: week,
        begin_at: new Date(
          convertDateUTCForIos(notifyDate[0].begin_at).getTime() +
          nowTime.getTimezoneOffset() * 60000,
        ),
        end_at: new Date(
          convertDateUTCForIos(notifyDate[0].end_at).getTime() +
          nowTime.getTimezoneOffset() * 60000,
        ),
        nodeUUID: notifyDate[0].device_uuid,
      };
    }
    return date;
  }

  render() {
    return (
      <NavBarView>
        <ScrollView
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="never"
          keyboardDismissMode="on-drag">
          <Text style={{ display: 'none' }}>{this.state.key}</Text>
          {this.state.show ? (
            <View
              style={{
                flex: 1,
              }}>
              <View style={{ marginTop: 20, backgroundColor: Tme('cardColor') }}>
                <TouchableOpacity
                  activeOpacity={1.0}
                  onPress={this.onClick.bind(this, 'date')}>
                  <View
                    style={[
                      styles.row,
                      { paddingVertical: 20, borderBottomWidth: 0 },
                    ]}>
                    <Text
                      style={[styles.rowTitleText, { color: Tme('textColor') }]}>
                      {I18n.t('setting.notify_by_time')}
                    </Text>
                    <View
                      style={{
                        marginLeft: 38,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <Text style={{ color: Tme('textColor') }}>
                        {_.isEmpty(this.state.notifyDate) ? (
                          <MaterialIcons
                            name="keyboard-arrow-right"
                            size={20}
                            color={Tme('textColor')}
                          />
                        ) : (
                          I18n.t('spec.on_b')
                        )}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              </View>
              <View style={{ height: 2 }} />
              {this.state.notifyDate ? (
                <TouchableOpacity
                  activeOpacity={1.0}
                  onPress={this.onClick.bind(this, 'date')}
                  style={{ backgroundColor: Tme('cardColor') }}>
                  {this.showDate()}
                </TouchableOpacity>
              ) : null}
              {this.state.deviceType !== 'camera' ? (
                <View
                  style={{
                    backgroundColor: Tme('bgColor'),
                    paddingHorizontal: 16,
                    paddingTop: 20,
                    paddingBottom: 10,
                  }}>
                  <Text style={{ fontSize: 12, color: Tme('cardTextColor') }}>
                    {I18n.t('device.device_state_desp')}
                  </Text>
                </View>
              ) : null}
              {this.state.deviceType !== 'camera' ? (
                <View style={{ backgroundColor: Tme('cardColor') }}>
                  <TouchableOpacity
                    activeOpacity={1.0}
                    onPress={this.onClick.bind(this, 'state')}>
                    <View style={[styles.row, { borderBottomWidth: 0 }]}>
                      <Text
                        style={[
                          styles.rowTitleText,
                          { color: Tme('textColor') },
                        ]}>
                        {I18n.t('device.device_state')}
                      </Text>
                      <View
                        style={{
                          marginLeft: 38,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}>
                        <SwitchBtn
                          key={Math.random()}
                          value={this.state.device_state}
                          change={this.onClick.bind(this, 'state')}
                        />
                      </View>
                    </View>
                  </TouchableOpacity>
                </View>
              ) : null}
              {this.state.show_battery ? (
                <View>
                  <View
                    style={{
                      backgroundColor: Tme('bgColor'),
                      paddingHorizontal: 16,
                      paddingTop: 20,
                      paddingBottom: 10,
                    }}>
                    <Text style={{ fontSize: 12, color: Tme('cardTextColor') }}>
                      {I18n.t('device.notify_battery_desp')}
                    </Text>
                  </View>
                  <View style={{ backgroundColor: Tme('cardColor') }}>
                    <TouchableOpacity
                      activeOpacity={1.0}
                      onPress={this.onClick.bind(this, 'battery')}>
                      <View style={[styles.row, { borderBottomWidth: 0 }]}>
                        <Text
                          style={[
                            styles.rowTitleText,
                            { color: Tme('textColor') },
                          ]}>
                          {I18n.t('device.notify_battery')}
                        </Text>
                        <View
                          style={{
                            marginLeft: 38,
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}>
                          <SwitchBtn
                            key={Math.random()}
                            value={this.state.low_battery}
                            change={this.onClick.bind(this, 'battery')}
                          />
                        </View>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : null}
              {this.state.deviceType !== 'camera' ? (
                <View
                  style={{
                    backgroundColor: Tme('bgColor'),
                    paddingHorizontal: 16,
                    paddingTop: 20,
                    paddingBottom: 10,
                  }}>
                  <Text style={{ color: Tme('cardTextColor'), fontSize: 12 }}>
                    {I18n.t('device.notify_desp')}
                  </Text>
                </View>
              ) : null}
              {this.renderItem(this.state.device)}
            </View>
          ) : null}
        </ScrollView>
      </NavBarView>
    );
  }

  showDate() {
    const { notifyDate } = this.state;
    if (notifyDate.week_day) {
      var tomorrow = '';
      if (moment(notifyDate.end_at).isBefore(notifyDate.begin_at)) {
        tomorrow = I18n.t('setting.next_day');
      }

      return (
        <View
          style={{
            padding: 16,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          <View>
            <View style={{ flexDirection: 'row', marginBottom: 4 }}>
              <Text style={{ color: Tme('textColor') }}>
                {formatDate(notifyDate.begin_at, 'hh:mm')}
              </Text>
              <Text style={{ color: Tme('textColor'), marginHorizontal: 4 }}>
                -
              </Text>
              {tomorrow == '' ? null : (
                <Text style={{ color: Tme('textColor'), marginRight: 4 }}>
                  {tomorrow}
                </Text>
              )}
              <Text style={{ color: Tme('textColor') }}>
                {formatDate(notifyDate.end_at, 'hh:mm')}
              </Text>
            </View>
            <Text style={{ color: Tme('textColor') }}>
              {this.showWday(notifyDate.week_day)}
            </Text>
          </View>
          <MaterialIcons
            name="keyboard-arrow-right"
            size={20}
            color={Tme('textColor')}
          />
        </View>
      );
    } else {
      return null;
    }
  }

  showWday(notifyWeek) {
    var week = [
      { key: 0, value: I18n.t('setting.sunday') },
      { key: 1, value: I18n.t('setting.monday') },
      { key: 2, value: I18n.t('setting.tuesday') },
      { key: 3, value: I18n.t('setting.wednesday') },
      { key: 4, value: I18n.t('setting.thursday') },
      { key: 5, value: I18n.t('setting.friday') },
      { key: 6, value: I18n.t('setting.saturday') },
    ];
    var temp = [];
    _.forEach(notifyWeek.sort(), (day, key) => {
      _.forEach(week, (w, k) => {
        if (w.key == day) {
          temp.push(w.value);
        }
      });
    });
    if (temp.length == 7) {
      return I18n.t('global.every_day');
    } else {
      return temp.length == 0 ? I18n.t('global.every_day') : temp.join(' ');
    }
  }

  renderItem(data) {
    var html = [];
    if (data.dv_type == 'camera') {
      return html;
    } else {
      _.each(data.cc_specs, (item, key) => {
        var row = _.find(this.Nd.notifys, function (o) {
          return o.value_id == item.value_id;
        });
        html.push(
          <View
            key={key}
            style={[
              {
                backgroundColor: Tme('cardColor'),
              },
            ]}>
            <View
              style={{
                paddingTop: 16,
                marginLeft: 16,
                paddingRight: 16,
                paddingBottom: 16,
                borderBottomWidth: 1,
                borderBottomColor: Tme('inputBorderColor'),
              }}>
              <Text style={{ fontSize: 17, color: Tme('cardTextColor') }}>
                {Helper.i(item.name)}
              </Text>
            </View>
            <TouchableOpacity
              activeOpacity={1.0}
              onPress={this.isEnabled.bind(this, item, 'is_enable')}>
              <View style={[styles.row, { borderBottomColor: 'transparent' }]}>
                <Text style={[styles.rowTitleText, { color: Tme('textColor') }]}>
                  {I18n.t('setting.change_notification')}
                </Text>
                <View
                  style={{
                    marginLeft: 38,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <SwitchBtn
                    key={Math.random()}
                    value={row ? row.is_enable : false}
                    change={this.isEnabled.bind(this, item, 'is_enable')}
                  />
                </View>
              </View>
            </TouchableOpacity>
            {_.includes(this.state.sn_tags, 'local_siren') ? (
              <TouchableOpacity
                activeOpacity={1.0}
                onPress={this.isEnabled.bind(this, item, 'is_urgent')}>
                <View style={[styles.row, { borderBottomColor: 'transparent' }]}>
                  <Text
                    style={[styles.rowTitleText, { color: Tme('textColor') }]}>
                    {I18n.t('setting.local_siren')}
                  </Text>
                  <View style={styles.rowRight}>
                    <SwitchBtn
                      key={Math.random()}
                      value={row ? row.is_urgent : false}
                      change={this.isEnabled.bind(this, item, 'is_urgent')}
                    />
                  </View>
                </View>
              </TouchableOpacity>
            ) : null}
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.click.bind(this, item)}>
              <View
                style={[
                  styles.row,
                  {
                    borderBottomColor: 'transparent',
                    borderBottomWidth: key == data.cc_specs.length - 1 ? 0 : 1,
                  },
                ]}>
                <View>
                  <Text
                    style={[styles.rowTitleText, { color: Tme('textColor') }]}>
                    {I18n.t('global.scene')}
                  </Text>
                </View>
                <View style={styles.touchRow}>
                  <Text
                    style={{ color: Tme('textColor'), fontSize: 14 }}
                    numberOfLines={1}>
                    {this.getSceneName(row ? row.scene_ids : [])}
                  </Text>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={20}
                    color={Tme('textColor')}
                  />
                </View>
              </View>
            </TouchableOpacity>
          </View>,
        );
      });
      return html;
    }
  }

  onClick(type) {
    switch (type) {
      case 'state':
        this.setState({
          device_state: !this.state.device_state,
        });
        break;
      case 'battery':
        this.setState({
          low_battery: !this.state.low_battery,
        });
        break;
      case 'date':
        this.props.navigation.push('DeviceNotifyDate', {
          notifyDate: this.state.notifyDate,
          title: I18n.t('setting.notify_by_time'),
        });
        break;
      default:
        break;
    }
  }

  isEnabled(item, type) {
    var row = _.find(this.Nd.notifys, function (o) {
      return o.value_id == item.value_id;
    });
    if (row) {
      if (type == 'is_enable') {
        row.is_enable = !row.is_enable;
      } else {
        row.is_urgent = !row.is_urgent;
      }
      this.editNotify(row);
    } else {
      var notify = new Notify();
      notify.value_id = item.value_id;
      notify.index = item.device_id;
      notify.spec_name = item.name;
      notify.sn_id = this.state.device.sn_id;
      if (type == 'is_enable') {
        notify.is_enable = true;
      } else {
        notify.is_urgent = true;
      }
      this.editNotify(notify);
    }
  }

  click(item) {
    var notify = _.find(this.Nd.notifys, function (o) {
      return o.value_id == item.value_id;
    });
    if (!notify) {
      notify = new Notify();
      notify.value_id = item.value_id;
      notify.index = item.device_id;
      notify.spec_name = item.name;
    }

    this.props.navigation.push('NotifyScene', {
      notify: notify,
      scenes: this.state.scenes,
      title: I18n.t('global.scene'),
    });
  }

  editNotify(row) {
    var temp = _.find(this.Nd.notifys, function (o) {
      return o.value_id == row.value_id;
    });
    if (temp) {
      var notifys = this.Nd.notifys;
      _.remove(notifys, function (o) {
        return o.value_id == row.value_id;
      });
      notifys.push(row);
      this.Nd.notifys = notifys;
    } else {
      this.Nd.notifys.push(row);
    }
    this.setState({
      key: Math.random(),
    });
  }

  getSceneName(ids) {
    var that = this;
    var temp = [];
    _.forEach(ids, id => {
      _.forEach(that.state.scenes, scene => {
        if (scene.uuid == id) {
          temp.push(getSceneName(scene.name));
        }
      });
    });
    if (temp.length == this.state.scenes.length) {
      return [I18n.t('global.all')];
    } else {
      return temp.length == 0 ? [I18n.t('global.all')] : temp.join(' ');
    }
  }
}
const styles = StyleSheet.create({
  touchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  rowTitleText: {
    fontSize: 14,
  },
  row: {
    paddingVertical: 12,
    marginLeft: 16,
    paddingRight: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    alignItems: 'center',
  },
  rowRight: {
    marginLeft: 38,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
export default DeviceNotifyScreen;
