import React, { Component } from 'react';
import { View, Text, TextInput, StyleSheet, ScrollView } from 'react-native';
import { Helper } from '../Helper';
import { Tme, Colors } from '../ThemeStyle';
import I18n from '../I18n';
import { NotificationCenter, EVENT_DEVICE } from '../NotificationCenter';
import NavBarView from '../share/NavBarView';
import { Toast } from '../Toast';
import CameraView from './CameraView';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { hideLoading, showLoading } from '../../ILoading';
import ScreenSizeContext from '../../WindowResizeContext';

class AddCamera extends Component {
  constructor(props) {
    super(props);
    this.state = {
      name: this.props.route.params.name || '',
      video_url: this.props.route.params.video_url || '',
      screenshot_url: this.props.route.params.screenshot_url || '',
      uuid: this.props.route.params.uuid || '',
      video_type: 'MP4',
      tuyaToken: '',
      password: '',
      uid: '',
      countryCode: '',
      homeId: '',
      username: '',
      qr: '',
    };

    this.props.navigation.setOptions({
      // eslint-disable-next-line react/no-unstable-nested-components
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  static contextType = ScreenSizeContext;

  componentDidMount() { }

  componentWillUnmount() { }

  rightClick() {
    this.save();
  }

  handleSuccess() {
    Toast.show();
  }

  render() {
    return (
      <NavBarView>
        <ScrollView
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="never"
          keyboardDismissMode="on-drag">
          <View
            style={{
              flex: 1,
              marginTop: 20,
            }}>
            <View>
              <View
                style={{
                  backgroundColor: Tme('cardColor'),
                  paddingHorizontal: 16,
                }}>
                <View
                  style={[
                    styles.account_view,
                    { borderColor: Tme('inputBorderColor') },
                  ]}>
                  <TextInput
                    placeholderTextColor={Tme('placeholder')}
                    style={Colors.TextInputStyle()}
                    autoCapitalize="none"
                    autoCorrect={false}
                    underlineColorAndroid="transparent"
                    placeholder={I18n.t('device.camera_name')}
                    value={this.state.name}
                    onChangeText={name => {
                      this.setState({ name });
                    }}
                  />
                </View>
              </View>
              <View
                style={{ marginTop: 16, backgroundColor: Tme('cardColor') }}>
                <View style={{ paddingHorizontal: 16, marginBottom: 10 }}>
                  <View
                    style={[
                      styles.account_view,
                      { borderColor: Tme('inputBorderColor') },
                    ]}>
                    <TextInput
                      placeholderTextColor={Tme('placeholder')}
                      style={Colors.TextInputStyle()}
                      autoCapitalize="none"
                      autoCorrect={false}
                      underlineColorAndroid="transparent"
                      placeholder={I18n.t('device.camera_video')}
                      value={this.state.video_url}
                      onChangeText={video_url => {
                        this.setState({ video_url });
                      }}
                    />
                  </View>
                </View>
              </View>
              <View style={{ paddingHorizontal: 16, marginTop: 16 }}>
                <Text style={{ fontSize: 12, color: Tme('textColor') }}>
                  {I18n.t('device.camera_video_desp')}
                </Text>
              </View>
              <View
                style={{ marginTop: 16, backgroundColor: Tme('cardColor') }}>
                <View style={{ paddingHorizontal: 16, marginBottom: 16 }}>
                  <View
                    style={[
                      styles.account_view,
                      { borderColor: Tme('inputBorderColor') },
                    ]}>
                    <TextInput
                      placeholderTextColor={Tme('placeholder')}
                      style={Colors.TextInputStyle()}
                      autoCapitalize="none"
                      underlineColorAndroid="transparent"
                      placeholder={I18n.t('device.camera_image')}
                      value={this.state.screenshot_url}
                      onChangeText={screenshot_url => {
                        this.setState({ screenshot_url });
                      }}
                    />
                  </View>
                </View>
              </View>
              <View style={{ paddingHorizontal: 16, marginTop: 16 }}>
                <Text style={{ fontSize: 12, color: Tme('textColor') }}>
                  {I18n.t('device.camera_image_desp')}
                </Text>
              </View>
            </View>
          </View>
          <View
            style={{
              flex: 1,
              marginTop: 20,
              marginBottom: 40,
              paddingHorizontal: 16,
            }}>
            <CameraView
              style={{
                height: this.context.winHeight,
                width: this.context.winWidth - 32,
              }}
            />
          </View>
        </ScrollView>
      </NavBarView>

    );
  }

  save() {
    if (this.state.name == '') {
      AlertModal.alert(I18n.t('device.camera_name_error'));
      return;
    }
    showLoading();
    Helper.httpPOST(
      '/partner/ip_cameras',
      {
        ensure: () => {
          hideLoading();
        },
        success: data => {
          this.props.navigation.goBack();
          this.handleSuccess();
          NotificationCenter.dispatchEvent(EVENT_DEVICE);
        },
      },
      { ...this.state },
    );
  }
}

const styles = StyleSheet.create({
  account_view: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 3,
    marginBottom: 10,
    marginTop: 16,
  },
});
export default AddCamera;
