import React, {useState, useEffect, useRef, useContext} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ScrollView,
} from 'react-native';
import I18n from '../I18n';
import {Helper} from '../Helper';
import * as Progress from 'react-native-progress';
import PahoManager from '../PahoManager';
import {Tme, Colors} from '../ThemeStyle';
import IdleTimerManager from 'react-native-idle-timer';
import DeviceControl from '../DeviceControl';
import NavBarView from '../share/NavBarView';
import CardView from '../share/CardView';
import _ from 'lodash';
import AlertModal from '../share/AlertModal';
import {hideLoading, showLoading} from '../../ILoading';
import ScreenSizeContext from '../../WindowResizeContext';
import PubSub from 'pubsub-js';
import {
  ERROR_REFETCH,
  EVENT_GRPC_CONTROLLER_CHANGE,
} from '../types/PubSubEvent';

const AddDevice = ({route, navigation}) => {
  const [progress, setProgress] = useState(0);
  const [indeterminate, setIndeterminate] = useState(true);
  const [timeShow, setTimeShow] = useState(true);
  const [inputValue, setInputValue] = useState('');
  const [inputShow, setInputShow] = useState(false);
  const [textShow, setTextShow] = useState(false);
  const [wait, setWait] = useState(true);
  const [csa, setCsa] = useState('');
  const [dskText, setDskText] = useState('');
  const [countdown, setCountdown] = useState(
    route.params.type === 'zigbee' ? 253 : 60,
  );
  const [countdownShow, setCountdownShow] = useState(true);
  // const [showView, setShowView] = useState(false);

  const downRef = useRef(null);
  const alertdRef = useRef(false);
  const progressTimerRef = useRef(null);
  const progressInterruptedRef = useRef(false);
  const pahoManagerRef = useRef(null);

  const context = useContext(ScreenSizeContext);

  const startCountdown = () => {
    downRef.current = setInterval(() => {
      setCountdown(prevCountdown => {
        if (prevCountdown > 0) {
          return prevCountdown - 1;
        } else {
          clearCountdown();
          AlertModal.alert(I18n.t('global.time_out'), '', [
            {
              text: I18n.t('home.confirm'),
              onPress: () => {
                navigation.goBack();
              },
            },
          ]);
          return 0;
        }
      });
    }, 1000);
  };

  const clearCountdown = () => {
    if (downRef.current) {
      clearInterval(downRef.current);
    }
    downRef.current = null;
  };

  // 启动智能倒计时填充逻辑
  const startProgressTimer = () => {
    if (progressTimerRef.current) {
      clearInterval(progressTimerRef.current);
    }

    progressInterruptedRef.current = false;
    const startProgress = 0.25;
    const targetProgress = 0.75;
    const duration = 30; // 30秒
    const increment = (targetProgress - startProgress) / duration;
    let currentProgress = startProgress;

    progressTimerRef.current = setInterval(() => {
      if (progressInterruptedRef.current) {
        clearProgressTimer();
        return;
      }

      currentProgress += increment;

      // 确保不超过目标进度
      if (currentProgress >= targetProgress) {
        currentProgress = targetProgress;
        clearProgressTimer();
      }

      setProgress(currentProgress);
    }, 1000); // 每秒更新一次
  };

  // 清除智能倒计时定时器
  const clearProgressTimer = () => {
    if (progressTimerRef.current) {
      clearInterval(progressTimerRef.current);
    }
    progressTimerRef.current = null;
  };

  // 中断智能倒计时
  const interruptProgressTimer = () => {
    progressInterruptedRef.current = true;
    clearProgressTimer();
  };

  const doFetchData = () => {
    showLoading();
    Helper.httpGET(
      Helper.urlWithQuery('/partner/controllers/conn_info', {
        sn_id: route.params.sn.id,
      }),
      {
        success: data => {
          console.log('sn conn info', data);
          if (_.isEmpty(data.mqtt)) {
            AlertModal.alert(
              I18n.t('home.add_device_fail'),
              '',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    navigation.goBack();
                  },
                },
              ],
              {cancelable: false},
            );
          } else {
            // setShowView(true);
            pahoManagerRef.current = new PahoManager({
              mqtt: data.mqtt,
              onConnect: onConnect,
              navigation: navigation,
            });
            pahoManagerRef.current.mount();
          }
        },
        ensure: () => {
          hideLoading();
        },
      },
    );
  };

  const onConnect = () => {
    const body = {
      type: route.params.type,
      sn_id: route.params.sn.id,
    };
    if (route.params.type === 'matter_wifi') {
      body.ssid = route.params.ssid;
      body.code = route.params.code;
      body.password = route.params.password;
    }
    if (route.params.type === 'matter_thread') {
      body.code = route.params.code;
    }
    console.log('onConnect', body);
    new DeviceControl(body).addDevice();
  };

  const handleSave = () => {
    showLoading();
    new DeviceControl({
      sn_id: route.params.sn.id,
      value: inputValue,
      dskText: dskText,
    }).setDsk();
  };

  const handleClose = () => {
    if (inputShow) {
      setInputShow(false);
      setTimeShow(true);
    } else {
      navigation.goBack();
    }
  };

  useEffect(() => {
    doFetchData();

    const controllerSubscription = PubSub.subscribe(
      EVENT_GRPC_CONTROLLER_CHANGE,
      (msg, payload) => {
        if (payload.queue === 'controller') {
          switch (payload.data.op_state) {
            case 'csa':
              setTimeShow(false);
              setTextShow(true);
              setCsa(payload.data.csa);
              break;
            case 'dsk':
              clearCountdown();
              setTimeShow(false);
              setWait(true);
              setInputShow(true);
              setDskText(payload.data.dsk);
              break;
            case 'dsk_invalid':
              hideLoading();
              setTimeShow(false);
              setWait(true);
              setInputShow(true);
              setInputValue('');
              AlertModal.alert(
                I18n.t('home.error') + ' ' + I18n.t('global.dsk_error'),
                '',
              );
              break;
            case 'starting':
              if (!downRef.current) {
                startCountdown();
                setTimeShow(true);
                setCountdownShow(true);
                setWait(false);
                setIndeterminate(false);
              }
              break;
            case 'in_progress':
              clearCountdown();
              hideLoading();
              setProgress(0.25);
              setWait(true);
              setCountdownShow(false);
              setTimeShow(true);
              setInputShow(false);
              setTextShow(false);

              // 仅对Matter WiFi和Matter Thread设备启动智能倒计时
              const deviceType = route.params.type;
              if (
                deviceType === 'matter_wifi' ||
                deviceType === 'matter_thread'
              ) {
                startProgressTimer();
              }
              break;
            case 'in_progress_1':
              clearCountdown();
              interruptProgressTimer(); // 中断智能倒计时
              hideLoading();

              // 根据设备类型设置不同的进度值
              const deviceType1 = route.params.type;
              const progress1 =
                deviceType1 === 'matter_wifi' || deviceType1 === 'matter_thread'
                  ? 0.75
                  : 0.5;

              setProgress(progress1);
              setWait(true);
              setTimeShow(true);
              setCountdownShow(false);
              setInputShow(false);
              setTextShow(false);
              break;
            case 'in_progress_2':
              clearCountdown();
              hideLoading();

              // 根据设备类型设置不同的进度值
              const deviceType2 = route.params.type;
              const progress2 =
                deviceType2 === 'matter_wifi' || deviceType2 === 'matter_thread'
                  ? 0.85
                  : 0.75;

              setProgress(progress2);
              setWait(true);
              setTimeShow(true);
              setCountdownShow(false);
              setInputShow(false);
              setTextShow(false);
              break;
            case 'device_added':
              hideLoading();
              clearCountdown();
              setProgress(1);
              setWait(true);
              setTimeShow(true);
              setCountdownShow(false);
              setInputShow(false);
              setTextShow(false);
              navigation.push('AddSuccess', {
                uuid: payload.data.devices[0].index,
                sn_id: route.params.sn.id,
                type: 'new',
              });
              break;
            case 'failed':
              hideLoading();
              clearCountdown();
              setIndeterminate(true);
              setCountdownShow(false);
              setProgress(0);
              if (!alertdRef.current) {
                alertdRef.current = true;
                AlertModal.alert(
                  I18n.t('home.add_device_fail'),
                  '',
                  [
                    {
                      text: 'OK',
                      onPress: () => {
                        navigation.goBack();
                      },
                    },
                  ],
                  {cancelable: false},
                );
              }
              break;
            default:
              break;
          }
        }
      },
    );

    const refetchSubscription = PubSub.subscribe(ERROR_REFETCH, () => {
      doFetchData();
    });

    IdleTimerManager.setIdleTimerDisabled(true);

    return () => {
      if (pahoManagerRef.current) {
        pahoManagerRef.current.unmount();
      }
      clearCountdown();
      clearProgressTimer(); // 清理智能倒计时定时器
      new DeviceControl({
        type: route.params.type,
        sn_id: route.params.sn.id,
      }).CmdStop();
      PubSub.unsubscribe(controllerSubscription);
      PubSub.unsubscribe(refetchSubscription);
      IdleTimerManager.setIdleTimerDisabled(false);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <NavBarView>
      <ScrollView style={{flex: 1, backgroundColor: Tme('bgColor')}}>
        <View
          style={{
            flex: 1,
            alignItems: 'center',
            marginTop: 20,
            padding: 20,
          }}>
          <CardView
            styles={{
              width: context.winWidth - 40,
              height: 580,
              padding: 20,
              alignItems: 'center',
              borderRadius: 8,
            }}>
            <View style={{marginTop: 79, marginBottom: 20}}>
              <Text
                style={{
                  fontSize: 22,
                  fontWeight: '600',
                  textAlign: 'center',
                  color: Tme('cardTextColor'),
                }}>
                {I18n.t('home.add_device')}
              </Text>
              {wait ? (
                <View style={{padding: 16}}>
                  <Text
                    style={{
                      fontSize: 14,
                      fontWeight: '600',
                      textAlign: 'center',
                      color: Tme('cardTextColor'),
                    }}>
                    {I18n.t('global.add_device_wait')}
                  </Text>
                </View>
              ) : (
                <View style={{padding: 16}}>
                  <Text
                    style={{
                      fontSize: 14,
                      fontWeight: '600',
                      textAlign: 'center',
                      color: Tme('cardTextColor'),
                    }}>
                    {I18n.t('global.add_device_s2_title')}
                  </Text>
                </View>
              )}
              {inputShow ? (
                <View style={{padding: 16}}>
                  <Text
                    style={{
                      fontSize: 14,
                      fontWeight: '600',
                      textAlign: 'center',
                      color: Tme('cardTextColor'),
                    }}>
                    {I18n.t('global.add_device_s2_desp')}
                  </Text>
                </View>
              ) : null}
            </View>
            <View>
              {timeShow ? (
                countdownShow ? (
                  <Progress.Circle
                    size={140}
                    progress={progress}
                    indeterminate={indeterminate}
                    showsText={true}
                    color={Colors.MainColor}
                    formatText={() => {
                      return `${countdown}`;
                    }}
                  />
                ) : (
                  <Progress.Circle
                    size={140}
                    color={Colors.MainColor}
                    progress={progress}
                    indeterminate={indeterminate}
                    showsText={true}
                    formatText={() => {
                      return `${parseInt(progress * 100, 10)}%`;
                    }}
                  />
                )
              ) : null}
              <View>
                {inputShow ? (
                  <View>
                    <View
                      style={[
                        {
                          borderColor: Tme('inputBorderColor'),
                          borderWidth: 1,
                          borderRadius: 16,
                          height: 48,
                        },
                      ]}>
                      <TextInput
                        autoCapitalize="none"
                        placeholder="DSK"
                        keyboardType="number-pad"
                        underlineColorAndroid="transparent"
                        autoCorrect={false}
                        value={inputValue}
                        maxLength={16}
                        placeholderTextColor={Tme('placeholder')}
                        onChangeText={setInputValue}
                        style={Colors.TextInputStyle()}
                      />
                    </View>
                    <View
                      style={{
                        marginTop: 16,
                        flexDirection: 'row',
                        flexWrap: 'wrap',
                      }}>
                      <Text
                        style={{
                          marginTop: 16,
                          color: Tme('cardTextColor'),
                        }}>
                        {dskText}
                      </Text>
                    </View>

                    <View style={{marginTop: 16}}>
                      <TouchableOpacity
                        activeOpacity={0.8}
                        style={{
                          height: 40,
                          borderRadius: 20,
                          backgroundColor: Colors.MainColor,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onPress={handleSave}>
                        <Text
                          style={{
                            fontSize: 14,
                            fontWeight: '600',
                            color: '#ffffff',
                            width: 120,
                            textAlign: 'center',
                          }}>
                          {I18n.t('home.submit')}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                ) : null}
                {textShow ? (
                  <View
                    style={{
                      paddingHorizontal: 10,
                      paddingVertical: 5,
                      borderRadius: 6,
                      backgroundColor: Tme('cardColor'),
                    }}>
                    <Text style={{color: Tme('cardTextColor')}}>{csa}</Text>
                  </View>
                ) : null}
              </View>
            </View>
            {inputShow ? null : (
              <View
                style={{
                  marginTop: 40,
                  alignItems: 'center',
                  marginBottom: 30,
                }}>
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={handleClose}
                  style={{
                    height: 40,
                    borderRadius: 20,
                    backgroundColor: Colors.MainColor,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text
                    style={{
                      fontSize: 14,
                      fontWeight: '600',
                      color: '#ffffff',
                      width: 120,
                      textAlign: 'center',
                    }}>
                    {I18n.t('home.cancel')}
                  </Text>
                </TouchableOpacity>
              </View>
            )}
            <View style={{marginTop: 20}}>
              <Text
                style={{
                  fontSize: 11,
                  lineHeight: 16,
                  color: Tme('cardTextColor'),
                }}>
                {I18n.t('global.add_device_special_desp')}
              </Text>
            </View>
          </CardView>
        </View>
      </ScrollView>
    </NavBarView>
  );
};

export default AddDevice;
