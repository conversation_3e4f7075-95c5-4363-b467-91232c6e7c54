export const webrtc = `<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WebRTC</title>
  <style>
    html,
    body {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      background: #000;
    }

    .divhead {
      width: 100%;
      height: 100%;
      position: relative;
    }

    #remoteVideo {
      width: 100%;
      height: 100%;
      object-fit: cover;
      background: #000;
    }

    .arc {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 50px;
      height: 50px;
      border: 3px solid rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      border-top-color: transparent;
      animation: spin 1s linear infinite;
      z-index: 100;
      background-color: rgba(0, 0, 0, 0.5);
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    }

    @keyframes spin {
      to {
        transform: translate(-50%, -50%) rotate(360deg);
      }
    }

    #canvas {
      position: absolute;
      top: 0;
      left: 0;
      display: none;
    }

    /* 调试面板样式 */
    .debug-panel {
      position: fixed;
      top: 10px;
      right: 10px;
      background: rgba(0, 0, 0, 0.7);
      padding: 10px;
      border-radius: 5px;
      z-index: 1000;
    }

    .debug-panel button {
      display: block;
      margin: 5px 0;
      padding: 8px 15px;
      background: #4CAF50;
      color: white;
      border: none;
      border-radius: 3px;
      cursor: pointer;
    }

    .debug-panel input {
      width: 100%;
      margin: 5px 0;
      padding: 5px;
    }

    .debug-log {
      position: fixed;
      bottom: 10px;
      left: 10px;
      right: 10px;
      max-height: 150px;
      background: rgba(0, 0, 0, 0.8);
      color: #fff;
      font-family: monospace;
      padding: 10px;
      overflow-y: auto;
      border-radius: 5px;
      display: none;
    }
  </style>
</head>

<body>
  <div class="divhead" id="videos">
    <video id="remoteVideo" style="object-fit: cover;" muted preload="auto" autoplay playsinline x-webkit-airplay="true"
      webkit-playsinline="true" x5-video-player-type="h5" x5-video-player-fullscreen="true"
      x5-video-orientation="portraint">
    </video>

    <div class="arc" id="loading" style="display: none;"></div>
    <div id="errorMessage" style="display: none; position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); 
      background: rgba(0,0,0,0.7); color: white; padding: 10px; border-radius: 5px;"></div>
    <canvas id="canvas"></canvas>
    <div id="screenshotIndicator" style="display: none; position: absolute; top: 50%; left: 50%; 
      transform: translate(-50%, -50%); background: rgba(0,0,0,0.7); color: white; 
      padding: 10px; border-radius: 5px;">正在截图...</div>
  </div>

  <script>
    // WebRTC 控制类
    class WebRTCController {
      constructor() {
        // 简化平台相关变量，只保留必要的平台判断
        this.platform = {
          isAndroid: false,
          isIos: false
        };
        
        // 其他初始化变量保持不变
        this.IsLocalDataChannel = true;
        this.websock = null;
        this.showVideo = true;
        this.connected = false;
        this.connecting = false;
        this.videoplaying = false;
        this.socketTask = false;
        this.startRecive = false;
        this.StartCalled = false;
        this.CheckedgetUserMedia = false;
        this.IsWebSocketCreateed = false;
        this.IsWebSocketConnecting = false;
        this.IsWebSocketOpened = false;
        this.IsLocalAudioTrack = false;
        this.IsLocalVideoTrack = false;
        this.IsSystemAudioDeviceOK = false;
        this.IsFailCreate = false;
        this.recorded = false;
        this.speak = false;
        this.mute = true;
        this.myPeerConnection = null;
        this.myDataChannel = null;
        this.stream = null;
        this.remoteMediaStream = null;
        this.mediaRecorder = null;
        this.remoteVideo = null;
        this.heartCheck = null;
        this.messagecallback = null;
        this.RTCPeerConnectionCreated = false;
        this.peerid = '';
        this.sessionId = '';
        this.wsurl = '';
        this.url = '';
        this.mymsg = false;
        this.roomId = '';
        this.IsReconnect = false;
        this.timeout = 2000;
        this.timedelay = 0;
        this.serverTimeoutObj = null;
        this.IceCandidate = [];
        this.connectmode = "live";
        this.connectsource = "MainStream";
        this.configuration = {
          "iceServers": [{ 'urls': ['stun:webrtc.qq-kan.com:3478?transport=udp'] }]
        };

        // 初始化
        this.init();
        
        // 绑定方法到实例
        this.initConnection = this.initConnection.bind(this);
        
        this.websocketonmessage = this.websocketonmessage.bind(this);
        this.websocketonopen = this.websocketonopen.bind(this);
        this.websocketonerror = this.websocketonerror.bind(this);
        this.websocketclose = this.websocketclose.bind(this);
        
        // 添加日志功能
        this.log = (...args) => {
          const debugLog = document.getElementById('debugLog');
          if (debugLog) {
            const message = args.map(arg => 
              typeof arg === 'object' ? JSON.stringify(arg) : arg
            ).join(' ');
            const time = new Date().toLocaleTimeString();
          }
        };

        // 重写 console.log
        this.oldConsoleLog = console.log;
        console.log = (...args) => {
          this.log(...args);
          this.oldConsoleLog.apply(console, args);
        };

        // 统一的消息处理
        this.messageHandler = this.messageHandler.bind(this);
        window.addEventListener('message', this.messageHandler);
        document.addEventListener('message', this.messageHandler);
      }

      messageHandler(event) {
        if (typeof event.data === 'string') {
          try {
            const { type, data } = JSON.parse(event.data);
            console.log('收到消息:', type, data);

            switch (type) {
              case 'init':
                this.handleInit(data);
                break;
              case 'ios_picture':
                this.scanpicture();
                break;
              case 'ios_sound':
                this.setlocalaudio();
                break;
              case 'ios_speak':
                this.setlocalspeak();
                break;
              default:
                console.log('未处理的消息类型:', type);
            }
          } catch (error) {
            console.error('处理消息时出错:', error);
          }
        }
      }

      handleInit(data) {
        console.log('处理初始化数据:', data);
        const { serverHost, deviceId, platform } = data;

        // 设置平台信息，只保留 Android 和 iOS 的判断
        if (platform) {
          this.platform = {
            isAndroid: platform === 'android',
            isIos: platform === 'ios'
          };
        }

        // 设置调试面板值
        if (document.getElementById('serverHost')) {
          document.getElementById('serverHost').value = serverHost || '';
        }
        if (document.getElementById('deviceId')) {
          document.getElementById('deviceId').value = deviceId || '';
        }

        if (serverHost && deviceId) {
          console.log('自动初始化WebRTC连接...');
          this.peerid = deviceId;
          this.sessionId = this.newGuid();
          this.meid = this.newGuid();
          
          this.showLoading('正在连接设备...');
          
          setTimeout(() => {
            this.initWebSocket(serverHost);
          }, 500);
        } else {
          this.showError('初始化参数不完整');
        }
      }

      init() {
        // 检测平台
        this.detectPlatform();
        // 初始化UI元素
        this.initElements();
        // 添加事件监听
        this.addEventListeners();
        // 处理网络状态
        this.handleNetworkStatusChange();
        // 等待 React Native 传递参数
        this.waitForReactNativeParams();
        console.log('WebRTC初始化完成');
        // 配置音频输出
        if (this.remoteVideo) {
          // 禁用静音
          this.remoteVideo.muted = false;
          // 启用音量
          this.remoteVideo.volume = 1.0;

          // 强制使用扬声器播放
          if (this.platform.isIos) {
            // iOS 需要用户交互才能播放音频
            document.addEventListener('touchstart', () => {
              this.switchToSpeaker();
            }, { once: true });
          } else {
            this.switchToSpeaker();
          }
        }
      }

      detectPlatform() {
        // 移除浏览器检测，由 React Native 传递平台信息
        console.log('等待 React Native 传递平台信息...');
      }

      initElements() {
        this.remoteVideo = document.getElementById("remoteVideo");
        if (this.remoteVideo) {
          this.remoteVideo.muted = true;
          this.initVideoEvents();
        }
        this.adjustVideoSize();
      }

      showLoading() {
        const loading = document.getElementById('loading');
        if (loading) {
          loading.style.display = "block";
        }
      }

      hideLoading() {
        const loading = document.getElementById('loading');
        if (loading) {
          loading.style.display = "none";
        }
      }

      showError(message) {
        if (window.ReactNativeWebView) {
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'error',
            data: message
          }));
        }
      }
    

      initConnection() {
        const serverHost = document.getElementById('serverHost').value;
        const deviceId = document.getElementById('deviceId').value;

        if (!serverHost || !deviceId) {
          this.showError('identifier');
          return;
        }

        // 立即显示 loading
        this.showLoading('正在连接设备...');

        this.peerid = deviceId;
        this.sessionId = this.newGuid();
        this.meid = this.newGuid();
        this.initWebSocket(serverHost);
      }

      // 其他方法保持不变，直接从 React 组件复制过来
      // ...（这里包含了所有其他的方法，如 WebRTC 连接、信令处理等）

      initVideoEvents() {
        this.remoteVideo.addEventListener('playing', () => {
          console.log("playing");
          this.hideLoading();
        });
        this.remoteVideo.addEventListener('play', this.OnVideoPlay.bind(this));
        this.remoteVideo.addEventListener('loadedmetadata', this.OnVideoLoadedMetaData.bind(this));
        this.remoteVideo.addEventListener('canplay', this.OnVideoCanPlay.bind(this));
        this.remoteVideo.addEventListener('error', this.OnVideoError.bind(this));
        this.remoteVideo.addEventListener('loadeddata', this.OnVideoLoadedData.bind(this));

        if (this.platform.isWeixin && !this.platform.isIos) {
          console.log("Your browser Weixin no ios");
        } else if (this.platform.isWeixin && this.platform.isIos) {
          document.addEventListener("WeixinJSBridgeReady", () => {
            if (!this.videoplaying) {
              this.videoplaying = true;
              const playPromise = this.remoteVideo.play();
              if (playPromise) {
                playPromise.then(() => {
                  this.videoplaying = true;
                  console.log("browser Weixin <video> play.");
                }).catch((e) => {
                  this.videoplaying = false;
                  console.log("<video> play error ", e.message);
                });
              }
            }
          }, false);
        }
      }

      OnVideoPlay() {
        console.log("<video> event OnVideoPlay------");
      }

      OnVideoLoadedMetaData() {
        console.log("<video> event OnVideoLoadedMetaData----------------");
        this.videoplay();
      }

      OnVideoCanPlay() {
        console.log("<video>  OnVideoCanPlay-----videoWidth = " + this.remoteVideo.videoWidth);
        console.log("<video>  OnVideoCanPlay-----videoHeight = " + this.remoteVideo.videoHeight);
        console.log("<documentElement>  OnVideoCanPlay------clientWidth = " + document.documentElement.clientWidth);
        console.log("<documentElement>  OnVideoCanPlay------clientHeight = " + document.documentElement.clientHeight);

        const loading = document.getElementById('loading');
        loading.style.display = "none";

        const canvas = document.getElementById('canvas');
        canvas.style.width = this.remoteVideo.videoWidth;
        canvas.style.height = this.remoteVideo.videoHeight;

        const Videos = document.getElementById("videos");

        if (window.ReactNativeWebView) {
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'videoStyle',
            data: [Videos.style.width, Videos.style.height]
          }));
        }

        if (this.remoteVideo.muted) {
          console.log("<video>----------------muted = true");
        } else {
          console.log("<video>----------------muted = false");
        }
      }

      OnVideoError() {
        console.log("<video> event OnVideoError----------------");
        this.showError('vidoeo error');
      }

      OnVideoLoadedData() {
        console.log("<video> event OnVideoLoadedData----------------");
      }

      videoplay() {
        if (!this.videoplaying) {
          this.videoplaying = true;
          const playPromise = this.remoteVideo.play();
          if (playPromise) {
            playPromise.then(() => {
              this.videoplaying = true;
              console.log("<video> play");
            }).catch((e) => {
              this.videoplaying = false;
              console.log("<video> play error", e.message);
              this.showError('视频播放失败：' + e.message);
            });
          }
        }
      }

      changeRemoteVideo(stream) {
        // 获取远程流后的处理
        console.log('远程视频流已更新');
      }

      addEventListeners() {
        // 使用箭头函数来保持正确的 this 上下文
        window.addEventListener('resize', () => this.adjustVideoSize());

        // 禁止页面滚动
        document.body.style.overflow = 'hidden';
        document.addEventListener('touchmove', (e) => {
          e.preventDefault();
        }, { passive: false });
      }

      handleNetworkStatusChange() {
        window.addEventListener('online', () => {
          if (!this.RTCPeerConnectionCreated) {
            this.showLoading('网络已恢复，正在重新连接...');
            this.IsReconnect = true;
            this.sessionId = this.newGuid();
            this.initWebSocket(this.wsurl);
          }
        });

        window.addEventListener('offline', () => {
          
        });
      }

      initWebSocket(host) {
        if (this.IsWebSocketConnecting) {
          console.log('WebSocket 正在连接中，请稍后...');
          return;
        }

        // 保存原始host用于重连
        this.wsurl = host;

        // 处理协议前缀
        let wsuri = '';

        // 检查host是否已包含协议前缀
        if (host.startsWith('ws://') || host.startsWith('wss://')) {
          wsuri = host + this.meid;
        } else {
          // 根据页面协议或cloud标志判断使用wss还是ws
          const isSecure = document.location.protocol === 'https:' ||
            (typeof this.props !== 'undefined' && this.props.from === 'cloud');
          const protocol = isSecure ? 'wss://' : 'ws://';

          // 确保host不以/结尾，以避免双斜杠
          const formattedHost = host.endsWith('/') ? host.slice(0, -1) : host;

          // 构建完整的WebSocket URL
          wsuri = protocol + formattedHost + '/wswebclient/' + this.meid;
        }

        console.log('正在初始化WebSocket连接:', wsuri);

        if (this.websock) {
          console.log('当前存在 WebSocket 连接，正在关闭...');
          this.websock.close();
          this.websock = null;
          console.log('WebSocket 连接已关闭');
        }

        try {
          console.log('尝试创建 WebSocket 连接...');
          this.websock = new WebSocket(wsuri);
          console.log('WebSocket 对象已创建');
          this.websock.onmessage = this.websocketonmessage.bind(this);
          this.websock.onopen = this.websocketonopen.bind(this);
          this.websock.onerror = this.websocketonerror.bind(this);
          this.websock.onclose = this.websocketclose.bind(this);

          // 设置连接超时
          this.websocketTimeout = setTimeout(() => {
            if (!this.IsWebSocketOpened) {
              console.log('WebSocket连接超时，尝试重连');
              this.IsWebSocketConnecting = false;
              this.initWebSocket(this.wsurl);
            }
          }, 10000);

          this.IsWebSocketConnecting = true;
          console.log('WebSocket 连接状态设置为 Connecting');
        } catch (error) {
          
          this.IsWebSocketConnecting = false;
          

          // 出错后延迟重试
          setTimeout(() => {
            this.initWebSocket(this.wsurl);
          }, 3000);
        }
      }

      // WebSocket 事件处理
      websocketonopen() {
        // 清除连接超时定时器
        if (this.websocketTimeout) {
          clearTimeout(this.websocketTimeout);
          this.websocketTimeout = null;
        }

        var that = this;
        this.IsWebSocketOpened = true;
        console.log('WebSocket onopen 事件触发，readyState:', this.websock.readyState);
        if (this.websock != null && this.websock.readyState === 1) {
          this.StartCalled = false;
          this.Connect();
        } else {
          setTimeout(() => {
            if (that.websock != null && that.websock.readyState === 1) {
              that.StartCalled = false;
              that.Connect();
            }
          }, 100);
        }
        this.showLoading('正在建立连接...');
        console.log('WebSocket连接已建立');
      }

      websocketonerror(e) {
        this.IsWebSocketOpened = false;
        this.IsWebSocketConnecting = false;
        this.websock = null;
        console.log('连接错误', e);
      }

      websocketonmessage(e) {
        const data = JSON.parse(e.data);
        console.log("Got message", data);
        switch (data.eventName) {
          case "_create": this.handleCreate(data.data); break;
          case "_call": this.handleCall(data.data); break;
          case "_offer": this.handleOffer(data.data); break;
          case "_answer": this.handleAnswer(data.data); break;
          case "_ice_candidate": this.handleCandidate(data.data); break;
          case "_session_disconnected": this.handleDisconnect(data.data); break;
          case "_post_message": this.handlePostMessage(data.data); break;
          case "_connectinfo": this.handleConnectInfo(data.data); break;
          case "_session_failed": this.handleSessionFailed(data.data); break;
          case "_ping": break;
          default: console.log("Got default message", data);
        }
        console.log("收到消息:", data);
      }

      websocketclose(e) {
        this.IsWebSocketOpened = false;
        this.IsWebSocketConnecting = false;
        this.websock = null;
        console.log('连接已断开', e);
        
        // 检查是否是非正常关闭（code 1006）
        const isAbnormalClose = e && e.code === 1006;
        
        console.log('WebSocket连接已关闭:', e);
        
        // 添加自动重连逻辑
        if (this.wsurl && !this.reconnectTimeout) {
          // 根据关闭类型调整重连时间
          const reconnectDelay = isAbnormalClose ? 1000 : 3000;
          
          console.log("将在" + reconnectDelay  +"ms 后尝试重新连接...");
          
          this.reconnectTimeout = setTimeout(() => {
            this.reconnectTimeout = null;
            if (this.RTCPeerConnectionCreated) {
              this.handleLeave();
            }
            this.IsReconnect = true;
            this.sessionId = this.newGuid();
            this.initWebSocket(this.wsurl);
          }, reconnectDelay);
        }
      }

      // WebRTC 连接管理
      async handleOffer(data) {
        this.IsReconnect = false;
        console.log("收到 Offer");

        if (!this.RTCPeerConnectionCreated) {
          this.initPeerConnection();
        }

        if (!this.RTCPeerConnectionCreated) {
          console.log("创建 PeerConnection 失败");
          this.sendDisconnect();
          this.IsReconnect = false;
          this.IsFailCreate = true;
          return;
        }

        try {
          const nativeRTCSessionDescription = (window.mozRTCSessionDescription || window.RTCSessionDescription);
          await this.myPeerConnection.setRemoteDescription(
            new nativeRTCSessionDescription({ type: 'offer', sdp: data.sdp })
          );

          const answer = await this.myPeerConnection.createAnswer();
          await this.myPeerConnection.setLocalDescription(answer);

          this.sendToServer({
            "eventName": "__answer",
            "data": {
              "sessionId": this.sessionId,
              "sessionType": "IE",
              "messageId": this.newGuid(),
              "from": this.meid,
              "to": this.peerid,
              "type": answer.type,
              "sdp": answer.sdp
            }
          });
        } catch (error) {
          console.error("处理 Offer 时出错:", error);
          this.sendDisconnect();
          this.IsReconnect = true;
          this.IsFailCreate = true;
        }
      }

      // 视频相关方法
      async scanpicture() {
        try {
          document.getElementById('screenshotIndicator').style.display = "block";
          const canvas = document.getElementById('canvas');
          const video = document.getElementById('remoteVideo');

          // 获取视频的实际分辨率
          const videoWidth = video.videoWidth;
          const videoHeight = video.videoHeight;

          // 设置canvas尺寸为视频的实际分辨率
          canvas.width = videoWidth;
          canvas.height = videoHeight;

          const context = canvas.getContext('2d');
          context.clearRect(0, 0, canvas.width, canvas.height);
          context.drawImage(video, 0, 0, videoWidth, videoHeight);

          // 使用高质量设置生成PNG图像
          const data = canvas.toDataURL('image/png', 1.0);
          console.log('截图数据:', data);
          // 通知截图结果
          if (window.ReactNativeWebView) {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'picture',
              data: data
            }));
          }
        } catch (error) {
          console.error('截图失败:', error);
          this.showError('截图失败');
        } finally {
          document.getElementById('screenshotIndicator').style.display = "none";
        }
      }

      // 音频控制方法
      async setlocalspeak() {
        console.log('切换对讲状态');

        if (!this.stream) {
          try {
            this.showLoading('正在申请音频权限...');
            await new Promise((resolve, reject) => {
              const audioConstraints = this.platform.isIos ?
                {
                  echoCancellation: { exact: true },
                  noiseSuppression: { exact: true },
                  autoGainControl: { exact: true },
                  googEchoCancellation: true,
                  googNoiseSuppression: true,
                  googAutoGainControl: true
                } :
                {
                  echoCancellation: true,
                  noiseSuppression: true,
                  autoGainControl: true
                };

              this.NewgetUserMedia({
                video: false,
                audio: audioConstraints
              }, async (stream) => {
                this.stream = stream;
                this.IsLocalAudioTrack = true;
                this.speak = true;

                try {
                  this.showLoading('正在建立音频连接...');
                  if (this.RTCPeerConnectionCreated) {
                    await this.handleLeave();
                    await this.sendDisconnect();
                  }

                  this.StartCalled = false;
                  this.RTCPeerConnectionCreated = false;

                  if (this.IsWebSocketCreateed) {
                    this.sessionId = this.newGuid();
                    await this.Call();
                  }

                  this.hideLoading();
                  resolve();
                } catch (error) {
                  this.showError('建立音频连接失败，请重试');
                  reject(error);
                }
              }, (error) => {
                let errorMsg = '获取音频权限失败';
                if (error.name === 'NotAllowedError') {
                  errorMsg = '请允许使用麦克风权限';
                } else if (error.name === 'NotFoundError') {
                  errorMsg = '未找到麦克风设备';
                }
                this.showError(errorMsg);
                this.speak = false;
                reject(error);
              });
            });
          } catch (error) {
            console.error('处理音频权限时出错:', error);
            this.speak = false;
          }
        } else {
          this.speak = !this.speak;
          if (this.myPeerConnection) {
            const senders = this.myPeerConnection.getSenders();
            const audioSenders = senders.filter(sender => sender.track && sender.track.kind === 'audio');

            if (audioSenders.length === 0 && this.speak) {
              this.showLoading('正在重新建立音频连接...');
              try {
                await this.handleNewStream(this.stream);
                this.hideLoading();
              } catch (error) {
                this.showError('重新建立音频连接失败，请重试');
                this.speak = false;
              }
            } else {
              audioSenders.forEach(sender => {
                if (sender.track) {
                  sender.track.enabled = this.speak;
                }
              });
            }
          }
        }

        if (window.ReactNativeWebView) {
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'speakStatus',
            data: this.speak
          }));
        }
      }

      setlocalaudio() {
        console.log('切换音频状态');
        this.mute = !this.mute;

        // 修改 remoteVideo 的静音状态
        if (this.remoteVideo) {
          this.remoteVideo.muted = this.mute;
          console.log('视频元素静音状态:', this.mute);
        }

        // 控制远程音频轨道
        if (this.myPeerConnection) {
          this.myPeerConnection.getReceivers().forEach(receiver => {
            if (receiver.track && receiver.track.kind === 'audio') {
              receiver.track.enabled = !this.mute; // 这里取反，因为 mute=true 表示静音
              console.log('远程音频轨道状态:', !this.mute);
            }
          });
        }

        // 通知状态变化
        if (window.ReactNativeWebView) {
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'audioStatus',
            data: !this.mute
          }));
        }
      }

      // 辅助方法
      newGuid() {
        const s4 = () => {
          return (65536 * (1 + Math.random()) | 0).toString(16).substring(1);
        }
        return (s4() + s4() + "-" + s4() + "-4" + s4().substr(0, 3) + "-" + s4() + "-" + s4() + s4() + s4()).toUpperCase();
      }

      adjustVideoSize() {
        const videos = document.getElementById('videos');
        const loading = document.getElementById('loading');
        const clientWidth = document.documentElement.clientWidth;
        const clientHeight = document.documentElement.clientHeight;
        const video = document.getElementById('remoteVideo');

        // 获取视频实际宽高比
        let aspectRatio = 16/9; // 默认宽高比
        if (video && video.videoWidth && video.videoHeight) {
          aspectRatio = video.videoWidth / video.videoHeight;
        }

        let width = clientWidth;
        let height = Math.floor(width / aspectRatio);

        // 如果计算的高度超出屏幕，则以高度为基准重新计算
        if (height > clientHeight) {
          height = clientHeight;
          width = Math.floor(height * aspectRatio);
        }

        // 设置视频容器尺寸
        videos.style.width = width + 'px';
        videos.style.height = height + 'px';

        // 居中显示
        videos.style.position = 'absolute';
        videos.style.left = '50%';
        videos.style.top = '50%';
        videos.style.transform = 'translate(-50%, -50%)';

        // 更新loading位置
        loading.style.left = '50%';
        loading.style.top = '50%';
        loading.style.transform = 'translate(-50%, -50%)';

        console.log('调整视频尺寸:', width, 'x', height);
      }

      Connect() {
        console.log('发起连接请求');
        this.sendToServer({
          "eventName": "__connectto",
          "data": {
            "sessionId": this.sessionId,
            "sessionType": "IE",
            "messageId": this.newGuid(),
            "from": this.meid,
            "to": this.peerid
          }
        });
      }

      sendToServer(message) {
        console.log('发送消息到服务器:', message);
        this.websocketsend(JSON.stringify(message));
      }

      websocketsend(data) {
        if (this.websock != null && this.websock.readyState === 1) {
          this.websock.send(data);
        }
      }

      Call() {
        console.log("开始呼叫");
        this.StartCalled = true;

        var audioenable = this.IsLocalAudioTrack ? "sendrecv" : "recvonly";
        var videoenable = this.IsLocalVideoTrack ? "sendrecv" : "recvonly";
        var datachannelenable = this.IsLocalDataChannel ? "true" : "false";

        console.log("呼叫配置 - 音频:", audioenable, "视频:", videoenable, "数据通道:", datachannelenable);
        
        // 避免ICE服务器配置被双重JSON序列化
        let iceServersConfig;
        if (typeof this.configuration === 'string') {
          // 如果已经是字符串，直接使用
          iceServersConfig = this.configuration;
        } else {
          // 如果是对象，转换为字符串
          iceServersConfig = JSON.stringify(this.configuration);
        }
        
        // 检查websock状态
        if (!this.websock || this.websock.readyState !== 1) {
          console.log("WebSocket未连接，尝试重新连接");
          if (this.wsurl) {
            this.initWebSocket(this.wsurl);
            // 延迟发送呼叫请求，确保WebSocket已连接
            setTimeout(() => {
              if (this.websock && this.websock.readyState === 1) {
                this.sendCallRequest(audioenable, videoenable, datachannelenable, iceServersConfig);
              } else {
                console.error("WebSocket重连失败，无法发送呼叫请求");
                this.showError("连接服务器失败，请稍后重试");
              }
            }, 1000);
            return;
          } else {
            console.error("无法重新连接，wsurl未定义");
            this.showError("连接信息不完整，请重新初始化");
            return;
          }
        }
        
        this.sendCallRequest(audioenable, videoenable, datachannelenable, iceServersConfig);
      }
      
      sendCallRequest(audioenable, videoenable, datachannelenable, iceServersConfig) {
        this.sendToServer({
          "eventName": "__call",
          "data": {
            "sessionId": this.sessionId,
            "sessionType": "IE",
            "messageId": this.newGuid(),
            "from": this.meid,
            "to": this.peerid,
            "mode": this.connectmode,
            "source": this.connectsource,
            "datachannel": datachannelenable,
            "audio": audioenable,
            "video": videoenable,
            "user": "admin",
            "pwd": "123456",
            "iceservers": iceServersConfig
          }
        });
      }
      handleCreate(data) {
        if (data.state === "online" || data.state === "sleep") {
          this.IsWebSocketCreateed = true;
          if (data.iceServers) {
            if (data.iceServers.constructor === Object) {
              console.log("_create iceServers ---object----", JSON.stringify(data.iceServers));
              this.configuration = JSON.parse(JSON.stringify(data.iceServers));
            } else {
              console.log("_create iceServers ---string----:", data.iceServers);
              this.configuration = JSON.parse(data.iceServers);
            }
          }
          if (!this.StartCalled) {
            console.log("handleCreate start call");
            this.Call();
          }
        } else {
          if (window.ReactNativeWebView) {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'offline',
              data: data.from
            }));
          }
          console.log("_create offline", data.from);
        }
      }

      handlePostMessage(data) {
        console.log("handlePostMessage recv", data);
        if (this.messagecallback) {
          this.messagecallback(data.message);
          this.messagecallback = null;
        }
      }

      handleDisconnect(data) {
        if (data.sessionId === this.sessionId) {
          console.log("handleDisconnect", JSON.stringify(data));
          if (this.RTCPeerConnectionCreated) {
            this.handleLeave();
          }
        }
      }

      handleSessionFailed(data) {
        console.log("handleSessionFailed", data);
        if (data.sessionId === this.sessionId) {
          if (this.RTCPeerConnectionCreated) {
            this.handleLeave();
            this.sendDisconnect();
          }
          this.sessionId = this.newGuid();
        }
      }

      sendDisconnect() {
        this.sendToServer({
          "eventName": "__disconnected",
          "data": {
            "sessionId": this.sessionId,
            "sessionType": "IE",
            "messageId": this.newGuid(),
            "from": this.meid,
            "to": this.peerid
          }
        });
      }

      handleConnectInfo(data) {
        console.log("Got ConnectInfo Message:", data.message);
      }

      handleCandidate(data) {
        const obj = JSON.parse(data.candidate);
        if (this.myPeerConnection && this.RTCPeerConnectionCreated) {
          console.log("candidate:", obj.candidate);
          const candidate = new RTCIceCandidate({
            sdpMLineIndex: obj.sdpMLineIndex,
            candidate: obj.candidate
          });
          this.myPeerConnection.addIceCandidate(candidate);
        } else {
          this.IceCandidate.push(obj);
        }
      }

      initPeerConnection() {
        try {
          console.log('开始创建PeerConnection...');
          
          // 检查配置对象的格式
          if (typeof this.configuration === 'string') {
            try {
              this.configuration = JSON.parse(this.configuration);
              console.log('成功解析ICE服务器配置字符串');
            } catch (error) {
              console.error('ICE服务器配置解析失败:', error);
              // 使用默认配置
              this.configuration = {
                "iceServers": [{ 'urls': ['stun:webrtc.qq-kan.com:3478?transport=udp'] }]
              };
            }
          }
          
          this.myPeerConnection = new RTCPeerConnection(this.configuration);

          if (this.myPeerConnection) {
            if (this.stream) {
              this.stream.getTracks().forEach(track => {
                console.log('添加轨道到PeerConnection:', track.kind, 'enabled:', track.enabled);
                this.myPeerConnection.addTrack(track, this.stream);
              });
            }

            this.myPeerConnection.ontrack = this.handleRemoteTrackAdded.bind(this);
            this.myPeerConnection.onicecandidate = this.handleIceCandidate.bind(this);
            this.myPeerConnection.oniceconnectionstatechange = this.handleIceConnectionStateChangeEvent.bind(this);
            this.myPeerConnection.onicegatheringstatechange = this.handleIceGatheringStateChangeEvent.bind(this);
            this.myPeerConnection.onsignalingstatechange = this.handleSignalingStateChangeEvent.bind(this);
            
            // 添加连接状态监听
            this.myPeerConnection.onconnectionstatechange = (event) => {
              console.log('连接状态变更为:', this.myPeerConnection.connectionState);
              
              if (this.myPeerConnection.connectionState === 'connected') {
                console.log('WebRTC连接已建立');
                this.hideLoading();
              } else if (this.myPeerConnection.connectionState === 'failed' || 
                         this.myPeerConnection.connectionState === 'disconnected') {
                console.log('WebRTC连接失败或断开');
                
                // 尝试重新连接
                if (!this.isReconnecting) {
                  this.isReconnecting = true;
                  setTimeout(() => {
                    this.isReconnecting = false;
                    if (this.RTCPeerConnectionCreated) {
                      this.handleLeave();
                      this.sendDisconnect();
                      this.sessionId = this.newGuid();
                      this.Call();
                    }
                  }, 2000);
                }
              }
            };

            console.log('PeerConnection创建成功');
            this.RTCPeerConnectionCreated = true;
          } else {
            console.log('创建PeerConnection失败');
            this.RTCPeerConnectionCreated = false;
          }
        } catch (e) {
          console.error('创建PeerConnection异常:', e.message);
          this.RTCPeerConnectionCreated = false;
        }
      }

      handleRemoteTrackAdded(e) {
        console.log('收到远程媒体轨道');
        this.remoteMediaStream = e.streams[0];
        if (this.remoteVideo) {
          e.streams[0].getTracks().forEach(track => {
            if (track.kind === 'audio') {
              track.enabled = true;
              console.log('远程音频轨道已启用');
            }
          });

          // 切换到扬声器
          this.switchToSpeaker();

          this.changeRemoteVideo(e.streams[0]);
          this.remoteVideo.srcObject = e.streams[0];
          this.remoteVideo.muted = true;
        }
      }

      // 添加切换到扬声器的方法
      async switchToSpeaker() {
        try {
          // 方法一：使用 setSinkId (仅支持 Chrome)
          if (typeof this.remoteVideo.setSinkId === 'function') {
            await this.remoteVideo.setSinkId('');  // 空字符串表示默认扬声器
            console.log('已切换到扬声器输出');
          }

          // 方法二：设置 audio 属性
          this.remoteVideo.setAttribute('x5-playsinline', 'true');
          this.remoteVideo.setAttribute('playsinline', 'true');
          this.remoteVideo.setAttribute('webkit-playsinline', 'true');

          // 方法三：iOS Safari 特殊处理
          if (this.platform.isIos) {
            // 设置视频元素样式以强制使用扬声器
            this.remoteVideo.style.width = '1px';
            this.remoteVideo.style.height = '1px';
            this.remoteVideo.style.position = 'absolute';
            this.remoteVideo.style.top = '-1px';
            this.remoteVideo.style.left = '-1px';
          }
        } catch (error) {
          console.error('切换到扬声器失败:', error);
        }
      }


      handleIceCandidate(event) {
        if (event.candidate) {
          console.log('onicecandidate:', event.candidate.candidate);
          this.sendToServer({
            "eventName": "__ice_candidate",
            "data": {
              "sessionId": this.sessionId,
              "sessionType": "IE",
              "messageId": this.newGuid(),
              "to": this.peerid,
              "from": this.meid,
              "candidate": JSON.stringify({
                'candidate': event.candidate.candidate,
                'sdpMid': event.candidate.sdpMid,
                'sdpMLineIndex': event.candidate.sdpMLineIndex
              })
            }
          });
        }
      }

      handleIceConnectionStateChangeEvent(event) {
        if (!this.myPeerConnection) return;

        if (event.target === this.myPeerConnection) {
          console.log("ICE连接状态变更为:", event.target.iceConnectionState);
          switch (this.myPeerConnection.iceConnectionState) {
            case "closed":
            case "failed":
            case "disconnected":
              if (this.RTCPeerConnectionCreated) {
                this.handleLeave();
                this.sendDisconnect();
              }
              break;
            case "connected":
              this.hideLoading();
              this.videoplay();
              break;
          }
        }
      }

      handleIceGatheringStateChangeEvent(event) {
        if (!this.myPeerConnection) return;
        if (event.target === this.myPeerConnection) {
          console.log("ICE收集状态变更为:", event.target.iceGatheringState);
        }
      }

      handleSignalingStateChangeEvent(event) {
        if (!this.myPeerConnection) return;
        if (event.target === this.myPeerConnection) {
          console.log("信令状态变更为:", event.target.signalingState);
          if (this.myPeerConnection.signalingState === "closed") {
            this.handleRelease();
          }
        }
      }

      handleLeave() {
        console.log('handleLeave');
        if (this.myPeerConnection) {
          if (this.myPeerConnection.signalingState === 'closed') {
            this.handleRelease();
          } else {
            this.myPeerConnection.getSenders().forEach(sender => {
              console.log('removeTrack', sender);
              this.myPeerConnection.removeTrack(sender);
            });
            this.myPeerConnection.close();
          }
        }
        this.videoplaying = false;
        this.RTCPeerConnectionCreated = false;
        this.StartCalled = false;
        this.IceCandidate = [];
      }

      handleRelease() {
        console.log('handleRelease');
        if (this.myPeerConnection) {
          this.myPeerConnection.onicecandidate = null;
          this.myPeerConnection.ontrack = null;
          this.myPeerConnection.onsignalingstatechange = null;
          this.myPeerConnection.onicegatheringstatechange = null;
          this.myPeerConnection = null;
        }
        if (!this.IsFailCreate) {
          this.IsReconnect = true;
        }
      }

      NewgetUserMedia(constraints, success, error) {
        console.log("开始获取用户媒体设备:", JSON.stringify(constraints));

        navigator.mediaDevices.getUserMedia(constraints)
          .then((stream) => {
            console.log("成功获取音频流:", stream);
            success(stream);
          })
          .catch((err) => {
            console.error("获取音频流失败:", err);
            error(err);
          });
      }

      // 等待并处理 React Native 传递的参数
      waitForReactNativeParams() {
        console.log('等待React Native参数...');
        window.addEventListener('message', (event) => {
          console.log('收到消息事件:', event.data);
          try {
            if (typeof event.data === 'string') {
              const { type, data } = JSON.parse(event.data);
              console.log('解析消息:', type, data);
              if (type === 'init') {
                const { serverHost, deviceId } = data;
                console.log('收到React Native初始化参数:', serverHost, deviceId);
                
                if (serverHost && deviceId) {
                  // 自动调用 initConnection 方法进行连接，不需要用户手动点击按钮
                  console.log('自动初始化WebRTC连接...');
                  this.peerid = deviceId;
                  this.sessionId = this.newGuid();
                  this.meid = this.newGuid();
                  // 显示loading状态
                  this.showLoading('正在连接设备...');
                  
                  // 初始化WebSocket连接
                  setTimeout(() => {
                    this.initWebSocket(serverHost);
                  }, 500);
                } else {
                  console.error('初始化参数不完整:', data);
                  this.showError('初始化参数不完整，请检查设置');
                }
              }
            }
          } catch (error) {
            console.error('处理初始化参数时出错:', error);
            this.showError('解析初始化数据失败: ' + error.message);
          }
        });

        // 通知 React Native 网页已准备就绪
        if (window.ReactNativeWebView) {
          console.log('向React Native发送ready消息');
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'ready'
          }));
        } else {
          console.warn('ReactNativeWebView对象不存在，无法发送ready消息');
        }
      }
    }

    // 创建 WebRTC 控制器实例并存储在全局变量中
    window.addEventListener('DOMContentLoaded', () => {
      console.log('DOM内容加载完成，初始化WebRTC控制器...');
      window.webrtcController = new WebRTCController();
      
      // 初始化等待接收 React Native 参数
      window.webrtcController.waitForReactNativeParams();
    });
    
    // 添加全局错误处理
    window.onerror = function(message, source, lineno, colno, error) {
      window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'error',}));
      return false;
    };
  </script>
</body>

</html>`;
