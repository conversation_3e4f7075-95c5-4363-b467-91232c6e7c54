import React, { Component } from 'react';
import { View, Text } from 'react-native';
import { Helper, HelperMemo } from '../Helper';
import _ from 'lodash';
import { Tme } from '../ThemeStyle';
import CardView from '../share/CardView';
import SwitchBtn from '../share/SwitchBtn';
import { Router } from '../Router';
import memoizeOne from 'memoize-one';
import DeviceControl from '../DeviceControl';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { getDeviceIcon, mainRadius, mainTitle } from '../Tools';
import ScreenSizeContext from '../../WindowResizeContext';
import IpcScanner from '../../IpcScanner';
import I18n from '../I18n';

class DeviceList extends Component {
  constructor(props) {
    super(props);
  }

  hasSwitch = memoizeOne(propsDevice => {
    var spec = Helper.getOneSwitchSpec(propsDevice);
    if (spec) {
      return spec;
    } else {
      return null;
    }
  });

  static contextType = ScreenSizeContext;

  render() {
    var { item } = this.props;
    var switchSpec = false;
    return (
      <View style={{ marginHorizontal: 4, marginBottom: 12 }}>
        <CardView
          withWaveBg={true}
          onChange={this.deviceShow.bind(this, item)}
          styles={{
            width: this.context.winWidth / 2 - 26,
            padding: 12,
            borderRadius: mainRadius(),
            height: 100,
          }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: 12,
            }}>
            {getDeviceIcon(item)}
            {switchSpec ? (
              <SwitchBtn
                key={Math.random()}
                cusStyle={{ marginRight: -3 }}
                value={_.toInteger(switchSpec.real_value) > 0}
                change={this.change.bind(this, switchSpec)}
              />
            ) : null}
          </View>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <Text
              numberOfLines={2}
              style={[
                {
                  color: Tme('cardTextColor'),
                  fontSize: mainTitle(),
                  fontWeight: '500',
                  width: this.context.winWidth / 2 - 60,
                },
              ]}>
              {item.display_name}
            </Text>
          </View>
          <View style={{ position: 'absolute', bottom: 10, right: 10 }}>
            {item.is_alive ? null : (
              <Ionicons
                name="information-circle-outline"
                size={16}
                color={Tme('cardTextColor')}
              />
            )}
          </View>
        </CardView>
      </View>
    );
  }

  change(switchSpec, e) {
    var param = e ? 255 : 0;
    var deviceControl = new DeviceControl({
      spec: switchSpec,
      sn_id: '',
      param: param,
      successCb: false,
      runCMD: true,
    });
    deviceControl.switch();
  }

  deviceShow(item) {
    if (item.is_tuya_camera) {
      this.showTuyaCamera(item);
    } else if (item.dv_type === 'ipc') {
      this.showIPCCamera(item);
    } else {
      Router.pushDeviceShow(this.props.navigation, { data: item });
    }
  }

  showIPCCamera(item) {
    let index = 0;
    const scan = IpcScanner.getScanResults();
    if (HelperMemo.ipc_is_local_play) {
      index = _.findIndex(scan, function (o) { return o.sn === item.sn; });
    } else {
      index = 0;
    }

    if (index !== -1) {
      this.props.navigation.push('LocalCameraShow', {
        host: scan[index].ip,
        clientid: item.ipc.webrtc_uuid,
        node: item,
        title: I18n.t('ipc.local_play'),
      });
    } else {
      this.props.navigation.push('IpcCameraShow', {
        title: item.display_name,
        node: item,
      });
    }
  }

  showTuyaCamera(item) {
    this.props.navigation.push('TuyaCameraShow', {
      node: item,
      title: item.display_name,
    });
  }
}

export default DeviceList;
