import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Keyboard,
  Image,
} from 'react-native';
import { TabView } from 'react-native-tab-view';
import { HelperMemo } from '../Helper';
import { Colors, Tme, IsDark } from '../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import _ from 'lodash';
import I18n from '../I18n';
import CardView from '../share/CardView';
import Svg, { Path, G } from 'react-native-svg';
import AlertModal from '../share/AlertModal';
import Permissions from '../Permissions';
import ScreenSizeContext from '../../WindowResizeContext';
import BgAnimateView from '../share/BgAnimateView';
import UpDownAnimateView from '../share/UpDownAnimateView';
import NbWhite from '../../img/nbiot_white_rgb.png';
import NbBlack from '../../img/nbiot_black_rgb.png';
import PresenPng from '../../img/controller.png';
import PresenPngW from '../../img/controller_w.png';

const viewBorderRadius = 8;

class ZWaveSvg extends Component {
  render() {
    return (
      <Svg
        width="35"
        height="35"
        preserveAspectRatio="xMidYMid meet"
        viewBox="0 0 24 24"
        style="-ms-transform: rotate(360deg); -webkit-transform: rotate(360deg); transform: rotate(360deg);">
        <Path
          d="M16.3 10.58c-3.16 0-5.7 2.55-5.7 5.7c0 3.15 2.55 5.72 5.7 5.72c3.15 0 5.7-2.57 5.7-5.72s-2.55-5.7-5.7-5.7m1.7 8.5h-4.81L15.81 15h-2.5l1.09-1.77h4.78l-2.55 4.05h2.55L18 19.08M16.3 3.93V2C8.41 2 2 8.42 2 16.31h1.92C3.94 9.46 9.5 3.93 16.3 3.93m0 3.81V5.82c-5.8 0-10.49 4.71-10.49 10.49h1.92c.02-4.73 3.86-8.57 8.57-8.57"
          fill={Tme('cardTextColor')}
        />
      </Svg>
    );
  }
}
class ZigbeeSvg extends Component {
  render() {
    return (
      <Svg
        width="35"
        height="35"
        preserveAspectRatio="xMidYMid meet"
        viewBox="0 0 24 24"
        style="-ms-transform: rotate(360deg); -webkit-transform: rotate(360deg); transform: rotate(360deg);">
        <Path
          d="M4.06 6.15c-.09.02-.18.07-.26.13A9.892 9.892 0 0 0 2 12a10 10 0 0 0 10 10c3 0 5.68-1.32 7.5-3.4l-2.5.25c-2.75.3-5.55.34-8.34.11c-.71-.02-1.42-.2-2.07-.51a2.615 2.615 0 0 1-1.52-2.16c-.01-.16.05-.29.16-.42l2.19-2.27l7.61-7.9v-.1h-4.19c-2.27.04-4.53.22-6.78.55M20.17 17.5c.09-.03.18-.06.26-.11A9.984 9.984 0 0 0 22 12A10 10 0 0 0 12 2C9.22 2 6.7 3.13 4.89 4.97h.28c3.11-.4 6.26-.5 9.39-.32c.94-.01 1.89.17 2.77.52A2.67 2.67 0 0 1 19 7.37c0 .16-.07.33-.18.45l-9.11 9.37l-.71.76v.11h4.14c2.36-.06 4.7-.25 7.03-.56z"
          fill={Tme('cardTextColor')}
        />
      </Svg>
    );
  }
}
class Svg433 extends Component {
  render() {
    return (
      <Svg
        width="35"
        height="35"
        preserveAspectRatio="xMidYMid meet"
        viewBox="0 0 24 24"
        style="-ms-transform: rotate(360deg); -webkit-transform: rotate(360deg); transform: rotate(360deg);">
        <Path
          d="M4 6V4h.1C12.9 4 20 11.1 20 19.9v.1h-2v-.1C18 12.2 11.8 6 4 6m0 4V8a12 12 0 0 1 12 12h-2A10 10 0 0 0 4 10m0 4v-2a8 8 0 0 1 8 8h-2a6 6 0 0 0-6-6m0 2a4 4 0 0 1 4 4H4v-4z"
          fill={Tme('cardTextColor')}
        />
      </Svg>
    );
  }
}
class TuyaSvg extends Component {
  render() {
    return (
      <Image
        style={{ width: 30, height: 35, tintColor: Tme('cardTextColor') }}
        source={require('../../img/tuya.png')}
      />
    );
  }
}

class MatterSvg extends Component {
  render() {
    return (
      <Image
        style={{ width: 30, height: 30, tintColor: Tme('cardTextColor') }}
        source={require('../../img/matter-icon.png')}
      />
    );
  }
}

class ThreadSvg extends Component {
  render() {
    return (
      <Svg xmlns="http://www.w3.org/2000/svg" width={30} height={30}>
        <G fill={Tme('cardTextColor')} fillRule="nonzero">
          <Path d="M15 0C6.729 0 0 6.73 0 15.002 0 23.218 6.638 29.909 14.831 30V15.015H9.922a2.65 2.65 0 0 0-2.646 2.648 2.65 2.65 0 0 0 2.646 2.646v3.27a5.922 5.922 0 0 1-5.915-5.916 5.923 5.923 0 0 1 5.915-5.918h4.91v-1.654a4.93 4.93 0 0 1 4.922-4.924 4.93 4.93 0 0 1 4.924 4.924 4.93 4.93 0 0 1-4.924 4.925H18.1V29.68C24.89 28.247 30 22.211 30 15.002 30 6.73 23.27 0 15 0Z" />
          <Path d="M21.409 10.091c0-.912-.742-1.654-1.655-1.654-.912 0-1.654.742-1.654 1.654v1.655h1.654c.913 0 1.655-.742 1.655-1.655Z" />
        </G>
      </Svg>
    );
  }
}

class CameraSvg extends Component {
  render() {
    return (
      <Svg
        width="35"
        height="35"
        preserveAspectRatio="xMidYMid meet"
        viewBox="0 0 24 24"
        style="-ms-transform: rotate(360deg); -webkit-transform: rotate(360deg); transform: rotate(360deg);">
        <Path
          d="M18.15 4.94c-.38-.03-.78.06-1.15.26l-8.65 5c-.96.56-1.28 1.8-.73 2.74l1.5 2.59a1.99 1.99 0 0 0 2.73.74l1.8-1.04c.27.46.67.83 1.16 1.04v1.77c0 1.09.89 1.96 2 1.96H22v-1.96h-5.19v-1.77a2.5 2.5 0 0 0 1.5-2.27c0-.46-.12-.89-.34-1.27l2.53-1.46c.97-.56 1.3-1.77.74-2.74l-1.5-2.59c-.34-.6-.95-.94-1.59-1M6.22 13.17l-4.22.7l.75 1.3l2 3.46l.75 1.3l2.72-3.3l-2-3.46z"
          fill={Tme('cardTextColor')}
        />
      </Svg>
    );
  }
}

class NbSvg extends Component {
  render() {
    if (IsDark()) {
      return (
        <Image
          style={{ width: 80, height: 40 }}
          source={NbWhite}
          resizeMode="contain"
        />
      );
    }
    return (
      <Image
        style={{ width: 80, height: 40 }}
        source={NbBlack}
        resizeMode="contain"
      />
    );
  }
}

class AddDeviceMenu extends Component {
  constructor(props) {
    super(props);

    this.state = {
      // bgColor: new Animated.Value(0),
      // poupMarginTop: new Animated.Value(0),
      sns: [],
      sn: '',
      showBack: false,
      index: 0,
      routes: [
        { key: 'meun', title: 'meun' },
        { key: 'sn', title: 'sn' },
      ],
      selectType: '',
    };

    this.bgAnRef = React.createRef();
    this.updownRef = React.createRef();

    this.snTags = HelperMemo.user_data.sn
      ? HelperMemo.user_data.sn.sn_tags
      : [];
  }

  static contextType = ScreenSizeContext;

  componentDidMount() {
    Keyboard.dismiss();

    this.initDate();

    this.bgAnRef.current.start();
    this.updownRef.current.start();
  }

  initDate() {
    if (this.props.route.params.params === 'delete') {
      const sn = HelperMemo.user_data.sn;
      if (!sn) {
        AlertModal.alert(I18n.t('global.please_add_controller'));
      } else {
        let temp = [];
        if (_.includes(sn.sn_tags, 'zwave')) {
          temp.push(sn);
        }

        if (temp.length === 0) {
          AlertModal.alert(I18n.t('global.please_add_controller'));
        } else {
          this.removeDevice(temp[0]);
        }
      }
    }
  }

  _renderScene({ route, jumpTo }) {
    switch (route.key) {
      case 'sn':
        return this.snView(jumpTo);
      case 'meun':
        return this.menuView(jumpTo);
    }
  }

  onSnClick(sn) {
    if (this.props.route.params.params === 'delete') {
      this.close();
      setTimeout(() => {
        this.props.navigation.push('removeDevice', {
          sn: sn,
        });
      }, 100);
    } else {
      this.setState({ sn: sn }, () => {
        this.addClckPush(this.state.selectType);
      });
    }
  }

  snView() {
    const { sns } = this.state;
    let html = [];
    sns.map((sn, index) => {
      html.push(
        <View style={{ marginBottom: 20 }} key={index}>
          <CardView
            withWaveBg={true}
            onChange={this.onSnClick.bind(this, sn)}
            styles={[
              {
                width: (this.context.winWidth - 80) / 2 - 15,
                padding: 20,
                height: 70,
              },
            ]}>
            <View
              style={{
                flexDirection: 'column',
                alignItems: 'center',
                marginTop: 8,
                justifyContent: 'center',
              }}>
              <Text
                numberOfLines={2}
                style={[
                  {
                    color: Tme('cardTextColor'),
                    fontSize: 14,
                    fontWeight: '500',
                  },
                ]}>
                {sn.display_name}
              </Text>
            </View>
          </CardView>
        </View>,
      );
    });
    return (
      <View style={{ marginTop: 10, paddingHorizontal: 20, marginBottom: 20 }}>
        <View style={{ marginVertical: 10 }}>
          <Text style={{ color: Tme('smallTextColor') }}>
            {I18n.t('global.select_add_controller')}
          </Text>
        </View>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            flexWrap: 'wrap',
          }}>
          {html}
        </View>
      </View>
    );
  }

  menuView() {
    let showMatter = false;
    if (_.includes(this.snTags, 'matter_wifi')) {
      showMatter = true;
    }

    if (_.includes(this.snTags, 'matter_thread')) {
      showMatter = true;
    }

    return (
      <ScrollView
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="never"
        keyboardDismissMode="on-drag">
        <View style={{ backgroundColor: Tme('cardColor') }}>
          <View
            style={{ marginTop: 10, paddingHorizontal: 20, marginBottom: 20 }}>
            <Text
              style={{ fontSize: 16, color: Colors.MainColor, marginBottom: 8 }}>
              Presen
            </Text>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                flexWrap: 'wrap',
              }}>
              <View style={{ marginBottom: 20 }}>
                <CardView
                  withWaveBg={true}
                  onChange={this.addClick.bind(this, 'controller')}
                  styles={[
                    {
                      width: (this.context.winWidth - 80) / 2 - 15,
                      height: 120,
                      padding: 20,
                    },
                  ]}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginBottom: 8,
                    }}>
                    <Image
                      style={{
                        width: 40,
                        height: 40,
                      }}
                      source={IsDark() ? PresenPngW : PresenPng}
                    />
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      marginTop: 8,
                      justifyContent: 'center',
                    }}>
                    <Text
                      numberOfLines={2}
                      style={[
                        {
                          color: Tme('cardTextColor'),
                          fontSize: 14,
                          fontWeight: '500',
                        },
                      ]}>
                      {I18n.t('home.controller')}
                    </Text>
                  </View>
                </CardView>
              </View>
              <View style={{ marginBottom: 20 }}>
                <CardView
                  withWaveBg={true}
                  onChange={this.addClick.bind(this, 'ipc')}
                  styles={[
                    {
                      width: (this.context.winWidth - 80) / 2 - 15,
                      height: 120,
                      padding: 20,
                    },
                  ]}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginBottom: 8,
                    }}>
                    <CameraSvg />
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      marginTop: 8,
                      justifyContent: 'center',
                    }}>
                    <Text
                      numberOfLines={2}
                      style={[
                        {
                          color: Tme('cardTextColor'),
                          fontSize: 14,
                          fontWeight: '500',
                        },
                      ]}>
                      {I18n.t('device.camera')}
                    </Text>
                  </View>
                </CardView>
              </View>
            </View>
          </View>
          {_.includes(this.snTags, 'zwave') ? this.rowShow('Z-Wave') : null}
          {_.includes(this.snTags, 'zigbee') ? this.rowShow('Zigbee') : null}
          {showMatter ? this.rowShow('Matter') : null}
          {/* {this.rowShow('Tuya')} */}
          {/* {this.rowShow('IPC')} */}
          {this.rowShow('NB-IoT')}
          {_.includes(this.snTags, '433') ? this.rowShow('433M') : null}
        </View>
      </ScrollView>
    );
  }

  onIndexChange(id) {
    this.setState({ index: id });
  }

  itemShow(type, title, index) {
    var svg = '';
    switch (type) {
      case 'zwave':
      case 'zwave_smart':
      case 'zwave_list':
        svg = <ZWaveSvg color={Colors.MainColor} />;
        break;
      case 'zigbee':
      case 'zigbee_smart':
      case 'zigbee_list':
        svg = <ZigbeeSvg />;
        break;
      case '433':
        svg = <Svg433 />;
        break;
      case 'matter_wifi':
        svg = <MatterSvg />;
        break;
      case 'matter_thread':
        svg = <ThreadSvg />;
        break;
      case 'tuya':
      case 'tuya_ez':
      case 'tuya_ap':
        svg = <TuyaSvg />;
        break;
      case 'ipc_ez':
      case 'ipc_ap':
      case 'ipc_wlan':
        svg = <CameraSvg />;
        break;
      case 'nb-iot':
        svg = <NbSvg />;
        break;
      case 'camera':
        svg = <CameraSvg />;
        break;
    }
    return (
      <View style={{ marginBottom: 20 }} key={index}>
        <CardView
          withWaveBg={true}
          onChange={this.addClick.bind(this, type)}
          styles={[
            {
              width: (this.context.winWidth - 80) / 2 - 15,
              height: 120,
              padding: 20,
            },
          ]}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 8,
            }}>
            {svg}
          </View>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginTop: 8,
              justifyContent: 'center',
            }}>
            <Text
              numberOfLines={2}
              style={[
                {
                  color: Tme('cardTextColor'),
                  fontSize: 14,
                  fontWeight: '500',
                },
              ]}>
              {title}
            </Text>
          </View>
        </CardView>
      </View>
    );
  }

  rowShow(type) {
    var items = [];
    var that = this;
    var title = type;

    if (type == 'camera') {
      title = I18n.t('device.camera');
    }
    switch (type) {
      case 'Z-Wave':
        items = [
          'zwave,Z-Wave',
          'zwave_smart,Z-Wave Smart Start',
          // 'zwave_list,Z-Wave Multiple',
        ];
        break;
      case 'Zigbee':
        items = [
          'zigbee,Zigbee',
          'zigbee_smart,Zigbee with Install Code',
          // 'zigbee_list,Zigbee Multiple',
        ];
        break;
      case 'Matter':
        const temp = [];
        if (_.includes(this.snTags, 'matter_thread')) {
          temp.push('matter_thread, Thread');
        }
        if (_.includes(this.snTags, 'matter_wifi')) {
          temp.push('matter_wifi, Matter Wifi');
        }
        items = temp;
        break;
      case 'Tuya':
        items = ['tuya_ez,Tuya EZ', 'tuya_ap,Tuya AP'];
        break;
      case 'IPC':
        items = [
          'ipc_ez,IP Camera EZ',
          'ipc_ap,IP Camera AP',
          'ipc_wlan,IP Camera WLAN',
        ];
        break;
      case 'NB-IoT':
        items = ['nb-iot,NB-IoT'];
        break;
      case '433M':
        items = ['433,433M'];
        break;
      case 'camera':
        items = ['camera,' + I18n.t('device.camera')];
    }
    return (
      <View style={{ marginTop: 10, paddingHorizontal: 20, marginBottom: 20 }}>
        <Text style={{ fontSize: 16, color: Colors.MainColor, marginBottom: 8 }}>
          {title}
        </Text>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            flexWrap: 'wrap',
          }}>
          {items.map((item, index) => {
            const temp = item.split(',');
            return that.itemShow(temp[0], temp[1], type + index);
          })}
        </View>
      </View>
    );
  }

  back() {
    this.setState({ index: 0, showBack: false, sns: [], selectType: '' });
  }

  render() {
    return (
      <BgAnimateView
        style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        ref={this.bgAnRef}>
        <View
          style={{
            position: 'absolute',
            top: 0,
            bottom: 0,
            right: 0,
            left: 0,
          }}>
          <TouchableOpacity style={{ flex: 1 }} onPress={() => this.close()} />
        </View>
        <UpDownAnimateView
          ref={this.updownRef}
          style={{
            width: this.context.winWidth - 40,
            height: this.context.winHeight * 0.66,
            backgroundColor: Tme('cardColor'),
            borderRadius: viewBorderRadius,
            paddingBottom: 10,
          }}>
          <View
            style={{
              width: this.context.winWidth - 40,
              height: 50,
              backgroundColor: Tme('cardColor'),
              alignItems: 'center',
              justifyContent: 'center',
              borderTopLeftRadius: viewBorderRadius,
              borderTopRightRadius: viewBorderRadius,
              flexDirection: 'row',
            }}>
            <Text
              style={{
                color: Tme('cardTextColor'),
                fontSize: 20,
                fontWeight: '500',
              }}>
              {this.props.route.params.params === 'delete'
                ? I18n.t('global.select_controller')
                : I18n.t('home.add_device')}
            </Text>
            <TouchableOpacity
              hitSlop={{ top: 20, right: 20, bottom: 20, left: 20 }}
              style={{ position: 'absolute', right: 16 }}
              activeOpacity={0.8}
              onPress={() => this.close()}>
              <MaterialIcons
                name="close"
                size={22}
                color={Tme('cardTextColor')}
                style={{ textAlign: 'right' }}
              />
            </TouchableOpacity>
            {this.state.showBack && (
              <TouchableOpacity
                hitSlop={{ top: 20, right: 20, bottom: 20, left: 20 }}
                style={{ position: 'absolute', left: 16 }}
                activeOpacity={0.8}
                onPress={() => this.back()}>
                <Ionicons
                  name="chevron-back"
                  size={22}
                  color={Tme('cardTextColor')}
                  style={{ textAlign: 'right' }}
                />
              </TouchableOpacity>
            )}
          </View>
          <TabView
            swipeEnabled={false}
            onIndexChange={this.onIndexChange.bind(this)}
            navigationState={{
              index: this.state.index,
              routes: this.state.routes,
            }}
            renderScene={this._renderScene.bind(this)}
            renderTabBar={() => null}
          />
        </UpDownAnimateView>
      </BgAnimateView>
    );
  }

  addClick(type) {
    if (type === 'controller') {
      HelperMemo.select_home = HelperMemo.user_data.home_id;
      this.close();
      setTimeout(() => {
        this.props.navigation.push('WifiScreen', {
          title: I18n.t('home.add_controller'),
        });
      }, 100);

      return;
    }
    if (type === 'ipc') {
      this.close();
      setTimeout(() => {
        this.props.navigation.push('IpcAddMenu', {
          title: I18n.t('home.add_device'),
          sn: this.state.sn,
        });
      }, 100);
      return;
    }
    // if (type.indexOf('ipc') > -1) {
    //   this.addClckPush(type);
    //   return;
    // }
    if (type.indexOf('nb-iot') > -1) {
      this.addClckPush(type);
      return;
    }
    if (type.indexOf('tuya') > -1) {
      this.addClckPush(type);
    } else {
      const sn = HelperMemo.user_data.sn;
      if (!sn) {
        AlertModal.alert(I18n.t('global.please_add_controller'));
        return;
      }

      if (type === 'matter_thread' || type === 'matter_wifi') {
        let temp = [];
        if (_.includes(sn.sn_tags, type)) {
          temp.push(sn);
        }

        if (temp.length === 0) {
          AlertModal.alert(I18n.t('global.please_add_controller'));
        } else {
          this.setState({ sn: temp[0] }, () => {
            this.addClckPush(type);
          });
        }
        return;
      }

      let temp = [];
      if (_.includes(sn.sn_tags, type.split('_')[0])) {
        temp.push(sn);
      }

      if (temp.length === 0) {
        AlertModal.alert(I18n.t('global.please_add_controller'));
      } else {
        this.setState({ sn: temp[0] }, () => {
          this.addClckPush(type);
        });
        // if (temp.length === 1) {
        //   this.setState({sn: temp[0]}, () => {
        //     this.addClckPush(type);
        //   });
        // } else {
        //   this.setState({
        //     sns: temp,
        //     index: 1,
        //     showBack: true,
        //     selectType: type,
        //   });
        // }
      }
    }
  }

  removeDevice(sn) {
    this.close();
    setTimeout(() => {
      this.props.navigation.push('removeDevice', {
        sn: sn,
      });
    }, 100);
  }

  addClckPush(type) {
    var title, screen;
    switch (type) {
      case 'zwave':
        screen = 'addDevice';
        title = '';
        break;
      case 'zigbee':
        screen = 'addDevice';
        title = '';
        break;
      case 'matter_thread':
        screen = 'ScanCodeScreen';
        title = '';
        break;
      case 'matter_wifi':
        screen = 'ScanCodeScreen';
        title = '';
        break;
      case 'zwave_smart':
        screen = 'SmartList';
        title = 'Smart Start';
        break;
      case 'zigbee_smart':
        // screen = 'ScanCodeScreen';
        screen = 'ZigbeeSmart';
        title = '';
        break;
      case '433':
        screen = 'Add433Device';
        title = I18n.t('home.add_433_device');
        break;
      case 'tuya':
        screen = 'WifiSetting';
        title = 'Tuya WiFi';
        break;
      case 'tuya_ez':
        screen = 'WifiSetting';
        title = 'Tuya EZ';
        break;
      case 'tuya_ap':
        screen = 'WifiSetting';
        title = 'Tuya AP';
        break;
      case 'ipc_ez':
      case 'ipc_ap':
        screen = 'WifiSetting';
        title = '';
        break;
      case 'ipc_wlan':
        screen = 'AddWlanDevice';
        title = '';
        break;
      case 'nb-iot':
        screen = 'AddController';
        break;
    }

    if (screen === 'ScanCodeScreen') {
      Permissions.CameraPermission(() => {
        this.close();
        setTimeout(() => {
          this.props.navigation.push('ScanCodeScreen', {
            type: type,
            sn: this.state.sn,
            title: I18n.t('home.scan'),
          });
        }, 100);
      });
    } else {
      this.close();
      setTimeout(() => {
        this.props.navigation.push(screen, {
          type: type,
          sn: this.state.sn,
          title: title,
        });
      }, 100);
    }
  }

  close() {
    this.bgAnRef.current.close();
    this.updownRef.current.close();
    this.props.navigation.goBack();
  }
}

const ShowModalView = props => {
  props.navigation.push('AddDeviceMenu', {
    params: props.params,
  });
};

module.exports = {
  ShowModalView: ShowModalView,
  AddDeviceMenu: AddDeviceMenu,
};
