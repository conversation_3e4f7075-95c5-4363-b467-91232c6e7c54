import React, {Component} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import I18n from '../I18n';
import {Tme} from '../ThemeStyle';
import {Helper} from '../Helper';
import {
  NotificationCenter,
  D433_SELECT_DEVICE,
  D433_SELECT_TYPE,
} from '../NotificationCenter';
import NavBarView from '../share/NavBarView';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import DeviceControl from '../DeviceControl';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';

export default class Add433Device extends Component {
  constructor(props) {
    super(props);

    this.timer = null;

    this.state = {
      start_learn: true,
      current_selected_device: null,
      current_selected_node_type: null,
      current_device_protocol: null,

      detected: null,
      selected_devices: [],
    };

    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  componentDidMount() {
    var that = this;
    NotificationCenter.addObserver(this, D433_SELECT_DEVICE, data => {
      that.setState({
        current_device_protocol: data.prot,
        current_selected_device: data.device,
        detected: data.detected,
      });
    });
    NotificationCenter.addObserver(this, D433_SELECT_TYPE, type => {
      that.setState({
        current_selected_node_type: type,
      });
    });
  }

  componentWillUnmount() {
    NotificationCenter.removeObserver(this, D433_SELECT_DEVICE);
    NotificationCenter.removeObserver(this, D433_SELECT_TYPE);
  }

  rightClick() {
    var that = this;
    var data = [];
    if (!this.state.current_selected_device) {
      AlertModal.alert(I18n.t('home.select_433_device_error'));
    }

    if (!this.state.current_selected_node_type) {
      AlertModal.alert(I18n.t('home.select_433_node_type'));
    } else {
      data.push({
        index: this.state.current_selected_device,
        node_type: this.state.current_selected_node_type,
      });
      Helper.httpPOST(
        '/partner/ftt/add_device',
        {
          success: () => {
            new DeviceControl({
              param: that.state.detected.port.port_id,
              sn_id: this.props.route.params.sn.id,
              successCb: false,
            }).end_learn();
            this.props.navigation.goBack();
          },
        },
        {
          selected_devices: data,
          port_id: this.state.detected.port.port_id,
        },
      );
    }
  }

  render() {
    return (
      <NavBarView>
        <View style={{height: 20}} />
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={this.click.bind(this, 'device')}
          style={{backgroundColor: Tme('cardColor')}}>
          <View
            style={{
              marginLeft: 18,
              marginRight: 20,
              flexDirection: 'row',
              paddingVertical: 16,
              justifyContent: 'space-between',
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('event.my_device')}
              </Text>
            </View>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text style={{color: Tme('cardTextColor')}}>
                {this.state.current_selected_device}
              </Text>
              <MaterialIcons
                name="keyboard-arrow-right"
                size={20}
                color={Tme('textColor')}
              />
            </View>
          </View>
        </TouchableOpacity>
        <View style={{height: 2}} />
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={this.click.bind(this, 'type')}
          style={{backgroundColor: Tme('cardColor')}}>
          <View
            style={{
              marginLeft: 18,
              marginRight: 20,
              flexDirection: 'row',
              paddingVertical: 16,
              justifyContent: 'space-between',
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('global.type')}
              </Text>
            </View>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text style={{color: Tme('cardTextColor')}}>
                {this.state.current_selected_node_type &&
                  I18n.t(
                    'home.add_433_node_type_' +
                      this.state.current_selected_node_type,
                  )}
              </Text>
              <MaterialIcons
                name="keyboard-arrow-right"
                size={20}
                color={Tme('textColor')}
              />
            </View>
          </View>
        </TouchableOpacity>
      </NavBarView>
    );
  }

  click(type) {
    if (type === 'device') {
      this.props.navigation.push('DeviceList', {
        sn: this.props.route.params.sn,
        title: I18n.t('home.device'),
      });
    } else {
      if (this.state.current_device_protocol) {
        var opts = [];
        this.state.current_device_protocol.node_types.map((v, k) => {
          opts.push({label: I18n.t('home.add_433_node_type_' + v), value: v});
        });
        this.props.navigation.push('DeviceType', {
          types: opts,
          title: I18n.t('home.select_433_node_type'),
        });
      } else {
        AlertModal.alert(I18n.t('room.room_no_device'));
      }
    }
  }
}
