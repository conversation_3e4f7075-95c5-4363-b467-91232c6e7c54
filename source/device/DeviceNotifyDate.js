import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import I18n from '../I18n';
import { Tme, Colors } from '../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import _ from 'lodash';
import {
  NotificationCenter,
  DEVICE_NOTIFY_DATE,
} from '../NotificationCenter';
import NavBarView from '../share/NavBarView';
import { SelectDate } from '../share/SelectDateView';
import AlertModal from '../share/AlertModal';
import moment from 'moment';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { PubSubEvent } from '../types/PubSubEvent';
import PubSub from 'pubsub-js';

const startTime = moment().startOf('day').toDate();
const endTime = moment().endOf('day').toDate();

export default class DeviceNotifyDate extends Component {
  constructor(props) {
    super(props);

    this.state = {
      clickRow: '',
      begin_at: '',
      end_at: '',
      week_day: [],
      nodeUUID: '',
    };

    this.week = [
      { key: 0, value: I18n.t('setting.sunday') },
      { key: 1, value: I18n.t('setting.monday') },
      { key: 2, value: I18n.t('setting.tuesday') },
      { key: 3, value: I18n.t('setting.wednesday') },
      { key: 4, value: I18n.t('setting.thursday') },
      { key: 5, value: I18n.t('setting.friday') },
      { key: 6, value: I18n.t('setting.saturday') },
    ];

    this.props.navigation.setOptions({
      // eslint-disable-next-line react/no-unstable-nested-components
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.next')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  componentDidMount() {
    var that = this;
    this.initDate();
    PubSub.subscribe(PubSubEvent.SMART_SELECT_EVENT, (msg, data) => {
      if (data.type == 'time') {
        that.setState({
          week_day: data.wday,
        });
      }
    });
  }

  initDate() {
    var that = this;
    const { notifyDate } = this.props.route.params;

    that.setState({
      week_day: notifyDate.week_day || [],
      begin_at: notifyDate.begin_at || startTime,
      end_at: notifyDate.end_at || endTime,
      nodeUUID: notifyDate.nodeUUID || '',
    });
  }

  rightClick() {
    this._next();
  }

  componentWillUnmount() {
    PubSub.unsubscribe(PubSubEvent.SMART_SELECT_EVENT);
  }

  render() {
    return (

      <NavBarView>

        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{
            backgroundColor: Tme('bgColor'),
            flex: 1,
          }}>
          <View style={{ height: 20 }} />
          <SelectDate
            from="start_at"
            title={I18n.t('setting.starting_time')}
            value={this.state.begin_at}
            onChange={this.time.bind(this)}
            navigation={this.props.navigation}
          />
          <SelectDate
            from="end_at"
            title={I18n.t('setting.end_time')}
            value={this.state.end_at}
            navigation={this.props.navigation}
            onChange={this.endTime.bind(this)}
          />
          <View style={{ height: 20 }} />
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.click.bind(this, 'week')}
            style={{ backgroundColor: Tme('cardColor') }}>
            <View
              style={{
                padding: 16,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View style={styles.rowTitle}>
                <Text
                  style={{
                    color: Tme('cardTextColor'),
                    textAlign: 'center',
                    fontSize: 17,
                  }}>
                  {I18n.t('setting.select_date')}
                </Text>
              </View>
              <View style={styles.touchRow}>
                <Text
                  style={{
                    color: Tme('textColor'),
                    marginLeft: 8,
                    flex: 1,
                    textAlign: 'right',
                  }}
                  numberOfLines={2}>
                  {this.showWday()}
                </Text>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </View>
          </TouchableOpacity>
          <View style={{ height: 20 }} />
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.delete.bind(this)}>
            <View
              style={{
                backgroundColor: Tme('cardColor'),
                paddingVertical: 16,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Text style={{ color: Colors.MainColor }}>
                {I18n.t('setting.delete')}
              </Text>
            </View>
          </TouchableOpacity>
        </ScrollView>
      </NavBarView>
    );
  }

  delete() {
    NotificationCenter.dispatchEvent(DEVICE_NOTIFY_DATE, {});
    this.props.navigation.goBack();
  }

  click() {
    this.props.navigation.push('SmartTimeView', {
      wday: this.state.week_day,
      cycle: true,
      show_cycle: false,
      title: I18n.t('setting.select_date'),
    });
  }

  time(date) {
    if (date) {
      this.setState({
        begin_at: date,
      });
    }
  }

  endTime(date) {
    if (date) {
      this.setState({
        end_at: date,
      });
    }
  }

  showWday() {
    var that = this;
    var temp = [];
    var week_day = this.state.week_day.sort();
    _.forEach(week_day, (day, key) => {
      _.forEach(that.week, (w, k) => {
        if (w.key == day) {
          temp.push(w.value);
        }
      });
    });
    if (temp.length == 7) {
      return I18n.t('global.every_day');
    } else {
      return temp.length == 0 ? I18n.t('global.every_day') : temp.join(' ');
    }
  }

  _next() {
    var errors = [];

    if (this.state.begin_at === null && this.state.end_at !== null) {
      errors.push(I18n.t('setting.place_end_time'));
    }
    if (this.state.begin_at !== null && this.state.end_at === null) {
      errors.push(I18n.t('setting.place_start_time'));
    }
    if (errors.length > 0) {
      AlertModal.alert(I18n.t('home.warning_message'), errors.join('\n'));
      return;
    }

    NotificationCenter.dispatchEvent(DEVICE_NOTIFY_DATE, {
      begin_at: this.state.begin_at,
      end_at: this.state.end_at,
      week_day: this.state.week_day,
      nodeUUID: this.state.nodeUUID,
    });
    this.props.navigation.goBack();
  }
}
const styles = StyleSheet.create({
  touchRow: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  rowTitle: {
    width: 90,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  account_view: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 3,
    marginBottom: 20,
    marginTop: 16,
  },
  acount: {
    height: 40,
    marginLeft: 6,
    marginRight: 6,
  },
});
