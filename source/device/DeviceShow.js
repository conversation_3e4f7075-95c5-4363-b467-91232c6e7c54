/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
  AppState,
} from 'react-native';
import I18n from '../I18n';
import { Helper, HelperMemo } from '../Helper';
import DeviceItemCCSpecs from '../device_spec/DeviceItemCCSpecs';
import _ from 'lodash';
import { Tme, Colors, IsDark } from '../ThemeStyle';
import ActionSheet from '@alessiocancian/react-native-actionsheet';
import Ionicons from 'react-native-vector-icons/Ionicons';
import DeviceControl from '../DeviceControl';
import { NotificationCenter, EVENT_DEVICE } from '../NotificationCenter';
import { getDeviceIcon, mainRadius } from '../Tools';
import { isOwnerOrAdmin } from '../Router';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { hideLoading, showLoading } from '../../ILoading';
import ScreenSizeContext from '../../WindowResizeContext';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';

class DeviceShow extends Component {
  constructor(props) {
    super(props);

    this.state = {
      isDataLoaded: false,
      device: {},
      sn: this.props.route.params.data ? this.props.route.params.data.sn : '',
      show_modal: false,
      change_name: this.props.route.params.data
        ? this.props.route.params.data.display_name
        : '',
      showView: false,
      modalVisible: false,
      clickRow: '',
      showSheet: false,
      showLoading: false,
      showLoadingError: false,
      isAlive: false,
      showLoadingErrorText: '',
      rowKey: Math.random(),
      setpoint_type: '',
    };

    this.firstFetch = true;
    this.actionSheet = null;

    this.appState = AppState.currentState;

    this.props.navigation.setOptions({
      headerRight: () => {
        if (isOwnerOrAdmin()) {
          return (
            <HeaderRightBtn
              rightClick={this.rightClick.bind(this)}
              icon={{ name: 'dots-horizontal', icon: 'MCIcons' }}
            />
          );
        }
        return null;
      },
    });
  }

  static contextType = ScreenSizeContext;

  componentDidMount() {
    this.doUnifyData();
    this.focusListener = this.props.navigation.addListener('focus', () => {
      this.doFetchData('');
      this.firstFetch = false;
    });

    PubSub.subscribe(PubSubEvent.ERROR_REFETCH, () => {
      this.doFetchData('');
    });

    this.AppStateEventer = AppState.addEventListener('change', nextAppState => {
      if (
        this.appState.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        console.log('App has come to the foreground!');
      }

      this.appState = nextAppState;

      console.log('AppState', this.appState);
    });
  }

  componentWillUnmount() {
    PubSub.unsubscribe(PubSubEvent.ERROR_REFETCH);
    if (this.focusListener) {
      this.focusListener();
    }

    this.AppStateEventer.remove();
  }

  doUnifyData() {
    Helper.httpGET(Helper.urlWithQuery('/home/<USER>'), {
      cloud: true,
      success: data => {
        HelperMemo.unify_commands_schema = data;
      },
      error: error => { },
    });
  }

  refresh() {
    this.doFetchData('refresh');
  }

  doFetchData(type) {
    var that = this;
    if (type == 'refresh') {
      this.setState({
        isDataLoaded: true,
      });
    } else {
      showLoading();
    }

    var params_notify = '';
    if (this.props.route.params.notify) {
      params_notify = '?notify=true';
    }
    Helper.httpGET(
      '/partner/devices/' + this.props.route.params.data.uuid + params_notify,
      {
        context: this.context,
        success: data => {
          that.setState({
            device: data.device,
            change_name: data.device.display_name,
            isAlive: data.device.is_alive,
            showView: true,
            rowKey: Math.random(),
          });
        },
        ensure: () => {
          if (type == 'refresh') {
            this.setState({
              isDataLoaded: false,
            });
          } else {
            hideLoading();
          }
        },
        error: data => {
          AlertModal.alert('', _.uniq(data).join('\n'), [
            {
              text: 'cancel',
              onPress: () => this.props.navigation.goBack(),
              style: 'cancel',
            },
          ]);
        },
      },
    );
  }

  onFailed(data) {
    this.setState({
      showLoading: false,
      showLoadingError: true,
      showLoadingErrorText: data.message,
    });
  }

  onRetryConnect() {
    this.setState({
      showLoading: false,
      showLoadingError: false,
      showLoadingErrorText: '',
    });
  }

  onClose() {
    if (!this.state.showLoading) {
      this.setState({
        showLoading: true,
        showLoadingError: false,
        showLoadingErrorText: '',
      });
    }
  }

  rightClick() {
    this.sheetShow();
  }

  menu(index) {
    if (this.state.device.dv_type == 'camera') {
      switch (index) {
        case 1:
          this.edit();
          break;
        case 2:
          this.event();
          break;
        case 3:
          this.notify();
          break;
        case 4:
          this.deleteMenu();
          break;
        default:
      }
    } else {
      switch (index) {
        case 1:
          this.edit();
          break;
        case 2:
          this.event();
          break;
        case 3:
          this.notify();
          break;
        case 4:
          this.info();
          break;
        case 5:
          this.deleteMenu();
          break;
        default:
      }
    }
  }

  deleteMatter() {
    const that = this;
    AlertModal.alert(
      I18n.t('home.remove_device'),
      I18n.t('global.activate_sure'),
      [
        {
          text: 'Cancel',
          onPress: () => { },
          style: 'cancel',
        },
        {
          text: 'OK',
          onPress: () => {
            showLoading();
            new DeviceControl({
              type: this.state.device.dv_type,
              index: that.state.device.index,
              long_id: that.state.device.long_id,
              sn_id: that.state.device.sn_id,
            }).removeDevice();
            Helper.httpPOST(
              '/partner/devices/delete',
              {
                ensure: () => {
                  hideLoading();
                },
                success: data => {
                  NotificationCenter.dispatchEvent(EVENT_DEVICE);
                  this.props.navigation.goBack();
                },
              },
              { uuid: that.state.device.uuid },
            );
          },
        },
      ],
      { cancelable: false },
    );
  }

  deleteMenu() {
    var that = this;
    if (this.state.device.dv_type == 'zwave') {
      this.deleteField();
    } else if (this.state.device.dv_type == 'zigbee') {
      AlertModal.alert(
        I18n.t('home.remove_device'),
        I18n.t('global.activate_sure'),
        [
          {
            text: 'Cancel',
            onPress: () => { },
            style: 'cancel',
          },
          {
            text: 'OK',
            onPress: () => {
              showLoading();
              new DeviceControl({
                type: 'zigbee',
                index: that.state.device.index,
                long_id: that.state.device.long_id,
                sn_id: that.state.device.sn_id,
              }).removeDevice();
              Helper.httpPOST(
                '/partner/devices/delete',
                {
                  ensure: () => {
                    hideLoading();
                  },
                  success: data => {
                    NotificationCenter.dispatchEvent(EVENT_DEVICE);
                    this.props.navigation.goBack();
                  },
                },
                { uuid: that.state.device.uuid },
              );
            },
          },
        ],
        { cancelable: false },
      );
    } else if (this.state.device.dv_type.startsWith('matter')) {
      that.deleteMatter();
    } else {
      AlertModal.alert(
        I18n.t('home.remove_device'),
        I18n.t('global.activate_sure'),
        [
          {
            text: 'Cancel',
            onPress: () => { },
            style: 'cancel',
          },
          {
            text: 'OK',
            onPress: () => {
              showLoading();
              Helper.httpPOST(
                '/partner/devices/delete',
                {
                  ensure: () => {
                    hideLoading();
                  },
                  success: data => {
                    NotificationCenter.dispatchEvent(EVENT_DEVICE);
                    this.props.navigation.goBack();
                  },
                },
                { uuid: that.state.device.uuid },
              );
            },
          },
        ],
        { cancelable: false },
      );
    }
  }

  info() {
    this.props.navigation.push('DeviceInfo', {
      device: this.state.device,
      title: I18n.t('device.device_information'),
    });
  }

  notify() {
    this.props.navigation.push('DeviceNotifyScreen', {
      uuid: this.state.device.uuid,
      title: I18n.t('setting.inform'),
    });
  }

  render() {
    let options = [];
    if (this.state.device.dv_type == 'camera') {
      options = [
        I18n.t('home.cancel'),
        I18n.t('home.edit'),
        I18n.t('home.event'),
        I18n.t('setting.inform'),
        I18n.t('home.remove_device'),
      ];
    } else {
      options = [
        I18n.t('home.cancel'),
        I18n.t('home.edit'),
        I18n.t('home.event'),
        I18n.t('setting.inform'),
        I18n.t('device.device_information'),
        I18n.t('home.remove_device'),
      ];
    }

    if (this.state.showView) {
      var icon = getDeviceIcon(this.state.device);
      return (
        <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={this.state.isDataLoaded}
              onRefresh={this.refresh.bind(this)}
            />
          }
          style={{ flex: 1, backgroundColor: Tme('bgColor') }}>
          <View
            style={{
              paddingVertical: 20,
              paddingLeft: 12,
              paddingRight: 12,
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: 12,
                paddingLeft: 8,
                paddingRight: 8,
              }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                {icon}
                <Text
                  style={[
                    { color: Tme('cardTextColor'), marginLeft: 12 },
                    Colors.RoomTitleFontStyle,
                  ]}>
                  {this.state.device.display_name}
                </Text>
                {this.state.device.dv_type !== 'camera' ? (
                  this.state.showLoadingError ? (
                    <TouchableOpacity
                      style={{ marginLeft: 8 }}
                      activeOpacity={0.8}
                      onPress={() => {
                        AlertModal.alert(this.state.showLoadingErrorText);
                      }}>
                      <Ionicons
                        size={24}
                        color={Tme('textColor')}
                        name="alert"
                      />
                    </TouchableOpacity>
                  ) : (
                    <ActivityIndicator
                      style={{ marginLeft: 8 }}
                      animating={this.state.showLoading}
                      size="small"
                      color={Tme('textColor')}
                    />
                  )
                ) : null}
              </View>
              {this.state.isAlive ? null : (
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={() => {
                    AlertModal.alert(I18n.t('global.device_failed'));
                  }}>
                  <Ionicons
                    name="information-circle-outline"
                    size={22}
                    color={Tme('cardTextColor')}
                  />
                </TouchableOpacity>
              )}
            </View>
          </View>
          <View
            style={{
              backgroundColor: Tme('cardColor2'),
              paddingTop: 10,
              flex: 1,
              borderRadius: mainRadius(),
            }}>
            <DeviceItemCCSpecs
              key={this.state.rowKey}
              parent={this}
              navigation={this.props.navigation}
              device={this.state.device}
              sn={this.state.sn}
              from="device"
            />
          </View>
          <ActionSheet
            ref={o => (this.actionSheet = o)}
            options={options}
            userInterfaceStyle={IsDark() ? 'dark' : 'light'}
            cancelButtonIndex={0}
            destructiveButtonIndex={options.length - 1}
            theme="ios"
            styles={{
              cancelButtonBox: {
                height: 50,
                marginTop: 6,
                alignItems: 'center',
                justifyContent: 'center',
              },
              buttonBox: {
                height: 50,
                alignItems: 'center',
                justifyContent: 'center',
              },
            }}
            onPress={index => {
              this.menu(index);
            }}
          />
        </ScrollView>
      );
    } else {
      return (
        <View
          style={{
            flex: 1,
            backgroundColor: Tme('bgColor'),
          }}
        />
      );
    }
  }

  sheetShow(rowData) {
    this.actionSheet.show();
  }

  edit() {
    if (this.state.device.dv_type != 'camera') {
      this.props.navigation.push('AddSuccess', {
        uuid: this.state.device.uuid,
        sn_id: this.state.device.sn_id,
        name: this.state.change_name,
        type: 'edit',
        title: I18n.t('home.edit_device'),
      });
    } else {
      this.props.navigation.push('AddCamera', {
        uuid: this.state.device.uuid,
        video_url: this.state.device.cc_specs[0].video_url,
        screenshot_url: this.state.device.cc_specs[0].screenshot_url,
        name: this.state.device.display_name,
        title: I18n.t('device.camera'),
      });
    }
  }

  event() {
    this.props.navigation.push('eventScreen', {
      device_name: this.props.route.params.data.display_name,
      device_uuid: this.state.device.uuid,
      title: I18n.t('home.event'),
    });
  }

  deleteField() {
    AlertModal.alert(
      I18n.t('home.warning_message'),
      I18n.t('global.zwave_delete_db'),
      [
        {
          text: 'Cancel',
          onPress: () => { },
          style: 'cancel',
        },
        {
          text: 'OK',
          onPress: () => {
            showLoading();
            Helper.httpPOST(
              '/partner/devices/delete',
              {
                ensure: () => {
                  hideLoading();
                },
                success: data => {
                  this.props.navigation.goBack();
                },
              },
              { uuid: this.state.device.uuid },
            );
          },
        },
      ],
      { cancelable: false },
    );
  }
}
export default DeviceShow;
