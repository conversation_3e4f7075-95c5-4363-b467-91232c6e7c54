import React from 'react';
import {View, TouchableOpacity, StyleSheet} from 'react-native';
import {ImageLoading} from '../event/EventScreen'; // 确保路径正确
import Ionicons from 'react-native-vector-icons/Ionicons';

interface ImagePreviewViewProps {
  imageUrl: string;
  onImagePress: () => void;
  onVideoPress?: () => void;
  hasVideo?: boolean;
  containerStyle?: any;
}

const ImagePreviewView: React.FC<ImagePreviewViewProps> = ({
  imageUrl,
  onImagePress,
  onVideoPress,
  hasVideo,
  containerStyle,
}) => {
  return (
    <View style={[styles.imageContainer, containerStyle]}>
      <TouchableOpacity activeOpacity={0.8} onPress={onImagePress}>
        <ImageLoading uri={imageUrl} height={180} />
      </TouchableOpacity>
      <View style={styles.actionButtonsContainer}>
        <TouchableOpacity style={styles.actionButton} onPress={onImagePress}>
          <Ionicons name="image" size={28} color="#fff" />
        </TouchableOpacity>
        {hasVideo && (
          <TouchableOpacity style={styles.actionButton} onPress={onVideoPress}>
            <Ionicons name="play" size={28} color="#fff" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  imageContainer: {
    width: '100%',
    borderRadius: 3,
    overflow: 'hidden',
  },
  actionButtonsContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
    gap: 20,
  },
  actionButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(64,64,64,0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default ImagePreviewView;
