import React, {useEffect, useRef, useContext, useCallback} from 'react';
import {SafeAreaView, Text, TouchableOpacity, Animated} from 'react-native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RouteProp} from '@react-navigation/native';

import AppConfig from '../../app_config';
import {Helper} from '../Helper';
import {Tme} from '../ThemeStyle';
import ScreenSizeContext from '../../WindowResizeContext';

const alertColor = AppConfig.ui.backgroud_border;

type RootStackParamList = {
  UrgentAlert: {
    device_name: string;
    spec_name: string;
    spec_value: string;
  };
};

type UrgentAlertScreenProps = {
  navigation: NativeStackNavigationProp<RootStackParamList, 'UrgentAlert'>;
  route: RouteProp<RootStackParamList, 'UrgentAlert'>;
};

interface ScreenContextType {
  width: number;
  winHeight: number;
}

const UrgentAlertScreen: React.FC<UrgentAlertScreenProps> = ({
  navigation,
  route,
}) => {
  const screenContext = useContext(ScreenSizeContext) as ScreenContextType;
  const opacityAnim = useRef(new Animated.Value(1)).current;
  const diameterAnim = useRef(new Animated.Value(1)).current;

  const startAnimation = useCallback(() => {
    diameterAnim.setValue(1);
    opacityAnim.setValue(1);
    Animated.parallel([
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 2000,
        useNativeDriver: true,
      }),
      Animated.timing(diameterAnim, {
        toValue: 1.9,
        duration: 2000,
        useNativeDriver: true,
      }),
    ]).start(() => startAnimation());
  }, [diameterAnim, opacityAnim]);

  useEffect(() => {
    startAnimation();
  }, [startAnimation]);

  return (
    <SafeAreaView
      style={{
        width: screenContext.width,
        height: screenContext.winHeight,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: Tme('bgColor'),
      }}>
      <Animated.View
        style={{
          position: 'absolute',
          width: screenContext.width / 2,
          height: screenContext.width / 2,
          top: screenContext.width / 2,
          backgroundColor: alertColor,
          borderRadius: screenContext.width / 2,
          transform: [
            {perspective: 1000},
            {
              scale: diameterAnim,
            },
          ],
          opacity: opacityAnim,
        }}
      />
      <TouchableOpacity
        activeOpacity={0.8}
        style={{
          position: 'absolute',
          width: screenContext.width / 2,
          height: screenContext.width / 2,
          top: screenContext.width / 2,
          backgroundColor: alertColor,
          borderRadius: screenContext.width / 2,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        hitSlop={{top: 40, right: 40, bottom: 40, left: 40}}
        onPress={() => navigation.goBack()}>
        <Text
          numberOfLines={1}
          style={{
            color: 'white',
            fontSize: 14,
            fontWeight: '500',
            lineHeight: 18,
          }}>
          {route.params.device_name}
        </Text>
        <Text
          numberOfLines={1}
          style={{
            color: 'white',
            fontSize: 18,
            fontWeight: '500',
            marginVertical: 16,
          }}>
          {Helper.i(route.params.spec_name)}
        </Text>
        <Text
          numberOfLines={1}
          style={{
            color: 'white',
            fontSize: 18,
            fontWeight: '500',
            lineHeight: 18,
          }}>
          {Helper.i(route.params.spec_value)}
        </Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default UrgentAlertScreen;
