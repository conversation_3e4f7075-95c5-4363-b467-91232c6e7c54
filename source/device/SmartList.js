import React, {Component} from 'react';
import {View, Text, TouchableOpacity, ScrollView} from 'react-native';
import I18n from '../I18n';
import {Tme} from '../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Ionicons from 'react-native-vector-icons/Ionicons';
import NavBarView from '../share/NavBarView';
import Permissions from '../Permissions';
import {NotificationCenter, SMART_LIST_ADD} from '../NotificationCenter';
import _ from 'lodash';
import AlertModal from '../share/AlertModal';
export default class SmartList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      list: [],
    };
  }

  componentDidMount() {
    NotificationCenter.addObserver(this, SMART_LIST_ADD, key => {
      if (key.length < 40) {
        AlertModal.alert(I18n.t('global.dsk_input'));
      } else {
        const list = _.cloneDeep(this.state.list);
        if (!list.includes(key)) {
          list.push(key);
        }
        this.setState({
          list,
        });
      }
    });
  }

  componentWillUnmount() {
    NotificationCenter.removeObserver(this, SMART_LIST_ADD);
  }

  deleteItem(v) {
    const list = _.cloneDeep(this.state.list);
    if (list.includes(v)) {
      list.splice(list.indexOf(v), 1);
    }
    this.setState({
      list,
    });
  }

  render() {
    return (
      <NavBarView>
        <View
          style={{
            marginTop: 20,
            paddingBottom: 2,
          }}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.click.bind(this, 'scan')}
            style={{
              backgroundColor: Tme('cardColor'),
            }}>
            <View
              style={{
                marginHorizontal: 20,
                flexDirection: 'row',
                paddingVertical: 16,
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <FontAwesome name="qrcode" size={20} color={Tme('textColor')} />
                <Text style={{marginLeft: 12, color: Tme('cardTextColor')}}>
                  {I18n.t('global.scan_key')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </View>
          </TouchableOpacity>
        </View>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={this.click.bind(this, 'input')}
          style={{backgroundColor: Tme('cardColor')}}>
          <View
            style={{
              marginHorizontal: 20,
              flexDirection: 'row',
              paddingVertical: 16,
              justifyContent: 'space-between',
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <FontAwesome name="edit" size={20} color={Tme('textColor')} />
              <Text style={{marginLeft: 10, color: Tme('cardTextColor')}}>
                {I18n.t('global.input_key')}
              </Text>
            </View>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <MaterialIcons
                name="keyboard-arrow-right"
                size={20}
                color={Tme('textColor')}
              />
            </View>
          </View>
        </TouchableOpacity>
        <Text
          style={{
            marginTop: 12,
            fontSize: 11,
            marginLeft: 20,
            color: Tme('smallTextColor'),
          }}>
          {I18n.t('device.add_dsk_desp')}
        </Text>
        <ScrollView style={{flex: 1}}>
          <Text
            style={{
              color: Tme('cardTextColor'),
              marginLeft: 20,
              marginBottom: 6,
              marginTop: 10,
            }}>
            DSKs:
          </Text>
          {this.state.list.reverse().map((v, k) => (
            <View style={{paddingBottom: 2}} key={k}>
              <View style={{backgroundColor: Tme('cardColor')}}>
                <View
                  style={{
                    marginHorizontal: 20,
                    flexDirection: 'row',
                    paddingVertical: 16,
                    justifyContent: 'space-between',
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      flex: 1,
                    }}>
                    <Text style={{color: Tme('cardTextColor'), fontSize: 12}}>
                      {v}
                    </Text>
                    <TouchableOpacity
                      activeOpacity={0.8}
                      onPress={this.deleteItem.bind(this, v)}>
                      <Ionicons
                        name="trash-outline"
                        size={20}
                        color="#fc577acc"
                      />
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          ))}
        </ScrollView>
        <View style={{alignItems: 'center', width: '100%'}}>
          <TouchableOpacity
            onPress={this.onNext.bind(this)}
            activeOpacity={1.0}
            style={{
              width: '100%',
              paddingVertical: 12,
              flexDirection: 'row',
              justifyContent: 'center',
              backgroundColor: Tme('cardColor'),
              alignItems: 'center',
            }}>
            <Text
              style={{
                fontSize: 16,
                fontWeight: '600',
                color: Tme('cardTextColor'),
              }}>
              {I18n.t('device.add_these_devices')}
              {this.state.list.length > 0 && ` (${this.state.list.length})`}
            </Text>
          </TouchableOpacity>
        </View>
      </NavBarView>
    );
  }

  onNext() {
    if (this.state.list.length > 0) {
      this.props.navigation.push('InputDsk', {
        sn: this.props.route.params.sn,
        list: this.state.list,
        type: 'list',
        inputType: this.props.route.params.type,
        title: I18n.t('home.add_device'),
      });
    } else {
      AlertModal.alert(I18n.t('device.dsk_next'));
    }
  }

  click(type) {
    Permissions.CameraPermission(() => {
      this.props.navigation.push('ScanQrScreen', {
        sn: this.props.route.params.sn,
        type: type,
        inputType: this.props.route.params.type,
        text: I18n.t('home.add_device'),
      });
    });
  }
}
