import React, { Component } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import I18n from '../I18n';
import { Helper } from '../Helper';
import * as Progress from 'react-native-progress';
import PahoManager from '../PahoManager';
import { Tme, Colors } from '../ThemeStyle';
import IdleTimerManager from 'react-native-idle-timer';
import DeviceControl from '../DeviceControl';
import NavBarView from '../share/NavBarView';
import CardView from '../share/CardView';
import _ from 'lodash';
import AlertModal from '../share/AlertModal';
import { hideLoading, showLoading } from '../../ILoading';
import ScreenSizeContext from '../../WindowResizeContext';
import PubSub from 'pubsub-js';
import { EVENT_GRPC_CONTROLLER_CHANGE } from '../types/PubSubEvent';

class RemoveDevice extends Component {
  constructor(props) {
    super(props);

    this.state = {
      progress: 0,
      wait: true,
      indeterminate: true,
      countdown: 60,
      countdownShow: true,
    };

    this.down = null;
    this.alertd = false;
  }
  static contextType = ScreenSizeContext;

  componentDidMount() {
    var that = this;

    this.doFetchData();

    PubSub.subscribe(
      EVENT_GRPC_CONTROLLER_CHANGE,
      payload => {
        if (payload.queue == 'controller') {
          switch (payload.data.op_state) {
            case 'starting':
              if (this.state.countdownShow) {
                that.countdown();
                that.setState({
                  indeterminate: false,
                  wait: false,
                  countdownShow: true,
                });
              }
              break;
            case 'in_progress':
              that.clearCountdown();
              that.setState({
                wait: true,
                countdownShow: false,
                progress: 0.25,
              });
              break;
            case 'removed':
              that.clearCountdown();
              that.setState(
                {
                  wait: true,
                  countdownShow: false,
                  progress: 1,
                },
                () => {
                  AlertModal.alert(
                    I18n.t('home.remove_device_success'),
                    '',
                    [
                      {
                        text: 'OK',
                        onPress: () => {
                          that.props.navigation.goBack();
                        },
                      },
                    ],
                    { cancelable: false },
                  );
                },
              );
              break;
            case 'failed':
              that.clearCountdown();
              if (!that.alertd) {
                that.alertd = true;
                AlertModal.alert(
                  I18n.t('home.remove_device_fail'),
                  '',
                  [
                    {
                      text: 'OK',
                      onPress: () => {
                        that.props.navigation.goBack();
                      },
                    },
                  ],
                  { cancelable: false },
                );
              }
              break;
            default:
              break;
          }
        }
      },
    );

    IdleTimerManager.setIdleTimerDisabled(true);
  }

  doFetchData() {
    var that = this;
    showLoading();
    Helper.httpGET(
      Helper.urlWithQuery('/partner/controllers/conn_info', {
        sn_id: this.props.route.params.sn.id,
      }),
      {
        success: data => {
          if (_.isEmpty(data.mqtt)) {
            AlertModal.alert(
              I18n.t('home.remove_device_fail'),
              '',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    that.props.navigation.goBack();
                  },
                },
              ],
              { cancelable: false },
            );
          } else {
            this.setState(
              {
                showView: true,
              },
              () => {
                this.pahoManager = new PahoManager({
                  mqtt: data.mqtt,
                  onConnect: that.onConnect.bind(that),
                  navigation: that.props.navigation,
                });
                this.pahoManager.mount();
              },
            );
          }
        },
        ensure: () => {
          hideLoading();
        },
      },
    );
  }

  countdown() {
    var time = 60;
    this.down = setInterval(() => {
      if (this.state.countdown > 0) {
        this.setState({
          countdown: time--,
        });
      } else {
        this.clearCountdown();
        AlertModal.alert(I18n.t('global.time_out'), '', [
          {
            text: I18n.t('home.confirm'),
            onPress: () => {
              this.props.navigation.goBack();
            },
          },
        ]);
      }
    }, 1000);
  }

  clearCountdown() {
    if (this.down) {
      clearInterval(this.down);
    }
    this.down = null;
  }

  onConnect() {
    new DeviceControl({
      type: 'zwave',
      sn_id: this.props.route.params.sn.id,
    }).removeDevice();
  }

  componentWillUnmount() {
    if (this.pahoManager) {
      this.pahoManager.unmount();
    }
    this.clearCountdown();
    new DeviceControl({
      type: 'zwave',
      sn_id: this.props.route.params.sn.id,
    }).CmdStop();
    PubSub.unsubscribe(EVENT_GRPC_CONTROLLER_CHANGE);
    IdleTimerManager.setIdleTimerDisabled(false);
  }

  render() {
    var that = this;
    return (
      <NavBarView>
        <View
          style={{
            flex: 1,
            backgroundColor: Tme('bgColor'),
            alignItems: 'center',
          }}>
          <View style={{ marginTop: 40 }}>
            <CardView
              withWaveBg={true}
              styles={{
                width: this.context.winWidth - 40,
                height: 520,
                padding: 20,
                alignItems: 'center',
                borderRadius: 8,
              }}>
              <View style={{ marginTop: 79, marginBottom: 20 }}>
                <Text
                  style={{
                    fontSize: 22,
                    fontWeight: '600',
                    textAlign: 'center',
                    color: Tme('cardTextColor'),
                  }}>
                  {I18n.t('home.remove_device')}
                </Text>
                {this.state.wait ? (
                  <View style={{ padding: 16 }}>
                    <Text
                      style={{
                        fontSize: 14,
                        fontWeight: '600',
                        textAlign: 'center',
                        color: Tme('cardTextColor'),
                      }}>
                      {I18n.t('global.add_device_wait')}
                    </Text>
                  </View>
                ) : (
                  <View style={{ padding: 16 }}>
                    <Text
                      style={{
                        fontSize: 14,
                        fontWeight: '600',
                        textAlign: 'center',
                        color: Tme('cardTextColor'),
                      }}>
                      {I18n.t('global.remove_device_title')}
                    </Text>
                  </View>
                )}
              </View>
              <View>
                {this.state.countdownShow ? (
                  <Progress.Circle
                    size={140}
                    progress={this.state.progress}
                    indeterminate={this.state.indeterminate}
                    showsText={true}
                    color={Colors.MainColor}
                    formatText={progress => {
                      return `${that.state.countdown}`;
                    }}
                  />
                ) : (
                  <Progress.Circle
                    size={140}
                    color={Colors.MainColor}
                    progress={this.state.progress}
                    indeterminate={this.state.indeterminate}
                    showsText={true}
                    formatText={() => {
                      return `${parseInt(this.state.progress * 100, 10)}%`;
                    }}
                  />
                )}
              </View>
              <View
                style={{
                  marginTop: 40,
                  alignItems: 'center',
                  marginBottom: 30,
                }}>
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={this.close.bind(this)}
                  style={{
                    height: 40,
                    borderRadius: 20,
                    backgroundColor: Colors.MainColor,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text
                    style={{
                      fontSize: 14,
                      fontWeight: '600',
                      color: '#ffffff',
                      width: 120,
                      textAlign: 'center',
                    }}>
                    {I18n.t('home.cancel')}
                  </Text>
                </TouchableOpacity>
              </View>
            </CardView>
          </View>
        </View>
      </NavBarView>
    );
  }

  close() {
    this.props.navigation.goBack();
  }
}
export default RemoveDevice;
