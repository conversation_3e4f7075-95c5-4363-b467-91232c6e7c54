import React from 'react';
import {Dimensions, Text, View} from 'react-native';
import Svg, {Path} from 'react-native-svg';
import {Tme} from '../ThemeStyle';
import CardView from '../share/CardView';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

type RootStackParamList = {
  IpcAddMenu: {
    sn: string;
    type: string;
  };
  WifiSetting: {
    sn: string;
    title: string;
    type: string;
  };
  AddWlanDevice: {
    sn: string;
    title: string;
    type: string;
  };
};

type IpcListSettingRouteProp = RouteProp<RootStackParamList, 'IpcAddMenu'>;

const CameraSvg = () => {
  return (
    <Svg
      width="35"
      height="35"
      preserveAspectRatio="xMidYMid meet"
      viewBox="0 0 24 24"
      style={{
        transform: [{rotate: '360deg'}],
      }}>
      <Path
        d="M18.15 4.94c-.38-.03-.78.06-1.15.26l-8.65 5c-.96.56-1.28 1.8-.73 2.74l1.5 2.59a1.99 1.99 0 0 0 2.73.74l1.8-1.04c.*********** 1.16 1.04v1.77c0 1.09.89 1.96 2 1.96H22v-1.96h-5.19v-1.77a2.5 2.5 0 0 0 1.5-2.27c0-.46-.12-.89-.34-1.27l2.53-1.46c.97-.56 1.3-1.77.74-2.74l-1.5-2.59c-.34-.6-.95-.94-1.59-1M6.22 13.17l-4.22.7l.75 1.3l2 3.46l.75 1.3l2.72-3.3l-2-3.46z"
        fill={Tme('cardTextColor')}
      />
    </Svg>
  );
};

interface MenuItem {
  key: string;
  title: string;
  scene: keyof RootStackParamList;
}

const items: MenuItem[] = [
  {
    key: 'ipc_ez',
    title: 'IP Camera EZ',
    scene: 'WifiSetting',
  },
  {
    key: 'ipc_ap',
    title: 'IP Camera AP',
    scene: 'WifiSetting',
  },
  {
    key: 'ipc_wlan',
    title: 'IP Camera WLAN',
    scene: 'AddWlanDevice',
  },
];

export default function IpcAddMenu() {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const route = useRoute<IpcListSettingRouteProp>();

  const itemClick = (item: MenuItem) => {
    if (item.scene === 'WifiSetting' || item.scene === 'AddWlanDevice') {
      navigation.push(item.scene, {
        sn: route.params.sn,
        title: '',
        type: item.key,
      });
    }
  };

  return (
    <View style={{flex: 1, backgroundColor: Tme('bgColor')}}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          flexWrap: 'wrap',
          padding: 20,
        }}>
        {items.map(item => (
          <View style={{marginBottom: 20}} key={item.key}>
            <CardView
              onChange={() => {
                itemClick(item);
              }}
              styles={[
                {
                  width: Dimensions.get('window').width / 2 - 30,
                  height: 120,
                  padding: 20,
                },
              ]}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 8,
                }}>
                <CameraSvg />
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginTop: 8,
                  justifyContent: 'center',
                }}>
                <Text
                  numberOfLines={2}
                  style={[
                    {
                      color: Tme('cardTextColor'),
                      fontSize: 14,
                      fontWeight: '500',
                    },
                  ]}>
                  {item.title}
                </Text>
              </View>
            </CardView>
          </View>
        ))}
      </View>
    </View>
  );
}
