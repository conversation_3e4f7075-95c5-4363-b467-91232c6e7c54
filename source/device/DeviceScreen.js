import React, { Component } from 'react';
import {
  View,
  Text,
  FlatList,
  SafeAreaView,
  TouchableOpacity,
  Platform,
  Animated,
} from 'react-native';
import { Helper, HelperMemo } from '../Helper';
import I18n from '../I18n';
import CardView from '../share/CardView';
import { NotificationCenter, EVENT_DEVICE } from '../NotificationCenter';
import { Tme, IsDark } from '../ThemeStyle';
import ActionSheet from '@alessiocancian/react-native-actionsheet';
import DeviceList from './DeviceList';
import { isOwnerOrAdmin } from '../Router';
import { ShowModalView } from './AddDeviceMenu';
import RoomScreen from '../room/RoomScreen';
import UserBackgroundImage from '../share/UserBackgroundImage';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { SkeletionDevice } from '../share/Skeletion';
import _ from 'lodash';
import { mainRadius, mainTitle } from '../Tools';
import AlertModal from '../share/AlertModal';
import ScreenSizeContext from '../../WindowResizeContext';
import PubSub from 'pubsub-js';
import { TabView } from 'react-native-tab-view';
import { PubSubEvent } from '../types/PubSubEvent';
class DeviceScreen extends Component {
  constructor(props) {
    super(props);

    this.state = {
      index: 0,
      route: [
        { key: 'room', title: I18n.t('home.room').toUpperCase() },
        { key: 'device', title: I18n.t('home.device').toUpperCase() },
      ],
    };
    this.roomRef = React.createRef();
    this.deviceRef = React.createRef();
    this.backgroundImageRef = React.createRef();

    this.focusListener = this.props.navigation.addListener('focus', () => {
      console.log('focus');
      this.doFetchData();
    });
  }
  static contextType = ScreenSizeContext;

  componentWillUnmount() {
    if (this.focusListener) {
      this.focusListener();
    }
  }

  showLoader() {
    this.setState({
      loading: true,
    });
  }

  hideLoader() {
    this.setState({
      loading: false,
    });
  }

  renderScene(navigation, { route }) {
    switch (route.key) {
      case 'device':
        return (
          <DeviceView
            navigation={navigation}
            parents={this}
            context={this.context}
            ref={this.deviceRef}
          />
        );
      case 'room':
        return (
          <RoomScreen
            loading={this.props.loading}
            ref={this.roomRef}
            context={this.context}
            navigation={navigation}
            parents={this}
          />
        );
    }
  }

  onIndexChange(i) {
    this.setState({
      index: i,
    });
  }

  doFetchData() {
    switch (this.state.index) {
      case 0:
        if (!_.isEmpty(this.roomRef.current)) {
          this.roomRef.current.doFetchData();
        }
        break;
      case 1:
        if (!_.isEmpty(this.deviceRef.current)) {
          this.deviceRef.current.doFetchData();
        }
        break;
    }
  }

  _renderTabBar = p => {
    return (
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}>
        <View style={{ flex: 1, flexDirection: 'row' }}>
          {p.navigationState.routes.map((route, i) => {
            let activeColor = Tme('scrollTabActiveColor');
            let tabColor = Tme('scrollTabColor');
            if (this.backgroundImageRef) {
              if (
                this.backgroundImageRef.current &&
                this.backgroundImageRef.current.child
              ) {
                if (this.backgroundImageRef.current.child.current.state.uri) {
                  activeColor = Tme('scrollTabActiveColor', 'D');
                  tabColor = Tme('scrollTabColor', 'D');
                } else {
                  activeColor = Tme('scrollTabActiveColor');
                  tabColor = Tme('scrollTabColor');
                }
              }
            }
            return (
              <TouchableOpacity
                activeOpacity={0.8}
                key={i}
                style={{
                  alignItems: 'center',
                  paddingVertical: 16,
                  paddingLeft: 16,
                }}
                onPress={() => {
                  this.setState({ index: i });
                }}>
                <Animated.Text
                  style={[
                    { color: this.state.index == i ? activeColor : tabColor },
                    {
                      fontSize: 22,
                      fontWeight: 'bold',
                    },
                  ]}>
                  {route.title}
                </Animated.Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  _renderScene = ({ route }) => {
    switch (route.key) {
      case 'first':
        return null;
      // return <FirstRoute />;
      case 'second':
        return null;
      // return <SecondRoute changeNotie={changeNotie} />;
      default:
        return null;
    }
  };

  render() {
    let tabKey = 'device_tab';
    if (
      this.backgroundImageRef.current &&
      this.backgroundImageRef.current.child
    ) {
      if (this.backgroundImageRef.current.child.current.state.uri) {
        tabKey = 'device_tab';
      } else {
        tabKey = 'device_tabBar';
      }
    }
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: Tme('bgColor') }}>
        {Platform.OS === 'android' && (
          <View style={{ height: HelperMemo.STATUS_BAR_HEIGHT }} />
        )}
        <UserBackgroundImage ref={this.backgroundImageRef} />
        <TabView
          key={tabKey}
          lazy={({ route }) => route.key === 'room'}
          navigationState={{
            index: this.state.index,
            routes: this.state.route,
          }}
          renderScene={this.renderScene.bind(this, this.props.navigation)}
          renderTabBar={this._renderTabBar.bind(this)}
          onIndexChange={this.onIndexChange.bind(this)}
        />
      </SafeAreaView>
    );
  }
}

class DeviceView extends Component {
  constructor(props) {
    super(props);

    this.state = {
      refreshing: false,
      visible: false,
      dataSource: [],
      show: false,
      btn: '',
    };

    this.firstFetch = React.createRef();
  }

  componentDidMount() {
    var that = this;
    NotificationCenter.addObserver(this, EVENT_DEVICE, () => {
      that.doFetchData();
    });

    if (!this.firstFetch.current) {
      this.doFetchData();
      this.firstFetch.current = 'first';
    }

    PubSub.subscribe(PubSubEvent.ERROR_REFETCH, () => {
      that.doFetchData();
    });
  }

  componentWillUnmount() {
    NotificationCenter.removeObserver(this, EVENT_DEVICE);
    PubSub.unsubscribe(PubSubEvent.ERROR_REFETCH);
  }

  doRefreshData() {
    this.doFetchData('refresh');
  }

  doFetchData(type) {
    var that = this;
    if (type == 'refresh') {
      this.setState({
        refreshing: true,
      });
    }
    Helper.httpGET('/partner/devices', {
      context: this.props.context,
      success: data => {
        let devices = data.devices;
        if (isOwnerOrAdmin()) {
          devices.unshift({ type: 'add' });
        }
        that.setState({
          dataSource: devices,
        });
      },
      ensure: () => {
        this.setState({ show: true });
        if (type == 'refresh') {
          this.setState({
            refreshing: false,
          });
        }
      },
    });
  }

  renderAddDevice() {
    return (
      <View style={{ marginHorizontal: 4, marginBottom: 12 }}>
        <CardView
          withWaveBg={true}
          onChange={() => {
            this.actionSheet.show();
          }}
          styles={{
            width: this.props.context.winWidth / 2 - 26,
            padding: 12,
            borderRadius: mainRadius(),
            height: 100,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Ionicons name="add-outline" size={22} color={Tme('cardTextColor')} />
          <Text
            style={{
              fontSize: mainTitle(),
              color: Tme('cardTextColor'),
              fontWeight: '500',
            }}>
            {I18n.t('home.add_and_remove_device')}
          </Text>
        </CardView>
      </View>
    );
  }

  sheetClick(index) {
    if (index === 1) {
      const sn = HelperMemo.user_data.sn?.sn;
      if (sn) {
        ShowModalView({
          navigation: this.props.navigation,
          params: 'delete',
        });
      } else {
        AlertModal.alert(I18n.t('global.please_add_controller'));
      }
    }
    if (index === 0) {
      ShowModalView({
        navigation: this.props.navigation,
      });
    }
  }

  render() {
    const that = this;
    if (this.state.show) {
      return (
        <>
          <ActionSheet
            ref={o => (this.actionSheet = o)}
            options={[
              I18n.t('home.cancel'),
              I18n.t('home.add_device'),
              I18n.t('home.remove_zwave'),
            ]}
            cancelButtonIndex={0}
            userInterfaceStyle={IsDark() ? 'dark' : 'light'}
            theme="ios"
            styles={{
              cancelButtonBox: {
                height: 50,
                marginTop: 6,
                alignItems: 'center',
                justifyContent: 'center',
              },
              buttonBox: {
                height: 50,
                alignItems: 'center',
                justifyContent: 'center',
              },
            }}
            onPress={index => {
              this.sheetClick(index - 1);
            }}
          />
          <FlatList
            style={{
              flex: 1,
              paddingHorizontal: 16,
              paddingTop: 20,
              // backgroundColor: Tme('bgColor2'),
              // borderTopRightRadius: mainRadius(),
              // borderTopLeftRadius: mainRadius(),
            }}
            columnWrapperStyle={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}
            ref={flatList => (this._flatList = flatList)}
            data={this.state.dataSource}
            renderItem={({ item }) => {
              if (item.type === 'add') {
                return this.renderAddDevice();
              }
              return (
                <DeviceList item={item} navigation={that.props.navigation} />
              );
            }}
            numColumns={2}
            onEndReachedThreshold={0.1}
            showsVerticalScrollIndicator={false}
            refreshing={this.state.refreshing}
            onRefresh={this.doRefreshData.bind(this)}
            keyExtractor={(item, index) => index.toString()}
            // eslint-disable-next-line react/no-unstable-nested-components
            ListFooterComponent={() => <View style={{ height: 20 }} />}
          />
        </>
      );
    } else {
      return <SkeletionDevice />;
    }
  }
}
export default DeviceScreen;
