import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Dimensions,
} from 'react-native';
import { Helper } from '../Helper';
import I18n from '../I18n';
import { Tme, Colors } from '../ThemeStyle';
import * as Progress from 'react-native-progress';
import PahoManager from '../PahoManager';
import IdleTimerManager from 'react-native-idle-timer';
import CardView from '../share/CardView';
import _ from 'lodash';
import NavBarView from '../share/NavBarView';
import DeviceControl from '../DeviceControl';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import AlertModal from '../share/AlertModal';
import { hideLoading, showLoading } from '../../ILoading';
import PubSub from 'pubsub-js';
import { EVENT_GRPC_CONTROLLER_CHANGE, SCAN_CODE } from '../types/PubSubEvent';

class ZigbeeSmart extends Component {
  constructor(props) {
    super(props);

    this.state = {
      installKey: '',
      addressKey: '',
      indeterminate: true,
      progress: 0,
      time_show: false,
      input_show: true,
      wait: true,
      countdown: 60,
    };
    this.rq = true;
  }

  // countdown() {
  //   var time = 60;
  //   this.down = setInterval(() => {
  //     if (this.state.countdown > 0) {
  //       this.setState({
  //         countdown: time--,
  //       });
  //     } else {
  //       this.clearCountdown();
  //       AlertModal.alert(I18n.t('global.time_out'), '', [
  //         {
  //           text: I18n.t('home.confirm'),
  //           onPress: () => {
  //             this.props.navigation.goBack();
  //           },
  //         },
  //       ]);
  //     }
  //   }, 1000);
  // }

  clearCountdown() {
    if (this.down) {
      clearInterval(this.down);
    }
    if (this.pahoManager) {
      this.pahoManager.unmount();
    }
    this.pahoManager = null;
    this.down = null;
  }

  componentDidMount() {
    this.doFetchData();

    PubSub.subscribe(SCAN_CODE, data => {
      this.setState(
        {
          installKey: data.installKey,
          addressKey: data.addressKey,
        },
        () => {
          this._save();
        },
      );
    });

    IdleTimerManager.setIdleTimerDisabled(true);
    var that = this;

    PubSub.subscribe(
      EVENT_GRPC_CONTROLLER_CHANGE,
      payload => {
        if (payload.queue == 'controller') {
          switch (payload.data.op_state) {
            case 'set_pl_success':
              that.setState({
                wait: false,
                indeterminate: false,
              });
              break;
            case 'set_pl_failed':
              that.setState(
                {
                  indeterminate: true,
                  progress: 0,
                },
                () => {
                  AlertModal.alert(
                    I18n.t('home.add_device_fail'),
                    '',
                    [
                      {
                        text: 'OK',
                        onPress: () => {
                          that.props.navigation.goBack();
                        },
                      },
                    ],
                    { cancelable: false },
                  );
                },
              );
              break;
            // case 'starting':
            //   that.countdown();
            //   that.setState({
            //     time_show: true,
            //     wait: false,
            //     indeterminate: false,
            //   });
            //   break;
            case 'link_key':
              if (
                payload.data.error == 0 &&
                !_.isEmpty(payload.data.link_key)
              ) {
                new DeviceControl({
                  sn_id: this.props.route.params.sn.id,
                  addressKey: that.state.addressKey,
                  link_key: payload.data.link_key,
                }).addDeviceWithLinkKey();
              } else {
                AlertModal.alert(
                  I18n.t('home.warning_message') + ' key is error',
                  '',
                );
                that.setState({
                  time_show: false,
                  input_show: true,
                  wait: true,
                });
              }
              break;
            case 'device_added':
              console.log('device_added', payload.data);
              that.setState(
                {
                  progress: 1,
                },
                () => {
                  this.props.navigation.push('AddSuccess', {
                    uuid: payload.data.devices[0].index,
                    type: 'new',
                    sn_id: that.props.route.params.sn.id,
                  });
                },
              );
              break;
            default:
              break;
          }
        }
      },
    );
  }

  componentWillUnmount() {
    IdleTimerManager.setIdleTimerDisabled(false);
    this.clearCountdown();
    PubSub.unsubscribe(EVENT_GRPC_CONTROLLER_CHANGE);
    PubSub.unsubscribe(SCAN_CODE);
  }

  render() {
    return (

      <NavBarView>
        <KeyboardAwareScrollView
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="never"
          keyboardDismissMode="on-drag"
          innerRef={ref => {
            this.scroll = ref;
          }}
          style={{
            flex: 1,
            backgroundColor: Tme('bgColor'),
            padding: 20,
          }}>
          <CardView
            showMenu={false}
            styles={[
              {
                height: 560,
                width: Dimensions.get('window').width - 40,
                borderRadius: 8,
              },
            ]}>
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <View style={{ marginTop: 79 }}>
                <Text
                  style={{
                    fontSize: 16,
                    fontWeight: '600',
                    textAlign: 'center',
                    color: Tme('cardTextColor'),
                  }}>
                  Zigbee with Install Code
                </Text>
              </View>
              {this.state.time_show ? (
                <View style={{ marginTop: 16, alignItems: 'center' }}>
                  {this.state.wait ? (
                    <View style={{ padding: 16, marginBottom: 20 }}>
                      <Text
                        style={{
                          fontSize: 14,
                          fontWeight: '600',
                          textAlign: 'center',
                          color: Tme('cardTextColor'),
                        }}>
                        {I18n.t('global.add_device_wait')}
                      </Text>
                    </View>
                  ) : (
                    <View style={{ padding: 16, marginBottom: 20 }}>
                      <Text
                        style={{
                          fontSize: 14,
                          fontWeight: '600',
                          textAlign: 'center',
                          color: Tme('cardTextColor'),
                        }}>
                        {I18n.t('global.add_device_s2_title')}
                      </Text>
                    </View>
                  )}
                  <Progress.Circle
                    size={140}
                    color={Colors.MainColor}
                    progress={this.state.progress}
                    indeterminate={this.state.indeterminate}
                    showsText={true}
                    formatText={() => {
                      return `${parseInt(this.state.progress * 100, 10)}%`;
                    }}
                  />
                </View>
              ) : null}
              {this.state.input_show ? (
                <View>
                  <View style={{ padding: 16 }}>
                    <Text style={{ fontSize: 14, color: Tme('cardTextColor') }}>
                      {I18n.t('global.add_zigbee_smart_desp')}
                    </Text>
                  </View>
                  <View
                    style={{
                      padding: 16,
                    }}>
                    <Text
                      style={{
                        color: Tme('cardTextColor'),
                        marginBottom: 16,
                        fontSize: 16,
                        fontWeight: '600',
                      }}>
                      EUI64
                    </Text>
                    <View
                      style={[
                        {
                          borderColor: Tme('inputBorderColor'),
                          borderWidth: 1,
                          borderRadius: 16,
                        },
                      ]}>
                      <TextInput
                        underlineColorAndroid="transparent"
                        autoCorrect={false}
                        autoCapitalize="characters"
                        value={this.state.addressKey}
                        maxLength={16}
                        onChangeText={addressKey =>
                          this.setState({ addressKey })
                        }
                        style={[
                          Colors.TextInputStyle(),
                          {
                            fontSize: 15,
                            fontWeight: '600',
                            letterSpacing: 1,
                          },
                        ]}
                      />
                    </View>
                    <Text
                      style={{
                        color: Tme('cardTextColor'),
                        marginBottom: 16,
                        marginTop: 16,
                        fontSize: 16,
                        fontWeight: '600',
                      }}>
                      {I18n.t('global.ins_key')}
                    </Text>
                    <View
                      style={[
                        styles.account_view,
                        { borderColor: Tme('inputBorderColor') },
                      ]}>
                      <TextInput
                        underlineColorAndroid="transparent"
                        autoCorrect={false}
                        autoCapitalize="characters"
                        value={this.state.installKey}
                        multiline={true}
                        numberOfLines={2}
                        maxLength={36}
                        onChangeText={installKey =>
                          this.setState({ installKey })
                        }
                        style={{
                          fontSize: 15,
                          fontWeight: '600',
                          letterSpacing: 1,
                          height: 66,
                          marginLeft: 6,
                          marginRight: 6,
                          color: Tme('cardTextColor'),
                        }}
                      />
                    </View>
                  </View>
                  <View
                    style={{
                      marginTop: 20,
                      paddingHorizontal: 16,
                      alignItems: 'center',
                    }}>
                    <TouchableOpacity
                      activeOpacity={0.8}
                      onPress={this._save.bind(this)}
                      style={{
                        width: Dimensions.get('window').width / 2,
                        height: 40,
                        borderRadius: 20,
                        backgroundColor: Colors.MainColor,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <Text
                        style={{
                          fontSize: 16,
                          fontWeight: '600',
                          color: '#ffffff',
                          textAlign: 'center',
                        }}>
                        {I18n.t('home.save')}
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <View
                    style={{
                      marginTop: 20,
                      paddingHorizontal: 16,
                      alignItems: 'center',
                    }}>
                    <TouchableOpacity
                      activeOpacity={0.8}
                      onPress={this.scanCode.bind(this)}
                      style={{
                        width: Dimensions.get('window').width / 2,
                        height: 40,
                        borderRadius: 20,
                        borderColor: Colors.MainColor,
                        borderWidth: 1,
                        borderStyle: 'solid',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <Text
                        style={{
                          fontSize: 16,
                          fontWeight: '600',
                          color: Colors.MainColor,
                          textAlign: 'center',
                        }}>
                        {I18n.t('global.scan_key')}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : null}
            </View>
          </CardView>
        </KeyboardAwareScrollView>
      </NavBarView>
    );
  }

  scanCode() {
    this.props.navigation.push('ScanCodeScreen', {
      type: 'zigbee',
    });
  }

  close() {
    this.props.navigation.goBack();
  }

  _save() {
    var errors = [];
    var that = this;
    if (this.state.addressKey.length !== 16) {
      errors.push('mac key error');
    }

    if (this.state.installKey.length === 0) {
      errors.push('install key error');
    }
    if (errors.length > 0) {
      AlertModal.alert(errors.join('\n'));
    } else {
      showLoading();
      Helper.httpPOST(
        '/partner/controllers/zigbee_decode',
        {
          ensure: () => {
            hideLoading();
          },
          cloud: true,
          success: data => {
            that.setState({
              time_show: true,
              input_show: false,
            });
          },
          error: data => {
            AlertModal.alert('install key error');
          },
        },
        { install_code: this.state.installKey, eui64: this.state.addressKey },
      );
    }
  }

  doFetchData() {
    showLoading();
    var that = this;
    Helper.httpGET(
      Helper.urlWithQuery('/partner/controllers/conn_info', {
        sn_id: this.props.route.params.sn.id,
      }),
      {
        success: data => {
          if (_.isEmpty(data.mqtt)) {
            AlertModal.alert(
              I18n.t('home.add_device_fail'),
              '',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    that.props.navigation.goBack();
                  },
                },
              ],
              { cancelable: false },
            );
          } else {
            this.setState(
              {
                showView: true,
              },
              () => {
                this.pahoManager = new PahoManager({
                  mqtt: data.mqtt,
                  navigation: that.props.navigation,
                });
                this.pahoManager.mount();
              },
            );
          }
        },
        ensure: () => {
          hideLoading();
        },
      },
    );
  }
}
const styles = StyleSheet.create({
  account_view: {
    borderWidth: 1,
    borderRadius: 16,
    height: 66,
  },
});

export default ZigbeeSmart;
