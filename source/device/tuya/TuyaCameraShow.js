/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import {
  View,
  TouchableOpacity,
  BackHandler,
  Platform,
  ActivityIndicator,
  Text,
  Dimensions,
} from 'react-native';
import { Helper } from '../../Helper';

import { Tme, Colors, IsDark } from '../../ThemeStyle';
import NavBarView from '../../share/NavBarView';
// import CameraView from '../CameraView';
import I18n from '../../I18n';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Orientation from 'react-native-orientation-locker';
import ActionSheet from '@alessiocancian/react-native-actionsheet';
import TuyaCameraControl from './TuyaCameraControl';
import CardView from '../../share/CardView';
// import _ from 'lodash';
import AlertModal from '../../share/AlertModal';
import HeaderRightBtn from '../../share/HeaderRightBtn';
import { hideLoading, showLoading } from '../../../ILoading';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../../types/PubSubEvent';

class TuyaCameraShow extends Component {
  constructor(props) {
    super(props);

    this.state = {
      isShow: true,
      speak: false,
      isPlay: true,
      timeout: false,
      username: '',
      password: '',
      uid: '',
      countryCode: '',
      homeId: '',
      videoWidth: Dimensions.get('window').width,
      showActive: true,
      controllerShow: true,
      windowWidth: Dimensions.get('window').width,
      windowHeight: Dimensions.get('window').height,
      videoHeight: 0,
      videoIcon: 'portrait',
    };

    this.cameraControlRef = React.createRef();
    this.timeout = null;

    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          rightClick={this.rightClick.bind(this)}
          icon={{ name: 'menu', icon: 'MCIcons' }}
        />
      ),
    });
  }

  componentDidMount() {
    this.doFetchData();

    PubSub.subscribe(PubSubEvent.EVENT_APP_STATE_ACTIVE, () => {
      this.onAppStateActive();
    });
    PubSub.subscribe(PubSubEvent.EVENT_APP_STATE_BACKGROUND, () => {
      this.onAppStateBackground();
    });

  }

  componentWillUnmount() {
    PubSub.unsubscribe(PubSubEvent.EVENT_APP_STATE_ACTIVE);
    PubSub.unsubscribe(PubSubEvent.EVENT_APP_STATE_BACKGROUND);
  }

  rightClick() {
    this.actionSheet.show();
  }

  onAppStateActive = () => {
    this.startCamert();
  };

  onAppStateBackground = () => {
    this.stopCamera();
  };

  startCamert() {
    if (!this.state.isPlay) {
      if (Platform.OS === 'android') {
        this.setState({
          isPlay: true,
        });
      } else {
        this.setState(
          {
            isPlay: true,
            isShow: true,
          },
          () => { },
        );
      }
    }
  }

  stopCamera() {
    if (this.state.isPlay) {
      if (Platform.OS == 'android') {
        this.setState(
          {
            isPlay: false,
          },
          () => { },
        );
      } else {
        this.setState(
          {
            isPlay: false,
            isShow: false,
          },
          () => { },
        );
      }
    }
  }

  menu(index) {
    switch (index) {
      case 0:
        this.props.navigation.push('AddSuccess', {
          uuid: this.props.route.params.node.uuid,
          name: this.props.route.params.node.display_name,
          type: 'edit',

          title: I18n.t('home.edit_device'),
        });
        break;
      case 1:
        this.props.navigation.push('eventScreen', {
          device_name: this.props.route.params.node.display_name,
          device_uuid: this.props.route.params.node.uuid,
          title: I18n.t('home.event'),
        });
        break;
      case 2:
        AlertModal.alert(
          I18n.t('home.remove_device'),
          I18n.t('global.activate_sure'),
          [
            {
              text: I18n.t('home.cancel'),
              onPress: () => console.log('Cancel Pressed'),
              style: 'cancel',
            },
            {
              text: I18n.t('home.confirm'),
              onPress: () => {
                showLoading();
                Helper.httpPOST(
                  '/partner/devices/delete',
                  {
                    ensure: () => {
                      hideLoading();
                    },
                    success: data => {
                      this.props.navigation.goBack();
                    },
                  },
                  { uuid: this.props.route.params.node.uuid },
                );
              },
            },
          ],
          { cancelable: false },
        );
        break;
    }
  }

  onBackAndroid = () => {
    if (this.state.videoWidth == this.state.windowWidth) {
      BackHandler.exitApp();
    } else {
      this.changePortrait();
    }
    return true;
  };

  activeClick() {
    if (this.state.videoWidth == this.state.windowWidth) {
      this.changeLandscape();
    } else {
      this.changePortrait();
    }
  }

  changeLandscape() {
    if (Platform.OS == 'ios') {
      Orientation.unlockAllOrientations();
      Orientation.lockToLandscapeLeft();
    }
    this.props.navigation.setOptions({
      orientation: 'landscape',
    });
    this.setState({
      controllerShow: false,
      videoWidth: this.state.windowHeight,
      videoHeight: this.state.windowWidth,
      videoIcon: 'landscape',
    });
  }

  changePortrait() {
    if (Platform.OS == 'ios') {
      Orientation.unlockAllOrientations();
      Orientation.lockToPortrait();
    }
    this.props.navigation.setOptions({
      orientation: ['portrait'],
    });
    this.setState({
      controllerShow: true,
      videoWidth: this.state.windowWidth,
      videoHeight: 203,
      videoIcon: 'portrait',
    });
  }

  render() {
    return (
      <NavBarView>
        {this.state.timeout ? (
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              marginTop: 20,
              padding: 20,
            }}>
            <CardView
              styles={{
                padding: 20,
                height: 250,
                borderRadius: 8,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text style={{ fontSize: 16, color: Tme('cardTextColor') }}>
                {I18n.t('global.device_failed')}
              </Text>
            </CardView>
          </View>
        ) : this.state.isShow ? (
          <View style={{ flex: 1 }}>
            {/* <CameraView
                devId={this.props.route.params.node.index}
                homeId={this.state.homeId}
                style={{
                  height: this.state.videoHeight,
                  width: this.state.videoWidth,
                }}
              /> */}
            {this.state.showActive ? (
              this.state.videoIcon == 'portrait' ? (
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={this.activeClick.bind(this)}
                  style={{
                    height: 30,
                    width: 30,
                    borderRadius: 8,
                    backgroundColor: 'rgba(0,0,0,0.2)',
                    position: 'absolute',
                    right: 10,
                    top: 160,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Ionicons name="expand-sharp" size={24} color="white" />
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={this.activeClick.bind(this)}
                  style={{
                    height: 30,
                    width: 30,
                    borderRadius: 8,
                    backgroundColor: 'rgba(0,0,0,0.2)',
                    position: 'absolute',
                    right: 20,
                    top: 20,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Ionicons name="close-outline" size={24} color="white" />
                </TouchableOpacity>
              )
            ) : null}
            {!this.state.showActive && (
              <View
                style={{
                  height: 203,
                  width: '100%',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: Tme('bgColor'),
                }}>
                <ActivityIndicator color={Colors.MainColor} />
              </View>
            )}
            <TuyaCameraControl
              parent={this}
              ref={this.cameraControlRef}
              navigation={this.props.navigation}
              devid={this.props.route.params.node.index}
              uuid={this.props.route.params.node.uuid}
            />
          </View>
        ) : (
          <View style={{ flex: 1, backgroundColor: Tme('bgColor') }} />
        )}
        <ActionSheet
          ref={o => (this.actionSheet = o)}
          options={[
            I18n.t('home.cancel'),
            I18n.t('home.edit'),
            I18n.t('home.event'),
            I18n.t('home.remove_device'),
          ]}
          cancelButtonIndex={0}
          userInterfaceStyle={IsDark() ? 'dark' : 'light'}
          destructiveButtonIndex={3}
          theme="ios"
          styles={{
            cancelButtonBox: {
              height: 50,
              marginTop: 6,
              alignItems: 'center',
              justifyContent: 'center',
            },
            buttonBox: {
              height: 50,
              alignItems: 'center',
              justifyContent: 'center',
            },
          }}
          onPress={index => {
            this.menu(index - 1);
          }}
        />
      </NavBarView>
    );
  }

  doFetchData() { }

  onChange = e => {
    if (e.status == 'ok') {
      if (this.timeout) {
        clearTimeout(this.timeout);
        this.timeout = null;
      }
      if (e.data == 'init') {
      } else {
        console.log(e);
        this.setState(
          {
            videoHeight: 203,
            showActive: true,
          },
          () => {
            this.cameraControlRef.current.initCameraControl();
            hideLoading();
          },
        );
      }
    } else {
      hideLoading();
      AlertModal.alert(
        I18n.t('home.warning_message'),
        I18n.t('home.try_again'),
      );
    }
  };
}
export default TuyaCameraShow;
