import React from 'react';
import {
  View,
  Text,
  NativeEventEmitter,
  NativeModules,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { Helper } from '../../Helper';
import NavBarView from '../../share/NavBarView';
import CardView from '../../share/CardView';
import I18n from '../../I18n';
import { Tme, Colors } from '../../ThemeStyle';
import QRCode from 'react-native-qrcode-svg';
import PahoManager from '../../PahoManager';
import _ from 'lodash';
import AlertModal from '../../share/AlertModal';
import { hideLoading, showLoading } from '../../../ILoading';
import PubSub from 'pubsub-js';
import { EVENT_GRPC_CONTROLLER_CHANGE } from '../../types/PubSubEvent';

export default class AddTuyaCamera extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      qr: '',
    };
  }

  componentDidMount() {
    this.doFetchData();
    const tuyaModule = NativeModules.TuyaModule;
    console.log('tuyaModule', tuyaModule);
    if (!tuyaModule.addListener) {
      console.warn('tuyaModule 缺少 addListener 方法');
    }
    var tuyaEventEmitter = new NativeEventEmitter(tuyaModule);
    var isAlert = false;
    var that = this;
    this.tuyaEvent = tuyaEventEmitter.addListener('TuyaCameraEvent', data => {
      if (data.status == 'error' && !isAlert) {
        isAlert = true;
        AlertModal.alert(
          I18n.t('home.add_device_fail'),
          '',
          [
            {
              text: 'OK',
              onPress: () => {
                this.props.navigation.goBack();
              },
            },
          ],
          { cancelable: false },
        );
      }
      console.log('TuyaCarmra: ', JSON.stringify(data));
    });

    PubSub.subscribe(
      EVENT_GRPC_CONTROLLER_CHANGE,
      (msg, payload) => {
        if (payload.queue == 'controller') {
          switch (payload.data.op_state) {
            case 'failed':
              if (!that.alertd) {
                that.alertd = true;
                AlertModal.alert(
                  I18n.t('home.add_device_fail'),
                  '',
                  [
                    {
                      text: 'OK',
                      onPress: () => {
                        this.props.navigation.goBack();
                      },
                    },
                  ],
                  { cancelable: false },
                );
              }
              break;
            case 'device_added':
              if (!that.alertd) {
                that.alertd = true;
                this.props.navigation.push('AddSuccess', {
                  uuid: payload.data.devices[0].index,
                  sn_id: '',
                  type: 'new',
                });
              }
              break;
          }
        }
      },
    );
  }

  componentWillUnmount() {
    if (this.getQr) {
      NativeModules.TuyaModule.tuyaCameraAddSuccess();
    }
    NativeModules.TuyaModule.logoutTuya(e => {
      console.log(e);
    });
    if (this.tuyaEvent) {
      this.tuyaEvent.remove();
      this.tuyaEvent = null;
    }
    if (this.pahoManager) {
      this.pahoManager.unmount();
    }

    PubSub.unsubscribe(EVENT_GRPC_CONTROLLER_CHANGE);
  }

  doFetchData() {
    var that = this;
    showLoading();
    Helper.httpGET(
      Helper.urlWithQuery('/partner/controllers/conn_info', { tuya: 'tuya' }),
      {
        success: data => {
          if (_.isEmpty(data.mqtt) || _.isEmpty(data.tuya)) {
            AlertModal.alert(
              I18n.t('home.add_device_fail'),
              '',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    this.props.navigation.goBack();
                  },
                },
              ],
              { cancelable: false },
            );
          } else {
            this.setState(
              {
                mqtt: data.mqtt,
                tuya: data.tuya,
              },
              () => {
                this.pahoManager = new PahoManager({
                  mqtt: data.mqtt,
                  onConnect: this.onConnect.bind(this),
                  navigation: that.props.navigation,
                });
                this.pahoManager.mount();
              },
            );
          }
        },
        ensure: () => {
          hideLoading();
        },
      },
    );
  }

  onConnect() {
    this.loginTuya();
  }

  getToken = () => {
    const { tuya } = this.state;
    console.log('run gettoken', tuya.home_id);
    console.log('run ', this.props.type);
    NativeModules.TuyaModule.getToken(tuya.home_id, (status, data) => {
      if (data.status === 'ok') {
        this.getCameraQR(data.data);
      } else {
        if (data.errors == 'USER_SESSION_LOSS') {
          console.log('没有登录');
          this.loginTuya();
        } else {
          console.log('get token failed', data);
        }
      }
    });
  };

  getCameraQR = token => {
    console.log('tuya token', token);
    NativeModules.TuyaModule.getCameraQR(
      this.props.ssid,
      this.props.password,
      token,
      (status, data) => {
        if (data.status === 'qr') {
          this.setState({
            qr: data.data,
          });
          this.getQr = true;
        } else {
          console.log(data);
        }
      },
    );
  };

  loginTuya = () => {
    const { tuya } = this.state;
    NativeModules.TuyaModule.loginTuya(
      'uid',
      tuya.country_code.toString(),
      tuya.username,
      tuya.password,
      (status, data) => {
        if (data.status === 'ok') {
          console.log('登录成功！');
          this.getToken();
        }
      },
    );
  };

  render() {
    return (
      <NavBarView>
        <ScrollView style={{ flex: 1 }}>
          <View
            style={{
              flex: 1,
              backgroundColor: Tme('bgColor'),
              alignItems: 'center',
              marginTop: 20,
              padding: 20,
            }}>
            <CardView
              styles={{
                height: 480,
                padding: 20,
                alignItems: 'center',
                borderRadius: 8,
              }}>
              <View style={{ padding: 16, alignItems: 'center' }}>
                <Text
                  style={{
                    fontSize: 14,
                    fontWeight: '600',
                    textAlign: 'center',
                    color: Tme('cardTextColor'),
                  }}>
                  {I18n.t('global.add_device_s2_title')}
                </Text>
                <ActivityIndicator
                  size="small"
                  color={Colors.MainColor}
                  style={{ marginTop: 8 }}
                />
                <View style={{ height: 8 }} />
                {this.state.qr == '' ? null : (
                  <QRCode size={280} value={this.state.qr} />
                )}
              </View>
            </CardView>
          </View>
        </ScrollView>
      </NavBarView>
    );
  }
}
