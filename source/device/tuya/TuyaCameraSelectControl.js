import React from 'react';
import {View, ScrollView, NativeModules} from 'react-native';
import RadioButtons from '../../RadioButtons';
import NavBarView from '../../share/NavBarView';
import {Toast} from '../../Toast';
import {Tme} from '../../ThemeStyle';

export default class TuyaCameraSelectControl extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      checked: this.props.route.params.defaultKey,
      data: this.props.route.params.data,
    };
  }

  onChange(e) {
    switch (this.props.route.params.type) {
      case 'motionSensitivity':
        this.onMotionSensitivity(e);
        break;
      case 'pir':
        this.onPIR(e);
        break;
      case 'decibelSensitivity':
        this.onDecibelSensitivity(e);
        break;
    }
  }

  onMotionSensitivity(e) {
    NativeModules.TuyaModule.setMotionSensitivity(
      e.toString(),
      (status, data) => {
        if (data.status == 'ok') {
          Toast.show();
          this.props.navigation.goBack();
        }
      },
    );
  }

  onPIR(e) {
    NativeModules.TuyaModule.setPir(e.toString(), (status, data) => {
      if (data.status == 'ok') {
        Toast.show();
        this.props.navigation.goBack();
      }
    });
  }

  onDecibelSensitivity(e) {
    NativeModules.TuyaModule.setDecibelSensitivity(
      e.toString(),
      (status, data) => {
        if (data.status == 'ok') {
          Toast.show();
          this.props.navigation.goBack();
        }
      },
    );
  }

  render() {
    return (
      <NavBarView>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{
            flex: 1,
          }}>
          <View style={{marginTop: 20, backgroundColor: Tme('cardColor')}}>
            <RadioButtons
              data={this.state.data}
              defaultKey={this.state.checked}
              onChange={this.onChange.bind(this)}
            />
          </View>
        </ScrollView>
      </NavBarView>
    );
  }
}
