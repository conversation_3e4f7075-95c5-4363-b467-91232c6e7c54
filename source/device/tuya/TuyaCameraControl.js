import React from 'react';
import {
  View,
  TouchableOpacity,
  NativeModules,
  Text,
  ScrollView,
  StyleSheet,
} from 'react-native';
import {Tme, Colors} from '../../ThemeStyle';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {Toast} from '../../Toast';
import I18n from '../../I18n';

export default class TuyaCameraControl extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isMute: false,
      isHD: true,
      isRecord: false,
      showControl: false,
      controlView: false,
    };
  }

  muteClick() {
    NativeModules.TuyaModule.muteClick((status, data) => {
      if (data.status == 'ok') {
        this.setState({
          isMute: data.data == 1,
        });
      } else {
        console.log(data);
      }
    });
  }

  setVideoClarity() {
    NativeModules.TuyaModule.setVideoClarity((status, data) => {
      if (data.status == 'ok') {
        this.setState({
          isHD: data.data == 0,
        });
      } else {
        console.log(data);
      }
    });
  }

  speakClick() {
    var that = this;
    NativeModules.TuyaModule.speakClick((s, d) => {
      that.setState({
        speak: d.data,
      });
    });
  }

  recordClick() {
    NativeModules.TuyaModule.recordClick((d, s) => {
      if (this.state.isRecord) {
        if (!s.data) {
          Toast.show(I18n.t('tuya.camera_record_end'));
        }
      }
      this.setState({
        isRecord: s.data,
      });
    });
  }

  onControl(e) {
    switch (e) {
      case 'up':
        NativeModules.TuyaModule.onUp(() => {});
        break;
      case 'down':
        NativeModules.TuyaModule.onDown(() => {});
        break;
      case 'right':
        NativeModules.TuyaModule.onRight(() => {});
        break;
      case 'left':
        NativeModules.TuyaModule.onLeft(() => {});
        break;
    }
  }

  onStop() {
    NativeModules.TuyaModule.onStop(() => {
      console.log('stop');
    });
  }

  snapShotClick() {
    NativeModules.TuyaModule.snapShotClick((s, d) => {
      if (d.status == 'ok') {
        Toast.show(I18n.t('tuya.camera_photo_saved'));
      }
    });
  }

  initCameraControl() {
    NativeModules.TuyaModule.getControl(this.props.devid, (status, data) => {
      if (data.data === null || data.data === '') {
        this.setState({
          showControl: false,
        });
      } else {
        this.setState({
          showControl: true,
        });
      }
    });
  }

  onPIRchange(e) {
    var value = e.nativeEvent.selectedSegmentIndex;
    NativeModules.TuyaModule.setPir(value.toString(), (status, data) => {
      if (data.status == 'ok') {
        this.setState(
          {
            PIRValue: value,
          },
          () => {
            Toast.show();
          },
        );
      }
    });
  }

  onMotionSwitchChange(e) {
    var value = e.nativeEvent.selectedSegmentIndex;
    NativeModules.TuyaModule.setMotionSwitch(
      value == 1 ? true : false,
      (status, data) => {
        if (data.status == 'ok') {
          this.setState(
            {
              motionSwith: value,
            },
            () => {
              Toast.show();
            },
          );
        }
      },
    );
  }

  onDecibelSensitivity(e) {
    var value = e.nativeEvent.selectedSegmentIndex;
    NativeModules.TuyaModule.setDecibelSensitivity(
      value.toString(),
      (status, data) => {
        if (data.status == 'ok') {
          this.setState(
            {
              motionSensitivity: value,
            },
            () => {
              Toast.show();
            },
          );
        }
      },
    );
  }

  onControlClick() {
    if (this.state.showControl) {
      this.setState({
        controlView: true,
      });
    }
  }

  onCloseControl() {
    this.setState({
      controlView: false,
    });
  }

  getPlayBackData() {
    this.props.navigation.push('CameraPlayBackScreen', {
      title: I18n.t('device.playback'),
    });
  }

  onSettingsClick() {
    this.props.navigation.push('TuyaCameraState', {
      devid: this.props.devid,
      uuid: this.props.uuid,
      title: I18n.t('device.device_information'),
    });
  }

  render() {
    return (
      <View style={{flex: 1, backgroundColor: Tme('bgColor')}}>
        {this.state.controlView ? (
          <>
            <View style={{flex: 1}}>
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={this.onCloseControl.bind(this)}
                style={{
                  height: 50,
                  backgroundColor: Tme('cardColor'),
                  width: '100%',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MCIcons
                  name="chevron-double-down"
                  size={20}
                  color={Tme('textColor')}
                />
              </TouchableOpacity>
              <View
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginTop: 40,
                  flex: 1,
                }}>
                <View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <TouchableOpacity
                    onPressOut={this.onStop.bind(this)}
                    onPressIn={this.onControl.bind(this, 'up')}
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <MCIcons
                      name="arrow-up-bold-circle"
                      size={50}
                      color={Colors.MainColor}
                    />
                  </TouchableOpacity>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      width: 150,
                    }}>
                    <TouchableOpacity
                      onPressOut={this.onStop.bind(this)}
                      onPressIn={this.onControl.bind(this, 'left')}
                      style={{
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <MCIcons
                        name="arrow-left-bold-circle"
                        size={50}
                        color={Colors.MainColor}
                      />
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPressOut={this.onStop.bind(this)}
                      onPressIn={this.onControl.bind(this, 'right')}
                      style={{
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <MCIcons
                        name="arrow-right-bold-circle"
                        size={50}
                        color={Colors.MainColor}
                      />
                    </TouchableOpacity>
                  </View>
                  <TouchableOpacity
                    onPressOut={this.onStop.bind(this)}
                    onPressIn={this.onControl.bind(this, 'down')}
                    style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <MCIcons
                      name="arrow-down-bold-circle"
                      size={50}
                      color={Colors.MainColor}
                    />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </>
        ) : (
          <ScrollView
            style={{flex: 1}}
            contentContainerStyle={{flexDirection: 'row', flexWrap: 'wrap'}}>
            <TouchableOpacity
              activeOpacity={0.8}
              style={[styles.gard_row, {backgroundColor: Tme('cardColor')}]}
              onPress={this.muteClick.bind(this)}>
              {this.state.isMute ? (
                <MCIcons
                  name="microphone"
                  size={32}
                  color={Tme('cardTextColor')}
                />
              ) : (
                <MCIcons
                  name="microphone-off"
                  size={32}
                  color={Tme('cardTextColor')}
                />
              )}
              <Text style={{marginTop: 10, color: Tme('cardTextColor')}}>
                {I18n.t('tuya.audio')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.8}
              style={[
                styles.gard_row,
                {marginHorizontal: 1, backgroundColor: Tme('cardColor')},
              ]}
              onPress={this.speakClick.bind(this)}>
              {this.state.speak ? (
                <MCIcons
                  name="text-to-speech"
                  size={32}
                  color={Tme('cardTextColor')}
                />
              ) : (
                <MCIcons
                  name="text-to-speech-off"
                  size={32}
                  color={Tme('cardTextColor')}
                />
              )}
              <Text style={{marginTop: 10, color: Tme('cardTextColor')}}>
                {I18n.t('tuya.speak')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.8}
              style={[styles.gard_row, {backgroundColor: Tme('cardColor')}]}
              onPress={this.setVideoClarity.bind(this)}>
              {this.state.isHD ? (
                <MCIcons
                  name="high-definition"
                  size={32}
                  color={Tme('cardTextColor')}
                />
              ) : (
                <MCIcons
                  name="standard-definition"
                  size={32}
                  color={Tme('cardTextColor')}
                />
              )}
              <Text style={{marginTop: 10, color: Tme('cardTextColor')}}>
                {I18n.t('tuya.hd')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.8}
              style={[styles.gard_row, {backgroundColor: Tme('cardColor')}]}
              onPress={this.recordClick.bind(this)}>
              <MCIcons
                name="record-rec"
                size={32}
                color={
                  this.state.isRecord ? Colors.MainColor : Tme('cardTextColor')
                }
              />
              <Text style={{marginTop: 10, color: Tme('cardTextColor')}}>
                {I18n.t('tuya.record')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.8}
              style={[
                styles.gard_row,
                {marginHorizontal: 1, backgroundColor: Tme('cardColor')},
              ]}
              onPress={this.snapShotClick.bind(this)}>
              <MCIcons name="camera" size={32} color={Tme('cardTextColor')} />
              <Text style={{marginTop: 10, color: Tme('cardTextColor')}}>
                {I18n.t('tuya.screenshot')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.8}
              style={[styles.gard_row, {backgroundColor: Tme('cardColor')}]}
              onPress={this.onControlClick.bind(this)}>
              <MCIcons
                name="arrow-all"
                size={32}
                color={
                  this.state.showControl
                    ? Tme('cardTextColor')
                    : Tme('placeholder')
                }
              />
              <Text
                style={[
                  {marginTop: 10},
                  {
                    color: this.state.showControl
                      ? Tme('cardTextColor')
                      : Tme('placeholder'),
                  },
                ]}>
                {I18n.t('tuya.direction')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.8}
              style={[styles.gard_row, {backgroundColor: Tme('cardColor')}]}
              onPress={this.getPlayBackData.bind(this)}>
              <MaterialIcons
                name="settings-backup-restore"
                size={32}
                color={Tme('cardTextColor')}
              />
              <Text style={[{marginTop: 10, color: Tme('cardTextColor')}]}>
                {I18n.t('device.playback')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.8}
              style={[
                styles.gard_row,
                {marginHorizontal: 1, backgroundColor: Tme('cardColor')},
              ]}
              onPress={this.onSettingsClick.bind(this)}>
              <Ionicons
                name="settings-outline"
                size={32}
                color={Tme('cardTextColor')}
              />
              <Text style={[{marginTop: 10, color: Tme('cardTextColor')}]}>
                {I18n.t('home.setting')}
              </Text>
            </TouchableOpacity>
          </ScrollView>
        )}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  gard_row: {
    width: '33%',
    alignItems: 'center',
    justifyContent: 'center',
    height: 128,
    marginBottom: 1,
  },
});
