import React, {Component} from 'react';
import {
  View,
  TouchableOpacity,
  NativeModules,
  NativeEventEmitter,
  Dimensions,
  Platform,
} from 'react-native';
import NavBarView from '../../share/NavBarView';
import CameraPlayBackView from './CameraPlayBackView';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Orientation from 'react-native-orientation-locker';
import {hideLoading, showLoading} from '../../../ILoading';
import ScreenSizeContext from '../../../WindowResizeContext';

class PlayBackViewShow extends Component {
  constructor(props) {
    super(props);
    this.state = {
      videoHeight: 203,
      isPlay: true,
      showActive: false,
      activeWitch: Dimensions.get('window').width,
      windowWidth: Dimensions.get('window').width,
      windowHeight: Dimensions.get('window').height,
      videoShow: false,
    };
  }

  _onOrientationDidChange = orientation => {
    console.log(orientation);
  };

  static contextType = ScreenSizeContext;

  componentDidMount() {
    Orientation.addOrientationListener(this._onOrientationDidChange);

    if (Platform.OS == 'android') {
      showLoading();
      this.videoPlayerEmitter = new NativeEventEmitter(
        NativeModules.CameraPlayBackView,
      );
      this.nativeEvent = this.videoPlayerEmitter.addListener(
        'playBackChange',
        e => {
          if (e.status == 'ok') {
            if (e.data == 'init') {
              NativeModules.TuyaModule.playBackStart();
            }
            hideLoading();
            this.setState(
              {
                showActive: true,
                activeWitch: this.state.windowHeight,
                videoHeight: this.state.windowWidth,
              },
              () => {
                this.props.navigation.setOptions({
                  orientation: 'landscape',
                });
              },
            );
          }
        },
      );
    } else {
      this.iosPlayBack();
    }
  }

  componentWillUnmount() {
    Orientation.removeOrientationListener(this._onOrientationDidChange);
    NativeModules.TuyaModule.playBackStop();
    hideLoading();
  }

  iosPlayBack() {
    showLoading();
    Orientation.getAutoRotateState(rotationLock => {
      if (rotationLock) {
        console.log('unlockAllOrientations');
        Orientation.unlockAllOrientations();
      }
      Orientation.lockToLandscapeLeft();
    });
    NativeModules.TuyaModule.initPlayBack(
      this.props.route.params.startTime.toString(),
      this.props.route.params.endTime.toString(),
      (s, d) => {
        if (d.status == 'ok') {
          NativeModules.TuyaModule.playBackStart();
          this.setState({
            showActive: true,
            activeWitch: this.state.windowHeight,
            videoHeight: this.state.windowWidth,
          });
        }
        hideLoading();
        this.props.navigation.setOptions({
          orientation: 'landscape',
        });
      },
    );
  }

  render() {
    return (
      <NavBarView>
        <CameraPlayBackView
          startTime={this.props.route.params.startTime.toString()}
          endTime={this.props.route.params.endTime.toString()}
          style={{
            height: this.state.videoHeight,
            width: this.state.activeWitch,
          }}
        />
        {this.state.showActive ? (
          <View
            style={{
              height: 90,
              width: 40,
              position: 'absolute',
              top: 30,
              right: 30,
              zIndex: 10,
              alignItems: 'center',
              justifyContent: 'space-between',
              paddingLeft: 20,
            }}>
            <TouchableOpacity
              onPress={this.playClick.bind(this)}
              activeOpacity={0.8}
              style={{
                height: 30,
                width: 30,
                borderRadius: 8,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: 'rgba(0,0,0,0.2)',
              }}>
              {this.state.isPlay ? (
                <Ionicons name="pause" size={20} color="white" />
              ) : (
                <Ionicons name="play" size={20} color="white" />
              )}
            </TouchableOpacity>
            <TouchableOpacity
              onPress={this.stopClick.bind(this)}
              activeOpacity={0.8}
              style={{
                height: 30,
                width: 30,
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: 8,
                backgroundColor: 'rgba(0,0,0,0.2)',
              }}>
              <Ionicons name="stop" size={20} color="white" />
            </TouchableOpacity>
          </View>
        ) : null}
      </NavBarView>
    );
  }

  stopClick() {
    this.setState(
      {
        showActive: false,
        activeWitch: this.context.winWidth,
        videoHeight: 203,
      },
      () => {
        NativeModules.TuyaModule.stopClick((s, d) => {
          if (d.status == 'ok') {
            this.props.navigation.setOptions({
              orientation: 'portrait',
            });
          }
        });
        if (Platform.OS == 'ios') {
          Orientation.unlockAllOrientations();
          Orientation.lockToPortrait();
        }
        this.props.navigation.goBack();
      },
    );
  }

  playClick() {
    if (this.state.isPlay) {
      console.log('pause click');
      NativeModules.TuyaModule.pauseClick((s, d) => {
        if (d.status == 'ok') {
          this.setState({
            isPlay: false,
          });
        }
      });
    } else {
      console.log('resume click');
      NativeModules.TuyaModule.resumeClick((s, d) => {
        if (d.status == 'ok') {
          this.setState({isPlay: true});
        }
      });
    }
  }
}
export default PlayBackViewShow;
