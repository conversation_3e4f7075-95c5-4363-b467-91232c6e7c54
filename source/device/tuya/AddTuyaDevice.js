import React from 'react';
import {
  View,
  Text,
  NativeEventEmitter,
  NativeModules,
  ScrollView,
  Platform,
} from 'react-native';
import { Helper } from '../../Helper';
import NavBarView from '../../share/NavBarView';
import CardView from '../../share/CardView';
import I18n from '../../I18n';
import { Tme, Colors } from '../../ThemeStyle';
import * as Progress from 'react-native-progress';
import PahoManager from '../../PahoManager';
import _ from 'lodash';
import AlertModal from '../../share/AlertModal';
import { hideLoading, showLoading } from '../../../ILoading';
import PubSub from 'pubsub-js';
import { EVENT_GRPC_CONTROLLER_CHANGE } from '../../types/PubSubEvent';

export default class AddTuyaDevice extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      indeterminate: true,
      progress: 0,
      countdown: 60,
    };

    this.playCountdown = null;
  }

  componentDidMount() {
    this.doFetchData();
    const that = this;
    let isAlert = false;
    const tuyaEventEmitter = new NativeEventEmitter(NativeModules.TuyaModule);
    this.tuyaCameraEvent = tuyaEventEmitter.addListener(
      'TuyaCameraEvent',
      data => {
        if (data.status == 'error' && !isAlert) {
          isAlert = true;
          if (data.errors) {
            AlertModal.alert(data.errors);
          }
        }
        if (data.status == 'ok') {
          if (data.data) {
            if (that.props.route.params.type == 'tuya_ez') {
              this.setState({ progress: 0.5 });
            } else {
              if (!isAlert) {
                isAlert = true;
                this.props.navigation.push('AddSuccess', {
                  uuid: data.data,
                  type: 'new',
                  sn_id: '',
                });
              }
            }
          }
        }
      },
    );

    PubSub.subscribe(
      EVENT_GRPC_CONTROLLER_CHANGE,
      (msg, payload) => {
        if (payload.queue == 'controller') {
          switch (payload.data.op_state) {
            case 'failed':
              that.clearCountdown();
              that.setState(
                {
                  indeterminate: true,
                  progress: 0,
                },
                () => {
                  if (!that.alertd) {
                    that.alertd = true;
                    AlertModal.alert(
                      I18n.t('home.add_device_fail'),
                      '',
                      [
                        {
                          text: 'OK',
                          onPress: () => {
                            this.props.navigation.goBack();
                          },
                        },
                      ],
                      { cancelable: false },
                    );
                  }
                },
              );
              break;
            case 'device_added':
              that.clearCountdown();
              that.setState(
                {
                  progress: 1,
                },
                () => {
                  this.props.navigation.push('AddSuccess', {
                    uuid: payload.data.devices[0].index,
                    type: 'new',
                    sn_id: '',
                  });
                },
              );
              break;
          }
        }
      },
    );
  }

  componentWillUnmount() {
    this.tuyaCameraEvent.remove();
    if (this.pahoManager) {
      this.pahoManager.unmount();
    }
    PubSub.unsubscribe(EVENT_GRPC_CONTROLLER_CHANGE);
    this.clearCountdown();
    if (Platform.OS == 'ios') {
      NativeModules.TuyaModule.tuyaCameraAddSuccess();
    } else {
      NativeModules.TuyaModule.wifiAddSuccess();
    }
  }

  doFetchData() {
    showLoading();
    var that = this;
    Helper.httpGET(
      Helper.urlWithQuery('/partner/controllers/conn_info', {
        tuya: 'tuya',
      }),
      {
        success: data => {
          if (_.isEmpty(data.mqtt) || _.isEmpty(data.tuya)) {
            AlertModal.alert(
              I18n.t('home.add_device_fail'),
              '',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    this.props.navigation.goBack();
                  },
                },
              ],
              { cancelable: false },
            );
          } else {
            this.setState(
              {
                mqtt: data.mqtt,
                tuya: data.tuya,
              },
              () => {
                if (this.props.route.params.type == 'tuya_ez') {
                  this.pahoManager = new PahoManager({
                    mqtt: data.mqtt,
                    onConnect: this.onConnect.bind(this),
                    navigation: that.props.navigation,
                  });
                  this.pahoManager.mount();
                } else {
                  this.loginTuya();
                }
              },
            );
          }
        },
        ensure: () => {
          hideLoading();
        },
      },
    );
  }

  onConnect() {
    if (this.props.route.params.type == 'tuya_ez') {
      this.loginTuya();
    }
  }

  loginTuya = () => {
    const { tuya } = this.state;
    NativeModules.TuyaModule.loginTuya(
      'uid',
      tuya.country_code.toString(),
      tuya.username,
      tuya.password,
      (status, data) => {
        if (data.status === 'ok') {
          console.log('登录成功！');
          this.getToken();
        }
      },
    );
  };

  getToken = () => {
    const { tuya } = this.state;
    console.log('run gettoken', tuya.home_id);
    console.log('run ', this.props.route.params.type);
    const that = this;
    NativeModules.TuyaModule.getToken(
      tuya.home_id.toString(),
      (status, data) => {
        if (data.status === 'ok') {
          if (that.props.route.params.type == 'tuya_ez') {
            that.ezStart(data.data);
          } else {
            AlertModal.alert(I18n.t('wifi.add_tuya_wifi'), '', [
              {
                text: I18n.t('home.cancel'),
                onPress: () => {
                  this.props.navigation.goBack();
                },
              },
              {
                text: I18n.t('home.confirm'),
                onPress: () => {
                  that.apStart(data.data);
                },
              },
            ]);
          }
        } else {
          if (data.data == 'USER_SESSION_LOSS') {
            console.log('没有登录');
            this.loginTuya();
          } else {
            console.log(JSON.stringify(data));
          }
        }
      },
    );
  };

  playTime = () => {
    this.setState({
      indeterminate: false,
    });
    this.playCountdown = setInterval(() => {
      if (this.state.countdown > 0) {
        this.setState({
          countdown: this.state.countdown - 1,
        });
      } else {
        this.clearCountdown();
        AlertModal.alert('', I18n.t('global.time_out'), [
          {
            text: I18n.t('home.confirm'),
            onPress: () => {
              this.props.navigation.goBack();
            },
          },
        ]);
      }
    }, 1000);
  };

  clearCountdown = () => {
    if (this.playCountdown) {
      clearInterval(this.playCountdown);
    }
    this.playCountdown = null;
  };

  ezStart = token => {
    this.playTime();
    NativeModules.TuyaModule.tuyaEZ(
      this.props.route.params.ssid,
      this.props.route.params.password,
      token,
    );
  };

  apStart = token => {
    NativeModules.TuyaModule.tuyaAP(
      this.props.route.params.ssid,
      this.props.route.params.password,
      token,
    );
    this.setState({
      indeterminate: false,
      progress: 0.5,
    });
  };

  render() {
    return (
      <NavBarView>
        <ScrollView style={{ flex: 1 }}>
          <View
            style={{
              flex: 1,
              backgroundColor: Tme('bgColor'),
              alignItems: 'center',
              marginTop: 20,
              padding: 20,
            }}>
            <CardView
              styles={{
                height: 320,
                padding: 20,
                alignItems: 'center',
                borderRadius: 8,
              }}>
              <View style={{ padding: 16 }}>
                <Text
                  style={{
                    fontSize: 14,
                    fontWeight: '600',
                    textAlign: 'center',
                    color: Tme('cardTextColor'),
                  }}>
                  {I18n.t('global.add_device_s2_title')}
                </Text>
              </View>
              <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                <Progress.Circle
                  size={140}
                  progress={this.state.progress}
                  indeterminate={this.state.indeterminate}
                  showsText={true}
                  color={Colors.MainColor}
                  formatText={() => {
                    return `${this.state.countdown}`;
                  }}
                />
              </View>
            </CardView>
          </View>
        </ScrollView>
      </NavBarView>
    );
  }
}
