import React, {useEffect, useState} from 'react';
import {
  View,
  FlatList,
  NativeModules,
  Text,
  TouchableOpacity,
  Platform,
  SafeAreaView,
} from 'react-native';
import moment from 'moment';
import {Tme} from '../../ThemeStyle';
import I18n from '../../I18n';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

export default function CameraPlayBackScreen(props) {
  const [lists, setLists] = useState([]);

  useEffect(() => {
    doFatchData();
  }, []);

  const doFatchData = () => {
    const date = moment().format('YYYY-M-D').split('-');
    NativeModules.TuyaModule.getPlayBackData(
      date[0],
      date[1],
      date[2],
      (s, d) => {
        var data = null;
        if (Platform.OS == 'android') {
          data = JSON.parse(d.data);
        } else {
          data = d.data;
        }
        setLists(data.items);
      },
    );
  };
  const onPress = (startTime, endTime) => {
    props.navigation.push('PlayBackViewShow', {
      startTime: startTime,
      endTime: endTime,
      title: I18n.t('device.playback'),
    });
  };

  const renderRow = ({item, index, separators}) => {
    var duration = moment(item.endTime * 1000) - moment(item.startTime * 1000);
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => onPress(item.startTime, item.endTime)}
        style={{
          marginBottom: 8,
          borderRadius: 4,
          backgroundColor: Tme('cardColor'),
          paddingHorizontal: 20,
          paddingVertical: 8,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <View>
          <Text style={{color: Tme('cardTextColor')}}>
            {moment(item.startTime * 1000).format('HH:mm:ss')}
          </Text>
          <Text style={{color: Tme('cardTextColor')}}>
            {moment('2000-01-01 00:00:00')
              .add(moment.duration(duration))
              .format('HH:mm:ss')}
          </Text>
        </View>
        <MaterialIcons
          name="keyboard-arrow-right"
          size={20}
          color={Tme('textColor')}
        />
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: Tme('bgColor'),
      }}>
      <FlatList
        style={{
          flex: 1,
          marginTop: 20,
          paddingHorizontal: 16,
        }}
        data={lists}
        renderItem={renderRow}
        numColumns={1}
        onEndReachedThreshold={0.1}
        keyExtractor={(item, index) => index.toString()}
        ListFooterComponent={<View style={{height: 10}} />}
      />
    </SafeAreaView>
  );
}
