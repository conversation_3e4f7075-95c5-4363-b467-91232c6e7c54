import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Platform,
  RefreshControl,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import {Tme, Colors} from '../../ThemeStyle';
import {Helper} from '../../Helper';

import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import DeviceControl from '../../DeviceControl';
import I18n from '../../I18n';
import _ from 'lodash';
import SwitchBtn from '../../share/SwitchBtn';
import AlertModal from '../../share/AlertModal';
import {hideLoading, showLoading} from '../../../ILoading';

export default class TuyaCameraState extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      isDataLoaded: false,
      sdState: I18n.t('tuya.sd_no'),
      storage: '0|0|0',
      signal: 0,
      motionSwith: false, //true为开启移动侦测报警，false为关闭移动侦测报警
      showMotion: false,
      showPIR: true,
      PIRValue: 0, //0：关闭，1：低灵敏度，2：中灵敏度，3：高灵敏度
      showMotionSensitivity: false,
      motionSensitivity: 0, //0：低，1：中，2：高
      showDecibelSwitch: false, //true 为打开声音侦测报警，false 为关闭声音侦测报警
      decibelSwitch: false,
      showDecibelSensitivity: false,
      decibelSensitivity: 0, //0：低灵敏度，1：高灵敏度
      motionTracking: false,
      motionTrackingSwitch: false,
      node: null,
    };
  }

  componentDidMount() {
    var that = this;
    this.doFetchData('');

    if (Platform.OS == 'ios') {
    }
    this.getSdState();
    if (Platform.OS == 'ios') {
    }

    this.naviveEvent = this.props.navigation.addListener('focus', () => {
      this.initCameraControl();
    });
  }

  componentWillUnmount() {
    if (this.naviveEvent) {
      this.naviveEvent();
    }
  }

  initCameraControl() {}

  getSdState = () => {
    //1：正常，2：异常（ SD 卡损坏或格式不对），3：空间不足， 4：正在格式化，5：无 SD 卡
    // Tuya.getSdCardStatus((status, data) => {
    //   if (data.status === 'ok') {
    //     let d = '';
    //     switch (data.data) {
    //       case 1:
    //         d = I18n.t('tuya.ok_s');
    //         break;
    //       case 2:
    //         d = I18n.t('tuya.sd_error');
    //         break;
    //       case 3:
    //         d = I18n.t('tuya.sd_storage_error');
    //         break;
    //       case 4:
    //         d = I18n.t('tuya.sd_formating');
    //         break;
    //       case 5:
    //         d = I18n.t('tuya.sd_no');
    //         break;
    //     }
    //     this.setState({
    //       sdState: d,
    //     });
    //   }
    // });
    // Tuya.getSdStorage((status, data) => {
    //   if (data.status === 'ok') {
    //     this.setState({
    //       storage: data.data,
    //     });
    //   }
    // });
    // if (Platform.OS == 'android') {
    //   Tuya.getWifiSignal(this.props.route.params.devId, (status, data) => {
    //     if (data.status === 'ok') {
    //       this.setState({
    //         signal: data.data,
    //       });
    //     }
    //   });
    // }
  };

  refresh = () => {
    this.doFetchData('refresh');
  };

  doFetchData = type => {
    if (type === 'refresh') {
      this.setState({
        isDataLoaded: true,
      });
    } else {
      showLoading();
    }
    Helper.httpGET('/partner/devices/' + this.props.route.params.uuid, {
      success: data => {
        this.setState({
          node: data.device,
        });
      },
      ensure: () => {
        if (type === 'refresh') {
          this.setState({
            isDataLoaded: false,
          });
        } else {
          hideLoading();
        }
      },
      error: data => {
        AlertModal.alert('', _.uniq(data).join('\n'), [
          {
            text: 'cancel',
            onPress: () => this.props.navigation.goBack(),
            style: 'cancel',
          },
        ]);
      },
    });
  };

  onMotionSwitchChange(e) {}

  onDecibelSwitch(e) {}

  showPIRValue() {
    let value = '';
    switch (this.state.PIRValue.toString()) {
      case '0':
        value = I18n.t('spec.off_b');
        break;
      case '1':
        value = I18n.t('tuya.low');
        break;
      case '2':
        value = I18n.t('tuya.middle');
        break;
      case '3':
        value = I18n.t('tuya.high');
        break;
    }
    return value;
  }

  showMotionSensitivityValue() {
    let value = '';
    switch (this.state.motionSensitivity.toString()) {
      case '0':
        value = I18n.t('tuya.low');
        break;
      case '1':
        value = I18n.t('tuya.middle');
        break;
      case '2':
        value = I18n.t('tuya.high');
        break;
    }
    return value;
  }

  showdecibelSensitivityValue() {
    let value = '';
    switch (this.state.decibelSensitivity.toString()) {
      case '0':
        value = I18n.t('tuya.low');
        break;
      case '1':
        value = I18n.t('tuya.high');
        break;
    }
    return value;
  }

  onPIRclick() {
    this.props.navigation.push('TuyaCameraSelectControl', {
      devid: this.props.route.params.devid,
      type: 'pir',
      defaultKey: this.state.PIRValue,
      data: [
        {key: 0, value: I18n.t('spec.off_b')},
        {key: 1, value: I18n.t('tuya.low')},
        {key: 2, value: I18n.t('tuya.middle')},
        {key: 3, value: I18n.t('tuya.high')},
      ],
      title: I18n.t('tuya.pir_switch'),
    });
  }

  onMotionSensitivity() {
    this.props.navigation.push('TuyaCameraSelectControl', {
      devid: this.props.route.params.devid,
      type: 'motionSensitivity',
      defaultKey: this.state.motionSensitivity,
      data: [
        {key: 0, value: I18n.t('tuya.low')},
        {key: 1, value: I18n.t('tuya.middle')},
        {key: 2, value: I18n.t('tuya.high')},
      ],

      title: I18n.t('tuya.motion_sensitivity'),
    });
  }

  onDecibelclick() {
    this.props.navigation.push('TuyaCameraSelectControl', {
      devid: this.props.route.params.devid,
      type: 'decibelSensitivity',
      defaultKey: this.state.motionSensitivity,
      data: [
        {key: 0, value: I18n.t('tuya.low')},
        {key: 1, value: I18n.t('tuya.middle')},
        {key: 2, value: I18n.t('tuya.high')},
      ],

      title: I18n.t('tuya.sound_sensitivity'),
    });
  }

  onMotionTrackingSwitch(e) {}

  showItem = () => {
    let html = [];
    this.state.node &&
      this.state.node.cc_specs.map((spec, index) => {
        html.push(
          <ShowItem
            node={this.state.node}
            spec={spec}
            key={index}
            navigation={this.props.navigation}
          />,
        );
      });
    return html;
  };

  render() {
    return (
      <SafeAreaView style={{flex: 1, backgroundColor: Tme('bgColor')}}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{flex: 1}}
          refreshControl={
            <RefreshControl
              refreshing={this.state.isDataLoaded}
              onRefresh={this.refresh.bind(this)}
            />
          }>
          <View style={{height: 20}} />
          <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
            <View style={[styles.row]}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('tuya.sd_state')}
              </Text>
              <Text style={{color: Tme('cardTextColor')}}>
                {this.state.sdState}
              </Text>
            </View>
          </View>
          <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
            <View style={styles.row}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('tuya.sd_storage')}
              </Text>
              <Text style={{color: Tme('cardTextColor')}}>
                {this.state.storage}
              </Text>
            </View>
          </View>
          <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
            <View style={styles.row}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('tuya.wifi_signal')}
              </Text>
              <Text style={{color: Tme('cardTextColor')}}>
                {this.state.signal}
              </Text>
            </View>
          </View>
          {this.state.showMotion && (
            <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
              <View style={styles.row}>
                <Text style={{color: Tme('cardTextColor')}}>
                  {I18n.t('tuya.motion_switch')}
                </Text>
                <SwitchBtn
                  cusStyle={{marginLeft: -3}}
                  trackColor={{true: Colors.MainColor}}
                  value={this.state.motionSwith}
                  change={this.onMotionSwitchChange.bind(this)}
                />
              </View>
            </View>
          )}
          {this.state.showDecibelSwitch && (
            <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
              <View style={styles.row}>
                <Text style={{color: Tme('cardTextColor')}}>
                  {I18n.t('tuya.sound_switch')}
                </Text>
                <SwitchBtn
                  cusStyle={{marginLeft: -3}}
                  trackColor={{true: Colors.MainColor}}
                  value={this.state.decibelSwitch}
                  change={this.onDecibelSwitch.bind(this)}
                />
              </View>
            </View>
          )}
          {this.state.showPIR && (
            <TouchableOpacity
              onPress={this.onPIRclick.bind(this)}
              activeOpacity={0.8}
              style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
              <View
                style={{
                  flexDirection: 'row',
                  paddingLeft: 20,
                  paddingRight: 16,
                  paddingVertical: 16,
                  justifyContent: 'space-between',
                }}>
                <Text style={{color: Tme('cardTextColor')}}>
                  {I18n.t('tuya.pir_switch')}
                </Text>
                <View style={{flexDirection: 'row'}}>
                  <Text style={{color: Tme('cardTextColor')}}>
                    {this.showPIRValue()}
                  </Text>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={16}
                    color={Tme('textColor')}
                  />
                </View>
              </View>
            </TouchableOpacity>
          )}
          {this.state.showMotionSensitivity && (
            <TouchableOpacity
              onPress={this.onMotionSensitivity.bind(this)}
              activeOpacity={0.8}
              style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
              <View
                style={{
                  flexDirection: 'row',
                  paddingLeft: 20,
                  paddingRight: 16,
                  paddingVertical: 16,
                  justifyContent: 'space-between',
                }}>
                <Text style={{color: Tme('cardTextColor')}}>
                  {I18n.t('tuya.motion_sensitivity')}
                </Text>
                <View style={{flexDirection: 'row'}}>
                  <Text style={{color: Tme('cardTextColor')}}>
                    {this.showMotionSensitivityValue()}
                  </Text>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={16}
                    color={Tme('textColor')}
                  />
                </View>
              </View>
            </TouchableOpacity>
          )}
          {this.state.showDecibelSensitivity && (
            <TouchableOpacity
              onPress={this.onDecibelclick.bind(this)}
              activeOpacity={0.8}
              style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
              <View
                style={{
                  flexDirection: 'row',
                  paddingLeft: 20,
                  paddingRight: 16,
                  paddingVertical: 16,
                  justifyContent: 'space-between',
                }}>
                <Text style={{color: Tme('cardTextColor')}}>
                  {I18n.t('tuya.sound_sensitivity')}
                </Text>
                <View style={{flexDirection: 'row'}}>
                  <Text style={{color: Tme('cardTextColor')}}>
                    {this.showdecibelSensitivityValue()}
                  </Text>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={16}
                    color={Tme('textColor')}
                  />
                </View>
              </View>
            </TouchableOpacity>
          )}
          {this.state.motionTracking && (
            <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
              <View style={styles.row}>
                <Text style={{color: Tme('cardTextColor')}}>
                  {I18n.t('tuya.motion_tracking')}
                </Text>
                <SwitchBtn
                  cusStyle={{marginLeft: -3}}
                  trackColor={{true: Colors.MainColor}}
                  value={this.state.motionTrackingSwitch}
                  change={this.onMotionTrackingSwitch.bind(this)}
                />
              </View>
            </View>
          )}
          <View style={{marginTop: 20}}>{this.showItem()}</View>
        </ScrollView>
      </SafeAreaView>
    );
  }
}

class ShowItem extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      value: this.props.spec.value,
    };
  }

  onClick = () => {
    this.props.navigation.push('eventScreen', {
      device_uuid: this.props.node.uuid,
      device_name: this.props.node.display_name,
      title: I18n.t('home.event'),
    });
  };

  setCheckBoxSwitch = (e, spec) => {
    new DeviceControl({
      spec: spec,
      param: e,
      sn_id: this.props.node.sn_id,
      runCMD: true,
    }).other();
    this.setState({
      value: e,
    });
  };

  render() {
    let html = [];
    const {spec, node} = this.props;
    if (spec.spec_type === 1) {
      var valueText;
      if (node.dv_type == 'zigbee' || node.dv_type == 'tuya') {
        valueText = spec.value + (spec.scale ? spec.scale : '');
      } else if (node.dv_type == 'zwave') {
        valueText = spec.battery_charge_text;
      } else {
        valueText = Helper.i(spec.battery_charge_text);
      }

      html.push(
        <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
          <View style={styles.row}>
            <Text style={{color: Tme('cardTextColor')}}>
              {Helper.i(spec.name)}
            </Text>
            <Text style={{color: Tme('cardTextColor')}}>{valueText}</Text>
          </View>
        </View>,
      );
    } else if (spec.options.length == 2) {
      html.push(
        <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
          <View style={styles.row}>
            <Text style={{color: Tme('cardTextColor')}}>
              {Helper.i(spec.name)}
            </Text>
            <SwitchBtn
              cusStyle={{marginLeft: -3}}
              trackColor={{true: Colors.MainColor}}
              value={this.state.value}
              change={e => this.setCheckBoxSwitch(e, spec)}
            />
          </View>
        </View>,
      );
    } else if (spec.spec_type === 3) {
      html.push(
        <TouchableOpacity
          onPress={this.onClick.bind(this)}
          style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
          <View style={styles.row}>
            <Text style={{color: Tme('cardTextColor')}}>
              {Helper.i(spec.name)}
            </Text>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={20}
              color={Tme('textColor')}
            />
          </View>
        </TouchableOpacity>,
      );
    }
    return html;
  }
}

const styles = StyleSheet.create({
  row: {
    marginHorizontal: 20,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
});
