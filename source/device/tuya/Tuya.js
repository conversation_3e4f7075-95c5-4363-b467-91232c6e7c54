import {NativeModules} from 'react-native';

const TuyaModule = NativeModules.TuyaModule;

export default class Tuya {
  /*
   * 获取tuya SD卡状态
   *
   * @param {Function} callback = (state,data) => {
   *  data 1：正常，2：异常（ SD 卡损坏或格式不对），3：空间不足， 4：正在格式化，5：无 SD 卡
   * }
   * */
  static getSdCardStatus(callback) {
    TuyaModule.getSdCardStatus(callback);
  }

  /**
   * 设置camera 录像模式
   *  model : "event"
   */
  static setCameraModel(model, callback) {
    TuyaModule.setCameraModel(model, callback);
  }

  /**
   * 设置Camera 本地存储开关
   */

  static setSdRecordSwitch(callback) {
    TuyaModule.setSdRecordSwitch(callback);
  }

  /**
   *  获取Camera sd卡空间
   */

  static getSdStorage(callback) {
    TuyaModule.getSdStorage(callback);
  }

  /**
   *  获取设备wifi信号强度
   */

  static getWifiSignal(devId, callback) {
    TuyaModule.getWifiSignal(devId, callback);
  }
}
