/* eslint-disable react/no-unstable-nested-components */
import React, {Component} from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  BackHandler,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Keyboard,
} from 'react-native';
import I18n from '../I18n';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {Tme, Colors} from '../ThemeStyle';
import {Helper, HelperMemo} from '../Helper';
import _ from 'lodash';
import NavBarView from '../share/NavBarView';
import CardView from '../share/CardView';
import * as Progress from 'react-native-progress';
import {NotificationCenter, EVENT_DEVICE} from '../NotificationCenter';
import {Toast} from '../Toast';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {hideLoading, showLoading} from '../../ILoading';

export default class AddSuccess extends Component {
  constructor(props) {
    super(props);

    this.state = {
      name:
        this.props.route.params.type == 'new'
          ? ''
          : this.props.route.params.name || '',
      indeterminate: true,
      winWidth: this.initWidth(Dimensions.get('window')),
    };
    this.checkNode = 0;
    this.setTime = null;

    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this.changeName();
  }

  componentDidMount() {
    var that = this;
    this.DimensionsEvent = Dimensions.addEventListener(
      'change',
      this._onResize.bind(this),
    );
    this.backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (that.props.route.params.type == 'new') {
        return true;
      } else {
        return false;
      }
    });
    if (this.props.route.params.type == 'edit') {
      setTimeout(() => {
        this.textInput.focus();
      }, 600);
    } else {
      this.nodeNewCheck();
    }
  }

  componentWillUnmount() {
    this.backHandler.remove();
    if (this.setTime !== null) {
      clearTimeout(this.setTime);
      this.setTime = null;
    }
    if (this.DimensionsEvent) {
      this.DimensionsEvent.remove();
    }
  }

  initWidth(window) {
    if (Platform.OS == 'android') {
      return window.width;
    } else {
      if (window.width > window.height) {
        return (
          window.width -
          HelperMemo.STATUS_BAR_HEIGHT -
          HelperMemo.BOTTOM_BAR_HEIGHT -
          22
        );
      } else {
        return window.width;
      }
    }
  }

  _onResize({window}) {
    this.setState({
      winWidth: this.initWidth(window),
      winHeight: window.height,
      width: window.width,
    });
  }

  nodeNewCheck() {
    var that = this;
    Helper.httpGET(
      Helper.urlWithQuery('/partner/devices/check_node', {
        index: this.props.route.params.uuid,
        sn_id: this.props.route.params.sn_id,
      }),
      {
        success: data => {
          if (that.setTime !== null) {
            clearTimeout(that.setTime);
            that.setTime = null;
          }
          that.setState({
            indeterminate: false,
            name: data.product_name,
          });
        },
        error: data => {
          if (that.checkNode < 4) {
            that.checkNode = that.checkNode + 1;
            that.setTime = setTimeout(() => {
              that.nodeNewCheck();
            }, 3000);
          } else {
            AlertModal.alert(I18n.t('home.add_device_fail'), '', [
              {
                text: I18n.t('home.confirm'),
                onPress: () => {
                  this.props.navigation.popToTop();
                },
              },
            ]);
          }
        },
      },
    );
  }

  render() {
    var html = '';
    if (this.props.route.params.type === 'new') {
      if (this.state.indeterminate) {
        html = (
          <CardView
            withWaveBg={true}
            styles={{
              height: 180,
              padding: 20,
              borderRadius: 8,
              alignItems: 'center',
            }}>
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                width: this.state.winWidth - 100,
              }}>
              <Progress.Circle
                size={140}
                color={Colors.MainColor}
                progress={0}
                indeterminate={this.state.indeterminate}
                showsText={true}
              />
            </View>
          </CardView>
        );
      } else {
        html = (
          <CardView
            withWaveBg={true}
            styles={{
              height: 400,
              padding: 20,
              borderRadius: 8,
              alignItems: 'center',
            }}>
            <View
              style={{
                justifyContent: 'center',
                marginTop: 75,
                marginBottom: 50,
                alignItems: 'center',
                height: 100,
              }}>
              <Ionicons
                name="checkmark-circle"
                size={100}
                color={Colors.MainColor}
              />
              <Text
                style={{
                  fontSize: 17,
                  color: Tme('cardTextColor'),
                  marginTop: 16,
                }}>
                {I18n.t('home.add_success')}
              </Text>
            </View>
            <View
              style={[
                styles.account_view,
                {borderColor: Tme('inputBorderColor')},
              ]}>
              <TextInput
                ref={ref => {
                  this.textInput = ref;
                }}
                clearButtonMode="always"
                placeholderTextColor={Tme('placeholder')}
                style={[
                  Colors.TextInputStyle(),
                  {width: this.state.winWidth - 100},
                ]}
                autoCapitalize="none"
                underlineColorAndroid="transparent"
                placeholder={I18n.t('home.device_name')}
                value={this.state.name}
                returnKeyType="go"
                onSubmitEditing={this.changeName.bind(this)}
                onChangeText={name => {
                  this.setState({name});
                }}
              />
            </View>
          </CardView>
        );
      }
    }
    return (
      <NavBarView>
        <ScrollView
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}>
          <KeyboardAvoidingView
            style={{flex: 1, alignItems: 'center', padding: 20}}>
            {this.props.route.params.type === 'edit' ? (
              <CardView
                withWaveBg={true}
                styles={{
                  height: 120,
                  padding: 20,
                  borderRadius: 8,
                  alignItems: 'center',
                }}>
                <View
                  style={[
                    styles.account_view,
                    {borderColor: Tme('inputBorderColor')},
                  ]}>
                  <TextInput
                    ref={ref => {
                      this.textInput = ref;
                    }}
                    clearButtonMode="always"
                    placeholderTextColor={Tme('placeholder')}
                    style={[
                      Colors.TextInputStyle(),
                      {width: this.state.winWidth - 100},
                    ]}
                    autoCapitalize="none"
                    underlineColorAndroid="transparent"
                    placeholder={I18n.t('home.device_name')}
                    value={this.state.name}
                    returnKeyType="go"
                    onSubmitEditing={this.changeName.bind(this)}
                    onChangeText={name => {
                      this.setState({name});
                    }}
                  />
                </View>
              </CardView>
            ) : (
              html
            )}
          </KeyboardAvoidingView>
        </ScrollView>
      </NavBarView>
    );
  }

  changeName() {
    // 改变设备名称，收起键盘
    var that = this;
    if (this.state.name !== '') {
      showLoading();
      Keyboard.dismiss();
      Helper.httpPOST(
        '/partner/devices/change_name',
        {
          ensure: () => {
            hideLoading();
          },
          success: data => {
            if (that.props.route.params.type == 'new') {
              if (that.props.route.params.from === 'ipc') {
                that.props.navigation.replace('ProductScreen', {
                  title: I18n.t('pay.charge'),
                });
              } else {
                that.props.navigation.popToTop();
              }
            } else {
              that.props.navigation.goBack();
            }
            Toast.show();
            NotificationCenter.dispatchEvent(EVENT_DEVICE);
          },
          error: data => {
            if (_.isArray(data)) {
              AlertModal.alert(_.uniq(data).join('\n'));
            } else {
              AlertModal.alert(data);
            }
          },
        },
        {
          sn_id: that.props.route.params.sn_id,
          name: that.state.name,
          uuid: that.props.route.params.uuid,
          type: that.props.route.params.type,
        },
      );
    } else {
      AlertModal.alert(
        I18n.t('home.warning_message'),
        I18n.t('home.not_device_input'),
        [{text: 'OK', onPress: () => this.textInput.focus()}],
      );
    }
  }
}

const styles = StyleSheet.create({
  tabView: {
    flex: 1,
  },
  account_view: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 20,
    marginTop: 20,
  },
});
