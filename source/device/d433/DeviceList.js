/* eslint-disable no-useless-escape */
import React, {Component} from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import I18n from '../../I18n';
import {Tme, Colors} from '../../ThemeStyle';
import {Helper} from '../../Helper';
import _ from 'lodash';
import {NotificationCenter, D433_SELECT_DEVICE} from '../../NotificationCenter';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import NavBarView from '../../share/NavBarView';

export default class DeviceList extends Component {
  constructor(props) {
    super(props);
    this.timer = null;
    this._flatList = null;

    this.state = {
      start_learn: true,
      detected: [],
      devices: [],
    };
  }

  componentDidMount() {
    this.pollData();
  }

  componentWillUnmount() {
    clearTimeout(this.timer);
  }

  pollData() {
    var that = this;
    Helper.httpGET(
      Helper.urlWithQuery('/partner/ftt/detected_devices', {
        start_learn: that.state.start_learn,
        sn: this.props.route.params.sn.sn,
      }),
      {
        success: data => {
          that.setState({
            start_learn: false,
            detected: data,
            devices: data.devices,
          });
        },
      },
    );

    this.timer = setTimeout(() => {
      that.pollData();
    }, 1000);
  }

  render() {
    return (
      <NavBarView>
        <View style={{padding: 16, flexDirection: 'row'}}>
          <Text
            style={{
              fontSize: 12,
              fontWeight: '600',
              marginRight: 8,
              color: Tme('cardTextColor'),
            }}>
            {I18n.t('home.detect_device')}
          </Text>
          <ActivityIndicator size="small" color={Colors.MainColor} />
        </View>
        <FlatList
          style={{
            flex: 1,
          }}
          ref={flatList => (this._flatList = flatList)}
          data={this.state.devices}
          renderItem={this._renderRow.bind(this)}
          numColumns={1}
          onEndReachedThreshold={0.1}
          ItemSeparatorComponent={this.line.bind(this)}
          keyExtractor={(item, index) => index.toString()}
        />
      </NavBarView>
    );
  }

  line() {
    return (
      <View
        style={{
          height: 1,
          backgroundColor: Tme('inputBorderColor'),
          marginLeft: 16,
        }}
      />
    );
  }

  _renderRow({item, index}) {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={this.click.bind(this, item)}
        style={{backgroundColor: Tme('cardColor')}}>
        <View
          style={{
            marginLeft: 18,
            marginRight: 20,
            flexDirection: 'row',
            paddingVertical: 16,
            justifyContent: 'space-between',
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Text style={{color: Tme('cardTextColor')}}>
              {item.index.replace(/^\-\d/, '') + '-' + item.protocol}
            </Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={20}
              color={Tme('textColor')}
            />
          </View>
        </View>
      </TouchableOpacity>
    );
  }

  click(item) {
    var that = this;
    var dev = _.find(that.state.detected.devices, v => {
      return item.index === v.index;
    });
    var prot = _.find(that.state.detected.protocols, v => {
      return v.protocol === dev.protocol;
    });
    NotificationCenter.dispatchEvent(D433_SELECT_DEVICE, {
      prot: prot,
      device: item.index,
      detected: this.state.detected,
    });
    this.props.navigation.goBack();
  }
}
