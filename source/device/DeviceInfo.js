import React, { Component } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Tme, GetStyle, Colors } from '../ThemeStyle';
import I18n from '../I18n';
import NavBarView from '../share/NavBarView';
import { mainRadius } from '../Tools';
import _ from 'lodash';
import { Helper } from '../Helper';
import { Toast } from '../Toast';
import Slider from '@react-native-community/slider';
import DeviceControl from '../DeviceControl';
import AlertModal from '../share/AlertModal';
export default class DeviceInfo extends Component {
  constructor(props) {
    super(props);

    this.state = {
      data: [],
      commands: [],
    };
    this._flatList = null;
  }

  componentDidMount() {
    console.log('device info', this.props.route.params.device);
    const temp = [];
    _.forEach(this.props.route.params.device.cc_specs, (spec, key) => {
      if (spec.spec_type === 4) {
        temp.push({
          ...spec,
        });
      }
    });

    this.setState({
      commands: temp,
    });
  }

  showConfigView() {
    const html = [];
    _.forEach(this.state.commands, (item, index) => {
      const op = item.data;
      if (!op.ReadOnly) {
        html.push(
          <View style={{ marginTop: 10 }} key={index}>
            <View style={GetStyle('spec')}>
              <View style={{ backgroundColor: Tme('cardColor') }}>
                <View>
                  <View style={styles.row} key="name">
                    <View style={styles.rowView}>
                      <Text style={[{ color: Tme('specNameText') }]}>
                        {Helper.i(item.name)}
                      </Text>
                    </View>
                  </View>
                  <View>
                    <View
                      style={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginBottom: 8,
                        marginTop: 20,
                      }}>
                      <Text
                        style={{
                          fontSize: 22,
                          fontWeight: '500',
                          color: Tme('cardTextColor'),
                        }}>
                        {op.Value}
                      </Text>
                    </View>
                    <Slider
                      minimumValue={Number(op.MinimumValue)}
                      maximumValue={Number(op.MaximumValue)}
                      step={1}
                      minimumTrackTintColor={Colors.MainColor}
                      value={op.Value}
                      onSlidingComplete={value =>
                        this.handleSliderChange(value, item)
                      }
                    />
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                      }}>
                      <Text style={{ color: Tme('cardTextColor') }}>
                        {Number(op.MinimumValue)}
                      </Text>
                      <Text style={{ color: Tme('cardTextColor') }}>
                        {Number(op.MaximumValue)}
                      </Text>
                    </View>
                    <View style={{ flexDirection: 'row', marginTop: 20 }}>
                      <View style={styles.rowView}>
                        <Text
                          style={{
                            color: Tme('textColor'),
                            fontSize: 12,
                          }}>
                          {op.Info}
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </View>,
        );
      }
    });
    return html;
  }

  handleSliderChange = (value, item) => {
    const temp = _.cloneDeep(this.state.commands);
    const index = _.findIndex(temp, t => t.value_id === item.value_id);
    temp[index].data.Value = value;
    this.setState(
      {
        commands: [...temp],
      },
      () => {
        new DeviceControl({
          spec: item,
          ParameterId: temp[index].data.ParameterId,
          param: value,
          type: item.dv_type,
          runCMD: true,
        }).setparameters();
        Toast.show();
        setTimeout(() => {
          AlertModal.alert(I18n.t('global.advanced_success'));
        }, 1000);
      },
    );
  };

  DefaultResetAllParameters() {
    new DeviceControl({
      spec: this.state.commands[0],
      runCMD: true,
    }).defaultResetAllParameters();
    Toast.show();

    setTimeout(() => {
      AlertModal.alert(I18n.t('global.advanced_success'));
    }, 1000);
  }

  interview() {
    const spec = this.props.route.params.device.cc_specs[0];
    new DeviceControl({
      spec: spec,
      runCMD: true,
    }).interview();
    Toast.show();
  }

  delete() {
    var that = this;
    const spec = this.props.route.params.device.cc_specs[0];
    AlertModal.alert(
      I18n.t('home.warning_message'),
      I18n.t('global.zwave_delete_field'),
      [
        {
          text: 'Cancel',
          onPress: () => { },
          style: 'cancel',
        },
        {
          text: 'OK',
          onPress: () => {
            new DeviceControl({
              spec: spec,
              runCMD: true,
            }).removeZwaveFailedDevice();
            Toast.show();
            setTimeout(() => {
              that.props.navigation.popToTop();
            }, 1000);
          },
        },
      ],
      { cancelable: false },
    );
  }

  // 渲染设备信息行的辅助方法
  renderDeviceInfoRow(label, value, isFlexEnd = false, textProps = {}) {
    return (
      <View style={{ backgroundColor: Tme('cardColor') }}>
        <View style={styles.row}>
          <View style={styles.rowView}>
            <Text style={{ color: Tme('cardTextColor'), fontSize: 14 }}>
              {label}
            </Text>
          </View>
          <View
            style={[
              styles.rowView,
              isFlexEnd && {
                flex: 1,
                marginLeft: 20,
                justifyContent: 'flex-end',
              },
            ]}>
            <Text
              style={{ color: Tme('textColor'), fontSize: 14 }}
              {...textProps}>
              {value}
            </Text>
          </View>
        </View>
      </View>
    );
  }

  // 渲染操作按钮的辅助方法
  renderActionButton(title, onPress) {
    return (
      <View style={{ marginTop: 10 }}>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={onPress}
          style={{
            backgroundColor: Tme('cardColor'),
            borderRadius: mainRadius(),
          }}>
          <View
            style={{
              paddingVertical: 16,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Text style={{ color: Colors.MainColor }}>
              {title}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  }

  // 获取Thread路由角色描述
  getThreadRoleDescription(role) {

    // 支持字符串值（新格式）
    const stringRoleMap = {
      'unspecified': I18n.t('device.thread_network_routing_role.unspecified'),
      'unassigned': I18n.t('device.thread_network_routing_role.unassigned'),
      'sleepy_end_device': I18n.t('device.thread_network_routing_role.sleepy_end_device'),
      'end_device': I18n.t('device.thread_network_routing_role.end_device'),
      'reed': I18n.t('device.thread_network_routing_role.reed'),
      'router': I18n.t('device.thread_network_routing_role.router'),
      'leader': I18n.t('device.thread_network_routing_role.leader'),
    };

    return stringRoleMap[role] || `${I18n.t('device.thread_network_routing_role.unspecified')} (${role})`;
  }

  // 获取电源状态描述
  getPowerSourceStatus(status) {

    // 支持字符串值（新格式）
    const stringStatusMap = {
      'unspecified': I18n.t('device.power_source_status.unspecified'),
      'active': I18n.t('device.power_source_status.active'),
      'standby': I18n.t('device.power_source_status.standby'),
      'unavailable': I18n.t('device.power_source_status.unavailable'),
    };

    return stringStatusMap[status] || `${I18n.t('device.power_source_status.unspecified')} (${status})`;
  }

  // 处理设备类型显示格式：将下划线分隔转换为驼峰命名
  formatDeviceType(type) {
    if (!type) {return '';}

    // 按下划线分割
    const parts = type.split('_');

    // 将每个部分的首字母大写，其余小写
    const formatted = parts.map(part => {
      if (!part) {return '';}
      return part.charAt(0).toUpperCase() + part.slice(1).toLowerCase();
    }).join('');

    return formatted;
  }

  render() {
    const { device } = this.props.route.params;
    let showAdV = false;
    const tempConfig = [];
    if (this.state.commands.length > 0) {
      showAdV = true;
      tempConfig.push('commands');
    }

    if (
      device.config_commands &&
      _.includes(device.config_commands, 'DefaultResetAllParameters')
    ) {
      showAdV = true;
      tempConfig.push('defult');
    }

    return (
      <NavBarView>
        <View
          style={{
            paddingTop: 10,
            flex: 1,
            borderRadius: mainRadius(),
          }}>
          <ScrollView
            contentContainerStyle={{ flexGrow: 1 }}
            showsVerticalScrollIndicator={false}>
            <View
              style={{ marginBottom: 20, marginHorizontal: 20, marginTop: 10 }}>
              <Text
                style={{
                  color: Tme('textColor'),
                }}>
                {I18n.t('device.device_information')}
              </Text>
              <View style={{ marginTop: 10, marginBottom: 20 }}>
                <View style={GetStyle('spec')}>
                  {this.renderDeviceInfoRow(I18n.t('home.device_name'), device.display_name, true)}
                  {this.renderDeviceInfoRow(I18n.t('global.type'), this.formatDeviceType(device.dv_type))}
                  {device.vendor && (
                    <>
                      {this.renderDeviceInfoRow(I18n.t('device.product_name'), device.vendor.product_name, true)}
                      {this.renderDeviceInfoRow(I18n.t('device.manufacturer'), device.vendor.manufacturer, true, { numberOfLines: 1 })}
                      {device.vendor.power_source_bat_percent_remaining && this.renderDeviceInfoRow(I18n.t('device.power_source_bat_percent_remaining'), `${device.vendor.power_source_bat_percent_remaining}%`, true)}
                      {/* {device.vendor.manufacturer_id && this.renderDeviceInfoRow(I18n.t('device.manufacturer_id'), device.vendor.manufacturer_id, true)} */}
                      {/* {device.vendor.product_id && this.renderDeviceInfoRow(I18n.t('device.product_id'), device.vendor.product_id, true)} */}
                      {device.vendor.power_source_description && this.renderDeviceInfoRow(I18n.t('device.power_source_description'), device.vendor.power_source_description, true)}
                      {/* {device.vendor.power_source_order && this.renderDeviceInfoRow(I18n.t('device.power_source_order'), device.vendor.power_source_order, true)} */}
                      {device.vendor.power_source_status && this.renderDeviceInfoRow(I18n.t('device.power_source_status.name'), this.getPowerSourceStatus(device.vendor.power_source_status), true)}
                      {device.vendor.thread_network_name && this.renderDeviceInfoRow(I18n.t('device.thread_network_name'), device.vendor.thread_network_name, true)}
                      {device.vendor.thread_network_channel && this.renderDeviceInfoRow(I18n.t('device.thread_network_channel'), device.vendor.thread_network_channel, true)}
                      {/* {device.vendor.thread_network_data_version && this.renderDeviceInfoRow(I18n.t('device.thread_network_data_version'), device.vendor.thread_network_data_version, true)} */}
                      {/* {device.vendor.thread_network_extended_pan_id && this.renderDeviceInfoRow(I18n.t('device.thread_network_extended_pan_id'), device.vendor.thread_network_extended_pan_id, true)} */}
                      {/* {device.vendor.thread_network_leader_router_id && this.renderDeviceInfoRow(I18n.t('device.thread_network_leader_router_id'), device.vendor.thread_network_leader_router_id, true)} */}
                      {/* {device.vendor.thread_network_mesh_local_prefix && this.renderDeviceInfoRow(I18n.t('device.thread_network_mesh_local_prefix'), device.vendor.thread_network_mesh_local_prefix, true)} */}
                      {/* {device.vendor.thread_network_overrun_count && this.renderDeviceInfoRow(I18n.t('device.thread_network_overrun_count'), device.vendor.thread_network_overrun_count, true)} */}
                      {device.vendor.thread_network_pan_id && this.renderDeviceInfoRow(I18n.t('device.thread_network_pan_id'), device.vendor.thread_network_pan_id, true)}
                      {/* {device.vendor.thread_network_partition_id && this.renderDeviceInfoRow(I18n.t('device.thread_network_partition_id'), device.vendor.thread_network_partition_id, true)} */}
                      {/* {device.vendor.thread_network_routing_role && this.renderDeviceInfoRow(I18n.t('device.thread_network_routing_role.name'), this.getThreadRoleDescription(device.vendor.thread_network_routing_role), true)} */}
                      {/* {device.vendor.thread_network_stable_data_version && this.renderDeviceInfoRow(I18n.t('device.thread_network_stable_data_version'), device.vendor.thread_network_stable_data_version, true)} */}
                      {/* {device.vendor.thread_network_weighting && this.renderDeviceInfoRow(I18n.t('device.thread_network_weighting'), device.vendor.thread_network_weighting, true)} */}
                    </>
                  )}
                  {this.renderDeviceInfoRow('ID', device.index)}
                  {device.dv_type == 'zigbee' && this.renderDeviceInfoRow('EUI64', device.long_id)}
                </View>
                {this.renderActionButton(I18n.t('device.interview'), this.interview.bind(this))}
                {this.renderActionButton(I18n.t('device.delete_field_device'), this.delete.bind(this))}
              </View>
              <>
                {showAdV && (
                  <Text
                    style={{
                      color: Tme('textColor'),
                    }}>
                    {I18n.t('global.advanced_setting')}
                  </Text>
                )}
                {tempConfig.length === 0 && (
                  <View style={{ marginTop: 20 }}>
                    <Text
                      style={{
                        color: Tme('textColor'),
                      }}>
                      {I18n.t('global.no_advanced_setting')}
                    </Text>
                  </View>
                )}
                {this.showConfigView()}
                <View style={{ height: 10 }} />
                {device.config_commands &&
                  _.includes(
                    device.config_commands,
                    'DefaultResetAllParameters',
                  ) && (
                    <TouchableOpacity
                      activeOpacity={0.8}
                      onPress={this.DefaultResetAllParameters.bind(this)}
                      style={{
                        backgroundColor: Tme('cardColor'),
                        borderRadius: mainRadius(),
                      }}>
                      <View
                        style={{
                          paddingVertical: 16,
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}>
                        <Text style={{ color: Colors.MainColor }}>
                          {I18n.t('setting.default_reset_all_parameters')}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  )}
              </>
            </View>
          </ScrollView>
        </View>
      </NavBarView>
    );
  }
}

const styles = StyleSheet.create({
  row: {
    marginLeft: 18,
    marginRight: 20,
    flexDirection: 'row',
    paddingVertical: 6,
    justifyContent: 'space-between',
  },
  rowView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
