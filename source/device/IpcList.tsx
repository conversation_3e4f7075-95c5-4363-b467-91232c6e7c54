import React, {useState, useEffect, useLayoutEffect, useRef} from 'react';
import {
  Text,
  View,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Pressable,
} from 'react-native';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {Tme, Colors} from '../ThemeStyle';
import IpcItem from '../home/<USER>'; // 导入 IpcItem 组件
import {Helper} from '../Helper';
import I18n from '../I18n';
import EmptyView from '../share/EmptyView';
import {hideLoading, showLoading} from '../../ILoading';
import {getDeviceHost, getViewFrom} from './ipc/IpcHelper';
import {useLifecycleEvents} from './ipc/hooks/useLifecycleEvents';
import IpcEvents from './IpcEvents';
import {IpcRouteParamList} from './ipc/types/navigation';
import {Device} from '../types/home';
import {mainRadius} from '../Tools';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

// 定义标签类型
type TabType = 'devices' | 'events';

// 定义 HeaderTabTitle 的属性类型
interface HeaderTabTitleProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
}

// 定义 SettingsButton 的属性类型
interface SettingsButtonProps {
  onPress: () => void;
}

// 使用导入的路由参数类型
type RootStackParamList = IpcRouteParamList;

// 美化后的自定义标题组件，使用 headerTitle
const HeaderTabTitle: React.FC<HeaderTabTitleProps> = ({
  activeTab,
  onTabChange,
}) => {
  return (
    <View
      style={[
        styles.tabsContainer,
        {
          backgroundColor: Tme('cardColor'),
          borderColor: Tme('inputBorderColor'),
        },
      ]}>
      <Pressable
        style={({pressed}) => [
          styles.tab,
          activeTab === 'devices' && [{backgroundColor: Tme('cardColor')}],
          pressed && {opacity: 0.8},
        ]}
        onPress={() => onTabChange('devices')}>
        <Text
          style={[
            {
              fontWeight: 'bold',
            },
            {
              color:
                activeTab === 'devices'
                  ? Colors.MainColor
                  : Tme('tabInactiveTextColor'),
            },
          ]}>
          {I18n.t('ipc.ipc_header')}
        </Text>
      </Pressable>
      <Pressable
        style={({pressed}) => [
          styles.tab,
          activeTab === 'events' && [{backgroundColor: Tme('cardColor')}],
          pressed && {opacity: 0.8},
        ]}
        onPress={() => onTabChange('events')}>
        <Text
          style={[
            {
              fontWeight: 'bold',
            },
            {
              color:
                activeTab === 'events'
                  ? Colors.MainColor
                  : Tme('tabInactiveTextColor'),
            },
          ]}>
          {I18n.t('home.event')}
        </Text>
      </Pressable>
    </View>
  );
};

// 设置按钮组件，用于 headerRight
const SettingsButton: React.FC<SettingsButtonProps> = ({onPress}) => {
  return (
    <TouchableOpacity
      style={styles.settingsButton}
      onPress={onPress}
      activeOpacity={0.7}>
      <View style={[styles.settingsButtonInner]}>
        <Icon name="settings" size={24} color={Tme('cardTextColor')} />
      </View>
    </TouchableOpacity>
  );
};

const IpcList: React.FC = () => {
  const route = useRoute<RouteProp<IpcRouteParamList, 'IpcList'>>();
  const initialTab = route.params?.initialTab || 'devices';
  const initialDeviceUUID = route.params?.initialDeviceUUID;
  const [activeTab, setActiveTab] = useState<TabType>(initialTab);
  const [devices, setDevices] = useState<Device[]>([]);
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const [refreshing, setRefreshing] = useState<boolean>(false);

  const isActive = useLifecycleEvents({
    navigation,
  });

  const deviceList = useRef<Device[]>([]);

  // 设置标题和右侧按钮
  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitleAlign: 'center',
      // eslint-disable-next-line react/no-unstable-nested-components
      headerTitle: (props: any) => (
        <HeaderTabTitle
          {...props}
          activeTab={activeTab}
          onTabChange={handleTabChange}
        />
      ),
      // eslint-disable-next-line react/no-unstable-nested-components
      headerRight: () => <SettingsButton onPress={handleSettingsPress} />,
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigation, activeTab]);

  // 移除原有的生命周期监听代码
  useEffect(() => {
    doFetchData('');
  }, []);

  const doFetchData = (type: string): void => {
    if (type == 'refresh') {
      setRefreshing(true);
    } else {
      showLoading();
    }
    Helper.httpGET(Helper.urlWithQuery('/partner/devices', {is_ipc: 1}), {
      success: (data: {devices: Device[]}) => {
        const temp: Device[] = [];

        data.devices.forEach(item => {
          if (item.dv_type === 'ipc') {
            temp.push(item);
          }
        });
        setDevices(temp);
        deviceList.current = temp;
      },
      ensure: () => {
        if (type == 'refresh') {
          setRefreshing(false);
        } else {
          hideLoading();
        }
      },
    });
  };

  // 获取摄像头连接数据并跳转到全屏页面
  const goToFullscreen = (data: Device): void => {
    const host = getDeviceHost(data);
    const from = getViewFrom(host);

    // 导航到全屏页面，直接传递 host 和 clientid
    navigation.push('IpcFullscreenView', {
      title: data.display_name || '',
      host: host,
      clientid: data.ipc!.webrtc_uuid,
      from: from, // cloud or local
    });
  };

  const handleSettingsPress = (): void => {
    navigation.push('IpcListSetting', {
      title: I18n.t('home.setting'),
      // devices: deviceList.current,
    });
  };

  const handleTabChange = (tab: TabType): void => {
    setActiveTab(tab);
  };

  // 根据当前选中的标签显示相应的内容
  const renderContent = () => {
    if (activeTab === 'devices') {
      return (
        <FlatList
          style={{
            flex: 1,
          }}
          data={devices}
          renderItem={({item}) => (
            <View
              style={{
                backgroundColor: Tme('cardColor'),
                marginHorizontal: 20,
                borderRadius: mainRadius(),
              }}>
              <IpcItem
                from="ipc"
                item={item}
                onBlur={!isActive}
                onFullscreen={() => {
                  // 导航到新的全屏页面而不是IpcCameraShow
                  goToFullscreen(item);
                }}
              />
            </View>
          )}
          keyExtractor={(item, index) => index.toExponential(2)}
          showsVerticalScrollIndicator={false}
          // eslint-disable-next-line react/no-unstable-nested-components
          ItemSeparatorComponent={() => <View style={{height: 20}} />}
          onRefresh={() => {
            doFetchData('refresh');
            deviceList.current = [];
          }}
          refreshing={refreshing}
          onEndReachedThreshold={0.1}
          ListEmptyComponent={<EmptyView />}
          // eslint-disable-next-line react/no-unstable-nested-components
          ListFooterComponent={() => <View style={{height: 20}} />}
        />
      );
    } else {
      return <IpcEvents initialDeviceUUID={initialDeviceUUID} />;
    }
  };

  return (
    <View style={[styles.container, {backgroundColor: Tme('bgColor')}]}>
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 10,
  },
  tabShadowContainer: {
    borderRadius: 20,
  },
  tabsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
    borderWidth: 1,
    width: 200,
    height: 40,
    overflow: 'hidden',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    paddingHorizontal: 12,
  },
  tabIcon: {
    marginRight: 4,
  },
  settingsButton: {
    marginRight: 12,
  },
  settingsButtonInner: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default IpcList;
