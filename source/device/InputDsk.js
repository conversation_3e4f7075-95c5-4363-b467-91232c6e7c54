import React, {Component} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
} from 'react-native';
import {Helper} from '../Helper';
import I18n from '../I18n';
import {Tme, Colors} from '../ThemeStyle';
import * as Progress from 'react-native-progress';
import PahoManager from '../PahoManager';
import {CameraScreen} from 'react-native-camera-kit';
import IdleTimerManager from 'react-native-idle-timer';
import DeviceControl from '../DeviceControl';
import CardView from '../share/CardView';
import NavBarView from '../share/NavBarView';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import _ from 'lodash';
import AlertModal from '../share/AlertModal';
import {hideLoading, showLoading} from '../../ILoading';
import ScreenSizeContext from '../../WindowResizeContext';
import PubSub from 'pubsub-js';
import {EVENT_GRPC_CONTROLLER_CHANGE} from '../types/PubSubEvent';

class InputDsk extends Component {
  constructor(props) {
    super(props);

    this.state = {
      dsk: '',
      indeterminate: true,
      progress: 0,
      time_show: this.props.route.params.type == 'list',
      input_show: this.props.route.params.type == 'input',
      scan_show: this.props.route.params.type == 'scan',
      addCodeList: [],
      userAddCodeList: {},
      wait: true,
    };
    this.rq = true;
  }

  static contextType = ScreenSizeContext;

  componentDidMount() {
    this.doFetchData();

    IdleTimerManager.setIdleTimerDisabled(true);
    var that = this;
    const userCode = {};
    this.props.route.params.list.map(v => {
      userCode[v] = false;
    });
    this.setState({
      userAddCodeList: userCode,
    });
    PubSub.subscribe(EVENT_GRPC_CONTROLLER_CHANGE, (msg, payload) => {
      if (payload.queue == 'controller') {
        switch (payload.data.op_state) {
          case 'set_pl_success':
            that.setState({
              wait: false,
              indeterminate: false,
            });
            break;
          case 'set_pl_failed':
            that.setState(
              {
                indeterminate: true,
                progress: 0,
              },
              () => {
                AlertModal.alert(
                  I18n.t('home.add_device_fail'),
                  '',
                  [
                    {
                      text: 'OK',
                      onPress: () => {
                        that.props.navigation.goBack();
                      },
                    },
                  ],
                  {cancelable: false},
                );
              },
            );
            break;
          // case 'in_progress_ss':
          //   that.setState({
          //     progress: 0.2,
          //   });
          //   break;
          // case 'in_progress':
          //   that.setState({
          //     progress: 0.4,
          //   });
          //   break;
          // case 'in_progress_1':
          //   that.setState({
          //     progress: 0.6,
          //   });
          //   break;
          // case 'in_progress_2':
          //   that.setState({
          //     progress: 0.8,
          //   });
          //   break;
          case 'device_added':
            const smartStartList = JSON.parse(
              payload.data.smart_start_list,
            ).value;
            const tempUserList = _.cloneDeep(that.state.userAddCodeList);
            const tempAddCode = _.cloneDeep(that.state.addCodeList);
            smartStartList.map(item => {
              if (!_.isEmpty(item.Unid)) {
                _.forEach(tempUserList, (v, k) => {
                  if (k === item.DSK && v === false) {
                    tempUserList[k] = true;
                    tempAddCode.push(k);
                  }
                });
              }
            });

            that.setState(
              {
                userAddCodeList: tempUserList,
                addCodeList: tempAddCode,
                progress: parseFloat(
                  (
                    tempAddCode.length / that.props.route.params.list.length
                  ).toFixed(1),
                  10,
                ),
              },
              () => {
                if (
                  that.props.route.params.list.length === tempAddCode.length
                ) {
                  AlertModal.alert(
                    I18n.t('home.add_device_success'),
                    '',
                    [
                      {
                        text: 'OK',
                        onPress: () => {
                          that.props.navigation.goBack();
                        },
                      },
                    ],
                    {cancelable: false},
                  );
                }
              },
            );
            break;
          default:
            break;
        }
      }
    });
  }

  componentWillUnmount() {
    IdleTimerManager.setIdleTimerDisabled(false);
    if (this.pahoManager) {
      this.pahoManager.unmount();
    }
    PubSub.unsubscribe(EVENT_GRPC_CONTROLLER_CHANGE);
  }

  render() {
    return (
      <NavBarView>
        <KeyboardAwareScrollView
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="never"
          keyboardDismissMode="on-drag"
          innerRef={ref => {
            this.scroll = ref;
          }}
          style={{
            flex: 1,
            backgroundColor: Tme('bgColor'),
          }}>
          {this.state.scan_show ? (
            <CameraScreen
              style={{height: this.context.winHeight}}
              scanBarcode={true}
              showFrame={true}
              laserColor={'blue'}
              frameColor={Colors.MainColor}
              onReadCode={event => this.readCode(event)}
              hideControls={false}
              colorForScannerFrame={'red'} //(default white) optional, change colot of the scanner frame
            />
          ) : (
            <View style={{margin: 20}}>
              <CardView
                showMenu={false}
                styles={[
                  {
                    width: this.context.winWidth - 40,
                    height: 480,
                    borderRadius: 8,
                  },
                ]}>
                <View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <View style={{marginTop: 79}}>
                    <Text
                      style={{
                        fontSize: 22,
                        fontWeight: '600',
                        textAlign: 'center',
                        color: Tme('cardTextColor'),
                      }}>
                      Smart Start
                    </Text>
                  </View>
                  {this.state.time_show ? (
                    <View style={{marginTop: 16, alignItems: 'center'}}>
                      {this.state.wait ? (
                        <View style={{padding: 16}}>
                          <Text
                            style={{
                              fontSize: 14,
                              fontWeight: '600',
                              textAlign: 'center',
                              color: Tme('cardTextColor'),
                            }}>
                            {I18n.t('global.add_device_wait')}
                          </Text>
                        </View>
                      ) : null}
                      <Progress.Circle
                        size={140}
                        color={Colors.MainColor}
                        progress={this.state.progress}
                        indeterminate={this.state.indeterminate}
                        showsText={true}
                        formatText={() => {
                          return `${parseInt(this.state.progress * 100, 10)}%`;
                        }}
                      />
                      <View style={{marginTop: 20, paddingHorizontal: 20}}>
                        <Text
                          style={{
                            color: Tme('smallTextColor'),
                            fontSize: 12,
                            marginBottom: 12,
                          }}>
                          {I18n.t('device.smart_add_desp', {
                            time: this.props.route.params.list.length * 2,
                          })}
                        </Text>
                      </View>
                      <View style={{marginTop: 10}}>
                        <Text
                          style={{
                            color: Tme('smallTextColor'),
                            fontSize: 12,
                            marginBottom: 12,
                          }}>
                          {I18n.t('device.added_devices')}:
                        </Text>
                        {this.state.addCodeList.map((v, k) => (
                          <Text
                            key={k}
                            style={{
                              fontSize: 11,
                              marginBottom: 12,
                              color: Tme('smallTextColor'),
                            }}>
                            {v}
                          </Text>
                        ))}
                      </View>
                    </View>
                  ) : null}
                  {this.state.input_show ? (
                    <View>
                      <View style={{padding: 16}}>
                        <Text
                          style={{fontSize: 14, color: Tme('cardTextColor')}}>
                          {I18n.t('global.add_smart_desp')}
                        </Text>
                      </View>
                      <View
                        style={{
                          padding: 16,
                        }}>
                        <Text
                          style={{
                            marginBottom: 16,
                            fontSize: 16,
                            fontWeight: '600',
                            color: Tme('cardTextColor'),
                          }}>
                          DSK
                        </Text>
                        <View
                          style={[
                            styles.account_view,
                            {borderColor: Tme('inputBorderColor')},
                          ]}>
                          <TextInput
                            returnKeyType="go"
                            autoCapitalize="none"
                            keyboardType="number-pad"
                            underlineColorAndroid="transparent"
                            autoCorrect={false}
                            value={this.state.dsk}
                            multiline={true}
                            numberOfLines={2}
                            maxLength={40}
                            onChangeText={dsk => this.setState({dsk})}
                            onSubmitEditing={this._save.bind(this)}
                            style={{
                              height: 66,
                              marginLeft: 6,
                              marginRight: 6,
                              fontSize: 15,
                              fontWeight: '600',
                              letterSpacing: 1,
                              color: Tme('cardTextColor'),
                            }}
                          />
                        </View>
                      </View>
                      <View style={{marginTop: 20, alignItems: 'center'}}>
                        <TouchableOpacity
                          activeOpacity={0.8}
                          onPress={this._save.bind(this)}
                          style={{
                            width: 120,
                            height: 40,
                            borderRadius: 20,
                            backgroundColor: Colors.MainColor,
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}>
                          <Text
                            style={{
                              fontSize: 16,
                              fontWeight: '600',
                              color: '#ffffff',
                              textAlign: 'center',
                            }}>
                            {I18n.t('home.save')}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  ) : null}
                </View>
              </CardView>
            </View>
          )}
        </KeyboardAwareScrollView>
      </NavBarView>
    );
  }

  readCode(event) {
    var that = this;
    if (this.rq) {
      that.rq = false;
      Helper.httpPOST(
        '/partner/controllers/smart_add',
        {
          success: data => {
            this.setState({
              scan_show: false,
              time_show: true,
            });
          },
          ensure: () => {},
        },
        {code: event.nativeEvent.codeStringValue},
      );
    }
  }

  close() {
    this.props.navigation.goBack();
  }

  _save() {
    var value = '';
    if (this.state.dsk) {
      if (/^\d{40}$/.test(this.state.dsk)) {
        var temp = this.state.dsk;
        var dsk = temp.replace(/(.{5})/g, '$1-');
        value = dsk.substring(0, dsk.length - 1);
      } else {
        AlertModal.alert(I18n.t('global.dsk_input'));
        return;
      }
    } else {
      AlertModal.alert(I18n.t('global.dsk_input'));
      return;
    }

    if (value !== '') {
      new DeviceControl({
        sn_id: this.props.route.params.sn.id,
        value: value,
      }).setPl();
      this.setState({
        input_show: false,
        time_show: true,
      });
    }
  }

  Xreplace(str, length) {
    var re = new RegExp('\\d{1, ' + length + '}', 'g');
    var ma = str.match(re);
    return ma.join('-');
  }

  onConnect() {
    if (
      this.props.route.params.type === 'list' &&
      this.props.route.params.list.length > 0
    ) {
      new DeviceControl({
        sn_id: this.props.route.params.sn.id,
        value: JSON.stringify(this.props.route.params.list),
      }).setPl();
    }
  }

  doFetchData() {
    showLoading();
    var that = this;
    Helper.httpGET(
      Helper.urlWithQuery('/partner/controllers/conn_info', {
        sn_id: this.props.route.params.sn.id,
      }),
      {
        success: data => {
          if (_.isEmpty(data.mqtt)) {
            AlertModal.alert(
              I18n.t('home.add_device_fail'),
              '',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    that.props.navigation.goBack();
                  },
                },
              ],
              {cancelable: false},
            );
          } else {
            this.setState(
              {
                showView: true,
              },
              () => {
                this.pahoManager = new PahoManager({
                  mqtt: data.mqtt,
                  onConnect: this.onConnect.bind(this),
                  navigation: that.props.navigation,
                });
                this.pahoManager.mount();
              },
            );
          }
        },
        ensure: () => {
          hideLoading();
        },
      },
    );
  }
}
const styles = StyleSheet.create({
  account_view: {
    borderWidth: 1,
    borderRadius: 16,
    height: 66,
  },
});

export default InputDsk;
