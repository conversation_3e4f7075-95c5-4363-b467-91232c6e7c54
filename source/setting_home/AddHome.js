/* eslint-disable react/no-unstable-nested-components */
import React, { useState, useRef, useEffect, useLayoutEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  ScrollView,
  KeyboardAvoidingView,
  Dimensions,
  Platform,
} from 'react-native';
import {Helper, HelperMemo} from '../Helper';
import I18n from '../I18n';
import {Tme, Colors} from '../ThemeStyle';
import CardView from '../share/CardView';
import _ from 'lodash';
import NavBarView from '../share/NavBarView';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {hideLoading, showLoading} from '../../ILoading';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';

export default function AddHome({ navigation, route }) {
  const { name: initialName, home_id } = route.params;
  const [name, setName] = useState(initialName);

  const focusRef = useRef(null);

  // 将 initWidth 函数定义移到 useState 之前
  const initWidth = useCallback((window) => {
    if (Platform.OS === 'android') {
      return window.width;
    } else {
      if (window.width > window.height) {
        return (
          window.width -
          HelperMemo.STATUS_BAR_HEIGHT -
          HelperMemo.BOTTOM_BAR_HEIGHT -
          22
        );
      } else {
        return window.width;
      }
    }
  }, []);

  const [winWidth] = useState(initWidth(Dimensions.get('window')));

  const save = useCallback(() => {
    if (_.isEmpty(name)) {
      AlertModal.alert(I18n.t('scene.scene_input'));
      return;
    }

    let body, url;
    if (home_id) {
      url = '/home/<USER>';
      body = {
        home_id: home_id,
        name: name,
      };
    } else {
      url = '/home/<USER>';
      body = {
        name: name,
      };
    }

    showLoading();
    Helper.httpPOST(
      url,
      {
        cloud: true,
        ensure: () => {
          hideLoading();
        },
        success: data => {
          PubSub.PubSub(PubSubEvent.EVENT_USER);
          navigation.goBack();
        },
      },
      body,
    );
  }, [name, home_id, navigation]);

  // 替代 componentDidMount 的效果
  useEffect(() => {
    const timer = setTimeout(() => {
      if (focusRef.current) {
        focusRef.current.focus();
      }
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // 设置导航标题右侧的保存按钮
  useLayoutEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={save}
        />
      ),
    });
  }, [navigation, save]);

  return (
    <NavBarView>
      <ScrollView
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="never"
        keyboardDismissMode="on-drag"
        style={{
          backgroundColor: Tme('bgColor'),
        }}>
        <KeyboardAvoidingView
          style={{flex: 1, alignItems: 'center', padding: 20}}>
          <CardView
            withWaveBg={true}
            styles={{
              height: 120,
              padding: 20,
              borderRadius: 8,
              alignItems: 'center',
            }}>
            <View
              style={[
                styles.account_view,
                {borderColor: Tme('inputBorderColor')},
              ]}>
              <TextInput
                ref={focusRef}
                returnKeyType="go"
                autoCapitalize="none"
                underlineColorAndroid="transparent"
                autoCorrect={false}
                defaultValue={name}
                value={name}
                onChangeText={setName}
                placeholderTextColor={Tme('placeholder')}
                placeholder={I18n.t('home.home_name')}
                style={[
                  Colors.TextInputStyle(),
                  {width: winWidth - 100},
                ]}
              />
            </View>
          </CardView>
        </KeyboardAvoidingView>
      </ScrollView>
    </NavBarView>
  );
}

const styles = StyleSheet.create({
  account_view: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 3,
    marginBottom: 20,
    marginTop: 20,
  },
});
