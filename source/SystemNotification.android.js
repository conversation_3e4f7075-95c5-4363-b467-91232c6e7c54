import React from 'react';
import { Platform } from 'react-native';
import { Router } from './Router';
import { Helper, HelperMemo } from './Helper';
import _ from 'lodash';
import I18n from './I18n';
import Getui from './Getui';
import AlertModal from './share/AlertModal';
import PubSub from 'pubsub-js';
import { PubSubEvent } from './types/PubSubEvent';
// 使用：
// 直接在首页中
// <SystemNotification />
//
export default class SystemNotification extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      pushMsg: '',
    };
  }

  onGetRegistrationIdPress() {
    Getui.getRegistrationID(result => {
      console.log('getui:', result.registerID);
      if (_.isEmpty(result.registerID)) {
        setTimeout(() => {
          this.onGetRegistrationIdPress();
        }, 5000);
      } else {
        HelperMemo.device_token = result.registerID;
        Helper.httpPOST(
          '/device_tokens',
          {
            ignore_error: true,
            success: data => {
              HelperMemo.token_id = data.device_token.id;
            },
            error: () => { },
          },
          {
            token: result.registerID,
            token_type: 'getui',
            device_type: Platform.OS,
          },
        );
      }
    });
  }

  componentDidMount() {
    Getui.setDebugMode(true);
    //连接状态
    this.connectListener = result => {
      console.log('connect', result);
    };
    Getui.addConnectEventListener(this.connectListener);

    //通知回调
    this.notificationListener = result => {
      if (result.notificationEventType == 'notificationOpened') {
        Getui.deleteNotify();
        if (result.extras) {
          console.log(result.extras);
          const extras = JSON.parse(result.extras);
          if (extras.type == 'event') {
            setTimeout(() => {
              this.props.navigation.push('eventScreen', {
                sn_id: extras.sn_id,
                title: I18n.t('home.event'),
              });
            }, 500);
          } else if (extras.type == 'spec_change') {
            if (extras.urgent) {
              setTimeout(() => {
                this.props.navigation.popToTop();
                HelperMemo.message = extras;
                this.props.navigation.navigate('Tabs', {
                  screen: 'homeScreen',
                });
                PubSub.publish(PubSubEvent.EVENT_APP_NOTIFY);
              }, 100);
            } else {
              setTimeout(() => {
                Router.pushDeviceShow(this.props.navigation, {
                  data: { uuid: extras.uuid, notify: true },
                });
              }, 500);
            }
          }
        }
      }
    };

    Getui.addNotificationListener(this.notificationListener);

    this.onGetRegistrationIdPress();

    Getui.isNotificationEnabled(isEnabled => {
      if (!isEnabled) {
        AlertModal.alert(I18n.t('permissions.notify_error'), '', [
          {
            text: I18n.t('home.cancel'),
          },
          {
            text: I18n.t('home.setting_btn'),
            onPress: () => {
              Getui.openNotification();
            },
          },
        ]);
      }
    });
  }

  componentWillUnmount() {
    Getui.removeListener(this.notificationListener);
  }

    render() {
      return null;
    }
  }
