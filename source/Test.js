import React from 'react';
import {Text, View} from 'react-native';
import AlertModal from './share/AlertModal';

export default class Test extends React.Component {
  constructor(props) {
    super(props); //这一句不能省略，照抄即可
    this.state = {
      animationType: 'none', //none slide fade
      modalVisible: true, //模态场景是否可见
      transparent: true, //是否透明显示
    };
  }

  render() {
    return (
      <View style={{alignItems: 'center'}}>
        <Text>sss</Text>
      </View>
    );
  }

  _setModalVisible = visible => {
    this.setState({modalVisible: visible});
  };

  startShow = () => {
    AlertModal.alert('开始显示了');
  };
}
