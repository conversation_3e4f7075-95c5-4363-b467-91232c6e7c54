import _ from 'lodash';
const NotificationCenter = {
  _events: [],

  init: function () {
    this._events = [];
  },

  addObserver: function (object, eventName, callback) {
    if (callback == null) {
      console.error('NotificationCenter: need callback');
      return;
    }

    this._events.push([object, eventName, callback]);
  },

  removeObserver: function (object, eventName) {
    this._events = _.reject(this._events, event => {
      if (event[0] == object && event[1] === eventName) {
        return true;
      }
    });
  },

  dispatchEvent: function (eventName, argsObj) {
    try {
      _.each(this._events, event => {
        if (event[1] === eventName) {
          try {
            event[2](argsObj);
          } catch (error) {
            console.warn(`NotificationCenter: Error in event handler for ${eventName}:`, error);
          }
        }
      });
    } catch (error) {
      console.warn('NotificationCenter: Error in dispatchEvent:', error);
    }
  },
};

module.exports = {
  NotificationCenter: NotificationCenter,
  EVENT_ROOM: 8,
  EVENT_DEVICE: 9,
  EVENT_SCENE_ELECT: 11,
  EVENT_HOME_ADD_DEVICE: 12,
  EVENT_DEVICE_REFRESH_VIA_HOME: 13,
  EVENT_TIMEZONE: 14,
  EVENT_MAP: 18,
  EVENT_SCENE_KEY: 20,
  WIFI_SETTING: 23,
  EVENT_GRPC_NODE_CHANGE: 26,
  SCHEME_EVENT: 28, // OS theme change event
  HOME_TOP_BAR: 29,
  HOME_TOP_BAR_SCROLL: 30,
  ALEXA_CALLBACK_EVENT: 31,
  SUN_SELECT_ADD: 32,
  TO_BAR_HOME_EVENT: 35,
  DASHBOARD_REFRESH: 36,
  DISMISS_OVERLAY: 38,
  SELECT_DEVICE_EVENT: 39,
  EVENT_ADD_CONTROLLER: 40,
  D433_SELECT_DEVICE: 41,
  D433_SELECT_TYPE: 42,
  EVENT_SELECT_DATE: 43,
  DEVICE_NOTIFY_DATE: 44,
  BACKGROUND_IMAGE: 45,
  FILTER_DEVICE_EVENT: 47,
  SELECT_LONG: 50,
  SMART_LIST_ADD: 53,
  CLOSE_USER: 56,
};
