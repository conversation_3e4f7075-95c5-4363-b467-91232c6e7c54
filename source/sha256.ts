import { NativeModules } from 'react-native';

interface Sha256Lib {
  addListener?: () => void;
  sha256: (data: string) => Promise<string>;
  sha256Bytes: (data: string) => Promise<string>;
  sha1: (data: string) => Promise<string>;
  sha1Bytes: (data: string) => Promise<string>;
}

const sha256Lib = NativeModules.sha256Lib as Sha256Lib;

if (!sha256Lib.addListener) {
  console.warn('sha256Lib 缺少 addListener 方法');
}

export function sha256(data: string): Promise<string> {
  return sha256Lib.sha256(data);
}

export function sha256Bytes(data: string): Promise<string> {
  return sha256Lib.sha256Bytes(data);
}

export function sha1(data: string): Promise<string> {
  return sha256Lib.sha1(data);
}

export function sha1Bytes(data: string): Promise<string> {
  return sha256Lib.sha1Bytes(data);
}
