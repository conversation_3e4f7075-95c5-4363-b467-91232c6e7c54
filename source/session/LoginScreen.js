import React, { Component } from 'react';
import {
  View,
  Text,
  TextInput,
  // Image,
  StyleSheet,
  TouchableOpacity,
  findNodeHandle,
  Keyboard,
  Platform,
} from 'react-native';
import _ from 'lodash';
import I18n from '../I18n';
import AppConfig from '../../app_config';
import { HelperMemo, Helper } from '../Helper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Tme, IsDark } from '../ThemeStyle';
import SessionBase from './SessionBase';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Icon from 'react-native-vector-icons/Ionicons';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import AlertModal from '../share/AlertModal';
import { StatusBar } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { hideLoading, showLoading } from '../../ILoading';
import ScreenSizeContext from '../../WindowResizeContext';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';
import LoginHint from '../share/LoginHint';

class LoginScreen extends Component {
  constructor(props) {
    super(props);

    this.state = {
      email: '',
      password: '',
      submiting: false,
      passwordHidden: true,
      isZh: false,
      isDark: IsDark(),
      showLoginHint: false,
    };

    this.scroll = null;
  }

  static contextType = ScreenSizeContext;

  componentDidMount() {
    AsyncStorage.getItem('privacy', (error, data) => {
      setTimeout(() => {
        if (data !== '1') {
          if (HelperMemo.lang == 'zh_cn') {
            this.help();
          }
        }
      }, 1000);
    });
  }

  _scrollToInput() { }

  help() {
    this.setState({ showLoginHint: true });
  }

  render() {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: Tme('cardColor') }}>
        {Platform.OS === 'android' && (
          <>
            <StatusBar
              backgroundColor="transparent"
              translucent={true}
              barStyle={IsDark() ? 'light-content' : 'dark-content'}
            />
          </>
        )}
        <KeyboardAwareScrollView
          keyboardShouldPersistTaps="never"
          keyboardDismissMode="on-drag"
          style={{
            flex: 1,
            marginTop: Platform.OS === 'ios' ? 0 : 60,
          }}
          innerRef={ref => {
            this.scroll = ref;
          }}>
          <SessionBase title="" isDark={this.state.isDark} />
          <View
            style={{
              flex: 1,
            }}>
            <View
              style={[
                styles.account_view,
                { borderColor: Tme('inputBorderColor') },
              ]}>
              <MaterialIcons
                name="person-outline"
                size={20}
                style={{ marginRight: 8 }}
                color={Tme('textColor')}
              />
              <TextInput
                testID="email"
                keyboardType="email-address"
                autoCapitalize="none"
                placeholder={
                  I18n.t('session.login_name_input') +
                  ' / ' +
                  I18n.t('session.login_phone')
                }
                placeholderTextColor={Tme('placeholder')}
                autoCorrect={false}
                underlineColorAndroid="transparent"
                value={this.state.email}
                onChangeText={email => this.setState({ email })}
                style={[
                  styles.acount,
                  {
                    width: this.context.winWidth - 58,
                    color: Tme('cardTextColor'),
                    fontWeight: '500',
                    fontSize: 17,
                  },
                ]}
              />
            </View>
            <View
              style={[
                styles.account_view,
                {
                  justifyContent: 'space-between',
                  borderColor: Tme('inputBorderColor'),
                },
              ]}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <MaterialIcons
                  name="lock-outline"
                  size={20}
                  style={{ marginRight: 8 }}
                  color={Tme('textColor')}
                />
                <TextInput
                  testID="password"
                  returnKeyType="go"
                  autoCapitalize="none"
                  placeholder={I18n.t('session.password_input')}
                  placeholderTextColor={Tme('placeholder')}
                  underlineColorAndroid="transparent"
                  autoCorrect={false}
                  value={this.state.password}
                  onChangeText={password => this.setState({ password })}
                  onSubmitEditing={this._loginBtnEvent.bind(this)}
                  secureTextEntry={this.state.passwordHidden}
                  style={[
                    styles.acount,
                    {
                      width: this.context.winWidth - 58 - 32,
                      color: Tme('cardTextColor'),
                      fontWeight: '500',
                      fontSize: 17,
                    },
                  ]}
                  onFocus={event => {
                    this._scrollToInput(findNodeHandle(event.target));
                  }}
                />
              </View>
              <TouchableOpacity
                activeOpacity={0.8}
                style={{ width: 26 }}
                onPress={() =>
                  this.setState({
                    passwordHidden: !this.state.passwordHidden,
                  })
                }>
                {this.state.passwordHidden ? (
                  <Icon name="eye" size={20} color={Tme('textColor')} />
                ) : (
                  <Icon name="eye-off" size={20} color={Tme('textColor')} />
                )}
              </TouchableOpacity>
            </View>
          </View>
          <View
            style={{
              marginTop: 16,
              marginHorizontal: 16,
              flexDirection: 'row',
              justifyContent: 'flex-end',
              alignItems: 'center',
            }}>
            <TouchableOpacity
              onPress={this.froget.bind(this)}
              activeOpacity={0.8}>
              <Text
                style={{
                  color: this.state.isDark
                    ? Tme('textColor')
                    : AppConfig.ui.backgroud_border,
                }}>
                {I18n.t('home.forget_password')}
              </Text>
            </TouchableOpacity>
          </View>
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              marginTop: 40,
              flexDirection: 'row',
            }}
          />
          {/* <TouchableOpacity
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              marginTop: 40,
              flexDirection: 'row',
            }}
            onPress={() => {
              this.setState({
                isZh: !this.state.isZh,
              });
            }}
            activeOpacity={0.8}>
            {this.state.isZh ? (
              <Image
                source={require('../../img/earth.png')}
                style={{ width: 20, height: 20, marginRight: 8 }}
              />
            ) : (
              <Image
                source={require('../../img/china.png')}
                style={{ width: 20, height: 20, marginRight: 8 }}
              />
            )}
            <Text
              style={{
                color: this.state.isDark
                  ? Tme('textColor')
                  : AppConfig.ui.backgroud_border,
              }}>
              {this.state.isZh
                ? I18n.t('session.change_other')
                : I18n.t('session.change_china')}
            </Text>
          </TouchableOpacity> */}
          <View
            style={{
              flex: 1,
              marginTop: 30,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <TouchableOpacity
              testID="loginBtn"
              activeOpacity={0.8}
              style={{
                width: this.context.winWidth / 3,
                backgroundColor: AppConfig.ui.backgroud_border,
                alignItems: 'center',
                justifyContent: 'center',
                height: 50,
                borderRadius: 30,
                shadowOffset: { width: 0, height: 0 },
                shadowColor: AppConfig.ui.backgroud_border,
                shadowOpacity: 0.5,
                shadowRadius: 10,
              }}
              onPress={this._loginBtnEvent.bind(this)}>
              <Text
                style={{
                  fontSize: 17,
                  fontWeight: '500',
                  lineHeight: 50,
                  color: 'white',
                }}>
                {I18n.t('home.sign')}
              </Text>
            </TouchableOpacity>
          </View>

          {/*
          <View style={{
            justifyContent: "center",
            alignItems: "center",
            marginTop: 30
          }}>
            <Text style={{ fontSize: 12, color: Tme("textColor") }}>{I18n.t("global.or")}</Text>
          </View>
          <WingBlank>
            <TouchableOpacity
              activeOpacity={0.8}
              style={[styles.demo_btn, {
                borderWidth: 0,
                marginBottom: 30
              }]} onPress={this.loginDemo.bind(this)}>
              <Text style={{ color: this.state.isDark ? "rgba(255, 255, 255, 0.87)" : AppConfig.ui.backgroud_border }}>{I18n.t("global.login_demo")}</Text>
            </TouchableOpacity>
          </WingBlank>
          */}
          <TouchableOpacity
            activeOpacity={1.0}
            onPress={this.signup.bind(this)}
            style={[styles.footer, { marginTop: 50 }]}>
            {/* <Text style={{ color: Tme("textColor") }}>{I18n.t("index.no_account")}</Text> */}
            <Text style={styles.footer_text}>{I18n.t('home.sign_up')}</Text>
          </TouchableOpacity>
        </KeyboardAwareScrollView>
        <LoginHint
          visible={this.state.showLoginHint}
          onClose={() => this.setState({ showLoginHint: false })}
          onShowHint={() => this.setState({ showLoginHint: true })}
          navigation={this.props.navigation}
        />
      </SafeAreaView>
    );
  }

  froget() {
    this.props.navigation.push('signUpView', {
      title: I18n.t('home.forget_password'),
      isDark: this.state.isDark,
      isZh: this.state.isZh,
      type: 'forget',
    });
  }

  signup() {
    this.props.navigation.push('SelectCountry', {
      title: I18n.t('session.select_country'),
      isDark: this.state.isDark,
      isZh: this.state.isZh,
      type: 'forget',
    });
  }

  loginDemo() {
    this.props.navigation.push('LoginDemo', {
      title: '',
      isDark: this.state.isDark,
    });
  }

  _loginBtnEvent() {
    var errors = [];
    if (_.isEmpty(this.state.email)) {
      if (this.state.isZh) {
        errors.push(I18n.t('session.not_input_phone'));
      } else {
        errors.push(I18n.t('session.not_email_or_phone'));
      }
    }
    if (_.isEmpty(this.state.password)) {
      errors.push(I18n.t('session.please_enter_password'));
    }
    if (errors.length > 0) {
      AlertModal.alert(I18n.t('home.warning_message'), errors.join('\n'));
      return;
    }

    Keyboard.dismiss();
    showLoading();
    // submit to server
    var body = {
      email: this.state.email,
      password: this.state.password,
    };
    Helper.httpPOST(
      '/users/signin',
      {
        error: error => {
          AlertModal.alert(
            I18n.t('home.warning_message'),
            _.flatten([error]).join('\n'),
          );
          hideLoading();
        },
        ensure: () => {
          hideLoading();
        },
        cloud: true,
        from: 'session',
        apiUrl: this.state.isZh
          ? `${AppConfig.cn_api}/iot`
          : `${AppConfig.api}/iot`,
        success: data => {
          // save user data to app
          var localData = {
            user: data.user,
            uuid: data.user.uuid,
            role: data.role,
            home_id: data.home_id,
            is_demo: data.is_demo,
            sn: data.sn,
          };
          AsyncStorage.setItem('user_data', JSON.stringify(localData), () => {
            HelperMemo.user_data = localData;
            PubSub.publish(PubSubEvent.EVENT_LOGGED_IN);
          });
        },
      },
      body,
    );
  }
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  btn: {
    textAlign: 'center',
    borderRadius: 4,
    padding: 10,
    color: '#333333',
    backgroundColor: AppConfig.ui.backgroud_border,
  },
  account_view: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    paddingTop: 20,
    borderBottomWidth: 1,
  },
  acount: {
    height: 48,
    marginLeft: 6,
    marginRight: 6,
  },
  big_btn: {
    flex: 1,
    marginTop: 20,
    marginHorizontal: 2,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 3,
    borderColor: AppConfig.ui.backgroud_border,
    borderWidth: 1,
    backgroundColor: AppConfig.ui.backgroud_border,
  },
  big_btn_line: {
    marginTop: 10,
    marginBottom: 10,
    alignItems: 'center',
  },
  line: {
    marginTop: 10,
    marginBottom: 10,
    fontSize: 14,
    color: AppConfig.ui.backgroud_border,
  },
  footer: {
    height: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footer_text: {
    color: AppConfig.ui.backgroud_border,
    marginTop: 8,
  },
  demo_btn: {
    flex: 1,
    marginTop: 20,
    marginHorizontal: 2,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 3,
  },
});
export default LoginScreen;
