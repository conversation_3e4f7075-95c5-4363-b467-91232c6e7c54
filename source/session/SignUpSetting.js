import React, {Component} from 'react';
import {View, Text, TouchableOpacity, SafeAreaView} from 'react-native';
import {Helper, HelperMemo} from '../Helper';
import I18n from '../I18n';
import {
  NotificationCenter,
  EVENT_TIMEZONE,
  SELECT_LONG,
} from '../NotificationCenter';
import PubSub from 'pubsub-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import AppConfig from '../../app_config';
import {Tme, Colors} from '../ThemeStyle';
import _ from 'lodash';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import AlertModal from '../share/AlertModal';
import {hideLoading, showLoading} from '../../ILoading';
import { PubSubEvent } from '../types/PubSubEvent';

const lang = [
  {
    key: 'en',
    value: 'English',
  },
  {
    key: 'zh_cn',
    value: '简体中文',
  },
];
export default class SignUpSetting extends Component {
  constructor(props) {
    super(props);
    this.state = {
      zone_id: this.props.route.params.zone_id,
      zone_name: '',
      time_zones: [],
      lang: this.getLang(),
      langKey: HelperMemo.lang,
    };
  }

  componentDidMount() {
    var that = this;
    this.doFetchData();

    NotificationCenter.addObserver(this, EVENT_TIMEZONE, data => {
      that.setState({
        zone_id: data.name,
      });
    });

    NotificationCenter.addObserver(this, SELECT_LONG, data => {
      that.setState({
        lang: data.value,
        langKey: data.key,
      });
    });
  }

  componentWillUnmount() {
    NotificationCenter.removeObserver(EVENT_TIMEZONE);
    NotificationCenter.removeObserver(SELECT_LONG);
  }

  getLang() {
    if (HelperMemo.lang) {
      return _.find(lang, l => l.key === HelperMemo.lang).value;
    } else {
      return 'English';
    }
  }

  doFetchData() {
    var that = this;
    showLoading();
    Helper.httpGET('/users/get_time_list', {
      ensure: () => {
        hideLoading();
      },
      cloud: true,
      form: 'session',
      apiUrl:
        this.props.route.params.country == 'cn'
          ? `${AppConfig.cn_api}/iot`
          : `${AppConfig.api}/iot`,
      success: data => {
        const zone_name = _.find(
          data.list,
          zone => zone.name == that.state.zone_id,
        );
        that.setState({
          time_zone: data.zone,
          time_zones: data.list,
          zone_name: zone_name ? zone_name.s : '',
        });
      },
    });
  }

  push_timezone_list() {
    this.props.navigation.push('timezoneList', {
      time_zones: this.state.time_zones,
      time_zone: this.state.time_zone,
      from: 'signup',
      title: I18n.t('home.time_zone'),
    });
  }

  _savezone() {
    const that = this;
    if (_.isEmpty(this.state.zone_id) || _.isEmpty(this.state.langKey)) {
      AlertModal.alert(I18n.t('home.need_set_locale_and_time_zone'));
      return;
    }

    var body = {
      timezone: this.state.zone_id,
      locale: this.state.langKey,
    };
    showLoading();
    Helper.httpPOST(
      '/users/' + that.props.route.params.user_id + '/set_config',
      {
        ensure: () => {
          hideLoading();
        },
        cloud: true,
        form: 'session',
        apiUrl:
          this.props.route.params.country == 'cn'
            ? `${AppConfig.cn_api}/iot`
            : `${AppConfig.api}/iot`,
        success: data => {
          AlertModal.alert(I18n.t('session.signup_success_login'), '', [
            {
              text: 'OK',
              onPress: () => {
                AsyncStorage.setItem(
                  'lang',
                  JSON.stringify(that.state.langKey),
                  () => {
                    HelperMemo.lang = that.state.langKey;
                    setTimeout(() => {
                      PubSub.publish(PubSubEvent.RESTART_APP);
                    }, 100);
                  },
                );
              },
            },
          ]);
        },
      },
      body,
    );
  }

  push_long_list() {
    this.props.navigation.push('langScreen', {
      from: 'signup',
      lang: this.state.langKey,
      title: I18n.t('home.language_for_mobile'),
    });
  }

  render() {
    return (
      <SafeAreaView style={{flex: 1, backgroundColor: Tme('cardColor')}}>
        <View
          style={{
            padding: 16,
          }}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.push_timezone_list.bind(this)}
            style={{backgroundColor: Tme('cardColor')}}>
            <View
              style={{
                flexDirection: 'row',
                paddingVertical: 20,
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="access-time"
                  size={20}
                  color={Tme('textColor')}
                />
                <Text style={{marginLeft: 6, color: Tme('cardTextColor')}}>
                  {I18n.t('global.timezone_setting')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text
                  style={{
                    color: Tme('cardTextColor'),
                    marginLeft: 8,
                  }}>
                  {this.state.zone_name}
                </Text>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </View>
          </TouchableOpacity>
          <View style={{height: 1, backgroundColor: Tme('bgColor')}} />
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.push_long_list.bind(this)}
            style={{backgroundColor: Tme('cardColor')}}>
            <View
              style={{
                flexDirection: 'row',
                paddingVertical: 20,
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <FontAwesome
                  name="language"
                  size={20}
                  color={Tme('textColor')}
                />
                <Text style={{marginLeft: 8, color: Tme('cardTextColor')}}>
                  {I18n.t('home.language_for_mobile')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text
                  style={{
                    color: Tme('cardTextColor'),
                    marginLeft: 8,
                  }}>
                  {this.state.lang}
                </Text>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </View>
          </TouchableOpacity>
        </View>

        <View style={{alignItems: 'center', marginTop: 20}}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this._savezone.bind(this)}
            style={{
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: Colors.MainColor,
              height: 50,
              borderRadius: 30,
              width: '30%',
              shadowOffset: {width: 0, height: 0},
              shadowColor: Colors.MainColor,
              shadowOpacity: 0.5,
              shadowRadius: 10,
            }}>
            <Text style={{color: '#fff'}}>{I18n.t('home.save')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }
}
