import React, {Component} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import {Colors} from '../ThemeStyle';
// import I18n from "../I18n";
export default class SessionBase extends Component {
  constructor(props) {
    super(props);
  }

  dashboard() {
    // ControllerList
  }

  render() {
    return (
      <>
        {Platform.OS == 'android' && <View style={{height: 20, flex: 1}} />}
        <TouchableOpacity
          // onPress={this.dashboard.bind(this)}
          activeOpacity={0.8}
          hitSlop={{top: 20, right: 20, bottom: 20, left: 20}}
          style={{
            flex: 1,
            alignItems: 'flex-end',
            justifyContent: 'center',
            paddingRight: 16,
          }}>
          {/* <Text style={{ color: Colors.MainColor, fontSize: 16 }}>{I18n.t("global.dashboard")}</Text> */}
        </TouchableOpacity>
        <View
          style={{height: 150, justifyContent: 'center', alignItems: 'center'}}>
          <View
            style={{
              shadowOffset: {width: 0, height: 0},
              shadowColor: Colors.MainColor,
              shadowOpacity: 0.4,
              shadowRadius: 10,
              marginBottom: 20,
            }}>
            <Image source={require('../../img/App.png')} style={styles.logo} />
          </View>
          <View>
            <Text
              style={{
                color: this.props.isDark
                  ? 'rgba(255, 255, 255, 0.87)'
                  : 'rgba(125,125,125,1)',
                fontWeight: '600',
                fontSize: 20,
              }}>
              {this.props.title}
            </Text>
          </View>
        </View>
      </>
    );
  }
}
const styles = StyleSheet.create({
  logo: {
    height: 80,
    width: 80,
    borderRadius: 12,
  },
  header: {
    height: 100,
    alignItems: 'center',
  },
});
