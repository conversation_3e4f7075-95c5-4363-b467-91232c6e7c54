import React, {Component} from 'react';
import {View, ScrollView, SafeAreaView} from 'react-native';
import {Helper} from '../Helper';
import I18n from '../I18n';
import {Tme, IsDark} from '../ThemeStyle';
import RadioButtons from '../RadioButtons';

const country_cn = [
  {key: 'cn', value: 'China'},
  {key: 'na', value: 'North America'},
  {key: 'sa', value: 'South America'},
  {key: 'eu', value: 'Europe'},
  {key: 'oc', value: 'Oceania'},
  {key: 'other_asia', value: 'Other districts of Asia'},
  {key: 'other', value: 'Other'},
];
const country_us = [
  {key: 'na', value: 'North America'},
  {key: 'sa', value: 'South America'},
  {key: 'eu', value: 'Europe'},
  {key: 'oc', value: 'Oceania'},
  {key: 'other_asia', value: 'Other districts of Asia'},
  {key: 'cn', value: 'China'},
  {key: 'other', value: 'Other'},
];
export default class SelectCountry extends Component {
  constructor(props) {
    super(props);

    this.state = {
      checked: '',
    };
  }

  render() {
    return (
      <SafeAreaView style={{flex: 1, backgroundColor: Tme('cardColor')}}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{
            flex: 1,
            backgroundColor: Tme('cardColor'),
          }}>
          <View style={{marginTop: 10}}>
            <RadioButtons
              data={Helper.isLanguageCh() ? country_cn : country_us}
              backgroundColor={Tme('cardColor')}
              defaultKey={this.state.checked}
              onChange={this.onChange.bind(this)}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }

  onChange(data) {
    this.setState(
      {
        checked: data,
      },
      () => {
        this.props.navigation.push('signUpView', {
          title: I18n.t('session.register'),
          isDark: IsDark(),
          country: this.state.checked,
          type: 'signup',
        });
      },
    );
  }
}
