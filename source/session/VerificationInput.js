import React, {useState} from 'react';
import {Text, Platform, View} from 'react-native';
import {Colors} from '../ThemeStyle';

import {
  CodeField,
  Cursor,
  useBlurOnFulfill,
  useClearByFocusCell,
} from 'react-native-confirmation-code-field';

const CELL_COUNT = 6;

const VerificationInput = ({onChangeText}) => {
  const [value, setValue] = useState('');
  const ref = useBlurOnFulfill({value, cellCount: CELL_COUNT});
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  });

  return (
    <CodeField
      ref={ref}
      {...props}
      value={value}
      onChangeText={e => {
        setValue(e);
        onChangeText(e);
      }}
      cellCount={CELL_COUNT}
      style={{flex: 1}}
      keyboardType="number-pad"
      textContentType="oneTimeCode"
      autoComplete={Platform.select({
        android: 'sms-otp',
        default: 'one-time-code',
      })}
      testID="my-code-input"
      renderCell={({index, symbol, isFocused}) => (
        <View
          style={{
            borderBottomColor: Colors.MainColor,
            borderBottomWidth: 1,
            borderStyle: 'solid',
          }}
          key={index}>
          <Text
            style={[
              {
                width: 40,
                lineHeight: 38,
                fontSize: 24,
                height: 40,
                textAlign: 'center',
              },
            ]}
            onLayout={getCellOnLayoutHandler(index)}>
            {symbol || (isFocused ? <Cursor /> : null)}
          </Text>
        </View>
      )}
    />
  );
};

export default VerificationInput;
