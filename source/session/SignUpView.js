import React, {Component} from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Keyboard,
  SafeAreaView,
} from 'react-native';
import _ from 'lodash';
import * as RNLocalize from 'react-native-localize';
import I18n from '../I18n';
import AppConfig from '../../app_config';
import {Helper} from '../Helper';
import {Tme, Colors} from '../ThemeStyle';
import VerificationInput from './VerificationInput';

import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Icon from 'react-native-vector-icons/Ionicons';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import CheckBox from '../check_box/index';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import AlertModal from '../share/AlertModal';
import {hideLoading, showLoading} from '../../ILoading';
import ScreenSizeContext from '../../WindowResizeContext';

class SignUpView extends Component {
  constructor(props) {
    super(props);

    this.state = {
      submiting: false,
      email_type: '',
      token: '',
      password_confirmation: '',
      password: '',
      email: '',
      passwordHidden_password: true,
      passwordHidden: true,
      isAgree: false,
      name: '',
    };
    this.userInput = '';
  }
  static contextType = ScreenSizeContext;
  componentWillUnmount() {}

  render() {
    var email_html;
    switch (this.state.email_type) {
      case 'token':
        email_html = (
          <View style={{flexDirection: 'column', marginTop: 20}}>
            <View
              style={[
                styles.account_view,
                {borderBottomWidth: 0, borderColor: Tme('inputBorderColor')},
              ]}>
              <Text
                style={{
                  paddingHorizontal: 14,
                  textAlign: 'center',
                  color: Tme('textColor'),
                }}>
                {I18n.t('session.send_token')}{' '}
                <Text
                  style={{color: Colors.MainColor}}
                  onPress={this.send_token.bind(this, this.userInput)}>
                  {I18n.t('session.send')}
                </Text>
              </Text>
            </View>
            <View
              style={{
                marginTop: 20,
                marginBottom: 2,
                paddingHorizontal: 40,
              }}>
              <VerificationInput
                onChangeText={value => {
                  this.setState({token: value});
                }}
              />
            </View>
            <View
              style={{
                marginTop: 60,
                marginBottom: 40,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <TouchableOpacity
                activeOpacity={0.8}
                style={[styles.big_btn, {width: this.context.winWidth / 3}]}
                onPress={this._password.bind(this, this.userInput)}>
                <Text style={styles.big_btn_text}>{I18n.t('home.next')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        );
        break;
      case 'password':
        email_html = (
          <View>
            {this.props.route.params.type !== 'forget' && (
              <View
                style={[
                  styles.account_view,
                  {
                    justifyContent: 'space-between',
                    borderColor: Tme('inputBorderColor'),
                  },
                ]}>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <MaterialIcons
                    name="person-outline"
                    size={20}
                    style={{marginRight: 8}}
                    color={Tme('textColor')}
                  />
                  <TextInput
                    autoCapitalize="none"
                    placeholder={I18n.t('session.user_name')}
                    placeholderTextColor={Tme('placeholder')}
                    underlineColorAndroid="transparent"
                    autoCorrect={false}
                    value={this.state.name}
                    onChangeText={name => this.setState({name})}
                    style={[
                      styles.acount,
                      {
                        width: this.context.winWidth - 58 - 32,
                        color: Tme('cardTextColor'),
                        fontWeight: '500',
                        fontSize: 17,
                      },
                    ]}
                  />
                </View>
              </View>
            )}
            <View
              style={[
                styles.account_view,
                {
                  justifyContent: 'space-between',
                  borderColor: Tme('inputBorderColor'),
                },
              ]}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <MaterialIcons
                  name="lock-outline"
                  size={20}
                  style={{marginRight: 8}}
                  color={Tme('textColor')}
                />
                <TextInput
                  autoCapitalize="none"
                  placeholder={I18n.t('home.password')}
                  placeholderTextColor={Tme('placeholder')}
                  underlineColorAndroid="transparent"
                  autoCorrect={false}
                  value={this.state.password}
                  onChangeText={password => this.setState({password})}
                  secureTextEntry={this.state.passwordHidden}
                  style={[
                    styles.acount,
                    {
                      width: this.context.winWidth - 58 - 32,
                      color: Tme('cardTextColor'),
                      fontWeight: '500',
                      fontSize: 17,
                    },
                  ]}
                />
              </View>
              <TouchableOpacity
                activeOpacity={0.8}
                style={{width: 26}}
                onPress={() =>
                  this.setState({
                    passwordHidden: !this.state.passwordHidden,
                  })
                }>
                {this.state.passwordHidden ? (
                  <Icon name="eye" size={20} color={Tme('textColor')} />
                ) : (
                  <Icon name="eye-off" size={20} color={Tme('textColor')} />
                )}
              </TouchableOpacity>
            </View>
            <View
              style={[
                styles.account_view,
                {
                  justifyContent: 'space-between',
                  borderColor: Tme('inputBorderColor'),
                },
              ]}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <MaterialIcons
                  name="lock-outline"
                  size={20}
                  style={{marginRight: 8}}
                  color={Tme('textColor')}
                />
                <TextInput
                  returnKeyType="go"
                  autoCapitalize="none"
                  placeholder={I18n.t('session.password_confirmation')}
                  placeholderTextColor={Tme('placeholder')}
                  underlineColorAndroid="transparent"
                  autoCorrect={false}
                  value={this.state.password_confirmation}
                  onChangeText={password_confirmation =>
                    this.setState({password_confirmation})
                  }
                  onSubmitEditing={this._signUp.bind(this, this.userInput)}
                  secureTextEntry={this.state.passwordHidden_password}
                  style={[
                    styles.acount,
                    {
                      width: this.context.winWidth - 58 - 32,
                      color: Tme('cardTextColor'),
                      fontWeight: '500',
                      fontSize: 17,
                    },
                  ]}
                />
              </View>
              <TouchableOpacity
                activeOpacity={0.8}
                style={{width: 26}}
                onPress={() =>
                  this.setState({
                    passwordHidden_password:
                      !this.state.passwordHidden_password,
                  })
                }>
                {this.state.passwordHidden_password ? (
                  <Icon name="eye" size={20} color={Tme('textColor')} />
                ) : (
                  <Icon name="eye-off" size={20} color={Tme('textColor')} />
                )}
              </TouchableOpacity>
            </View>

            <View
              style={{
                marginTop: 60,
                marginBottom: 40,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <TouchableOpacity
                activeOpacity={0.8}
                style={[styles.big_btn, {width: this.context.winWidth / 3}]}
                onPress={this._signUp.bind(this, this.userInput)}>
                <Text style={styles.big_btn_text}>
                  {this.props.route.params.type === 'forget'
                    ? I18n.t('home.save')
                    : I18n.t('session.register')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        );
        break;
      default:
        var placeholder =
          I18n.t('session.login_name_input') +
          ' / ' +
          I18n.t('session.login_phone');
        var icon = 'person-outline';
        if (this.props.route.params.type == 'signup') {
          if (this.props.route.params.country == 'cn') {
            placeholder =
              I18n.t('session.login_name_input') +
              ' / ' +
              I18n.t('session.login_phone');
          } else {
            placeholder = I18n.t('session.login_name_input');
          }
        }
        email_html = (
          <View>
            <View
              style={[
                styles.account_view,
                {borderColor: Tme('inputBorderColor')},
              ]}>
              <MaterialIcons
                name={icon}
                size={20}
                style={{marginRight: 8}}
                color={Tme('textColor')}
              />
              <TextInput
                keyboardType="email-address"
                autoCapitalize="none"
                placeholder={placeholder}
                placeholderTextColor={Tme('placeholder')}
                autoCorrect={false}
                underlineColorAndroid="transparent"
                value={this.state.email}
                onChangeText={email => this.setState({email})}
                style={[
                  styles.acount,
                  {
                    width: this.context.winWidth - 58,
                    color: Tme('cardTextColor'),
                    fontWeight: '500',
                    fontSize: 17,
                  },
                ]}
              />
            </View>
            {this.props.route.params.type == 'signup' ? (
              <View
                style={{
                  marginTop: 30,
                  marginRight: 16,
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <CheckBox
                  rightTextView={
                    <Text style={{color: Tme('textColor'), marginLeft: 4}}>
                      {I18n.t('global.agree')}
                    </Text>
                  }
                  onClick={() => this.onClick()}
                  isChecked={this.state.isAgree}
                  checkedImage={
                    <MaterialCommunityIcons
                      name="checkbox-marked-outline"
                      size={20}
                      color={Colors.MainColor}
                    />
                  }
                  unCheckedImage={
                    <MaterialCommunityIcons
                      name="checkbox-blank-outline"
                      size={20}
                      color={Tme('textColor')}
                    />
                  }
                />
                <TouchableOpacity
                  style={{marginLeft: 4}}
                  activeOpacity={0.8}
                  onPress={this._privacy.bind(this)}>
                  <Text style={{color: Colors.MainColor}}>
                    {I18n.t('global.privacy')}
                  </Text>
                </TouchableOpacity>
              </View>
            ) : null}
            <View
              style={{
                marginTop: 60,
                marginBottom: 40,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <TouchableOpacity
                activeOpacity={0.8}
                style={[styles.big_btn, {width: this.context.winWidth / 3}]}
                onPress={this._valid_email.bind(this)}>
                <Text style={styles.big_btn_text}>{I18n.t('home.next')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        );
    }

    return (
      <SafeAreaView style={{flex: 1, backgroundColor: Tme('cardColor')}}>
        <KeyboardAwareScrollView
          keyboardShouldPersistTaps="never"
          keyboardDismissMode="on-drag"
          style={[{flex: 1}]}>
          <View style={{marginTop: 10}}>{email_html}</View>
        </KeyboardAwareScrollView>
      </SafeAreaView>
    );
  }

  onClick() {
    this.setState({
      isAgree: !this.state.isAgree,
    });
  }

  _privacy() {
    this.props.navigation.push('HintView', {
      urlTitle: 'terms-of-service',
      type: 'about',
      from: 'signup',
      country: this.props.country,
    });
  }

  _signUp(type) {
    var that = this;
    var errors = [];
    var timezone = RNLocalize.getTimeZone();
    if (_.isEmpty(this.state.password)) {
      errors.push(I18n.t('session.please_enter_password'));
    }

    var reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/;
    if (!reg.test(this.state.password)) {
      errors.push(I18n.t('session.password_length'));
    }
    if (this.state.password !== this.state.password_confirmation) {
      errors.push(I18n.t('session.two_password'));
    }
    if (_.isEmpty(this.state.name)) {
      errors.push(I18n.t('session.no_name_desp'));
    }

    if (errors.length > 0) {
      AlertModal.alert(I18n.t('home.warning_message'), errors.join('\n'));
      return;
    }

    var body = {
      password: this.state.password,
      password_confirmation: this.state.password_confirmation,
      email: this.state.email,
      name: this.state.name,
      token: this.state.token,
      zone: this.props.route.params.country,
      timezone: timezone,
    };
    //type: this.props.type, // forget singup
    showLoading();
    var url = '/users';
    if (this.props.type === 'forget') {
      url = '/users/forget_password';
    }

    Keyboard.dismiss();
    Helper.httpPOST(
      url,
      {
        ensure: () => {
          hideLoading();
        },
        cloud: true,
        form: 'session',
        apiUrl:
          this.props.route.params.country == 'cn'
            ? `${AppConfig.cn_api}/iot`
            : `${AppConfig.api}/iot`,
        success: data => {
          if (that.props.type === 'forget') {
            AlertModal.alert(I18n.t('home.edit_success'), '', [
              {
                text: 'OK',
                onPress: () => {
                  this.props.navigation.popToTop();
                },
              },
            ]);
          } else {
            this.props.navigation.push('SignUpSetting', {
              title: I18n.t('home.locale_and_time_zone_config'),
              user_id: data.user_id,
              zone_id: data.timezone,
              country: this.state.checked,
              type: 'signup',
            });
          }
        },
      },
      body,
    );
  }

  _password(type) {
    var that = this;

    var body = {
      token: this.state.token,
      name: this.state.email,
    };
    showLoading();
    Helper.httpPOST(
      '/users/valid_token',
      {
        cloud: true,
        form: 'session',
        apiUrl:
          this.props.route.params.country == 'cn'
            ? `${AppConfig.cn_api}/iot`
            : `${AppConfig.api}/iot`,
        ensure: () => {
          hideLoading();
        },
        success: data => {
          that.setState(
            {email_type: 'password', title: I18n.t('session.password_input')},
            () => {
              AlertModal.alert(I18n.t('session.please_enter_password'));
            },
          );
        },
      },
      body,
    );
  }

  send_token(type) {
    var that = this;
    var body = {
      type: type,
      id: this.state.email,
    };
    showLoading();

    Helper.httpPOST(
      '/users/send_token',
      {
        cloud: true,
        form: 'session',
        apiUrl:
          this.props.route.params.country == 'cn'
            ? `${AppConfig.cn_api}/iot`
            : `${AppConfig.api}/iot`,
        success: data => {
          that.setState(
            {email_type: 'token', title: I18n.t('session.get_token')},
            () => {
              if (type == 'email') {
                AlertModal.alert(I18n.t('session.token_send'));
              } else {
                AlertModal.alert(I18n.t('session.token_send_phone'));
              }
            },
          );
        },
        ensure: () => {
          hideLoading();
        },
      },
      body,
    );
  }

  login() {
    this.props.navigation.pop();
  }

  _valid_email() {
    var that = this;
    var errors = [];
    if (_.isEmpty(this.state.email)) {
      if (this.props.route.params.type == 'signup') {
        if (this.props.route.params.country == 'cn') {
          errors.push(I18n.t('session.no_user_name'));
        } else {
          errors.push(I18n.t('session.not_email_or_phone'));
        }
      } else {
        errors.push(I18n.t('session.no_user_name'));
      }
    } else {
      var reg_email = /^[\w+\-.]+@[a-z\d\-.]+\.[a-z]+$/i;
      var re_phone = /^1\d{10}$/;
      if (this.props.route.params.type == 'signup') {
        if (this.props.route.params.country == 'cn') {
          if (this.state.email.indexOf('@') > 0) {
            if (reg_email.test(this.state.email)) {
              that.userInput = 'email';
            } else {
              errors.push(I18n.t('session.email_type'));
            }
          } else {
            if (re_phone.test(this.state.email)) {
              that.userInput = 'phone';
            } else {
              errors.push(I18n.t('session.not_phone'));
            }
          }
        } else {
          if (reg_email.test(this.state.email)) {
            that.userInput = 'email';
          } else {
            errors.push(I18n.t('session.email_type'));
          }
        }
      }
    }
    if (this.props.route.params.type == 'signup') {
      if (!this.state.isAgree) {
        errors.push(
          I18n.t('global.i_agree') + ' ' + I18n.t('global.privacy') + '!',
        );
      }
    }
    if (errors.length > 0) {
      AlertModal.alert(I18n.t('home.warning_message'), errors.join('\n'));
      return;
    }

    var body = {
      email: this.state.email,
      type: this.props.route.params.type,
    };

    Helper.httpPOST(
      '/users/valid_email_or_phone',
      {
        cloud: true,
        form: 'session',
        apiUrl:
          this.props.route.paramscountry == 'cn'
            ? `${AppConfig.cn_api}/iot`
            : `${AppConfig.api}/iot`,
        ensure: () => {
          hideLoading();
        },
        success: data => {
          setTimeout(() => {
            that.send_token(that.userInput);
          }, 300);
        },
      },
      body,
    );
  }
}

const styles = StyleSheet.create({
  slideContent: {
    flex: 1,
  },
  account_view: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    paddingTop: 20,
    borderBottomWidth: 1,
  },
  acount: {
    height: 48,
    marginLeft: 6,
    marginRight: 6,
  },
  big_btn: {
    backgroundColor: AppConfig.ui.backgroud_border,
    alignItems: 'center',
    height: 50,
    borderRadius: 30,
    shadowOffset: {width: 0, height: 0},
    shadowColor: AppConfig.ui.backgroud_border,
    shadowOpacity: 0.5,
    shadowRadius: 10,
  },
  big_btn_line: {
    marginTop: 10,
    marginBottom: 10,
    alignItems: 'center',
  },
  big_btn_text: {
    fontSize: 17,
    fontWeight: '500',
    lineHeight: 50,
    color: 'white',
  },
  footer: {
    height: 60,
    marginTop: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footer_text: {
    color: AppConfig.ui.backgroud_border,
    marginTop: 8,
  },
});
export default SignUpView;
