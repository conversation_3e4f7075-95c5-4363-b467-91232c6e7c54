import React, {useEffect} from 'react';
import {Tme} from '../ThemeStyle';
import WebViewWithProgress from '../WebViewWithProgress';
import {View} from 'react-native';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';

type LoginPrivacyParams = {
  LoginPrivacy: {
    url: string;
    onBack?: () => void;
    title: string;
  };
};

export default function LoginPrivacy() {
  const navigation = useNavigation();
  const route = useRoute<RouteProp<LoginPrivacyParams, 'LoginPrivacy'>>();

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      if (route.params?.onBack) {
        route.params.onBack();
      }
    });
    return unsubscribe;
  }, [navigation, route.params]);

  return (
    <View style={{flex: 1, backgroundColor: Tme('bgColor')}}>
      <WebViewWithProgress
        withoutDefaultHeaders={false}
        source={{uri: route.params.url}}
      />
    </View>
  );
}
