import React from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import _ from 'lodash';
import I18n from '../I18n';
import AppConfig from '../../app_config';
import {Helper<PERSON>em<PERSON>, Helper} from '../Helper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {Tme} from '../ThemeStyle';
import SessionBase from './SessionBase';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import AlertModal from '../share/AlertModal';
import {hideLoading, showLoading} from '../../ILoading';
import ScreenSizeContext from '../../WindowResizeContext';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';

function LoginDemo({route}) {
  const [email, setEmail] = React.useState('');

  const context = React.useContext(ScreenSizeContext);

  const _loginBtnEvent = () => {
    var errors = [];

    if (_.isEmpty(email)) {
      errors.push(I18n.t('session.not_email_or_phone'));
    }
    if (errors.length > 0) {
      AlertModal.alert(I18n.t('home.warning_message'), errors.join('\n'));
      return;
    }
    showLoading();
    // submit to server
    var body = {
      email: email,
    };
    Helper.httpPOST(
      '/users/user_demo',
      {
        ensure: () => {
          hideLoading();
        },
        cloud: true,
        success: data => {
          // save user data to app
          var localData = {
            user: data.user,
            uuid: data.user.uuid,
            role: data.role,
            home_id: data.home_id,
            is_demo: data.is_demo,
            sn: data.sn,
          };

          AsyncStorage.setItem('user_data', JSON.stringify(localData), () => {
            HelperMemo.user_data = localData;
          });
          PubSub.publish(PubSubEvent.RESTART_APP);
        },
      },
      body,
    );
  };

  return (
    <SafeAreaView style={[{flex: 1, backgroundColor: Tme('bgColor')}]}>
      <KeyboardAwareScrollView
        keyboardShouldPersistTaps="never"
        keyboardDismissMode="on-drag"
        style={[{flex: 1, backgroundColor: Tme('bgColor')}]}>
        <SessionBase
          title={I18n.t('global.login_demo')}
          isDark={route.params.isDark}
        />

        <View style={{flex: 1}}>
          <View
            style={[
              styles.account_view,
              {borderColor: Tme('inputBorderColor')},
            ]}>
            <MaterialIcons
              name="person-outline"
              size={20}
              style={{marginRight: 8}}
              color={Tme('textColor')}
            />
            <TextInput
              keyboardType="email-address"
              autoCapitalize="none"
              placeholder={I18n.t('session.login_name_input')}
              placeholderTextColor={Tme('placeholder')}
              autoCorrect={false}
              underlineColorAndroid="transparent"
              value={email}
              onChangeText={e => setEmail(e)}
              style={[
                styles.acount,
                {
                  width: context.winWidth - 58,
                  color: Tme('cardTextColor'),
                  fontWeight: '500',
                  fontSize: 17,
                },
              ]}
            />
          </View>
        </View>
        <View
          style={{
            flex: 1,
            marginTop: 60,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <TouchableOpacity
            activeOpacity={0.8}
            style={{
              backgroundColor: AppConfig.ui.backgroud_border,
              alignItems: 'center',
              height: 50,
              width: context.winWidth / 3,
              borderRadius: 30,
              shadowOffset: {width: 0, height: 0},
              shadowColor: AppConfig.ui.backgroud_border,
              shadowOpacity: 0.5,
              shadowRadius: 10,
            }}
            onPress={_loginBtnEvent}>
            <Text
              style={{
                fontSize: 17,
                fontWeight: '500',
                lineHeight: 50,
                color: 'white',
              }}>
              {I18n.t('home.sign')}
            </Text>
          </TouchableOpacity>
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  logo: {
    height: 86,
    width: 86,
    borderRadius: 12,
  },
  header: {
    height: 100,
    alignItems: 'center',
  },
  content: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  btn: {
    textAlign: 'center',
    borderRadius: 4,
    padding: 10,
    color: '#333333',
    backgroundColor: AppConfig.ui.backgroud_border,
  },
  account_view: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    paddingTop: 20,
    borderBottomWidth: 1,
  },
  acount: {
    height: 48,
    marginLeft: 6,
    marginRight: 6,
  },
  big_btn: {
    flex: 1,
    marginTop: 20,
    marginHorizontal: 2,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 3,
    borderColor: AppConfig.ui.backgroud_border,
    borderWidth: 1,
    backgroundColor: AppConfig.ui.backgroud_border,
  },
  big_btn_line: {
    marginTop: 10,
    marginBottom: 10,
    alignItems: 'center',
  },
  line: {
    marginTop: 10,
    marginBottom: 10,
    fontSize: 14,
    color: AppConfig.ui.backgroud_border,
  },
  footer: {
    height: 60,
    borderTopColor: '#d7dae2',
    borderTopWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footer_text: {
    color: AppConfig.ui.backgroud_border,
  },
  demo_btn_text: {
    color: AppConfig.ui.backgroud_border,
  },
  demo_btn: {
    flex: 1,
    marginTop: 20,
    marginHorizontal: 2,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 3,
    borderColor: AppConfig.ui.backgroud_border,
    borderWidth: 1,
    backgroundColor: 'white',
  },
});

export default LoginDemo;
