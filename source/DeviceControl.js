import { Helper, <PERSON>er<PERSON>em<PERSON> } from './Helper';
import { colorSwitchCmdParams } from './Tools';
import _ from 'lodash';
import { encode } from 'base-64';

export default class DeviceControl {
  constructor(props) {
    this.props = props;

    this.unify = HelperMemo.unify_commands_schema;
  }

  formString(str, args) {
    _.forEach(args, (v, k) => {
      str = str.replaceAll(`%{${k}}`, v);
    });
    return str;
  }

  decodeCmd(type) {
    // Demo[type];
  }

  end_learn(callback) {
    var cmd = {
      type: '433',
      cmd: 'cmd',
      params1: this.props.param + ',end learn',
    };
    if (callback) {
      callback(cmd);
    } else {
      this.runCMD([cmd]);
    }
  }

  other(callback) {
    var cmd = this.cmd();
    if (callback) {
      callback(cmd);
    }
  }
  /**
   * props : {
   * spec: spec,
   * param: param,
   * successCb: boolean
   * }
   */
  switch(callback) {
    var cmd = this.cmd();
    if (callback) {
      callback(cmd);
    }
  }

  temperatureunit(callback) {
    var cmd = this.cmd();
    if (callback) {
      callback(cmd);
    }
  }

  dimmer(callback) {
    var cmd = this.cmd();
    if (callback) {
      callback(cmd);
    }
  }

  color_Temperature(callback) {
    var cmd = this.cmd();
    if (callback) {
      callback(cmd);
    }
  }

  thermostat_mode(callback) {
    var cmd = this.cmd();
    if (callback) {
      callback(cmd);
    }
  }

  thermostat_setpoint(callback) {
    var cmd = this.cmd('thermostat_setpoint');
    if (callback) {
      callback(cmd);
    }
  }

  doorlock(callback) {
    var cmd = this.cmd();
    if (callback) {
      callback(cmd);
    }
  }

  /**
   * props: {
   * spec: spec,
   * color: color,
   * }
   */
  colorSwitch(callback) {
    this.props.param = colorSwitchCmdParams(
      this.props.spec.value_hash,
      this.props.color,
      this.props.spec.dv_type,
    );
    var cmd = this.cmd();
    console.log('colorSwitch', cmd);
    if (callback) {
      callback(cmd);
    }
  }

  addDevice() {
    if (this.props.type === 'matter_wifi') {
      this.runCMD([
        {
          type: this.props.type,
          cmd: 'add_device',
          params1: '',
          params2: JSON.stringify({
            ssid: this.props.ssid,
            password: this.props.password,
            code: this.props.code,
          }),
        },
      ]);
    } else if (this.props.type === 'matter_thread') {
      this.runCMD([
        {
          type: this.props.type,
          cmd: 'add_device',
          params1: '',
          params2: JSON.stringify({
            code: this.props.code,
          }),
        },
      ]);
    } else {
      this.runCMD([{ type: this.props.type, cmd: 'add_device' }]);
    }
  }

  addDeviceWithLinkKey() {
    this.runCMD([
      {
        type: 'zigbee',
        cmd: 'add_device_with_link_key',
        params1: `${this.props.addressKey},${this.props.link_key}`,
      },
    ]);
  }

  removeDevice() {
    var params1 = '';
    if (this.props.type == 'zigbee') {
      params1 = `${this.props.index},${this.props.long_id}`;
    }
    if (this.props.type.startsWith('matter')) {
      params1 = `${this.props.index}`;
    }
    this.runCMD([
      { type: this.props.type, cmd: 'remove_device', params1: params1 },
    ]);
  }

  CmdStop() {
    this.runCMD([{ type: this.props.type, cmd: 'stop' }]);
  }

  setDsk() {
    const dask = this.props.dskText.substring(5, this.props.dskText.length);
    this.runCMD([
      {
        type: 'zwave',
        cmd: 'set_dsk',
        params1: `${this.props.value}${dask}`,
      },
    ]);
  }

  setPl() {
    this.runCMD([{ type: 'zwave', cmd: 'set_pl', params1: this.props.value }]);
  }

  setparameters() {
    this.cmd();
  }

  defaultResetAllParameters() {
    var spec = this.props.spec;
    let params1 = `${spec.device_id},${spec.instance_id},${spec.cc},DefaultResetAllParameters`;

    this.runCMD([
      {
        type: spec.dv_type,
        cmd: 'device_control',
        params1: params1,
        params2: JSON.stringify({}),
      },
    ]);
  }

  windowcovering(callback) {
    var cmd = this.cmd();
    if (callback) {
      callback(cmd);
    }
  }

  removeZwaveFailedDevice() {
    var spec = this.props.spec;
    let params1 = `${spec.device_id}`;
    params1 = params1 + ',' + this.unify.removeoffline.RemoveOffline.unify.cmd;
    this.runCMD([
      {
        type: spec.dv_type,
        cmd: 'remove_offline',
        params1: params1,
        params2: JSON.stringify(
          this.unify.removeoffline.RemoveOffline.unify.payload,
        ),
      },
    ]);
  }

  interview() {
    var spec = this.props.spec;
    let params1 = `${spec.device_id}`;
    params1 = params1 + ',' + this.unify.interview.Interview.unify.cmd;
    this.runCMD([
      {
        type: spec.dv_type,
        cmd: 'interview',
        params1: params1,
        params2: JSON.stringify(this.unify.interview.Interview.unify.payload),
      },
    ]);
  }

  readConfig(params1, spec, type, action, actionValue, params1Value = {}) {
    const is_matter = spec.dv_type.startsWith('matter');
    let p1,
      p2 = '';
    if (is_matter) {
      const p1Value = Object.keys(params1Value);
      const paramsCmd = this.unify[type][action].matter.cmd;
      if (p1Value.length > 0) {
        const matterValue = this.formString(paramsCmd, params1Value);
        p1 = params1 + ',' + encode(matterValue);
      } else {
        p1 = params1 + ',' + encode(paramsCmd);
      }
    } else {
      p1 = params1 + ',' + this.unify[type][action].unify.cmd;
      p2 = this.replaceValue(type, action, actionValue);
    }

    return { p1, p2 };
  }

  replaceValue(type, action, actionValue) {
    const cmd = _.cloneDeep(this.unify[type][action].unify.payload);
    _.forEach(actionValue, (val, key) => {
      cmd[key] = val;
    });
    return cmd;
  }

  cmd(type = '') {
    var spec = this.props.spec;
    var param = this.props.param;
    var cmd = '';
    var params1 = '';
    var params2 = '';
    var type1 = '';
    const is_matter = spec.dv_type.startsWith('matter');
    if (spec.dv_type !== 'tuya') {
      type1 = spec.dv_type;
      cmd = 'device_control';
      // params1 = `${spec.device_id},${spec.instance_id},${spec.cc},${spec.value_id},${param}`;
      params1 = `${spec.device_id},${spec.instance_id},${spec.cc}`;
      switch (spec.name.toLowerCase()) {
        case 'switch':
          if (Number(param) === 0) {
            const p = this.readConfig(params1, spec, 'switch', 'Off', {
              value: true,
            });

            params1 = p.p1;
            params2 = p.p2;
          }
          if (Number(param) === 255) {
            const p = this.readConfig(params1, spec, 'switch', 'On', {
              value: true,
            });

            params1 = p.p1;
            params2 = p.p2;
          }
          break;
        case 'dimmer':
          const d = this.readConfig(
            params1,
            spec,
            'dimmer',
            'MoveToLevelWithOnOff',
            { Level: param },
            { Level: param },
          );

          params1 = d.p1;
          params2 = d.p2;
          break;
        case 'colortemperature':
          const c = this.readConfig(
            params1,
            spec,
            'colortemperature',
            'MoveToColorTemperature',
            { ColorTemperatureMireds: param },
            { ColorTemperatureMireds: param },
          );

          params1 = c.p1;
          params2 = c.p2;
          break;
        case 'colorswitch':
          // Hue: Math.round(color.hsv.h / 360 * 254),
          //   Saturation: Math.round(color.hsv.s * 254),
          const hue = Math.round((param.h / 360) * 254);
          const saturate = Math.round(param.s * 254);
          const cs = this.readConfig(
            params1,
            spec,
            'colorswitch',
            'MoveToHueAndSaturation',
            { Hue: hue, Saturation: saturate },
            { Hue: hue, Saturation: saturate },
          );

          params1 = cs.p1;
          params2 = cs.p2;
          console.log('colorswitch', params1, params2);
          break;
        case 'doorlock':
          // 0 = lock, 255 = unlock
          if (param === 255) {
            const p = this.readConfig(
              params1,
              spec,
              'doorlock',
              'LockDoor',
              {},
            );

            params1 = p.p1;
            params2 = p.p2;
          } else {
            const p = this.readConfig(
              params1,
              spec,
              'doorlock',
              'UnlockDoor',
              {},
            );

            params1 = p.p1;
            params2 = p.p2;
          }
          break;
        case 'thermostatmode':
          const t = this.readConfig(
            params1,
            spec,
            'thermostatmode',
            'SystemMode',
            { SystemMode: param },
            { SystemMode: param },
          );

          params1 = t.p1;
          params2 = t.p2;
          break;
        case 'thermostatsetpoint':
        case 'heatingsetpoint':
        case 'coolingsetpoint':
          // 0 = 'heatingSetpoint', 1= 'coolingSetpoint'
          // matter 3 = cool 4 = heat;
          // unify Cool = cool Heat = heat;
          let setpoint_type = spec.setpoint_type;
          let cmd_type = '';
          if (this.props.setpoint_type) {
            setpoint_type = this.props.setpoint_type;
          }
          if (setpoint_type.toString() === '3' || setpoint_type === 'Cool') {
            cmd_type = 'OccupiedCoolingSetpoint';
          }
          if (setpoint_type.toString() === '4' || setpoint_type === 'Heat') {
            cmd_type = 'OccupiedHeatingSetpoint';
          }
          // let pointType = 0;
          // if (is_matter) {
          //   if (setpoint_type.toString() === '3') {
          //     pointType = 1;
          //   }
          //   if (setpoint_type.toString() === '4') {
          //     pointType = 0;
          //   }
          // }
          let unifyValue = {
            [cmd_type]: param,
          };

          const tp = this.readConfig(
            params1,
            spec,
            'thermostatsetpoint',
            cmd_type,
            unifyValue,
            unifyValue,
          );

          params1 = tp.p1;
          params2 = tp.p2;
          break;
        case 'windowcovering':
          if (this.props.param_type === 'percentage') {
            if (is_matter) {
              const paramsCmd =
                this.unify.windowcovering.GoToLiftPercentage.matter.cmd;
              const matterValue = this.formString(paramsCmd, {
                LiftPercent100thsValue: param,
              });
              params1 = params1 + ',' + encode(matterValue);
            }
          } else {
            let action = 'UpOrOpen';
            switch (param) {
              case 'up':
                action = 'UpOrOpen';
                break;
              case 'down':
                action = 'DownOrClose';
                break;
              case 'stop':
                action = 'Stop';
                break;
            }
            const p = this.readConfig(
              params1,
              spec,
              'windowcovering',
              action,
              {},
            );

            params1 = p.p1;
            params2 = p.p2;
          }
          break;
        case 'configurationparameters':
          params1 =
            params1 + ',' + this.unify.setparameters.SetParameter.unify.cmd;
          const setParam = this.unify.setparameters.SetParameter.unify.payload;
          setParam.ParameterId = this.props.ParameterId;
          setParam.Value = param;
          params2 = setParam;
          break;
        case 'temperatureunit':
          const paramsCmd =
            this.unify.temperatureunit.TemperatureDisplayMode.matter.cmd;
          const matterValue = this.formString(paramsCmd, {
            TemperatureDisplayMode: param,
          });
          params1 = params1 + ',' + encode(matterValue);
          break;
      }
    } else if (spec.dv_type == 'tuya') {
      type1 = 'tuya';
      cmd = 'device_control';
      switch (spec.name.toLowerCase()) {
        case 'switch':
          if (param == 0) {
            param = false;
          } else {
            param = true;
          }
          break;
      }
      params1 = spec.device_id;
      params2 = { commands: [{ code: spec.cc, value: param }] };
    }
    var temp = {
      type: type1,
      cmd: cmd,
      params1: params1,
      params2: JSON.stringify(params2),
    };
    if (this.props.runCMD) {
      this.runCMD([temp]);
    } else {
      temp.sn = this.props.sn;
      temp.sn_id = this.props.sn_id;
      temp.home_id = this.props.home_id;
      return [temp];
    }
  }

  runCMD(cmd) {
    if (this.props.successCb == false) {
      Helper.runCMD(cmd, this.props.sn_id, { successCb: false });
    } else {
      Helper.runCMD(cmd, this.props.sn_id);
    }
  }
}
