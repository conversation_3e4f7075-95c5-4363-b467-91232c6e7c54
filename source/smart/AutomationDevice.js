/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import { View, Text } from 'react-native';
import I18n from '../I18n';
import { observer } from 'mobx-react/native';
import SelectDevice from '../select_device_spec/SelectDevice';
import _ from 'lodash';
import { Tme } from '../ThemeStyle';
import NavBarView from '../share/NavBarView';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';

@observer
class AutomationDevice extends Component {
  constructor(props) {
    super(props);
    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.next')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this.nextPage();
  }
  componentDidMount() { }

  render() {
    return (
      <NavBarView>
        <View style={{ paddingHorizontal: 20, paddingVertical: 10 }}>
          <Text style={{ color: 'gray' }}>
            {I18n.t('automation.select_two_controller')}
          </Text>
        </View>
        <View style={{ flex: 1, backgroundColor: Tme('bgColor') }}>
          <SelectDevice
            showTitle={false}
            rooms={this.props.route.params.routine.rooms}
            product="trigger"
            spec_settings={this.props.route.params.routine}
            device={this.props.route.params.routine}
            navigation={this.props.navigation}
            type="condition"
          />
        </View>
      </NavBarView>
    );
  }

  nextPage() {
    var condDevices = _.groupBy(
      this.props.route.params.routine.conditions,
      'checked',
    ).true;
    if (condDevices) {
      this.props.navigation.push('SmartSelectCondition', {
        data: this.props.route.params.data,
        routine: this.props.route.params.routine,
        title: I18n.t('automation.select_conditions'),
      });
    } else {
      AlertModal.alert(I18n.t('trigger.conditional_device'));
    }
  }
}
export default AutomationDevice;
