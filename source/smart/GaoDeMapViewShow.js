import React, {Component} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Platform,
  NativeModules,
  NativeEventEmitter,
} from 'react-native';
const AMapGeolocation = NativeModules.AMapGeolocation;

const AMapEventEmitter = new NativeEventEmitter(AMapGeolocation);
import {DEVICE_HEIGHT} from '../Helper';
import GPSHelper from '../GPSHelper';
import I18n from '../I18n';
import {EVENT_MAP, NotificationCenter} from '../NotificationCenter';
import AppConfig from '../../app_config';
import {Tme} from '../ThemeStyle';
import {
  init,
  setNeedAddress,
  start,
  setLocatingWithReGeocode,
  stop,
} from 'react-native-amap-geolocation';
import {MapView, AMapSdk} from '../amap/index';
import MapCircle from './MapCircle';
import axios from 'axios';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {hideLoading, showLoading} from '../../ILoading';
import ScreenSizeContext from '../../WindowResizeContext';

class GaoDeMapViewShow extends Component {
  constructor(props) {
    super(props);

    AMapSdk.init(
      Platform.select({
        android: '82b00bf7dcae3645d454544f9c4457be',
        ios: 'd22701c2277010686b0608373c038bf6',
      }),
    );
    init({
      ios: 'd22701c2277010686b0608373c038bf6',
      android: '82b00bf7dcae3645d454544f9c4457be',
    });

    this.state = {
      zoom: 14,
      lat: this.props.route.params.lat || '',
      lng: this.props.route.params.lng || '',

      address: '',
      city: '',
      viewHeigth: DEVICE_HEIGHT,
      Gloaction: this.props.route.params.lng
        ? this.props.route.params.lng
          ? true
          : false
        : false,
      is_location: false,
    };

    this.map = React.createRef();

    this.props.navigation.setOptions({
      // eslint-disable-next-line react/no-unstable-nested-components
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.next')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  static contextType = ScreenSizeContext;

  componentDidMount() {
    this.initLocation();
    if (this.state.lat === '') {
      this.onLocation();
    }
  }

  componentWillUnmount() {
    if (this.AMapEvent) {
      this.AMapEvent.remove();
    }
    stop();
  }

  initLocation() {
    if (this.props.route.params.lng) {
      const location = GPSHelper.wgs84togcj02(
        this.props.route.params.lng,
        this.props.route.params.lat,
      );
      this.setState({
        lat: location[1],
        lng: location[0],
        address: this.props.route.params.address,
      });
    }
  }

  onLocation() {
    var that = this;
    showLoading();
    if (Platform.OS == 'ios') {
      setLocatingWithReGeocode(true);
    } else {
      setNeedAddress(true);
    }
    this.AMapEvent = AMapEventEmitter.addListener(
      'AMapGeolocation',
      location => {
        if (location.address) {
          stop();
          that.setState(
            {
              address: location.address,
              lat: location.latitude,
              lng: location.longitude,
              Gloaction: true,
            },
            () => {
              this.map.moveCamera(
                {
                  target: {
                    latitude: Number(this.state.lat),
                    longitude: Number(this.state.lng),
                  },
                  zoom: 14,
                },
                1,
              );
            },
          );
        }
      },
    );
    // 开始连续定位
    start();
    hideLoading();
  }

  rightClick() {
    const location = GPSHelper.gcj02towgs84(this.state.lng, this.state.lat);
    NotificationCenter.dispatchEvent(EVENT_MAP, {
      lat: location[1],
      lng: location[0],
      address: this.state.address,
    });
    this.props.navigation.goBack();
  }

  onLayout(event) {
    this.setState({
      viewHeigth: event.nativeEvent.layout.height,
    });
  }

  async gaoDeGpsToAdd(latlng) {
    const {data} = await axios.get(
      AppConfig.gaode_api_url +
        `/v3/geocode/regeo?key=${AppConfig.gapde_api_key}&poitype=&radius=100&extensions=all&batch=false&roadlevel=0&location=${latlng}`,
    );
    var addr = data.regeocode.formatted_address;
    if (!addr || addr.length == 0) {
      addr = 'N/A';
    }
    this.setState({address: addr});
  }

  render() {
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: Tme('bgColor'),
        }}>
        {this.state.Gloaction ? (
          <View style={{flex: 1, backgroundColor: Tme('cardColor')}}>
            <MapView
              maxZoom={14}
              minZoom={12}
              zoomControlsEnabled={false}
              rotateGesturesEnabled={false}
              tiltGesturesEnabled={false}
              scaleControlsEnabled={false}
              compassEnabled={false}
              style={{flex: 1, backgroundColor: Tme('cardColor')}}
              onLayout={event => this.onLayout(event)}
              ref={ref => (this.map = ref)}
              initialCameraPosition={{
                target: {
                  latitude: Number(this.state.lat),
                  longitude: Number(this.state.lng),
                },
                zoom: 14,
              }}
              onACameraMove={event => {
                this.setState({
                  is_location: false,
                });
              }}
              onACameraIdle={this.onRegionChange.bind(this)}>
              {/* <MapView.Circle
                strokeWidth={5}
                strokeColor="rgba(0, 0, 255, 0.5)"
                fillColor="rgba(255, 0, 0, 0.5)"
                radius={1000}
                coordinate={{
                  latitude: Number(this.state.lat),
                  longitude: Number(this.state.lng),}}
              /> */}
            </MapView>
            <MapCircle
              viewHeigth={this.state.viewHeigth}
              zoom={this.state.zoom}
            />
            <View
              style={{
                position: 'absolute',
                backgroundColor: 'white',
                bottom: 20,
                right: 20,
              }}>
              <TouchableOpacity
                activeOpacity={0.8}
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: 40,
                  height: 40,
                }}
                onPress={this.changeCenter.bind(this)}>
                <Image
                  style={{width: 25, height: 25}}
                  source={require('../../img/address.png')}
                />
              </TouchableOpacity>
            </View>
            <View
              style={{
                width: this.context.winWidth,
                position: 'absolute',
                backgroundColor: 'rgb(255,255,255)',
                top: 0,
              }}>
              <View style={{paddingVertical: 10, paddingLeft: 15}}>
                <Text style={{fontSize: 14, color: '#141D24'}}>
                  {this.state.address}
                </Text>
              </View>
            </View>
          </View>
        ) : null}
      </View>
    );
  }

  onRegionChange({nativeEvent}) {
    if (!this.state.is_location) {
      this.gaoDeGpsToAdd(
        nativeEvent.cameraPosition.target.longitude.toString().substring(0, 9) +
          ',' +
          nativeEvent.cameraPosition.target.latitude.toString().substring(0, 9),
      );
      this.setState({
        lat: nativeEvent.cameraPosition.target.latitude,
        lng: nativeEvent.cameraPosition.target.longitude,
        // eslint-disable-next-line radix
        zoom: parseInt(nativeEvent.cameraPosition.zoom),
      });
    }
  }

  changeCenter() {
    this.onLocation();
  }
}
export default GaoDeMapViewShow;
