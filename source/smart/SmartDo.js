/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import { View, ScrollView } from 'react-native';
import I18n from '../I18n';
import { Helper } from '../Helper';
import PubSub from 'pubsub-js';
import moment from 'moment';
import _ from 'lodash';
import SelectDevice from '../select_device_spec/SelectDevice';
import { observer } from 'mobx-react/native';
import { Tme } from '../ThemeStyle';
import RadioButtons from '../RadioButtons';
import NavBarView from '../share/NavBarView';
import { toJS } from 'mobx';
import BackgroundGeolocation from 'react-native-background-geolocation';
import { Toast } from '../Toast';
import ExternalUrlTarget from '../select_device_spec/ExternalUrlTarget';

import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { hideLoading, showLoading } from '../../ILoading';
import { EVENT_ROUTINE } from '../types/PubSubEvent';

@observer
class SmartDo extends Component {
  constructor(props) {
    super(props);
    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this._save();
  }

  componentDidMount() {
    if (this.props.route.params.data.target_uuid) {
      this.props.route.params.routine.target_uuid =
        this.props.route.params.data.target_uuid;
    }
  }

  render() {
    var html;
    switch (this.props.route.params.routine.target_type) {
      case 'device':
        return (
          <NavBarView>
            <View
              style={{
                flex: 1,
                backgroundColor: Tme('bgColor'),
              }}>
              <SelectDevice
                showTitle={false}
                rooms={this.props.route.params.routine.rooms}
                product="routine"
                spec_settings={this.props.route.params.routine}
                device={this.props.route.params.routine}
                navigation={this.props.navigation}
                type="target"
              />
            </View>
          </NavBarView>
        );
      case 'scene':
        html = (
          <View
            style={{
              backgroundColor: Tme('bgColor'),
              shadowOffset: { height: 4 },
              shadowColor: 'rgba(0,0,0,0.02)',
              shadowOpacity: 0.2,
              shadowRadius: 1,
              paddingTop: 20,
            }}>
            <View style={{ backgroundColor: Tme('cardColor') }}>
              <RadioButtons
                data={this.props.route.params.routine.radio_scenes}
                defaultKey={this.props.route.params.routine.target_uuid}
                onChange={this.onChange.bind(this)}
              />
            </View>
          </View>
        );
        break;
      case 'external_url':
        html = (
          <View
            style={{
              backgroundColor: Tme('bgColor'),
              shadowOffset: { height: 4 },
              shadowColor: 'rgba(0,0,0,0.02)',
              shadowOpacity: 0.2,
              shadowRadius: 1,
              paddingTop: 20,
            }}>
            <View style={{ backgroundColor: Tme('cardColor') }}>
              <ExternalUrlTarget
                routine={this.props.route.params.routine}
                data={this.props.route.params.data}
                navigation={this.props.navigation}
              />
            </View>
          </View>
        );
        break;
      default:
        html = (
          <View
            style={{
              backgroundColor: Tme('bgColor'),
              shadowOffset: { height: 4 },
              shadowColor: 'rgba(0,0,0,0.02)',
              shadowOpacity: 0.2,
              shadowRadius: 1,
              paddingTop: 20,
            }}>
            <View style={{ backgroundColor: Tme('cardColor') }}>
              <RadioButtons
                data={this.props.route.params.routine.actions}
                defaultKey={this.props.route.params.routine.target_uuid}
                onChange={this.onChange.bind(this)}
              />
            </View>
          </View>
        );
        break;
    }
    return (
      <NavBarView>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{
            flex: 1,
            backgroundColor: Tme('bgColor'),
          }}>
          {html}
        </ScrollView>
      </NavBarView>
    );
  }

  onChange(e) {
    this.props.route.params.routine.target_uuid = e;
  }

  _save() {
    var that = this;
    var body = {};
    var url = '/partner/automations';
    if (this.props.route.params.routine.type == 'gps') {
      body = {
        address: this.props.route.params.routine.address,
        lat: this.props.route.params.routine.lat,
        lng: this.props.route.params.routine.lng,
        cycle: this.props.route.params.routine.is_cycle.toString(),
        begin_at: moment(
          new Date(this.props.route.params.routine.begin_at),
        ).format('HH:mm'),
        end_at: moment(new Date(this.props.route.params.routine.end_at)).format(
          'HH:mm',
        ),
        returned: this.props.route.params.routine.is_returned,
        type: this.props.route.params.routine.type,
      };
    } else if (this.props.route.params.routine.type == 'time') {
      body = {
        type: this.props.route.params.routine.type,
        begin_at: moment(
          new Date(this.props.route.params.routine.begin_at),
        ).format('HH:mm'),
        cycle: this.props.route.params.routine.is_cycle.toString(),
      };
    } else if (this.props.route.params.routine.type == 'sun') {
      body = {
        type: this.props.route.params.routine.type,
        is_sunrise: this.props.route.params.routine.is_sunrise,
        lat: this.props.route.params.routine.lat,
        lng: this.props.route.params.routine.lng,
        address: this.props.route.params.routine.address,
      };
    } else {
      body = {
        type: this.props.route.params.routine.type,
        cycle: this.props.route.params.routine.is_cycle.toString(),
        begin_at: moment(
          new Date(this.props.route.params.routine.begin_at),
        ).format('HH:mm'),
        end_at: moment(new Date(this.props.route.params.routine.end_at)).format(
          'HH:mm',
        ),
      };
      var condDevices = _.groupBy(
        this.props.route.params.routine.conditions,
        'checked',
      ).true;
      var temp = [];
      _.forEach(condDevices, function (v, k) {
        temp.push(toJS(v));
      });
      Object.assign(body, {}, { conditions: temp });
    }

    if (this.props.route.params.routine.target_type == 'device') {
      let targetDevices = [];
      targetDevices = _.groupBy(
        this.props.route.params.routine.targets,
        'checked',
      ).true;
      if (targetDevices == undefined) {
        AlertModal.alert(
          I18n.t('home.warning_message'),
          I18n.t('device.setup_device'),
        );
        return;
      }
      if (targetDevices.length == 0) {
        AlertModal.alert(
          I18n.t('home.warning_message'),
          I18n.t('device.setup_device'),
        );
        return;
      }
      var temp = [];
      _.forEach(targetDevices, function (v, k) {
        temp.push(toJS(v));
      });

      Object.assign(body, {}, { targets: temp });
    } else {
      var isTargetUUID = false;
      if (this.props.route.params.routine.target_type == 'scene') {
        var scene = _.groupBy(
          this.props.route.params.routine.radio_scenes,
          'key',
        )[this.props.route.params.routine.target_uuid];

        if (scene) {
          isTargetUUID = true;
        }
      }

      if (this.props.route.params.routine.target_type == 'action') {
        var action = _.groupBy(this.props.route.params.routine.actions, 'key')[
          this.props.route.params.routine.target_uuid
        ];
        if (action) {
          isTargetUUID = true;
        }
      }

      if (isTargetUUID) {
        Object.assign(
          body,
          {},
          {
            target_uuid: this.props.route.params.routine.target_uuid,
          },
        );
      } else {
        if (this.props.route.params.routine.target_type === 'external_url') {
          if (
            this.props.route.params.routine.targets.length == 0 ||
            _.isEmpty(this.props.route.params.routine.targets[0].app_url)
          ) {
            AlertModal.alert(
              I18n.t('home.warning_message'),
              I18n.t('routine.please_external'),
            );
          } else {
            Object.assign(
              body,
              {},
              {
                targets: [
                  {
                    app_url: this.props.route.params.routine.targets[0].app_url,
                    delay: this.props.route.params.routine.targets[0].delay,
                  },
                ],
              },
            );
          }
        } else {
          AlertModal.alert(
            I18n.t('home.warning_message'),
            I18n.t('device.setup_device'),
          );
          return;
        }
      }
    }

    if (this.props.route.params.routine.uuid) {
      Object.assign(
        body,
        {},
        {
          uuid: this.props.route.params.routine.uuid,
        },
      );
    }
    Object.assign(
      body,
      {},
      {
        name: this.props.route.params.routine.name,
        target_type: this.props.route.params.routine.target_type,
        wday: this.props.route.params.routine.wday.slice(),
        scene_ids: this.props.route.params.routine.scene_ids.slice(),
      },
    );
    showLoading();
    Helper.httpPOST(
      url,
      {
        ensure: () => {
          hideLoading();
        },
        success: data => {
          if (that.props.route.params.routine.type == 'gps') {
            // BackgroundGeolocation.getGeofence(data.uuid + '1')
            BackgroundGeolocation.getGeofence(data.uuid)
              .then(geofence => {
                BackgroundGeolocation.removeGeofence(data.uuid).then(
                  success => {
                    that.setGeofence(data);
                  },
                );
              })
              .catch(e => {
                if (e === '404') {
                  that.setGeofence(data);
                }
              });
          }
          setTimeout(() => {
            PubSub.publish(EVENT_ROUTINE);
            this.props.navigation.popToTop();
            Toast.show();
          }, 500);
        },
        error: data => {
          AlertModal.alert(I18n.t('global.error_desp'));
        },
      },
      body,
    );
  }

  setGeofence(data) {
    BackgroundGeolocation.addGeofence({
      identifier: data.uuid,
      radius: Helper.BackgroundGeolocationRadius,
      latitude: parseFloat(this.props.route.params.routine.lat),
      longitude: parseFloat(this.props.route.params.routine.lng),
      notifyOnEntry: this.props.route.params.routine.is_returned === 'true',
      notifyOnExit: this.props.route.params.routine.is_returned === 'false',
    });
  }
}
export default SmartDo;
