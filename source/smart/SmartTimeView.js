import React, { Component } from 'react';
import { View, ScrollView } from 'react-native';
import { observer } from 'mobx-react/native';
import CheckBox from '../share/CheckBox';
import { Tme } from '../ThemeStyle';
import _ from 'lodash';
import I18n from '../I18n';
import RadioButtons from '../RadioButtons';
import NavBarView from '../share/NavBarView';
import HeaderRightBtn from '../share/HeaderRightBtn';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';
@observer
class SmartTimeView extends Component {
  constructor(props) {
    super(props);

    this.state = {
      cycle: this.props.route.params.cycle,
      wday: this.props.route.params.wday,
    };

    this.week = [
      { key: 0, value: I18n.t('setting.sunday') },
      { key: 1, value: I18n.t('setting.monday') },
      { key: 2, value: I18n.t('setting.tuesday') },
      { key: 3, value: I18n.t('setting.wednesday') },
      { key: 4, value: I18n.t('setting.thursday') },
      { key: 5, value: I18n.t('setting.friday') },
      { key: 6, value: I18n.t('setting.saturday') },
    ];

    this.props.navigation.setOptions({
      // eslint-disable-next-line react/no-unstable-nested-components
      headerRight: () => (
        <HeaderRightBtn
          text={this.props.route.params.from === 'ipc' ? I18n.t('home.save') : I18n.t('home.next')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this._save();
  }

  _save() {
    PubSub.publish(PubSubEvent.SMART_SELECT_EVENT, {
      type: 'time',
      cycle: this.state.cycle,
      wday: this.state.wday,
    });
    PubSub.publish(PubSubEvent.ipc_wday, {
      cycle: this.state.cycle,
      wday: this.state.wday,
    });
    this.props.navigation.goBack();
  }

  render() {
    var checkbox = [];
    var that = this;

    if (!this.state.cycle) {
      checkbox.push(
        <RadioButtons
          key={Math.random()}
          data={this.week}
          defaultKey={this.state.wday ? this.state.wday[0] : '0'}
          onChange={this.change.bind(this)}
        />,
      );
    } else {
      var length = this.week.length - 1;
      _.forEach(this.week, function (data, index) {
        checkbox.push(
          <View key={index} style={{ backgroundColor: Tme('cardColor') }}>
            <View style={{ paddingHorizontal: 20 }}>
              <CheckBox
                isLast={index == length}
                value={data.value}
                index={data.key}
                isChecked={_.includes(that.state.wday, data.key)}
                onClick={that.onClick.bind(that)}
              />
            </View>
          </View>,
        );
      });
    }
    return (
      <NavBarView>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={[
            {
              flex: 1,
            },
          ]}>
          {this.props.route.params.show_cycle ? (
            <View
              style={{
                flex: 1,
                marginTop: 20,
              }}>
              <RadioButtons
                data={[
                  { key: 'run_once', value: I18n.t('routine.run_once') },
                  { key: 'run_multiple', value: I18n.t('routine.run_multiple') },
                ]}
                defaultKey={this.state.cycle ? 'run_multiple' : 'run_once'}
                onChange={this.onCycle.bind(this)}
              />
            </View>
          ) : null}
          <View style={{ backgroundColor: Tme('cardColor') }}>
            <View
              style={{
                height: 20,
                backgroundColor: Tme('bgColor'),
              }}
            />
            {checkbox}
          </View>
        </ScrollView>
      </NavBarView>
    );
  }

  onCycle(data) {
    this.setState({
      cycle: data == 'run_multiple',
      wday: data == 'run_once' ? [1] : [0, 1, 2, 3, 4, 5, 6],
    });
  }
  change(e) {
    this.setState({
      wday: [e],
    });
  }

  onClick(data) {
    var temp = [];
    this.state.wday.map(v => {
      temp.push(v);
    });

    if (_.includes(this.state.wday, data)) {
      temp.splice(temp.indexOf(data), 1);
    } else {
      temp.push(data);
    }
    this.setState({
      wday: temp,
    });
  }
}
export default SmartTimeView;
