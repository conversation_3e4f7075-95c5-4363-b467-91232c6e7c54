import React, { Component } from 'react';
import { View, ScrollView } from 'react-native';
import { observer } from 'mobx-react/native';
import CheckBox from '../share/CheckBox';
import { Tme } from '../ThemeStyle';
import { getSceneName } from '../Tools';
import _ from 'lodash';
import I18n from '../I18n';
import NavBarView from '../share/NavBarView';
import HeaderRightBtn from '../share/HeaderRightBtn';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';

@observer
class SmartSceneView extends Component {
  constructor(props) {
    super(props);

    this.state = {
      scene_ids: this.props.route.params.from === 'ipc' ? this.props.route.params.scene_ids : this.props.route.params.routine.scene_ids
        ? this.props.route.params.routine.scene_ids.toJS()
        : this.props.route.params.data.scene_ids
          ? this.props.route.params.data.scene_ids
          : [],
      scene_names: this.getSceneName(),
    };
    this.props.navigation.setOptions({
      // eslint-disable-next-line react/no-unstable-nested-components
      headerRight: () => (
        <HeaderRightBtn
          text={this.props.route.params.from == 'ipc' ? I18n.t('home.save') : I18n.t('home.next')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this._save();
  }

  _save() {
    PubSub.publish(PubSubEvent.SMART_SELECT_EVENT, {
      type: 'scene',
      ids: this.state.scene_ids,
      names: this.state.scene_names,
    });
    this.props.navigation.goBack();
  }

  getSceneName() {
    var that = this;
    var temp = [];
    if (this.props.route.params.from === 'ipc') {
      _.forEach(this.props.route.params.scene_ids, (uuid, key) => {
        var scene = _.filter(
          that.props.route.params.scenes,
          function (s) {
            return s.uuid === uuid;
          },
        );
        if (scene.length > 0) {
          temp.push(scene[0].name);
        }
      });
    } else {
      if (this.props.route.params.routine.scenes.length > 0) {
        if (this.props.route.params.routine.scene_ids.length > 0) {
          _.forEach(this.props.route.params.routine.scene_ids, (uuid, key) => {
            var scene = _.filter(
              that.props.route.params.routine.scenes,
              function (s) {
                return s.uuid === uuid;
              },
            );
            if (scene.length > 0) {
              temp.push(scene[0].name);
            }
          });
        } else {
          if (this.props.route.params.data) {
            if (this.props.route.params.data.scene_ids.length > 0) {
              _.forEach(this.props.route.params.data.scene_ids, (uuid, key) => {
                var scene = _.filter(
                  that.props.route.params.routine.scenes,
                  function (e) {
                    return e.uuid === uuid;
                  },
                );
                if (scene.length > 0) {
                  temp.push(scene[0].name);
                }
              });
            }
          }
        }
      }
    }
    return temp;
  }

  checkItem(index, data, length) {
    var that = this;
    return (
      <View key={index} style={{ backgroundColor: Tme('cardColor') }}>
        <View style={{ paddingHorizontal: 20 }}>
          <CheckBox
            isLast={index == length}
            key={index}
            value={getSceneName(data.name)}
            index={data.uuid}
            isChecked={_.includes(that.state.scene_ids, data.uuid)}
            onClick={that.onClick.bind(that, data)}
          />
        </View>
      </View>
    );
  }

  render() {
    var checkbox = [];
    var that = this;
    if (this.props.route.params.from === 'ipc') {
      _.forEach(this.props.route.params.scenes, function (data, index) {
        checkbox.push(
          that.checkItem(
            index,
            data,
            that.props.route.params.scenes.length - 1,
          ),
        );
      });
    } else {
      var length = this.props.route.params.routine.scenes.length - 1;
      _.forEach(this.props.route.params.routine.scenes, function (data, index) {
        checkbox.push(
          that.checkItem(index, data, length),
        );
      });
    }

    return (
      <NavBarView>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View
            style={{
              flex: 1,
              marginTop: 20,
            }}>
            {checkbox}
          </View>
        </ScrollView>
      </NavBarView>
    );
  }

  onClick(data) {
    var ids = _.cloneDeep(this.state.scene_ids);
    var names = _.cloneDeep(this.state.scene_names);

    if (_.includes(ids, data.uuid)) {
      ids = _.pull(ids, data.uuid);
      names = _.pull(names, data.name);
    } else {
      ids.push(data.uuid);
      names.push(data.name);
    }
    this.setState({ scene_names: names, scene_ids: ids });
  }
}
export default SmartSceneView;
