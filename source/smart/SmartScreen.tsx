import React, {
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {
  View,
  Animated,
  SafeAreaView,
  Platform,
  TouchableOpacity,
} from 'react-native';
import I18n from '../I18n';
import {Tme} from '../ThemeStyle';
import ActionScreen from '../action/ActionScreen';
import SceneScreen from '../scenes/SceneScreen';
import {isOwnerOrAdmin} from '../Router';
import AutomationScreen from './AutomationScreen';
import UserBackgroundImage from '../share/UserBackgroundImage';
import {HelperMemo, Helper} from '../Helper';
import {BottomSheetModalProvider} from '@gorhom/bottom-sheet';
import {TabView} from 'react-native-tab-view';
import {
  SmartScreenProps,
  TabRoute,
  ActionItem,
  AutomationItem,
} from '../types/smart';

// 扩展SmartScreenProps的子组件接口定义
interface ActionProps extends SmartScreenProps {
  actions?: ActionItem[];
  parents?: any;
}

interface AutoProps extends SmartScreenProps {
  automations?: AutomationItem[];
  parents?: any;
}

// 使用forwardRef实现引用转发
const Action = forwardRef<any, ActionProps>((props, ref) => {
  const actionRef = useRef<any>(null);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    setData: (data: ActionItem[]) => {
      if (actionRef.current) {
        actionRef.current.setData(data);
      }
    },
  }));

  return (
    <ActionScreen
      ref={actionRef}
      actions={props.actions || []}
      navigation={props.navigation}
      parents={props.parents}
    />
  );
});

const Auto = forwardRef<any, AutoProps>((props, ref) => {
  const autoRef = useRef<any>(null);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    setData: (data: AutomationItem[], showAdd: boolean) => {
      if (autoRef.current) {
        autoRef.current.setData(data, showAdd);
      }
    },
  }));

  return (
    <AutomationScreen
      ref={autoRef}
      automations={props.automations || []}
      navigation={props.navigation}
    />
  );
});

const SmartScreen: React.FC<SmartScreenProps> = ({navigation}) => {
  // 只保留必要的状态
  const [index, setIndex] = useState(0);
  const [isFetching, setIsFetching] = useState(false);
  const [_tabKey, setTabKey] = useState('tabBar'); // 添加下划线表示刻意不使用

  const sceneRef = useRef<any>(null);
  const actionRef = useRef<any>(null);
  const autoRef = useRef<any>(null);
  const backgroundImageRef = useRef<any>(null);

  // 保存当前实例引用给子组件使用
  const selfRef = useRef<any>({
    sceneRef,
    actionRef,
    autoRef,
  });

  const routes: TabRoute[] = [
    {key: 'scene', title: I18n.t('home.scene').toUpperCase()},
    {key: 'action', title: I18n.t('action.action').toUpperCase()},
    {key: 'auto', title: I18n.t('global.auto').toUpperCase()},
  ];

  useEffect(() => {
    const focusListener = navigation.addListener('focus', () => {
      if (!isFetching) {
        doFetchData();
      }
      setTabKey(Math.random().toString());
    });

    return () => {
      focusListener();
    };
  }, [navigation, isFetching]);

  const renderScene = ({route}: {route: TabRoute; jumpTo: any}) => {
    switch (route.key) {
      case 'scene':
        return (
          <SceneScreen
            navigation={navigation}
            parents={selfRef.current}
            jumpTo={(idx: number) => setIndex(idx)}
            ref={sceneRef}
          />
        );
      case 'action':
        return (
          <Action
            navigation={navigation}
            parents={selfRef.current}
            ref={actionRef}
          />
        );
      case 'auto':
        return (
          <Auto
            navigation={navigation}
            parents={selfRef.current}
            ref={autoRef}
          />
        );
      default:
        return null;
    }
  };

  const doFetchData = () => {
    setIsFetching(true);

    Helper.httpGET(Helper.urlWithQuery('/partner/automations/list', {}), {
      success: (data: any) => {
        const newScenes = data.scenes;
        const newActions = data.actions;
        const newAutomations = data.automations;
        let showAutoAdd = false;

        if (isOwnerOrAdmin()) {
          showAutoAdd = true;
          newActions.unshift({type: 'add'});
          newScenes.unshift({type: 'add'});
        }

        if (sceneRef.current) {
          sceneRef.current.setScenes(newScenes);
        }
        if (actionRef.current) {
          actionRef.current.setData(newActions);
        }
        if (autoRef.current) {
          autoRef.current.setData(newAutomations, showAutoAdd);
        }

        setIsFetching(false);
      },
      error: () => {
        setIsFetching(false);
      },
    });
  };

  const renderTabBar = (props: any) => {
    return (
      <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
        <View style={{flex: 1, flexDirection: 'row'}}>
          {props.navigationState.routes.map((route: TabRoute, i: number) => {
            let activeColor = Tme('scrollTabActiveColor');
            let tabColor = Tme('scrollTabColor');

            if (backgroundImageRef.current?.child?.current?.state?.uri) {
              activeColor = Tme('scrollTabActiveColor', 'D');
              tabColor = Tme('scrollTabColor', 'D');
            }

            return (
              <TouchableOpacity
                activeOpacity={0.8}
                key={i}
                style={{
                  alignItems: 'center',
                  paddingVertical: 16,
                  paddingLeft: 16,
                }}
                onPress={() => setIndex(i)}>
                <Animated.Text
                  style={[
                    {color: index === i ? activeColor : tabColor},
                    {
                      fontSize: 22,
                      fontWeight: 'bold',
                    },
                  ]}>
                  {route.title}
                </Animated.Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  let currentTabKey = 'tab';
  if (backgroundImageRef.current?.child?.current?.state?.uri) {
    currentTabKey = 'tab';
  } else {
    currentTabKey = 'tabBar';
  }

  return (
    <BottomSheetModalProvider>
      <SafeAreaView style={{flex: 1, backgroundColor: Tme('bgColor')}}>
        {Platform.OS === 'android' && (
          <View style={{height: HelperMemo.STATUS_BAR_HEIGHT}} />
        )}
        <UserBackgroundImage ref={backgroundImageRef} />

        <TabView
          key={currentTabKey}
          navigationState={{
            index,
            routes,
          }}
          renderScene={renderScene}
          renderTabBar={renderTabBar}
          onIndexChange={setIndex}
        />
      </SafeAreaView>
    </BottomSheetModalProvider>
  );
};

export default SmartScreen;
