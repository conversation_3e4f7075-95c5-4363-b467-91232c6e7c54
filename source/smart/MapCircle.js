import React, {Component} from 'react';
import {View, Text} from 'react-native';
import ScreenSizeContext from '../../WindowResizeContext';

class MapCircle extends Component {
  constructor(props) {
    super(props);
  }

  static contextType = ScreenSizeContext;

  render() {
    let circleWidth = 15;
    switch (this.props.zoom) {
      case 12:
        circleWidth = 12;
        break;
      case 13:
        circleWidth = 20;
        break;
      case 14:
        circleWidth = 28;
        break;
    }
    return (
      <View
        pointerEvents="none"
        style={[
          {
            position: 'absolute',
            width: circleWidth,
            height: circleWidth,
            borderRadius: circleWidth / 2,
            top: (this.props.viewHeigth - circleWidth) / 2,
            left: this.context.winWidth / 2 - circleWidth / 2,
            opacity: 0.8,
            borderStyle: 'dashed',
            backgroundColor: '#E2EBF2',
            borderWidth: 2,
            borderColor: '#317CFD',
            alignItems: 'center',
            justifyContent: 'center',
          },
        ]}>
        <View
          style={{
            opacity: 1,
            zIndex: 99,
            backgroundColor: 'white',
            borderRadius: 6,
            width: 90,
            height: 30,
            justifyContent: 'center',
            alignItems: 'center',
            position: 'absolute',
            top: (circleWidth - 90) / 2,
            left: (circleWidth - 90) / 2,
          }}>
          <Text style={{textAlign: 'center'}}>100m</Text>
          <View
            style={{
              width: 0,
              height: 0,
              borderTopWidth: 14,
              borderTopColor: '#fff',
              borderRightWidth: 10,
              borderRightColor: 'transparent',
              borderLeftWidth: 10,
              borderLeftColor: 'transparent',
              borderBottomWidth: 10,
              borderBottomColor: 'transparent',
              position: 'absolute',
              bottom: -15,
              left: this.props.zoom >= 12 ? 33 : 35,
            }}
          />
        </View>
        <View
          style={{
            width: 12,
            height: 12,
            borderRadius: 15,
            borderWidth: 4,
            borderColor: '#317CFD',
          }}
        />
      </View>
    );
  }
}
export default MapCircle;
