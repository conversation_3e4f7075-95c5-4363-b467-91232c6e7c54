/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable no-shadow */
import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from 'react-native';
import moment from 'moment';
import { convertDateUTCForIos, getSceneName } from '../Tools';
import I18n from '../I18n';
import { Tme, Colors } from '../ThemeStyle';
import _ from 'lodash';
import { observer } from 'mobx-react/native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import NavBarView from '../share/NavBarView';
import { SelectDate } from '../share/SelectDateView';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { PubSubEvent } from '../types/PubSubEvent';
import PubSub from 'pubsub-js';

const nowTimeStamp = Date.now();
const now = new Date(nowTimeStamp);
const start = moment().startOf('day').toDate();
const end_time = moment().endOf('day').toDate();
@observer
class SmartSelectCondition extends Component {
  constructor(props) {
    super(props);

    this.state = {
      startTime: start,
      endTime: end_time,
      cycle: this.props.route.params.data
        ? this.props.route.params.data.is_cycle
        : true,
      wday: this.props.route.params.data
        ? this.props.route.params.data.week_day
          ? this.props.route.params.data.week_day
          : [1, 2, 3, 4, 5, 6, 0]
        : [1, 2, 3, 4, 5, 6, 0],
      scene_names: this.getSceneName([]),
    };
    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.next')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });

    this.week = [
      { key: 0, value: I18n.t('setting.sunday') },
      { key: 1, value: I18n.t('setting.monday') },
      { key: 2, value: I18n.t('setting.tuesday') },
      { key: 3, value: I18n.t('setting.wednesday') },
      { key: 4, value: I18n.t('setting.thursday') },
      { key: 5, value: I18n.t('setting.friday') },
      { key: 6, value: I18n.t('setting.saturday') },
    ];
  }

  rightClick() {
    this._save();
  }

  componentDidMount() {
    var that = this;
    if (this.props.route.params.data) {
      that.setState({
        startTime: new Date(
          convertDateUTCForIos(
            this.props.route.params.data.begin_at,
          ).getTime() +
          now.getTimezoneOffset() * 60000,
        ),
        endTime: new Date(
          convertDateUTCForIos(
            this.props.route.params.data.end_at,
          ).getTime() +
          now.getTimezoneOffset() * 60000,
        ),
      });
    }

    PubSub.subscribe(PubSubEvent.SMART_SELECT_EVENT, (msg, data) => {
      if (data.type == 'scene') {
        that.props.route.params.routine.scene_ids = data.ids;
        that.setState({ scene_names: that.getSceneName(data.names) });
      } else if (data.type == 'time') {
        that.setState({
          cycle: data.cycle,
          wday: data.wday,
        });
      }
    });
  }

  componentWillUnmount() {
    PubSub.unsubscribe(PubSubEvent.SMART_SELECT_EVENT);
  }

  getSceneName(names) {
    var that = this;
    var temp = [];
    if (names.length === 0) {
      if (this.props.route.params.data) {
        if (this.props.route.params.routine.scenes.length > 0) {
          if (this.props.route.params.data.scene_ids.length > 0) {
            _.forEach(this.props.route.params.data.scene_ids, (uuid, key) => {
              var scene = _.filter(
                that.props.route.params.routine.scenes,
                function (scene) {
                  return scene.uuid === uuid;
                },
              );
              if (scene.length > 0) {
                temp.push(getSceneName(scene[0].name));
              }
            });
            that.props.route.params.routine.scene_ids =
              this.props.route.params.data.scene_ids;
          }
        }
      }
    } else {
      _.forEach(names, (name, k) => {
        temp.push(getSceneName(name));
      });
    }
    if (temp.length == this.props.route.params.routine.scenes.length) {
      return [I18n.t('global.all')];
    } else {
      return temp.length == 0 ? [I18n.t('global.all')] : temp;
    }
  }

  render() {
    return (
      <NavBarView>
        <View
          style={[
            {
              backgroundColor: Tme('bgColor'),
              flex: 1,
            },
          ]}>
          <ScrollView showsVerticalScrollIndicator={false}>
            <View
              style={{
                flex: 1,
                marginTop: 20,
              }}>
              <TouchableOpacity
                testID="scene"
                activeOpacity={0.8}
                onPress={this.click.bind(this, 'scene')}
                style={{ backgroundColor: Tme('cardColor') }}>
                <View
                  style={{
                    padding: 20,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                  <View style={styles.rowTitle}>
                    <Text
                      style={{ color: Tme('cardTextColor'), fontSize: 17 }}>
                      {I18n.t('global.scene')}
                    </Text>
                  </View>
                  <View style={styles.touchRow}>
                    <Text
                      style={{ color: Tme('textColor'), fontSize: 17 }}
                      numberOfLines={1}>
                      {this.state.scene_names
                        ? this.state.scene_names.length > 0
                          ? this.state.scene_names.join(' ')
                          : null
                        : null}
                    </Text>
                    <MaterialIcons
                      name="keyboard-arrow-right"
                      size={20}
                      color={Tme('textColor')}
                    />
                  </View>
                </View>
              </TouchableOpacity>
              <View
                style={{
                  marginHorizontal: 20,
                }}>
                <Text
                  style={{
                    justifyContent: 'flex-start',
                    color: Tme('cardTextColor'),
                    fontSize: 12,
                    marginTop: 10,
                  }}>
                  {I18n.t('trigger.scene_condition')}
                </Text>
              </View>
            </View>
            {this.props.route.params.routine.type != 'time' ? (
              <View>
                {this.props.route.params.routine.type != 'sun' ? (
                  <View>
                    <View
                      style={{
                        backgroundColor: Tme('cardColor'),
                        marginTop: 20,
                      }}>
                      <SelectDate
                        from="start_at"
                        key={1}
                        title={I18n.t('setting.starting_time')}
                        value={this.state.startTime}
                        onChange={this.time.bind(this)}
                      />
                    </View>
                    <View style={{ backgroundColor: Tme('cardColor') }}>
                      <SelectDate
                        from="end_at"
                        key={1}
                        title={I18n.t('setting.end_time')}
                        value={this.state.endTime}
                        onChange={this.endTime.bind(this)}
                      />
                    </View>
                  </View>
                ) : null}

                <View style={{ height: 20 }} />
                <TouchableOpacity
                  testID="week"
                  activeOpacity={0.8}
                  onPress={this.click.bind(this, 'week')}
                  style={{ backgroundColor: Tme('cardColor') }}>
                  <View
                    style={{
                      padding: 20,
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}>
                    <View style={styles.rowTitle}>
                      <Text
                        style={{
                          color: Tme('cardTextColor'),
                          textAlign: 'center',
                          fontSize: 17,
                        }}>
                        {I18n.t('routine.is_cyclic')}
                      </Text>
                    </View>
                    <View style={[styles.touchRow, { flex: 1 }]}>
                      <View
                        style={{
                          flexDirection: 'column',
                          alignItems: 'flex-end',
                        }}>
                        <Text
                          style={{
                            color: Colors.GoldenColor,
                            marginLeft: 8,
                          }}>
                          {this.state.cycle
                            ? I18n.t('routine.run_multiple')
                            : I18n.t('routine.run_once')}
                        </Text>
                        <Text
                          style={{
                            color: Tme('textColor'),
                            marginLeft: 8,
                            flexWrap: 'wrap',
                          }}>
                          {this.showWday()}
                        </Text>
                      </View>
                      <MaterialIcons
                        name="keyboard-arrow-right"
                        size={20}
                        color={Tme('textColor')}
                      />
                    </View>
                  </View>
                </TouchableOpacity>
              </View>
            ) : null}
          </ScrollView>
        </View>
      </NavBarView>
    );
  }

  showWday() {
    var that = this;
    var temp = [];
    const deepWeek = _.cloneDeep(this.state.wday);
    var week_day = deepWeek.sort();
    _.forEach(week_day, (day, key) => {
      _.forEach(that.week, (w, k) => {
        if (w.key == day) {
          temp.push(w.value);
        }
      });
    });
    if (temp.length == 7) {
      return I18n.t('global.every_day');
    } else {
      return temp.length == 0 ? I18n.t('global.every_day') : temp.join(' ');
    }
  }

  click(type) {
    var title = '';
    var screen = '';
    switch (type) {
      case 'scene':
        title = I18n.t('global.scene');
        screen = 'SmartSceneView';
        break;
      case 'week':
        title = I18n.t('routine.is_cyclic');
        screen = 'SmartTimeView';
        break;
      default:
        break;
    }
    this.props.navigation.push(screen, {
      data: this.props.route.params.data,
      routine: this.props.route.params.routine,
      wday: this.state.wday,
      cycle: this.state.cycle,
      show_cycle: true,
      title: title,
    });
  }

  time(date) {
    if (date) {
      this.setState({
        startTime: date,
      });
    }
  }
  endTime(date) {
    if (date) {
      this.setState({
        endTime: date,
      });
    }
  }

  _save() {
    if (this.props.route.params.routine.type !== 'time') {
      if (this.props.route.params.routine.type !== 'sun') {
        this.props.route.params.routine.begin_at =
          this.state.startTime.toString();
        this.props.route.params.routine.end_at = this.state.endTime.toString();
      }
      this.props.route.params.routine.is_cycle = this.state.cycle;
      this.props.route.params.routine.wday = this.state.wday;
    }

    this.props.navigation.push('SelectTargetType', {
      data: this.props.route.params.data,
      routine: this.props.route.params.routine,
      title: I18n.t('automation.select_target_type'),
    });
  }
}
const styles = StyleSheet.create({
  touchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  rowTitle: {
    width: 70,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  account_view: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 3,
    marginBottom: 20,
    marginTop: 16,
  },
  acount: {
    height: 40,
    marginLeft: 6,
    marginRight: 6,
  },
});
export default SmartSelectCondition;
