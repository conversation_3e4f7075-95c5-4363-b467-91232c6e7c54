import React, {Component} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Keyboard,
  FlatList,
  // Image,
  Platform,
} from 'react-native';

import {Tme} from '../ThemeStyle';
import DeviceInfo from 'react-native-device-info';

import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import I18n from '../I18n';
import CardView from '../share/CardView';
import AlertModal from '../share/AlertModal';
import GrayscaledImage from '../share/GrayscaledImage';
import ScreenSizeContext from '../../WindowResizeContext';
import BgAnimateView from '../share/BgAnimateView';
import UpDownAnimateView from '../share/UpDownAnimateView';
const viewBorderRadius = 8;

class AddAutomationMenu extends Component {
  constructor(props) {
    super(props);

    this.updownRef = React.createRef();
    this.bgAnimateRef = React.createRef();
  }

  static contextType = ScreenSizeContext;

  componentDidMount() {
    Keyboard.dismiss();
    this.updownRef.current.start();
    this.bgAnimateRef.current.start();
  }

  componentWillUnmount() {}

  render() {
    var types = [
      {key: 'time', value: I18n.t('routine.to_time')},
      {key: 'sun', value: I18n.t('automation.by_sunrise')},
      {key: 'gps', value: I18n.t('routine.to_location')},
      {key: 'device', value: I18n.t('global.by_device')},
    ];
    return (
      <BgAnimateView
        style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        ref={this.bgAnimateRef}>
        <View
          style={{
            position: 'absolute',
            top: 0,
            bottom: 0,
            right: 0,
            left: 0,
          }}>
          <TouchableOpacity style={{flex: 1}} onPress={() => this.close()} />
        </View>
        <UpDownAnimateView
          ref={this.updownRef}
          style={{
            width: this.context.winWidth - 60,
            backgroundColor: Tme('cardColor'),
            borderRadius: viewBorderRadius,
          }}>
          <View
            style={{
              width: this.context.winWidth - 60,
              height: 50,
              backgroundColor: Tme('cardColor'),
              alignItems: 'center',
              justifyContent: 'center',
              borderTopLeftRadius: viewBorderRadius,
              borderTopRightRadius: viewBorderRadius,
              flexDirection: 'row',
            }}>
            <Text
              style={{
                color: Tme('cardTextColor'),
                fontSize: 20,
                fontWeight: '500',
              }}>
              {I18n.t('automation.automation_type')}
            </Text>
            <TouchableOpacity
              hitSlop={{top: 20, right: 20, bottom: 20, left: 20}}
              style={{position: 'absolute', right: 16}}
              activeOpacity={0.8}
              onPress={() => this.close()}>
              <MaterialIcons
                name="close"
                size={22}
                color={Tme('cardTextColor')}
                style={{textAlign: 'right'}}
              />
            </TouchableOpacity>
          </View>
          <FlatList
            style={{
              paddingVertical: 20,
              paddingHorizontal: 16,
              backgroundColor: Tme('cardColor'),
              borderBottomLeftRadius: viewBorderRadius,
              borderBottomRightRadius: viewBorderRadius,
            }}
            columnWrapperStyle={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}
            ref={flatList => (this._flatList = flatList)}
            data={types}
            renderItem={this._renderRow.bind(this)}
            numColumns={2}
            onEndReachedThreshold={0.1}
            keyExtractor={item => item.key}
          />
        </UpDownAnimateView>
      </BgAnimateView>
    );
  }

  _renderRow({item, index}) {
    var image = this.getAutomationIcon(item);
    return (
      <View style={{marginBottom: 20}}>
        <CardView
          withWaveBg={true}
          onChange={this.onChange.bind(this, item.key)}
          styles={[
            {
              width: (this.context.winWidth - 80) / 2 - 15,
              height: 120,
              padding: 8,
              alignItems: 'center',
              justifyContent: 'center',
            },
          ]}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 8,
            }}>
            <GrayscaledImage source={image} style={{width: 35, height: 35}} />
          </View>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginTop: 8,
              justifyContent: 'center',
            }}>
            <Text
              numberOfLines={2}
              style={[
                {
                  color: Tme('cardTextColor'),
                  fontSize: 16,
                  fontWeight: '500',
                },
              ]}>
              {item.value}
            </Text>
          </View>
        </CardView>
      </View>
    );
  }

  onChange(e) {
    if (e === 'gps' && Platform.OS === 'android') {
      DeviceInfo.hasGms().then(hasGms => {
        if (hasGms) {
          this.onMeunClick(e);
        } else {
          AlertModal.alert(I18n.t('routine.no_google'));
        }
      });
    } else {
      this.onMeunClick(e);
    }
  }

  onMeunClick(e) {
    this.close();
    setTimeout(() => {
      this.props.navigation.push('AutomationName', {
        type: e,
        title: I18n.t('home.name'),
      });
    }, 50);
  }

  getAutomationIcon(item) {
    var icon;
    switch (item.key) {
      case 'time':
        icon = require('../../img/icons/auto/icons8-clock-96.png');
        break;
      case 'gps':
        icon = require('../../img/icons/auto/icons8-map-marker-96.png');
        break;
      case 'sun':
        icon = require('../../img/icons/auto/icons8-sunrise-96.png');
        break;
      case 'device':
        icon = require('../../img/icons/auto/icons8-module-96.png');
        break;
      case 'guard':
        icon = require('../../img/icons/auto/icons8-guard-96.png');
        break;
      default:
        icon = require('../../img/icons/scene/icons8-home-page-96.png');
        break;
    }
    return icon;
  }

  close() {
    this.props.navigation.goBack();
  }
}

const ShowModalView = navigation => {
  navigation.push('AddAutomationMenu');
};

export {
  ShowModalView,
  AddAutomationMenu,
};
