import React, {Component} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from 'react-native';
import MapView from 'react-native-maps';
import BackgroundGeolocation from 'react-native-background-geolocation';
import Geocoder from '../google_geocoder/Geocoder';
import {DEVICE_HEIGHT} from '../Helper';
import GPSHelper from '../GPSHelper';
import I18n from '../I18n';
import {EVENT_MAP, NotificationCenter} from '../NotificationCenter';
import {Tme, Colors} from '../ThemeStyle';
import MapCircle from './MapCircle';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {hideLoading, showLoading} from '../../ILoading';
import ScreenSizeContext from '../../WindowResizeContext';

class MapViewShow extends Component {
  constructor(props) {
    super(props);
    this.mapView = React.createRef();

    const location = this.wgs84togcj02();
    this.state = {
      zoom: 17,
      lat: location ? location[1] : '',
      lng: location ? location[0] : '',
      latitude: this.props.route.params.lat,
      longitude: this.props.route.params.lng,
      address: this.props.route.params.address
        ? this.props.route.params.address
        : '',
      city: '',
      viewHeigth: DEVICE_HEIGHT,
      Gloaction:
        this.props.route.params.lat && this.props.route.params.lng
          ? true
          : false,
      is_location: false,
      radius: 100,
      showLoading: false,
    };
    this.changeCenterstate = false;

    this.props.navigation.setOptions({
      // eslint-disable-next-line react/no-unstable-nested-components
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.next')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  static contextType = ScreenSizeContext;

  componentDidMount() {
    var that = this;
    if (this.state.lat === '') {
      showLoading();

      BackgroundGeolocation.getCurrentPosition().then(position => {
        var lat = position.coords.latitude;
        var lng = position.coords.longitude;
        const location = GPSHelper.wgs84togcj02(lng, lat);
        Geocoder.Coder({lat: location[1], lng: location[0]}, data => {
          if (data.status === 'ok') {
            hideLoading();
            that.setState({
              lat: location[1],
              lng: location[0],
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
              address: data.data,
              Gloaction: true,
            });
          } else {
            hideLoading();
          }
        });
      });
    }
  }

  rightClick() {
    NotificationCenter.dispatchEvent(EVENT_MAP, {
      lat: this.state.latitude,
      lng: this.state.longitude,
      address: this.state.address,
    });
    this.props.navigation.goBack();
  }

  wgs84togcj02() {
    let location = null;
    if (this.props.route.params.lat && this.props.route.params.lng) {
      location = GPSHelper.wgs84togcj02(
        this.props.route.params.lng,
        this.props.route.params.lat,
      );
    }
    return location;
  }

  onLayout(event) {
    this.setState({
      viewHeigth: event.nativeEvent.layout.height,
    });
  }

  render() {
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: Tme('bgColor'),
        }}>
        {this.state.Gloaction ? (
          <View style={{flex: 1, backgroundColor: Tme('cardColor')}}>
            <MapView
              ref={this.mapView}
              testID="mapView"
              onLayout={event => this.onLayout(event)}
              onRegionChangeComplete={this.onRegionChange.bind(this)}
              onRegionChange={() => {
                this.setState({
                  is_location: false,
                });
              }}
              provider="google"
              maxZoomLevel={14}
              minZoomLevel={12}
              pitchEnabled={false}
              rotateEnabled={false}
              initialRegion={{
                latitude: Number(this.state.lat),
                longitude: Number(this.state.lng),
                latitudeDelta: 0.01,
                longitudeDelta: 0.01,
              }}
              style={{flex: 1, backgroundColor: Tme('cardColor')}}
              // onPress={this.onClick.bind(this)}
            >
              {/* <Circle center={{ latitude: this.state.lat, longitude: this.state.lng }}
                radius={100}
                strokeColor="#900"
                fillColor="#900" /> */}
              {/* {this.state.marker ?
               : null}
          {this.state.marker ? <Marker coordinate={this.state.marker} title={this.state.address} /> : null} */}
            </MapView>
            <MapCircle
              viewHeigth={this.state.viewHeigth}
              zoom={this.state.zoom}
            />
            <View
              style={{
                position: 'absolute',
                backgroundColor: 'white',
                bottom: 20,
                right: 20,
              }}>
              <TouchableOpacity
                activeOpacity={0.8}
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: 40,
                  height: 40,
                }}
                onPress={this.changeCenter.bind(this)}>
                <Image
                  style={{width: 25, height: 25}}
                  source={require('../../img/address.png')}
                />
              </TouchableOpacity>
            </View>
            <View
              style={{
                width: this.context.winWidth,
                position: 'absolute',
                backgroundColor: 'rgb(255,255,255)',
                top: 0,
              }}>
              <View
                style={{
                  flex: 1,
                  paddingVertical: 10,
                  paddingLeft: 15,
                  flexDirection: 'row',
                }}>
                {this.state.showLoading && (
                  <View style={{marginRight: 8}}>
                    <ActivityIndicator size="small" color={Colors.MainColor} />
                  </View>
                )}
                <View style={{flex: 1}}>
                  <Text style={{fontSize: 14, color: '#141D24'}}>
                    {this.state.address}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        ) : null}
      </View>
    );
  }

  onRegionChange(region) {
    var that = this;
    if (!this.state.is_location) {
      const location = GPSHelper.gcj02towgs84(
        region.longitude,
        region.latitude,
      );
      //google 地图已经做了偏移
      this.setState({
        lat: region.latitude,
        lng: region.longitude,
        latitude: location[1],
        longitude: location[0],
        zoom: this.getZoomLevelFromRegion(region),
      });
      Geocoder.Coder({lat: region.latitude, lng: region.longitude}, data => {
        if (data.status === 'ok') {
          that.setState({
            is_location: true,
            address: data.data,
          });
        } else {
        }
      });
    }
  }

  getZoomLevelFromRegion(region) {
    const {longitudeDelta} = region;
    const lngD = (360 + longitudeDelta) % 360;
    const tiles = this.context.winWidth / 256;
    const portion = lngD / 360;
    const tilePortion = portion / tiles;
    return Math.round(Math.log2(1 / tilePortion));
  }

  changeCenter() {
    if (this.changeCenterstate) {
      return;
    }
    this.changeCenterstate = true;
    var that = this;
    this.setState({showLoading: true}, () => {
      BackgroundGeolocation.getCurrentPosition()
        .then(position => {
          var lat = position.coords.latitude;
          var lng = position.coords.longitude;
          const location = GPSHelper.wgs84togcj02(lng, lat);
          Geocoder.Coder(
            {
              lat: location[1],
              lng: location[0],
            },
            data => {
              if (data.status === 'ok') {
                this.changeCenterstate = false;
                that.setState(
                  {
                    lat: location[1],
                    lng: location[0],
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude,
                    address: data.data,
                    showLoading: false,
                  },
                  () => {
                    that.mapView.current.animateToRegion({
                      latitude: location[1],
                      longitude: location[0],
                      latitudeDelta: 0.01,
                      longitudeDelta: 0.01,
                    });
                  },
                );
              }
            },
          );
        })
        .catch(error => console.log(error));
    });
  }
}
export default MapViewShow;
