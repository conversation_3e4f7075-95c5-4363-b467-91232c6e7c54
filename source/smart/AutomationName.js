/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import {
  View,
  ScrollView,
  TextInput,
  Text,
  Keyboard,
  // Image,
  Linking,
  TouchableOpacity,
} from 'react-native';
import { Helper } from '../Helper';
import I18n from '../I18n';
import { Tme, Colors } from '../ThemeStyle';
import _ from 'lodash';
import { observer } from 'mobx-react/native';
import Routine from '../models/Routine';
import { DeviceTarget } from '../models/DeviceTarget';
import { DeviceCondition } from '../models/DeviceCondition';
import NavBarView from '../share/NavBarView';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { getSceneName } from '../Tools';
import BackgroundGeolocation from 'react-native-background-geolocation';
import AlertModal from '../share/AlertModal';
import GrayscaledImage from '../share/GrayscaledImage';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { hideLoading, showLoading } from '../../ILoading';
@observer
class AutomationName extends Component {
  routine = new Routine();

  constructor(props) {
    super(props);
    this.state = {
      data: '',
      viewShow: true,
      count: 1,
    };

    this.inputRef = React.createRef();

    this.props.navigation.setOptions({
      headerRight: () => {
        return (
          <HeaderRightBtn
            text={I18n.t('home.next')}
            rightClick={this.rightClick.bind(this)}
          />
        );
      },
    });
  }

  componentDidMount() {
    if (this.props.route.params.type) {
      this.routine.type = this.props.route.params.type;
    }
    this.doFetchData();
  }

  rightClick(time) {
    if (this.props.route.params.type === 'gps') {
      BackgroundGeolocation.getProviderState().then(event => {
        if (
          event.status !== BackgroundGeolocation.AUTHORIZATION_STATUS_ALWAYS
        ) {
          AlertModal.alert('', I18n.t('automation.gps_auto_desp'), [
            {
              text: I18n.t('home.cancel'),
            },
            {
              text: I18n.t('home.setting_btn'),
              onPress: () => {
                Linking.openSettings();
              },
            },
          ]);
        } else {
          BackgroundGeolocation.getState().then(state => {
            if (state.enabled) {
              this._save();
            } else {
              BackgroundGeolocation.startGeofences();
              this._save();
            }
          });
        }
      });
    } else {
      this._save();
    }
  }

  getAutomationIcon() {
    var icon;
    switch (this.routine.type) {
      case 'time':
        icon = require('../../img/icons/auto/icons8-clock-96.png');
        break;
      case 'gps':
        icon = require('../../img/icons/auto/icons8-map-marker-96.png');
        break;
      case 'sun':
        icon = require('../../img/icons/auto/icons8-sunrise-96.png');
        break;
      case 'device':
        icon = require('../../img/icons/auto/icons8-module-96.png');
        break;
      case 'guard':
        icon = require('../../img/icons/auto/icons8-guard-96.png');
        break;
    }
    return icon;
  }

  render() {
    return (
      <NavBarView>
        {this.state.viewShow ? (
          <ScrollView
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="never"
            keyboardDismissMode="on-drag">
            <View
              style={{
                flex: 1,
                marginTop: 20,
                backgroundColor: Tme('bgColor'),
              }}>
              <View
                style={{
                  paddingHorizontal: 20,
                  paddingVertical: 10,
                  backgroundColor: Tme('cardColor'),
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <View style={{ paddingVertical: 8, marginRight: 4 }}>
                  <GrayscaledImage
                    source={this.getAutomationIcon()}
                    style={{ width: 25, height: 25 }}
                  />
                </View>
                <TextInput
                  ref={r => (this.inputRef.current = r)}
                  testID="autoInput"
                  placeholderTextColor={Tme('placeholder')}
                  style={[
                    Colors.TextInputStyle(),
                    { flex: 1, fontWeight: '600', fontSize: 16 },
                  ]}
                  autoCapitalize="none"
                  underlineColorAndroid="transparent"
                  placeholder={I18n.t('home.name')}
                  value={this.routine.name}
                  onBlur={() => {
                    Keyboard.dismiss();
                  }}
                  onChangeText={name => this.onChange(name)}
                />
              </View>
              <View
                style={{ alignItems: 'flex-end', marginTop: 8, marginRight: 8 }}>
                {this.props.route.params.type == 'gps' && (
                  <TouchableOpacity
                    style={{ flexDirection: 'row', alignItems: 'center' }}
                    activeOpacity={0.8}
                    onPress={this.onClickHelp.bind(this)}>
                    <MaterialIcons
                      name="help-outline"
                      size={14}
                      color={Tme('smallTextColor')}
                    />
                    <Text
                      style={{
                        fontSize: 12,
                        color: Tme('smallTextColor'),
                        marginLeft: 4,
                      }}>
                      {I18n.t('global.click_help')}
                    </Text>
                  </TouchableOpacity>
                  // <View
                  //   style={{
                  //     backgroundColor: Tme('bgColor'),
                  //     paddingHorizontal: 16,
                  //     paddingTop: 10,
                  //     paddingBottom: 10,
                  //   }}>
                  //   <Text style={{fontSize: 12, color: Tme('cardTextColor')}}>
                  //     {I18n.t('automation.gps_auto_desp')}
                  //   </Text>
                  // </View>
                )}
              </View>
            </View>
          </ScrollView>
        ) : null}
      </NavBarView>
    );
  }

  onClickHelp() {
    this.props.Navigation.push('HintView', {
      urlTitle: 'gps-help',
    });
  }

  doFetchData() {
    var that = this;
    showLoading();
    Helper.httpGET(
      Helper.urlWithQuery(
        '/partner/automations/data',
        this.props.route.params.uuid
          ? { uuid: this.props.route.params.uuid }
          : {},
      ),
      {
        success: data => {
          var devices = [];
          _.forEach(data.devices, function (v, k) {
            if (v.index != 1) {
              devices.push(v);
            }
          });

          var radio_scenes = [];
          _.forEach(data.scenes, (v, k) => {
            radio_scenes.push({
              key: v.uuid,
              value: getSceneName(v.name),
            });
          });
          var actions = [];
          _.forEach(data.actions, (v, k) => {
            actions.push({ key: v.uuid, value: v.name });
          });

          that.routine.devices = devices;
          that.routine.actions = actions;
          that.routine.scenes = data.scenes;
          that.routine.radio_scenes = radio_scenes;
          that.routine.rooms = data.rooms;
          that.routine.home_address = data.home_address;
          if (that.props.route.params.uuid) {
            that.routine.uuid = that.props.route.params.uuid;
          }

          if (data.automation) {
            that.setState({ data: data.automation });

            that.routine.name = data.automation.name;
            that.routine.type = data.automation.type;
            that.routine.target_type = data.automation.target_type;
            if (data.automation.targets) {
              var targets = [];
              _.each(data.automation.targets, (v, k) => {
                targets.push(new DeviceTarget(v));
              });
              that.routine.targets = targets;
            }
            if (data.automation.conditions) {
              var conditions = [];
              _.each(data.automation.conditions, (v, k) => {
                conditions.push(new DeviceCondition(v));
              });
              that.routine.conditions = conditions;
            }
          }
          that.setState(
            {
              viewShow: true,
            },
            () => {
              if (_.isEmpty(that.props.route.params.uuid)) {
                setTimeout(() => {
                  this.inputRef.current.focus();
                }, 500);
              }
            },
          );
        },
        ensure: () => {
          hideLoading();
        },
        error: data => {
          AlertModal.alert('', _.uniq(data).join('\n'), [
            {
              text: 'cancel',
              onPress: () => this.props.navigation.goBack(),
              style: 'cancel',
            },
          ]);
        },
      },
    );
  }

  _save() {
    Keyboard.dismiss();
    let routine = this.routine;
    if (routine.name) {
      var screen = '';
      if (routine.type == 'sun') {
        screen = 'SunScreen';
      }
      if (routine.type == 'time') {
        screen = 'AutomationTimeView';
      }
      if (routine.type == 'gps') {
        screen = 'AutomationMap';
      }
      if (routine.type == 'device') {
        screen = 'AutomationDevice';
      }
      this.props.navigation.push(screen, {
        data: this.state.data,
        routine: routine,
        title: I18n.t('automation.if'),
      });
    } else {
      AlertModal.alert(I18n.t('scene.scene_input'));
    }
  }

  onChange(e) {
    this.routine.name = e;
  }
}
export default AutomationName;
