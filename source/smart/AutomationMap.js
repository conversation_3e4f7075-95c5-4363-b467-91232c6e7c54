import React, {Component} from 'react';
import {View, Text, TouchableOpacity, Platform} from 'react-native';
import {EVENT_MAP, NotificationCenter} from '../NotificationCenter';
import I18n from '../I18n';
import {Tme} from '../ThemeStyle';
import {Helper<PERSON>emo, Helper} from '../Helper';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import RadioButtons from '../RadioButtons';
import NavBarView from '../share/NavBarView';
import DeviceInfo from 'react-native-device-info';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';

export default class AutomationMap extends Component {
  constructor(props) {
    super(props);

    this.state = {
      returned: this.props.route.params.data
        ? this.props.route.params.data.is_returned
          ? 'true'
          : 'false'
        : 'true',
      address: '',
      lat: '',
      lng: '',
      is_foreign: this.props.route.params.is_foreign,
      cycle: this.props.route.params.data
        ? this.props.route.params.data.is_cycle
        : true,
    };

    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.next')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this._save();
  }

  componentDidMount() {
    var that = this;

    NotificationCenter.addObserver(this, EVENT_MAP, data => {
      that.setState({
        lat: data.lat,
        lng: data.lng,
        address: data.address,
      });
    });
    this.initLocation();
  }

  initLocation() {
    var lat = '';
    var lng = '';
    var address = '';
    if (this.props.route.params.data && this.props.route.params.data.location) {
      const location = Helper.locationFormat(
        this.props.route.params.data.location,
      );
      if (location[1]) {
        lat = location[1];
      }
      if (location[0]) {
        lng = location[0];
      }
      if (this.props.route.params.data.location_info) {
        address = this.props.route.params.data.location_info;
      }
    } else {
      if (this.props.route.params.routine.home_address.location) {
        const location = Helper.locationFormat(
          this.props.route.params.routine.home_address.location,
        );
        if (location[0]) {
          lng = location[0];
        }
        if (location[1]) {
          lat = location[1];
        }
      }
      if (this.props.route.params.routine.home_address.location_info) {
        address = this.props.route.params.routine.home_address.location_info;
      }
    }

    if (lat && lng) {
      this.setState({
        address: address,
        lat: lat,
        lng: lng,
      });
    }
  }

  componentWillUnmount() {
    NotificationCenter.removeObserver(this, EVENT_MAP);
  }

  render() {
    var address = '';
    if (this.state.returned === 'true') {
      address = I18n.t('routine.get_to') + ': ' + this.state.address;
    } else {
      address = I18n.t('routine.leave') + ': ' + this.state.address;
    }
    return (
      <NavBarView>
        <View style={{padding: 0}}>
          <View style={{marginTop: 20}}>
            <RadioButtons
              data={[
                {key: 'get_to', value: I18n.t('routine.get_to')},
                {key: 'leave', value: I18n.t('routine.leave')},
              ]}
              defaultKey={this.state.returned == 'true' ? 'get_to' : 'leave'}
              onChange={this.change.bind(this)}
            />
          </View>
          <View style={{marginTop: 20}}>
            <TouchableOpacity
              testID="mapBtn"
              onPress={this.pushMap.bind(this)}
              activeOpacity={1.0}
              style={{
                paddingTop: 20,
                paddingBottom: 15,
                paddingHorizontal: 15,
                backgroundColor: Tme('cardColor'),
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View style={{flex: 1}}>
                <Text style={{fontSize: 17, color: Tme('cardTextColor')}}>
                  {this.state.address
                    ? address
                    : I18n.t('routine.please_address')}
                </Text>
              </View>
              <View styl={{width: 25}}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </NavBarView>
    );
  }

  pushMap() {
    var that = this;
    // that.pushGaoDe();
    if (HelperMemo.user_data.user.zone == 'cn') {
      if (Platform.OS == 'android') {
        DeviceInfo.hasGms().then(hasGms => {
          if (hasGms) {
            that.pushGoogleMap();
          } else {
            that.pushGaoDe();
          }
        });
      } else {
        that.pushGaoDe();
      }
    } else {
      if (Platform.OS == 'android') {
        DeviceInfo.hasGms().then(hasGms => {
          if (hasGms) {
            that.pushGoogleMap();
          } else {
            that.pushGaoDe();
          }
        });
      } else {
        that.pushGoogleMap();
      }
    }
  }

  pushGoogleMap() {
    this.props.navigation.push('MapViewShow', {
      lat: this.state.lat,
      lng: this.state.lng,
      address: this.state.address,
    });
  }

  pushGaoDe() {
    this.props.navigation.push('GaoDeMapViewShow', {
      lat: this.state.lat,
      lng: this.state.lng,
      address: this.state.address,
    });
  }

  onCycle(data) {
    this.setState({
      cycle: data == 'is_cyclic',
    });
  }

  _save() {
    if (this.state.address === '') {
      AlertModal.alert(
        I18n.t('home.warning_message'),
        I18n.t('routine.please_address'),
      );
      return;
    }
    if (!this.state.lng) {
      AlertModal.alert(
        I18n.t('home.warning_message'),
        I18n.t('routine.please_address'),
      );
      return;
    }
    if (!this.state.lat) {
      AlertModal.alert(
        I18n.t('home.warning_message'),
        I18n.t('routine.please_address'),
      );
      return;
    }

    this.props.route.params.routine.is_returned = this.state.returned;
    this.props.route.params.routine.address = this.state.address;
    this.props.route.params.routine.lat = this.state.lat;
    this.props.route.params.routine.lng = this.state.lng;

    this.props.navigation.push('SmartSelectCondition', {
      data: this.props.route.params.data,
      routine: this.props.route.params.routine,
      title: I18n.t('automation.select_conditions'),
    });
  }

  change(e) {
    this.setState({
      returned: e == 'get_to' ? 'true' : 'false',
    });
  }
}
