import React, { Component } from 'react';
import { View, ScrollView } from 'react-native';
import I18n from '../I18n';
import moment from 'moment';
import _ from 'lodash';
import CheckBox from '../share/CheckBox';
import { Tme } from '../ThemeStyle';
import { SelectDate } from '../share/SelectDateView';
import NavBarView from '../share/NavBarView';

const nowTimeStamp = Date.now();
const now = new Date(nowTimeStamp);

const start = moment().startOf('day').toDate();
import RadioButtons from '../RadioButtons';
import AlertModal from '../share/AlertModal';
import { convertDateUTCForIos } from '../Tools';
import HeaderRightBtn from '../share/HeaderRightBtn';

export default class AutomationTimeView extends Component {
  constructor(props) {
    super(props);
    this.state = {
      wday: this.props.route.params.data
        ? this.props.route.params.data.week_day
          ? this.props.route.params.data.week_day
          : [0, 1, 2, 3, 4, 5, 6]
        : [0, 1, 2, 3, 4, 5, 6],
      startTime: start,
      cycle: this.props.route.params.data
        ? this.props.route.params.data.is_cycle
        : true,
    };
    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.next')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
    this.week = [
      { key: 0, value: I18n.t('setting.sunday') },
      { key: 1, value: I18n.t('setting.monday') },
      { key: 2, value: I18n.t('setting.tuesday') },
      { key: 3, value: I18n.t('setting.wednesday') },
      { key: 4, value: I18n.t('setting.thursday') },
      { key: 5, value: I18n.t('setting.friday') },
      { key: 6, value: I18n.t('setting.saturday') },
    ];
  }

  rightClick() {
    this._save();
  }

  componentDidMount() {
    var that = this;

    if (this.props.route.params.data) {
      that.setState({
        startTime: new Date(
          convertDateUTCForIos(
            this.props.route.params.data.begin_at,
          ).getTime() +
          now.getTimezoneOffset() * 60000,
        ),
      });
    }
  }

  formatTime(time) {
    return new Date(moment(time).utc().format('YYYY-MM-DD HH:mm'));
  }

  render() {
    var checkbox = [];
    var that = this;

    if (!this.state.cycle) {
      checkbox.push(
        <RadioButtons
          key={Math.random()}
          data={this.week}
          defaultKey={this.state.wday ? this.state.wday[0] : 1}
          onChange={this.change.bind(this)}
        />,
      );
    } else {
      var length = this.week.length - 1;
      _.forEach(this.week, function (data, index) {
        checkbox.push(
          <View key={index} style={{ backgroundColor: Tme('cardColor') }}>
            <View style={{ paddingHorizontal: 20 }}>
              <CheckBox
                isLast={index == length}
                value={data.value}
                index={data.key}
                isChecked={_.includes(that.state.wday, data.key)}
                onClick={that.onClick.bind(that)}
              />
            </View>
          </View>,
        );
      });
    }
    return (
      <NavBarView>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{
            flex: 1,
            backgroundColor: Tme('bgColor'),
          }}>
          <View style={{ marginTop: 20 }}>
            <RadioButtons
              data={[
                { key: 'run_once', value: I18n.t('routine.run_once') },
                {
                  key: 'run_multiple',
                  value: I18n.t('routine.run_multiple'),
                },
              ]}
              defaultKey={this.state.cycle ? 'run_multiple' : 'run_once'}
              onChange={this.onCycle.bind(this)}
            />
          </View>

          <View style={{ marginTop: 20, backgroundColor: Tme('cardColor') }}>
            {checkbox}
          </View>

          <View style={{ marginTop: 20, backgroundColor: Tme('cardColor') }}>
            <SelectDate
              title={I18n.t('setting.starting_time')}
              value={this.state.startTime}
              onChange={this.time.bind(this)}
              navigation={this.props.navigation}
            />
          </View>
          <View style={{ height: 20 }} />
        </ScrollView>
      </NavBarView>
    );
  }

  change(e) {
    this.setState({
      wday: [e],
    });
  }

  onCycle(data) {
    if (data == 'run_multiple') {
      this.setState({
        wday: [0, 1, 2, 3, 4, 5, 6],
        cycle: true,
      });
    } else {
      this.setState({
        wday: [0],
        cycle: false,
      });
    }
  }

  time(date) {
    if (date) {
      this.setState({
        startTime: date,
      });
    }
  }

  onClick(data) {
    var temp = [];
    this.state.wday.map(v => {
      temp.push(v);
    });

    if (_.includes(this.state.wday, data)) {
      temp.splice(temp.indexOf(data), 1);
    } else {
      temp.push(data);
    }
    this.setState({
      wday: temp,
    });
  }

  _save() {
    if (this.state.wday.length === 0) {
      AlertModal.alert(
        I18n.t('home.warning_message'),
        I18n.t('routine.please_date'),
      );
      return;
    }
    this.props.route.params.routine.wday = this.state.wday;
    this.props.route.params.routine.is_cycle = this.state.cycle;
    this.props.route.params.routine.begin_at = this.state.startTime.toString();

    this.props.navigation.push('SmartSelectCondition', {
      data: this.props.route.params.data,
      routine: this.props.route.params.routine,
      text: I18n.t('automation.select_conditions'),
    });
  }
}
