import React, {
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Platform,
  FlatList,
  Dimensions,
} from 'react-native';
import {Helper, HelperMemo} from '../Helper';
import I18n from '../I18n';
import moment from 'moment';
import BackgroundGeolocation from 'react-native-background-geolocation';
import {Tme, Colors, IsDark} from '../ThemeStyle';
import ActionSheet from '@alessiocancian/react-native-actionsheet';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import SwitchBtn from '../share/SwitchBtn';
import CardView from '../share/CardView';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {Toast} from '../Toast';
import {SnackbarShow, SnackbarHide} from '../Snackbar';
import {ShowModalView} from './AddAutomationMenu';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {isOwnerOrAdmin} from '../Router';
import {
  convertDateUTCForIos,
  getAutomationIcon,
  mainRadius,
  mainTitle,
} from '../Tools';
import _ from 'lodash';
import AlertModal from '../share/AlertModal';
import GrayscaledImage from '../share/GrayscaledImage';
import {IconView} from '../setting/SettingScreen';
import {hideLoading, showLoading} from '../../ILoading';
import PubSub from 'pubsub-js';
import {EVENT_ROUTINE} from '../types/PubSubEvent';
import {AutomationItem, SmartScreenProps} from '../types/smart';

const weekdays: Record<number, string> = {
  1: 'monday',
  2: 'tuesday',
  3: 'wednesday',
  4: 'thursday',
  5: 'friday',
  6: 'saturday',
  0: 'sunday',
};

const nowTimeStamp = Date.now();
const now = new Date(nowTimeStamp);

interface AutomationScreenProps extends SmartScreenProps {
  automations?: AutomationItem[];
}

const AutomationScreen = forwardRef<any, AutomationScreenProps>(
  ({navigation}, ref) => {
    const [dataSource, setDataSource] = useState<AutomationItem[]>([]);
    const [showAdd, setShowAdd] = useState(false);
    const [refreshing, setRefreshing] = useState(false);
    const [winWidth, setWinWidth] = useState(
      initWidth(Dimensions.get('window')),
    );
    // 添加 optionsState 状态来强制刷新 ActionSheet 选项
    const [optionsState, setOptionsState] = useState<string[]>([]);

    const actionSheet = useRef<any>(null);
    const firstFetch = useRef(true);
    const firstFetchRef = useRef<any>(null);
    const clickRowRef = useRef<AutomationItem | null>(null);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      setData: (data: AutomationItem[], show_Add: boolean) => {
        if (firstFetch.current) {
          firstFetch.current = false;
          setDataSource(data);
          setShowAdd(show_Add);
        }
      },
    }));

    useEffect(() => {
      PubSub.subscribe(EVENT_ROUTINE, () => {
        doFetchData();
      });

      if (!firstFetchRef.current) {
        doFetchData();
        firstFetchRef.current = 'first';
      }

      const dimensionsEvent = Dimensions.addEventListener('change', onResize);

      return () => {
        if (dimensionsEvent) {
          dimensionsEvent.remove();
        }
        PubSub.unsubscribe(EVENT_ROUTINE);
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    function initWidth(window: any) {
      if (Platform.OS == 'android') {
        return window.width;
      } else {
        if (window.width > window.height) {
          return (
            window.width -
            HelperMemo.STATUS_BAR_HEIGHT -
            HelperMemo.BOTTOM_BAR_HEIGHT -
            22
          );
        } else {
          return window.width;
        }
      }
    }

    const onResize = ({window}: any) => {
      setWinWidth(initWidth(window));
    };

    const addAuto = () => {
      ShowModalView(navigation);
    };

    const showHeader = () => {
      if (showAdd) {
        return (
          <View style={{marginTop: 20}}>
            <CardView
              onChange={addAuto}
              styles={[
                {
                  width: winWidth - 40,
                  height: 60,
                  borderRadius: mainRadius(),
                },
              ]}>
              <View style={{flex: 1}}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    flex: 1,
                  }}>
                  <IconView>
                    <Ionicons
                      name="add-outline"
                      size={20}
                      color={Tme('cardTextColor')}
                    />
                  </IconView>
                  <Text
                    style={{
                      fontSize: mainTitle(),
                      color: Tme('cardTextColor'),
                      marginLeft: 6,
                      fontWeight: '500',
                    }}>
                    {I18n.t('automation.add_automation')}
                  </Text>
                </View>
              </View>
            </CardView>
          </View>
        );
      }
      return null;
    };

    const renderRow = ({
      item,
      index,
    }: {
      item: AutomationItem;
      index: number;
    }) => {
      const image = getAutomationIcon(item);
      return (
        <View style={{marginBottom: 1}}>
          <CardView
            styles={[
              {
                borderRadius: 0,
                width: winWidth - 40,
                paddingBottom: 20,
              },
              index === 0 && {
                marginTop: 20,
                borderTopLeftRadius: mainRadius(),
                borderTopRightRadius: mainRadius(),
              },
              index + 1 === dataSource.length && {
                borderBottomLeftRadius: mainRadius(),
                borderBottomRightRadius: mainRadius(),
              },
            ]}>
            <View style={{paddingLeft: 20, marginRight: 16}}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'flex-end',
                  marginTop: 20,
                }}
              />
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <View
                  style={{flexDirection: 'row', alignItems: 'center', flex: 1}}>
                  <GrayscaledImage
                    source={image}
                    style={{width: 24, height: 24, marginRight: 8}}
                  />
                  <Text
                    numberOfLines={2}
                    style={[
                      {
                        color: Tme('cardTextColor'),
                      },
                      Colors.CardFontStyle,
                    ]}>
                    {item.name}
                  </Text>
                </View>
                <TouchableOpacity
                  testID="autoMeun"
                  activeOpacity={0.8}
                  style={{
                    width: 30,
                    marginLeft: 30,
                    alignItems: 'flex-end',
                  }}
                  hitSlop={{top: 20, right: 20, bottom: 20, left: 20}}
                  onPress={() => sheetShow(item)}>
                  <MCIcons
                    name="dots-horizontal"
                    size={22}
                    color={Tme('smallTextColor')}
                  />
                </TouchableOpacity>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <View style={{flex: 5}}>{textShow(item)}</View>
                <View
                  style={{
                    alignItems: 'flex-end',
                    justifyContent: 'center',
                    flex: 1,
                  }}>
                  <SwitchBtn
                    cusStyle={{marginRight: -3}}
                    trackColor={{true: Colors.MainColor}}
                    value={item.is_enabled!}
                    change={() => changeTrigger(item)}
                  />
                </View>
              </View>
            </View>
          </CardView>
        </View>
      );
    };

    const sheetShow = (rowData: AutomationItem) => {
      clickRowRef.current = rowData;
      // 更新选项状态，强制重新渲染
      setOptionsState([
        I18n.t('home.cancel'),
        I18n.t('home.edit'),
        I18n.t('home.remove_btn'),
      ]);
      actionSheet.current?.show();
    };

    const sheetClick = (index: number) => {
      if (!clickRowRef.current) {
        return;
      }

      if (index === 0) {
        setting(clickRowRef.current);
      }
      if (index == 1) {
        remove(clickRowRef.current);
      }
    };

    const remove = (data: AutomationItem) => {
      const body = {
        uuid: data.uuid,
        routines_type: 'delete',
      };

      setTimeout(() => {
        AlertModal.alert(I18n.t('global.activate_sure'), '', [
          {
            text: I18n.t('home.cancel'),
            onPress: () => {},
          },
          {
            text: I18n.t('home.confirm'),
            onPress: () => {
              showLoading();
              Helper.httpPOST(
                '/partner/automations/delete',
                {
                  ensure: () => {
                    hideLoading();
                  },
                  success: (res: any) => {
                    if (data.type === 'gps') {
                      BackgroundGeolocation.removeGeofence(data.uuid).then(
                        () => {
                          console.log('[removeGeofence] success');
                        },
                      );
                    }
                    const automations = res.automations;
                    if (isOwnerOrAdmin()) {
                      automations.unshift({type: 'add'});
                    }

                    setDataSource(automations);
                    Toast.show();
                  },
                },
                body,
              );
            },
          },
        ]);
      }, 100);
    };

    const textShow = (data: AutomationItem) => {
      const week: string[] = [];

      if (data.week_day) {
        if (data.week_day.length == 7) {
          week.push(I18n.t('automation.everyday'));
        } else {
          if (data.week_day.sort().join(',') == '1,2,3,4,5') {
            week.push(I18n.t('automation.workday'));
          } else {
            data.week_day.sort().map(v => {
              week.push(I18n.t('setting.' + weekdays[v]));
            });
          }
        }
      }

      let startTime: Date | undefined;
      let endTime: Date | undefined;

      if (data.begin_at) {
        startTime = new Date(
          convertDateUTCForIos(data.begin_at).getTime() +
            now.getTimezoneOffset() * 60000,
        );
      }
      if (data.end_at) {
        endTime = new Date(
          convertDateUTCForIos(data.end_at).getTime() +
            now.getTimezoneOffset() * 60000,
        );
      }

      return (
        <View style={{marginTop: 10}}>
          <View style={{flexDirection: 'row'}}>
            <Text style={{color: Tme('smallTextColor')}} numberOfLines={2}>
              {week.join(', ')}
            </Text>
          </View>
          <View style={{flexDirection: 'row', marginTop: 4}}>
            {data.type == 'sun' ? null : (
              <Text style={{color: Tme('smallTextColor'), marginRight: 4}}>
                {startTime && moment(startTime).format('HH:mm')}
              </Text>
            )}
            {data.type === 'sun' || data.type === 'time' ? null : (
              <MaterialIcons
                size={18}
                color={Tme('smallTextColor')}
                name="trending-flat"
                style={{marginRight: 4}}
              />
            )}
            {data.type === 'time' || data.type === 'sun' ? null : (
              <Text style={{color: Tme('smallTextColor'), marginRight: 4}}>
                {endTime && moment(endTime).format('HH:mm')}
              </Text>
            )}
            <Text style={{color: Tme('smallTextColor')}}>
              {data.is_cycle
                ? I18n.t('routine.run_multiple')
                : I18n.t('routine.run_once')}
            </Text>
          </View>
          {data.target_type === 'external_url' && (
            <View style={{flexDirection: 'row', marginTop: 4}}>
              <Text style={{color: Tme('smallTextColor')}} numberOfLines={2}>
                {data.targets?.[0]?.app_url}
              </Text>
            </View>
          )}

          {data.target_type === 'device' && (
            <View
              style={{
                flexDirection: 'row',
                marginTop: 4,
                alignItems: 'center',
              }}>
              <Text
                style={{
                  marginRight: 5,
                  color: Tme('smallTextColor'),
                  fontSize: 11,
                }}>
                {data.targets?.length}
              </Text>
              <Text style={{color: Tme('smallTextColor'), fontSize: 11}}>
                {I18n.t('global.device_count')}
              </Text>
            </View>
          )}
          {!_.isEmpty(data.target_name) && (
            <View
              style={{
                flexDirection: 'row',
                marginTop: 4,
                alignItems: 'center',
              }}>
              <Text
                style={{
                  color: Tme('smallTextColor'),
                  fontSize: 11,
                  marginRight: 5,
                }}>
                {data.target_type === 'action' && I18n.t('global.action')}
                {data.target_type === 'scene' && I18n.t('global.scene')}
              </Text>
              <Text
                style={{
                  color: Tme('smallTextColor'),
                  fontSize: 11,
                }}>
                {data.target_name}
              </Text>
            </View>
          )}
        </View>
      );
    };

    const changeTrigger = (data: AutomationItem) => {
      showLoading();
      const body = {
        uuid: data.uuid,
        routines_type: 'on_off',
      };

      Helper.httpPOST(
        '/partner/automations/toggle',
        {
          ensure: () => {
            hideLoading();
          },
          success: (res: any) => {
            if (data.type === 'gps') {
              if (data.is_enabled) {
                BackgroundGeolocation.removeGeofence(data.uuid).then(() => {
                  console.log('[removeGeofence] success');
                });
              } else {
                const location = Helper.locationFormat(data.location!);
                BackgroundGeolocation.addGeofence({
                  identifier: data.uuid,
                  radius: Helper.BackgroundGeolocationRadius,
                  latitude: parseFloat(location[1]),
                  longitude: parseFloat(location[0]),
                  notifyOnEntry: data.is_returned,
                  notifyOnExit: !data.is_returned,
                });
              }
            }
            const automations = res.automations;
            let TempshowAdd = false;
            if (isOwnerOrAdmin()) {
              TempshowAdd = true;
            }
            setDataSource(automations);
            setShowAdd(TempshowAdd);
          },
        },
        body,
      );
    };

    const setting = (data: AutomationItem) => {
      navigation.push('AutomationName', {
        uuid: data.uuid,
        title: I18n.t('home.name'),
      });
    };

    const doRefreshData = () => {
      doFetchData('refresh');
    };

    const doFetchData = (type?: string) => {
      if (type == 'refresh') {
        setRefreshing(true);
      } else {
        if (firstFetch.current) {
          // parents.showLoader();
        } else {
          SnackbarShow();
        }
      }

      Helper.httpGET(Helper.urlWithQuery('/partner/automations'), {
        success: (data: any) => {
          const automations = data.automations;
          let TempshowAdd = false;
          if (isOwnerOrAdmin()) {
            TempshowAdd = true;
          }
          setShowAdd(TempshowAdd);
          setDataSource(automations);
        },
        ensure: () => {
          if (type == 'refresh') {
            setRefreshing(false);
          } else {
            if (firstFetch.current) {
              firstFetch.current = false;
              hideLoading();
            } else {
              SnackbarHide();
            }
          }
        },
      });
    };

    return (
      <View style={{flex: 1, backgroundColor: 'trasparent'}}>
        <FlatList
          showsVerticalScrollIndicator={false}
          ListHeaderComponent={showHeader}
          style={{flex: 1, paddingHorizontal: 20}}
          data={dataSource}
          renderItem={renderRow}
          numColumns={1}
          onEndReachedThreshold={0.1}
          refreshing={refreshing}
          // eslint-disable-next-line react/no-unstable-nested-components
          ListFooterComponent={() => <View style={{height: 20, width: 1}} />}
          onRefresh={doRefreshData}
          keyExtractor={(itme, index) => index.toString()}
        />
        <ActionSheet
          ref={actionSheet}
          userInterfaceStyle={IsDark() ? 'dark' : 'light'}
          options={
            optionsState.length > 0
              ? optionsState
              : [
                  I18n.t('home.cancel'),
                  I18n.t('home.edit'),
                  I18n.t('home.remove_btn'),
                ]
          }
          cancelButtonIndex={0}
          destructiveButtonIndex={2}
          theme="ios"
          styles={{
            cancelButtonBox: {
              height: 50,
              marginTop: 6,
              alignItems: 'center',
              justifyContent: 'center',
            },
            buttonBox: {
              height: 50,
              alignItems: 'center',
              justifyContent: 'center',
            },
          }}
          onPress={index => {
            sheetClick(index - 1);
          }}
        />
      </View>
    );
  },
);

export default AutomationScreen;
