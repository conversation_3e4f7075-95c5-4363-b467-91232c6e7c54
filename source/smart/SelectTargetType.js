/* eslint-disable react/no-unstable-nested-components */
import React, {Component} from 'react';
import {View} from 'react-native';
import I18n from '../I18n';
import {Tme} from '../ThemeStyle';
import RadioButtons from '../RadioButtons';
import NavBarView from '../share/NavBarView';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';

export default class SelectTargetType extends Component {
  constructor(props) {
    super(props);
    this.state = {
      device_scene: this.props.route.params.data.target_type
        ? this.props.route.params.data.target_type
        : '',
    };
    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.next')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this._save();
  }

  render() {
    return (
      <NavBarView>
        <View
          style={{
            shadowOffset: {height: 4},
            shadowColor: 'rgba(0,0,0,0.02)',
            shadowOpacity: 0.2,
            shadowRadius: 1,
            backgroundColor: Tme('bgColor'),
            marginTop: 20,
          }}>
          <View style={{backgroundColor: Tme('cardColor')}}>
            <RadioButtons
              defaultKey={this.state.device_scene}
              onChange={this.change.bind(this)}
              data={[
                {key: 'device', value: I18n.t('routine.cond_device')},
                {key: 'scene', value: I18n.t('routine.cond_scene')},
                {key: 'action', value: I18n.t('routine.cond_action')},
                // {key: 'external_url', value: I18n.t('routine.cond_url')},
              ]}
            />
          </View>
        </View>
      </NavBarView>
    );
  }

  _save() {
    if (this.state.device_scene === '') {
      AlertModal.alert(
        I18n.t('home.warning_message'),
        I18n.t('routine.please_to'),
      );
    } else {
      this.props.navigation.push('SmartDo', {
        data: this.props.route.params.data,
        routine: this.props.route.params.routine,
        title: I18n.t('automation.do'),
      });
    }
  }

  change(e) {
    this.setState(
      {
        device_scene: e,
      },
      () => {
        this.props.route.params.routine.target_type = e;
        this._save();
      },
    );
  }
}
