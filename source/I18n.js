import I18n from 'react-native-i18n';
I18n.fallbacks = true;
I18n.translations || (I18n.translations = {});
I18n.translations.ar = I18n.extend((I18n.translations.ar || {}), JSON.parse('{"timezones":{"Abu Dhabi":"ابو ظبي","Adelaide":"أديلايد","Alaska":"ألاسكا","Almaty":"ألماتي","American Samoa":"ساموا الأمريكية","Amsterdam":"أمستردام","Arizona":"أريزونا","Astana":"أستانا","Athens":" أثينا","Atlantic Time (Canada)":"توقيت المحيط الأطلسي (كندا)","Auckland":"أوكلاند","Azores":"الأزور","Baghdad":"بغداد","Baku":"باكو","Bangkok":"بانكوك","Beijing":"بيكين","Belgrade":"بلغراد","Berlin":"برلين","Bern":"لرن","Bogota":"بوغوتا","Brasilia":"برازيليا","Bratislava":"براتيسلافا","Brisbane":"بريسبان","Brussels":"بروكسيل","Bucharest":" بوخارست","Budapest":"بودابست","Buenos Aires":"وينس آيرس\\"","Cairo":"القاهرة","Canberra":"كانبيرا","Cape Verde Is.":"كاب فيردي","Caracas":"كراكاس","Casablanca":"الدار البيضاء","Central America":"أمريكا الوسطى","Central Time (US & Canada)":"التوقيت المركزي (الولايات المتحدة وكندا)","Chatham Is.":"هل تشاتام","Chennai":"تشيناي","Chihuahua":"تشيهواهوا","Chongqing":"تشونغتشينغ","Copenhagen":"كوبنهاغن","Darwin":"داروين","Dhaka":"دكا","Dublin":"دبلن","Eastern Time (US & Canada)":"التوقيت الشرقى (الولايات المتحدة وكندا)","Edinburgh":"أدنبرة","Ekaterinburg":" ايكاترينبرغ","Fiji":"فيجي","Georgetown":"جورج تاون","Greenland":"جرينلاند","Guadalajara":"غوادالاخارا","Guam":"غوام","Hanoi":"هانوي","Harare":"هراري","Hawaii":"هاواي","Helsinki":"هلسنكي","Hobart":"هوبارت","Hong Kong":"هونغ كونغ","Indiana (East)":"إنديانا (شرق)","International Date Line West":"خط التوقيت الدولي الغربي","Irkutsk":" إيركوتسك ","Islamabad":"إسلام أباد","Istanbul":"اسطنبول","Jakarta":"جاكرتا","Jerusalem":"القدس","Kabul":"كابول","Kaliningrad":"كالينينغراد","Kamchatka":" كامتشاتكا ","Karachi":"كراتشي","Kathmandu":" كاتماندو","Kolkata":"كالكوتا","Krasnoyarsk":" كراسنويارسك ","Kuala Lumpur":"كوالا لمبور","Kuwait":"الكويت","Kyiv":"كييف","La Paz":"لاباز","Lima":"ليما","Lisbon":"لشبونة","Ljubljana":"ليوبليانا","London":"لندن","Madrid":"مدريد","Magadan":"ماغادان","Marshall Is.":"جزر مارشال","Mazatlan":"مازاتلان","Melbourne":"ملبورن","Mexico City":"المكسيك","Mid-Atlantic":"منتصف المحيط الأطلسي","Midway Island":"جزيرة ميدواي","Minsk":"مينسك","Monrovia":"مونروفيا","Monterrey":"مونتيري","Montevideo":"مونتيفيديو","Moscow":"موسكو","Mountain Time (US & Canada)":"توقيت جبال الروكي (الولايات المتحدة وكندا)","Mumbai":"بومباي","Muscat":"مسقط","Nairobi":" نيروبي","New Caledonia":"نوفيل-كاليدونيا","New Delhi":"نيودلهي","Newfoundland":"نيوفاوندلاند","Novosibirsk":" نوفوسيبيرسك ","Nuku\'alofa":"نوكوالوفا","Osaka":"أوساكا","Pacific Time (US & Canada)":"توقيت المحيط الهادي (الولايات المتحدة وكندا)","Paris":"باريس","Perth":"بيرث","Port Moresby":"بورت مورسبي","Prague":"براغ","Pretoria":"بريتوريا","Quito":"كيتو","Rangoon":"رانغون","Riga":"ريغا","Riyadh":"الرياض","Rome":"روما","Samara":"سامارا, روسيا","Samoa":"ساموا","Santiago":"سانتياغو","Sapporo":"سابورو","Sarajevo":"سراييفو","Saskatchewan":"ساسكاتشوان","Seoul":"سيول","Singapore":" سنغافورة ","Skopje":"سكوبي","Sofia":"صوفيا","Solomon Is.":"جزر سالومون","Srednekolymsk":"سريدنكوليمسك","Sri Jayawardenepura":" سري جاياواردنابورا ","St. Petersburg":"سانت بترسبورغ-","Stockholm":"ستوكهولم","Sydney":"سيدني","Taipei":"تايبيه","Tallinn":"تالين","Tashkent":" طشقند","Tbilisi":"تبيليسي","Tehran":"طهران","Tijuana":"تيخوانا","Tokelau Is.":"توكيلاو","Tokyo":"طوكيو","UTC":"التوقيت العالمي","Ulaan Bataar":"أولان باتور","Ulaanbaatar":"أولان باتور","Urumqi":"أورومتشي","Vienna":" فيينا","Vilnius":" فيلنيوس","Vladivostok":"فلاديفوستوك","Volgograd":"فولغوغراد","Warsaw":"وارسو","Wellington":"ويلينغتون","West Central Africa":"غرب ووسط أفريقيا","Yakutsk":" ياكوتسك ","Yerevan":"يريفان","Zagreb":" زغرب","Zurich":"زيوريخ"}}'));
I18n.translations.de = I18n.extend((I18n.translations.de || {}), JSON.parse('{"action":{"action":"Aktionen","action_device":"Bitte richten Sie Zielgeräte ein！","action_input":"Aktionsname darf nicht leer sein！","action_name":"Aktionsname","activate":"Aktivieren","add_action":"Aktion hinzufügen","add_action_type":"Bitte geben Sie den Aktionsnamen ein:","add_favorite":"Zu Verknüpfungen hinzufügen?","change_action":"Aktivieren Sie diese Aktion:","current_action":"Aktuelle Aktion","delete_action":"Möchten Sie diese Aktion wirklich entfernen?","favorite":"LIEBLINGSMASSNAHMEN","remove_favorite":"Von Verknüpfungen entfernen"},"automation":{"automation_type":"Automatisierungstyp","by_sunrise":"Bei Sonnenaufgang / Sonnenuntergang","city":"Stadt","current_located_city":"Aktuell gelegene Stadt","current_location":"Aktueller Standort","do":"Tun","if":"Wenn","locating":"Ortung ...","new_automation":"Neue Automatisierung","only_app":"Nur Unterstützung in der Presen App","relocate":"Umziehen","select_conditions":"Bedingungen auswählen","select_location":"Ort auswählen","select_target_type":"Wählen Sie den Zieltyp","sunrise":"Sonnenaufgang","sunset":"Sonnenuntergang"},"dashboard":{"change_pin":"PIN ändern","enter_pin":"Geben Sie Ihre Controller-PIN ein","set_pin":"Controller-PIN einrichten","show_password":"Passwort anzeigen"},"date":{"abbr_day_names":["So","Mo","Di","Mi","Do","Fr","Sa"],"abbr_month_names":[null,"Jan","Feb","Mär","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],"day_names":["Sonntag","Montag","Dienstag","Mittwoch","Donnerstag","Freitag","Samstag"],"formats":{"default":"%d.%m.%Y","long":"%e. %B %Y","short":"%e. %b"},"month_names":[null,"Januar","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember"],"order":["day","month","year"]},"datetime":{"distance_in_words":{"about_x_hours":{"one":"etwa eine Stunde","other":"etwa %{count} Stunden"},"about_x_months":{"one":"etwa ein Monat","other":"etwa %{count} Monate"},"about_x_years":{"one":"etwa ein Jahr","other":"etwa %{count} Jahre"},"almost_x_years":{"one":"fast ein Jahr","other":"fast %{count} Jahre"},"half_a_minute":"eine halbe Minute","less_than_x_minutes":{"one":"weniger als eine Minute","other":"weniger als %{count} Minuten"},"less_than_x_seconds":{"one":"weniger als eine Sekunde","other":"weniger als %{count} Sekunden"},"over_x_years":{"one":"mehr als ein Jahr","other":"mehr als %{count} Jahre"},"x_days":{"one":"ein Tag","other":"%{count} Tage"},"x_minutes":{"one":"eine Minute","other":"%{count} Minuten"},"x_months":{"one":"ein Monat","other":"%{count} Monate"},"x_seconds":{"one":"eine Sekunde","other":"%{count} Sekunden"},"x_years":{"one":"ein Jahr","other":"%{count} Jahr"}},"prompts":{"day":"Tag","hour":"Stunden","minute":"Minute","month":"Monat","second":"Sekunde","year":"Jahr"}},"device":{"FLiRS":"FLIRS Gerät","battery":"Batterie","battery_operated":"Batteriebetriebene Fernbedienung","camera":"IP Kamera","camera_image":"IP Kamera Bildlink","camera_image_desp":"Bitte geben Sie die IP-Kamera-Screenshot-URL ein. Wenn diese URL eine Authentifizierung für den Zugriff benötigt, müssen Sie auch den Benutzernamen und das Passwort in dieser URL angeben. Möglicherweise müssen Sie dazu das Handbuch Ihrer IP-Kamera heranziehen.","camera_name":"IP-Kameraname","camera_name_error":"Der Name der IP-Kamera darf nicht leer sein","camera_screenshot_url_error":"Der IP-Kamera-Bildlink darf nicht leer sein","camera_video":"IP Kamera Videolink","camera_video_desp":"Bitte geben Sie die Video-Stream-URL Ihrer IP-Kamera ein und wählen Sie das richtige Videoformat. Wenn diese URL eine Authentifizierung für den Zugriff benötigt, müssen Sie auch den Benutzernamen und das Passwort in dieser URL angeben. Möglicherweise müssen Sie dazu das Handbuch Ihrer IP-Kamera heranziehen.","camera_video_url_error":"Der Video-Link der IP-Kamera darf nicht leer sein","change_color":"Farbe ändern","cloud_add_camera":"Fügen Sie eine IP-Kamera hinzu, fügen Sie bitte den Controller hinzu","conf_apply_battery":"Wir müssen die Batterie Geräteeinstellungen aufwachen aktivieren wurden geändert","delay":"verspätet","delete_field_device":"Als ausgefallenes Gerät entfernen","delete_type":"Welche Art von Gerät Sie löschen möchten？","device":"Ausrüstung","device_active":"Gerät ist aktiv","device_dead":"Gerät ist tot","device_information":"Geräteinformationen","device_interviewed":"Gerät wird interviewt","device_not_interviewed":"Gerät ist nicht vollständig interviewt","device_operating":"Gerät arbeitet","device_sleeping":"Gerät schläft","edit_device":"Ändern Sie den Gerätenamen","force_interview":"Force Interview","full":"alle","interview":"Interview","is_awake":"Ist wach","is_sleeping":"Schläft","main":"Haupt angetrieben","mark_failed":"Markiert als gescheitert","mark_failed_alert":"Die Knotensteuerung überprüft, ob die klaren, dann wird es als Versagen gekennzeichnet. Dieser Vorgang dauert etwa 30 Sekunden. Dann wird, wenn nötig, können Sie die Funktion „Löschen“ verwenden, um Knoten zu löschen.","mark_failed_delete":"Sind Sie sicher, dass Sie das Gerät löschen? Um einen Knoten aus dem Netzwerk-Controller zu entfernen, ohne die Notwendigkeit für weitere Maßnahmen. Dieser Vorgang kann eine Minute dauern.","no_device_name":"Der Gerätename darf nicht leer sein！","off_b":"geschlossen","on_b":"geöffnet","operating":"Betriebs","operating_time":"Betriebszeit","play":"Spielen","remove_camera_device_hint":"Um ein IP-Kamera-Gerät zu löschen, gehen Sie auf die Gerätedetailseite (klicken Sie auf den Gerätenamen, um es zu öffnen), um es zu löschen","remove_directly":"Direkt entfernen","seconds":"zweite","set_to":"einrichten","setting":"einrichten","setup_device":"Bitte setzen Sie das Zielgerät","value":"Wert","value_changed_to":"Ändern Sie den Wert","value_not_stored_indevice":"Aber nicht im Gerätespeicher","when":"wann"},"errors":{"format":"%{attribute} %{message}","messages":{"accepted":"muss akzeptiert werden","blank":"muss ausgefüllt werden","confirmation":"stimmt nicht mit %{attribute} überein","empty":"muss ausgefüllt werden","equal_to":"muss genau %{count} sein","even":"muss gerade sein","exclusion":"ist nicht verfügbar","greater_than":"muss größer als %{count} sein","greater_than_or_equal_to":"muss größer oder gleich %{count} sein","inclusion":"ist kein gültiger Wert","invalid":"ist nicht gültig","less_than":"muss kleiner als %{count} sein","less_than_or_equal_to":"muss kleiner oder gleich %{count} sein","model_invalid":"Gültigkeitsprüfung ist fehlgeschlagen: %{errors}","not_a_number":"ist keine Zahl","not_an_integer":"muss ganzzahlig sein","odd":"muss ungerade sein","other_than":"darf nicht gleich %{count} sein","present":"darf nicht ausgefüllt werden","required":"muss ausgefüllt werden","taken":"ist bereits vergeben","too_long":{"one":"ist zu lang (mehr als 1 Zeichen)","other":"ist zu lang (mehr als %{count} Zeichen)"},"too_short":{"one":"ist zu kurz (weniger als 1 Zeichen)","other":"ist zu kurz (weniger als %{count} Zeichen)"},"wrong_length":{"one":"hat die falsche Länge (muss genau 1 Zeichen haben)","other":"hat die falsche Länge (muss genau %{count} Zeichen haben)"}},"template":{"body":"Bitte überprüfen Sie die folgenden Felder:","header":{"one":"Konnte %{model} nicht speichern: ein Fehler.","other":"Konnte %{model} nicht speichern: %{count} Fehler."}}},"event":{"inner":"innere","my_device":"Mein Gerät","my_room":"Mein Zimmer","open_app":"Öffne App","outer":"äußere"},"global":{"account":"Kontoeinstellungen","account_setting":"Kontoeinstellungen","activate_action":"Diese Aktion ausführen?","activate_scene":"Diese Szene aktivieren?","activate_sure":"Bist du sicher, dass du das machst?","add_device_s2_desp":"Bitte geben Sie die ersten fünf Ziffern des Geräteschlüssels ein. Den Geräteschlüssel finden Sie im Gerätehandbuch.","add_device_s2_title":"Bitte drücken Sie die Tasten auf dem Gerät, die dem Gerätehandbuch folgen, um das Gerät hinzuzufügen.","add_device_wait":"Bitte warten","add_smart_desp":"Bitte geben Sie den vollständigen Geräteschlüssel ein, der 40-stellig sein sollte. Den Geräteschlüssel finden Sie im Gerätehandbuch.","add_zigbee_smart_desp":"Bitte geben Sie die MAC-Adresse des Geräts und den Installationscode ein. Beide sollten auf dem Wrapper Ihres Geräts oder in dessen Bedienungsanleitung abgedruckt sein. MAC-Adressen haben 16 Zeichen und der Installationscode ist 36.","advanced_setting":"Erweiterte Einstellungen","agree":"Ich stimme dem zu","alexa_link_error":"Link mit Alexa fehlgeschlagen, bitte nochmal versuchen!","alexa_link_success":"Sie haben eine Verknüpfung mit Alexa hergestellt und die Fertigkeit Presen Smart Home aktiviert.","all":"Alle","auto":"Automatisierung","away":"Weg","by_device":"Durch Geräteänderungen","centigrade":"Celsius","change_datacenter":"Controller-Datencenter ändern","choose_alarm_ringtone":"Um zu wählen, dass ein Alarmton auf Ihrem Controller abgespielt wird, müssen Sie die Haupttaste auf dem Controller drücken, um den Alarm auszuschalten","choose_ringtone":"Klingelton auswählen","click_help":"Überprüfen Sie die Hilfeinformationen","click_to_find_more":"Klicken Sie hier, um mehr zu erfahren","cloud_required":"Sie müssen eine Verbindung zur Internetverbindung herstellen, um diese Funktion nutzen zu können. Zurzeit verbinden Sie sich mit dem Ethernet.","community":"Gemeinschaft","connecting":"Anschließen...","controller_setting":"CONTROLLER-EINSTELLUNGEN","copyright_policy":"Urheberrechtspolitik","dashboard":"Instrumententafel","datacenter_setting":"Datencentereinstellungen","default":"Standard","device_failed":"Dieses Gerät reagiert nicht, Sie können versuchen, es aus- und wieder einzuschalten, es ein- oder auszuschalten oder es auszulösen, um zu testen, ob es fehlerhaft ist oder die Batterie schwach ist. Wenn es längere Zeit nicht reagiert, sollten Sie versuchen, es zurückzusetzen oder zu entfernen.","device_not_support":"Dieses Attribut unterstützt das nicht!","download_alert":"An Ihre registrierte Mailbox gesendet","dsk_error":"Geräteschlüssel ist ungültig","dsk_input":"Der Geräteschlüssel muss 40-stellig sein","enter_your":"Bitte geben Sie Ihre","error_desp":"Möglicherweise haben wir ein Problem. Bitte versuchen Sie es erneut.","error_title":"Entschuldigung, Verbindung fehlgeschlagen","every_day":"Jeden Tag","fahrenheit":"Fahrenheit","grpc_link_error":"Verbindung fehlgeschlagen, bitte aktualisieren, um es erneut zu versuchen","grpc_unlink":"Verbindung fehlgeschlagen, Verbindung wird wiederhergestellt ...","home":"Zuhause","i_agree":"Sie müssen dem zustimmen","input_key":"Geräteschlüssel eingeben","input_ssid":"Bitte SSID eingeben oder WLAN verbinden","input_wifi_password":"Bitte geben Sie das WLAN-Passwort ein!","ins_key":"Schlüssel installieren","is_alive":"IS Alive","link_with_alexa":"Verlinke mit Alexa","login_demo":"Mit Demokonto anmelden","logout":"Ausloggen","my_smarthome":"Mein intelligentes Zuhause","need_login":"Bitte loggen Sie sich jetzt ein","no_data":"keine Daten!","off_sn":"Steuern und organisieren Sie Geräte in einem Controller.","on_sn":"Wenn diese Option aktiviert ist, können Sie alle Geräte der Controller gleichzeitig verwalten und alle Geräte können zusammenarbeiten. Sie können beispielsweise eine Aktion erstellen, um mehrere Geräte mit verschiedenen Controllern zu steuern.","open_license":"Open Source Lizenz","open_wifi":"Open WIFI","or":"oder","privacy":"Nutzungsbedingungen","privacy_statement":"Datenschutzerklärung","quick":"Tastenkombinationen","release_log":"Update Log","remove_device_title":"Bitte drücken Sie die Tasten auf dem Gerät entsprechend der Bedienungsanleitung, um das Gerät zu entfernen.","s2_add":"S2 Add","s2_device":"S2 Device","scan_key":"Geräteschlüssel scannen","scene":"Szene","scene_delete":"Die Standardszene kann nicht entfernt werden","select_controller":"Controller auswählen","select_this_controller":"Zu diesem Controller wechseln","service":"Dienstleistungen","setting_wifi":"WIFI einrichten","setting_wifi_desp":"WIFI 5G und WIFI in Restaurant und Flughafen erfordern keine webbasierte Authentifizierung","sleep":"Schlaf","sn_global":"globaler Modus","sn_normal":"single controller mode","sn_setting":"Controller Management","ssid":"SSID","target":"Ziel","temperature_setting":"Temperatureinstellungen","terms_of_service":"Nutzungsbedingungen","time_out":"Betriebszeitlimit, bitte erneut versuchen!","timezone_setting":"Zeitzoneneinstellungen","type":"Art","use":"benutzen","user_role_desp":"Sie können den Zugriff des aktuellen Controllers für andere Personen freigeben. Geben Sie einfach die E-Mail-Adresse ein, die Sie hier freigeben möchten. Die Personen, mit denen Sie geteilt haben, können nur Ihre Daten verwenden, z. B. Ihre Geräte steuern, Szenen aktivieren, Aktionen ausführen, aber keine Änderungen an Ihren Daten vornehmen, z. B. Geräte hinzufügen oder entfernen, Szenen erstellen oder bearbeiten usw., und natürlich auch diese Der Controller kann nicht zurückgesetzt oder neu gestartet werden. Nachdem die E-Mail hinzugefügt wurde, muss der andere Benutzer die App neu starten, damit sie wirksam wird.","vacation":"Urlaub","wifi_connection":"WIFI-Verbindung","wifi_link_btn":"Die Netzwerk-LED leuchtet","wifi_link_desp":"Bitte drücken Sie die obere Taste auf dem Controller, bis die Netzwerk-LED immer leuchtet, um fortzufahren.","wifi_password":"WIFI Passwort","wired_connection":"Kabelverbindung","zwave_delete_db":"Wenn dieses Gerät verloren geht oder nicht funktioniert, können Sie es direkt entfernen. Es wird wieder angezeigt, wenn es weiterhin mit Ihrem Controller kommuniziert.","zwave_delete_field":"Entfernen Sie dieses ausgefallene Gerät. Dieser Vorgang schlägt möglicherweise fehl, wenn dieses Gerät nicht als ausgefallenes Gerät erkannt wird."},"guard":{"enter_guard_mode":"Aktivieren Sie den Schutzmodus","exit_guard_mode":"Beenden Sie den Guard-Modus","guard_h1":"Presen kann Ihnen helfen, Ihr Zuhause zu bewachen, wenn Sie nicht da sind","guard_h2":"SMART ALERTS","guard_h2_deps":"Wenn Sie in den Schutzmodus wechseln, kann Presen Ihnen Benachrichtigungen senden, wenn der Bewegungssensor oder Rauchsensor ausgelöst oder die Tür geöffnet wird.","guard_h3":"WEGBELEUCHTUNG","guard_h3_deps":"Presen kann Ihr Licht automatisch ein- und ausschalten, damit es so aussieht, als wäre jemand zu Hause, wenn Sie nicht da sind.","guard_mode":"Schutzmodus"},"help":{"cookie_hint":"Diese Website verwendet Cookies, um Ihr Benutzererlebnis zu verbessern. Detaillierte Informationen über die Verwendung von Cookies auf dieser Website wird in unserem %s zur Verfügung gestellt. Durch die Nutzung dieser Website Stimmen Sie der Verwendung von Cookies. %s","privacy":"Datenschutz","yes_i_agree":"Ja, ich bin einverstanden"},"helpers":{"select":{"prompt":"Bitte wählen"},"submit":{"create":"%{model} erstellen","submit":"%{model} speichern","update":"%{model} aktualisieren"}},"home":{"433_device":"433 Geräte","a":"mehr","add_433_device":"433M Gerät hinzufügen","add_433_node_type_doorlock":"Doorlock","add_433_node_type_other":"Others","add_433_node_type_pir":"PIR","add_433_node_type_smoke_sensor":"Smoke Sensor","add_433_node_type_water_sensor":"Water Sensor","add_433_waiting_for_ports":"Überprüfen Sie die serielle Schnittstelle, die der Controller unterstützt, stellen Sie bitte sicher, dass Ihr Controller Serial Port USB-Geräte angeschlossen ist ...","add_controller":"In-Controller","add_device":"Gerät hinzufügen","add_device_433_progress_error":"Kein 433M Port auf Ihrem Controller gefunden","add_device_433_progress_loading":"Überprüfung","add_device_type":"Welche Art von Gerät Sie hinzufügen möchten？","add_qiock":"Add to Quick Control","add_success":"Success！","cancel":"annulliert","card_1":"Controller Betriebszustand","card_2":"Controller Laufzeit","card_3":"Anzahl der Geräte","cellphone":"Telefonnummer","change_controller":"Ändern-Controller","confirm":"bestätigen","controller":"Regler","controller_information":"Controller-Informationen","controller_input":"GeräteVName","controller_sn_enter":"Bitte geben Sie die Seriennummer des Controllers","controller_state":"Controller-Status","current_controller":"Der Stromregler","day":"Tag","delete_success":"Erfolgreich gelöscht!","detect_device":"Prüfgeräte","device":"Ausrüstung","device_input":"Gerätename","device_management":"Device Management","device_name":"Gerätename","edit":"bearbeiten","edit_controller":"Ändern Sie den Namen des Controllers","edit_device":"Ändern Sie den Gerätenamen","edit_success":"Ändern Erfolg！","email":"Briefkasten","error":"Fehler","event":"Nachrichten","failed":"Der Vorgang ist fehlgeschlagen！","favorite":"LIEBLINGSSZENEN","forget_password":"Passwort vergessen","good_afternoon":"guten Tag","good_evening":"Guten Abend","good_morning":"Guten Morgen","have_notify":"Sie haben das Ereignis deaktiviert","have_version":"Es gibt eine neue Version！","home":"Zuhause","indoor_humidity":"Raumfeuchtigkeit","indoor_temp":"Innentemp","ip":"IP","language":"Sprache","language_for_mobile":"Sprache","local_network":"LAN","login_name":"E-Mail oder Telefonnummer","name":"Name","network_issue":"Netzwerkproblem","network_issue_desp":"Ihr Netzwerk ist zu langsam, Sie können versuchen, die App neu zu starten oder zu einem schnellen Netzwerk zu wechseln","next":"Der nächste Schritt","no_controller":"Keine Controller-Daten verfügbar. Sie müssen zuerst einen Controller in der Present App hinzufügen, den Controller einschalten und mit dem Internet verbinden. Warten Sie dann einige Sekunden, um den Bildschirm zu aktualisieren.","no_controller_input":"Der Controller-Name darf nicht leer sein！","no_device":"Keine Inneninformationen","not_device_input":"Der Gerätename darf nicht leer sein！","not_user":"Benutzer existiert nicht！","off_line":"offline","on_line":"Online","password":"Kennwort","profile":"Konfiguration","qiock_control":"Quick control","recent_event":"Die jüngsten Ereignisse","recently_devices":"Kürzlich Geräte","refresh":"Aktualisieren Sie den Controller","remove_433_device_hint":"Sie können auf die Geräte-Detail-Seite (klicken Sie auf Gerätename), um 433M Gerät zu entfernen.","remove_btn":"löschen","remove_device":"Gerät löschen","remove_qiock":"Remove from Quick Control","room":"Zimmer","routine":"Routinen","save":"einreichen","scene":"Szene","select_433_device":"Die folgenden Geräte wurden erkannt, bitte wählen Sie das Gerät, das Sie hinzufügen möchten","select_433_node_type":"Bitte wählen Sie den Gerätetyp für dieses Gerät aus","select_433_port":"Bitte wählen Sie das serielle Gerät aus, das Sie hinzufügen möchten","select_433_protocol":"Bitte wählen Sie das Protokoll aus, das Ihr Gerät unterstützt","setting":"einrichten","setting_btn":"einrichten","sign":"einloggen","sign_out":"verlassen","sign_up":"Anmeldung","sn":"Seriennummer","sn_error":"Seriennummer ist nicht vorhanden！","sn_used":"Die Seriennummer ist bereits im Einsatz！","state":"Zustand","success":"Erfolg","successful":"Der erfolgreiche Betrieb!","time_zone":"Wähle deine Zeitzone","token":"Verifizierungscode","trigger":"Gestänge","try_again":"Versuchen Sie es später noch einmal！","user":"Benutzer","user_has":"Benutzer bereits vorhanden ist！","user_user":"Sie können nicht Ihre eigenen hinzufügen！","version":"Die Versionsnummer","voice":"Stimme","vs_update":"Ob sofort aktualisiert werden soll","warning_message":"Prompt","wide_network":"WAN","z_device":"Z-Wave-Geräte"},"index":{"433m":"Z-Wave und normale RF","433m_desp":"Presen unterstützt die bekannten RF-Sensoren (433M, 345M, 319M) einiger namhafter Hersteller neben Z-Wave-Geräten und sie können zusammenarbeiten, um Ihre Heimsicherheit und Automatisierung zu verbessern","about":"Über uns","about_about":"auf","about_desp":"Presen ist ein einfacheres, intelligenteres Smart Home. Wir konzentrieren Hunderte von vertrauenswürdigen Marken auf eine mobile App, sodass Sie Ihr Smart Home ganz einfach überwachen, steuern und automatisieren können, wo auch immer Sie sind.","about_desp_three":"Jetzt stell dir vor, wenn du dir das nicht vorstellen musst. Da es Presen verwendet, ist es bereits verfügbar. Dinge wie Lichter, Türen, Klimaanlagen und Schalter können jetzt besser für Sie arbeiten, so dass Sie sich sicherer, kontrollierter, effizienter und glücklicher fühlen. Mit der Presen-Technologie werden unbegrenzte Möglichkeiten von Ihren eigenen Bedürfnissen und Ihrer Kreativität bestimmt.","about_desp_two":"Denk nur darüber nach, wenn es weiß, wann du nicht nach Hause gehen kannst und wie du beruhigt sein kannst. Stellen Sie sich vor, sie wüssten immer, was Sie brauchen und wann es gebraucht wird. Stellen Sie sich vor, wenn es wüsste, dass Sie sich besser kennen als Sie.","about_name":"Über Presen","about_our":"Wir konzentrieren uns auf das Smart Home","about_title":"Was ist Presen?","alerting":"24/7 Warnung","alerting_desp":"Die Funktionen für Presen-Ereignisse und Benachrichtigungen können problemlos angepasst werden, um Notfall- und Benachrichtigungsnachrichten an Ihr Mobiltelefon zu senden","ali_desp":"Sprachsteuerung über 天猫精灵 serielle Geräte, steuern Sie Ihr Zuhause nur mit Ihrer Stimme","app":"APP","automation":"EINFACHE AUTOMATISIERUNG","automation_desp":"Mit der Unterstützung von Triggern und Szenen können Sie viele Arten von Szenarien nach Ihren Bedürfnissen erstellen","build":"Erstellen Sie die besten Smart Home","change":"Veränderung","contact_address":"Anschrift","for_live":"Das leben in","get_app":"Erhalten Sie kostenlose App","has_account":"Sie haben bereits ein Konto？","help_you":"Helfen Sie alle nach Hause verwalten","home":"Zuhause","no_account":"Kein Account？","or_sign_up":"oder registrieren","or_sign_up_type":"Oder nutzen Sie Ihre","our":"uns","our_app":"Meine App","product":"Feature","product_desp":"Auf der Straße, von Ihrem Büro oder während Sie an einem tropischen Strand liegen, können Sie Ihr Zuhause überall kontrollieren. Sie können die vollständige Kontrolle über Ihr Haus vom Bildschirm Ihres PC, Smartphone oder einem anderen internetfähigen Gerät aus haben.","product_desp_1":"lange Einführung","product_name":"Komplette Haussteuerung und Automatisierung","product_name_1":"Das erste Produkt","product_title":"Komplette Automatisierungsplattform einschließlich lokaler Controller, Cloud-Server, Benutzer- und Admin-Anwendungen, die nahtlos zusammenarbeiten.","product_title_1":"kurze Einführung","remote":"FERNBEDIENUNG","remote_desp":"Steuern Sie Ihr Heim von Ihrem Smartphone oder einem anderen internetfähigen Gerät, auch ohne Internet in Ihrem LAN, mit sicherer Internetverbindung","routines":"ROUTINEN","routines_desp":"Mit Presen Routines können Sie ganz einfach Ihre eigenen Automatisierungsszenarien erstellen","security":"BESSERE SICHERHEIT","security_desp":"Presen bietet Ihnen eine neue Generation des Haussicherheitsschutzes","sharing":"HOME TEILEN","sharing_desp":"Alle Ihre Familienmitglieder können ihre eigenen Konten haben, um Ihr Haus mit unterschiedlichem Zugang zu kontrollieren","voice":"STIMMENKONTROLLE","voice_desp":"Sprachsteuerung über Amazon Echo serielle Geräte, steuern Sie Ihr Zuhause nur mit Ihrer Stimme","we_well":"Wir werden eine intelligente Ihr Zuhause schaffen","work_with":"Presen Werke"},"mongoid":{"attributes":{"c/action":{"name":"Aktionsname"},"c/node":{"c_display_name":"Gerätename"},"c/scene":{"name":"Name der Szene"}},"errors":{"format":"%{attribute} %{message}","messages":{"accepted":"muss akzeptiert werden","blank":"muss ausgefüllt werden","confirmation":"stimmt nicht mit %{attribute} überein","empty":"muss ausgefüllt werden","equal_to":"muss genau %{count} sein","even":"muss gerade sein","exclusion":"ist nicht verfügbar","greater_than":"muss größer als %{count} sein","greater_than_or_equal_to":"muss größer oder gleich %{count} sein","inclusion":"ist kein gültiger Wert","invalid":"ist nicht gültig","less_than":"muss kleiner als %{count} sein","less_than_or_equal_to":"muss kleiner oder gleich %{count} sein","model_invalid":"Gültigkeitsprüfung ist fehlgeschlagen: %{errors}","not_a_number":"ist keine Zahl","not_an_integer":"muss ganzzahlig sein","odd":"muss ungerade sein","other_than":"darf nicht gleich %{count} sein","present":"darf nicht ausgefüllt werden","record_invalid":"Gültigkeitsprüfung ist fehlgeschlagen: %{errors}","required":"muss ausgefüllt werden","restrict_dependent_destroy":{"has_many":"Datensatz kann nicht gelöscht werden, da abhängige %{record} existieren.","has_one":"Datensatz kann nicht gelöscht werden, da ein abhängiger %{record}-Datensatz existiert."},"taken":"ist bereits vergeben","too_long":{"one":"ist zu lang (mehr als 1 Zeichen)","other":"ist zu lang (mehr als %{count} Zeichen)"},"too_short":{"one":"ist zu kurz (weniger als 1 Zeichen)","other":"ist zu kurz (weniger als %{count} Zeichen)"},"wrong_length":{"one":"hat die falsche Länge (muss genau 1 Zeichen haben)","other":"hat die falsche Länge (muss genau %{count} Zeichen haben)"}}}},"number":{"currency":{"format":{"delimiter":".","format":"%n %u","precision":2,"separator":",","significant":false,"strip_insignificant_zeros":false,"unit":"€"}},"format":{"delimiter":".","precision":2,"separator":",","significant":false,"strip_insignificant_zeros":false},"human":{"decimal_units":{"format":"%n %u","units":{"billion":{"one":"Milliarde","other":"Milliarden"},"million":{"one":"Million","other":"Millionen"},"quadrillion":{"one":"Billiarde","other":"Billiarden"},"thousand":"Tausend","trillion":{"one":"Billion","other":"Billionen"},"unit":""}},"format":{"delimiter":"","precision":3,"significant":true,"strip_insignificant_zeros":true},"storage_units":{"format":"%n %u","units":{"byte":{"one":"Byte","other":"Bytes"},"gb":"GB","kb":"KB","mb":"MB","tb":"TB"}}},"percentage":{"format":{"delimiter":"","format":"%n %"}},"precision":{"format":{"delimiter":""}}},"room":{"add_room":"Zimmer hinzufügen","desp":"EREIGNIS bedeutet, dass in diesem Raum in den letzten 24 Stunden Änderungen am Gerät vorgenommen wurden.","device_in_room":"Die Ausstattung im Zimmer","event":"Ereignis","filter_room":"Filtern nach Zimmer","normal":"normal","room_name":"Zimmer Name","room_no_device":"Kein Gerät ausgewählt！","room_no_name":"Raumname darf nicht leer sein！","room_switch_hint":"In diesem Raum alle Swtich Steuergeräte","setting_room":"Einstellung der Raum"},"routine":{"add":"Fügen Sie eine Routine hinzu","address":"Adresse","cond_action":"Schalten Sie eine Aktion ein","cond_device":"Schalten Sie die Geräte ein","cond_scene":"Schalten Sie eine Szene ein","edit":"Routine bearbeiten","get_to":"Ankunft","is_cyclic":"Wiederholen Sie die","last":"Zurück","leave":"Verlassen","one_cond":"Einmal","please_address":"Bitte wählen Sie den GPS-Standort","please_date":"Bitte wählen Sie das Datum aus","please_leave":"Bitte wählen Sie Ankommen oder Verlassen","please_time":"Bitte wählen Sie die Zeit aus","please_to":"Bitte wählen Sie, was Sie einschalten möchten","please_type":"Bitte wählen Sie den Routinetyp","routine":"Routinen","routine_cond":"Wann","routine_desp":"Routine kann eine Szene oder Geräte Basis auf der Zeit oder GPS-Standort einschalten (müssen mit Presen App arbeiten)","routine_location":"GPS-Standort","routine_type":"Routinetyp","run_multiple":"Wiederholen Sie die","run_once":"Einmal","select_action":"Wählen Sie die Aktion aus","select_device":"Wählen Sie Geräte aus","select_scene":"Wählen Sie die Szene aus","time":"Zeit","to_location":"Durch GPS-Standort","to_time":"Zum Zeitpunkt","ui":"Diese Funktion muss mit der Presen App und Presen Cloud funktionieren. Bitte besuchen Sie https://www.presensmarthome.com"},"scene":{"activate":"Aktivierung","add_scene":"In Szene","add_scene_type":"Bitte geben Sie den Namen der Szene:","change_scene":"Ob die Szene wechseln","current_scene":"aktuelle Szene","delete_scene":"löschen Szenen","scene_device":"Bitte fügen Sie ein Gerät aus！","scene_input":"Szenenname darf nicht leer sein！","scene_name":"Szenenname"},"services":{"alexa":"Steuern Sie Ihre Geräte und Automatisierungen mit Amazon Alexa-fähigen Geräten.","alexa_desp":"Wie z. B. \\"Alexa, turn on home scene\\".","ali":"Steuern Sie Ihre Geräte und Automatisierungen verwenden TmallGenie.","ali_desp":"Wie \\"天猫精灵, 打开卧室的灯 \\".","google":"Steuern Sie Ihre Geräte und Automatisierungen mit von Google Assistant unterstützten Geräten.","google_desp":"Wie \\"Hey Google, activate home\\".","ifttt":"Arbeiten Sie mit IFTTT, um eine Verbindung mit mehr 3. Diensten herzustellen.","ifttt_desp":"Z. B. wenn der Bewegungssensor ausgelöst wird, erhalten Sie einen Anruf."},"session":{"60_s":"Wiederholen Sie nach 60 Sekunden！","cellphone_input":"Bitte geben Sie Ihre Handynummer ein!","change_password":"Kennwort ändern","confir_input":"Bitte bestätigen Sie Ihr Passwort aus！","email":"Die Mailbox existiert bereits!","email_input":"Bitte geben Sie die E-Mail！","email_or_phone":"E-Mail oder Telefonnummer","email_type":"E-Mail-Format ist nicht korrekt！","get_token":"Verifizierungscode","is_cellphone":"Telefonnummer existiert bereits！","is_email":" E-Mail ist bereits vorhanden！","login_name_input":"Bitte greifen Sie auf die Mailbox oder Telefonnummer","new_input":"Bitte geben Sie ein neues Passwort！","new_password":"neues Passwort","no_user_name":"Benutzername darf nicht leer sein！","not_eamil":"Mailbox existiert nicht！","not_email_or_phone":"Bitte besuchen Sie Ihre E-Mail oder Telefonnummer","not_phone":"Die Telefonnummer existiert nicht！","not_token":"Bestätigungs-Code ist nicht korrekt！","old_error":"Das alte Passwort ist nicht korrekt！","old_input":"Bitte geben Sie Ihr altes Passwort ein！","old_password":"altes Kennwort","password_confirmation":"Kennwort bestätigen","password_input":"Bitte geben Sie Ihr Passwort","password_length":"Das Passwort darf nicht weniger als sechs！","please_enter_password":"Bitte geben Sie Ihr Passwort ein","register":"Anmeldung","reset_password":"Kennwort zurücksetzen","send":"senden","send_eamil":"Senden Sie Bestätigungscode an die E-Mail","send_token":"Ein Token wurde an Ihre Mailbox gesendet. Bitte überprüfen Sie Ihre Mailbox.","signup_success_login":"Konto erfolgreich erstellt, bitte melden Sie sich jetzt an","token_input":"Ein Bestätigungscode wurde an Ihre E-Mail gesendet. Bitte überprüfen Sie den unten stehenden Bestätigungscode und geben Sie ihn ein, um fortzufahren!","token_send":"Verification Code erfolgreich gesendet！","two_password":"Die beiden Passwörter stimmen nicht überein！","user_name":"Benutzername","wrong_name":"Benutzername oder Passwort falsch！"},"setting":{"add":"Hinzugefügt","advanced_setting":"Erweiterte Einstellungen","backup":"Backup-Controller","backup_desp":"Controller Backup","backup_message":"Bitte schalte das Gerät nicht aus Backup-Controller。。。","base":"Grundeinstellungen","change_notification":"Änderungsbenachrichtigung","check_device":"Bitte Änderungsbenachrichtigungen wählen Sie die Attribute der Geräte, die Sie nicht empfangen möchten.","check_device_for_urgent":"Nur empfangen Sie dringende Benachrichtigungen, wenn Geräte Änderungen nach.","check_scene":"Erhalten Sie keine Benachrichtigungen, wenn Ihr Haus in den folgenden aufgegebenen Szenen ist keine Szene ausgewählt, es bedeutet alle Gerät Änderungsbenachrichtigungen werden nicht egal welche Szene senden, es ist.","check_scene_for_urgent":"Nur Benachrichtigungen Sie dringende, wenn in den folgenden Szenen.","close_account":"Konto schließen","close_desp":"Benutzer können sich nach dem Schließen des Accounts nicht einloggen! Wenn Sie Ihr Konto erneut öffnen müssen, kontaktieren Sie uns bitte!","download_data":"Daten herunterladen","download_desp":"Laden Sie persönliche Daten herunter","end_time":"Endzeit","friday":"Freitag","inform":"Beachten","local_siren":"Lokale Sirene","login_close_desp":"Das Konto wurde geschlossen. Um ein Konto zu eröffnen, kontaktieren Sie uns bitte!","monday":"Montag","not_end_time":"Die Endzeit ist eingestellt auf:","not_start_time":"Wenn Sie keine Zeit benötigen, stellen Sie die Startzeit auf:","notify":"Benachrichtigungscenter","notify_by_device":"Geräte-Benachrichtigung","notify_by_time":"Bitte nicht stören","notify_desp":"Globale Notification-Steuerelement, werden Sie nicht benachrichtigt, wenn es ausgeschaltet ist.","notify_device_noti_desp":"Sie erhalten keine Änderungsbenachrichtigungen für die folgenden Geräte","notify_dnd_desp":"Die Rüstzeit Sie keine Benachrichtigungen erhalten möchten","notify_time":"Zeit","notify_urgent_device":"Dringende Mitteilung","notify_urgent_noti_desp":"Sie erhalten eine spezielle Meldung, wenn diese Geräte ändern. (Dringende Benachrichtigung wird die Dot nicht stören und Geräte Benachrichtigung Einstellungen ignorieren)","place_end_time":"Bitte wählen Sie die Endzeit aus!","place_select_period":"Bitte wählen Sie Zeit！","place_start_time":"Bitte wählen Sie die Startzeit!","property":"Eigenschaften","record":"Wiederherstellen Controller","record_desp":"Wiederherstellen Controller","record_error":"Der Controller Wiederherstellung fehlgeschlagen! Bitte versuchen Sie es erneut！","record_message":"Bitte schalte das Gerät nicht aus Steuer wiederherzustellen。。。","record_success":"Der Controller Reduktion Erfolg！","reset":"Reset-Controller","reset_desp":"Der Controller wird zurückgesetzt auf die Werkseinstellungen. Hinweis: Alle Zimmer und andere Daten werden gelöscht!","reset_message":"Der Controller werden alle Daten gelöscht werden。。。。","reset_success":"Ihr Controller wurde erfolgreich zurückgesetzt.","reset_title":"Controller zurücksetzen?","restart":"Neustart-Controller","restart_desp":"Starten Sie den Controller, Neustart dauert ca. 5 Minuten! Nach dem Neustart abgeschlossen ist, bitte aktualisieren Sie die Seite","restart_message":"Sie sicher, dass Sie den Controller zurücksetzen？","restart_title":"Controller neu starten?","saturday":"Samstag","select_date":"Datum auswählen","select_device_scene":"Bitte Gerät oder Szene auswählen！","select_period":"Auswahlzeitraum","start_end":"Die Endzeit ist kleiner als die Startzeit!","starting_time":"Anfangszeit","sunday":"Sonntag","thursday":"Donnerstag","tuesday":"Dienstag","update_desp":"Der Upgrade-Prozess nicht ausschalten, das Power-Upgrade dauert etwa 5 bis 7 Minuten! Nachdem die Aktualisierung abgeschlossen ist, bitte aktualisieren Sie die Seite.","update_desp_one":"Eine neue Version: ","update_failed_in_progress":"Es wird bereits eine Aktualisierung durchgeführt.","update_failed_other":"Update fehlgeschlagen, bitte später erneut versuchen!","update_failed_up_to_date":"Es ist keine Aktualisierung erforderlich, Sie sind auf dem neuesten Stand.","update_online":"Upgrade-Controller","update_title":"Aktualisieren ...","updated":"Das aktuelle System auf dem neuesten Stand, kein Upgrade!","updating":"Wir bekamen ein Upgrade sind。。。","upgrade_desp":"Der Controller wird aktualisiert. Schalten Sie den Controller nicht aus. Der Vorgang dauert ca. 5 Minuten. Dies hängt von Ihrer Netzwerkgeschwindigkeit ab. Der Controller wird nach Abschluss neu gestartet und Sie erhalten eine Benachrichtigung.","upgrade_success_to":"Der Controller wurde auf aktualisiert.","wednesday":"Mittwoch"},"spec":{"access_control":"Zugangskontrolle","air_flow":"Luftstrom","air_temperature":"Druck","alarm":"Alarm","alarm_s":"Induktionsalarm","angle_position":"Winkel","app_url":"Auslöser URL","appliance":"Geräte","atmospheric_pressure":"Druck","auto":"Automatisch","aux":"Hilfsausrüstung","auxiliary":"Auxiliar","barometric_pressure":"Druck","battery":"Batterie","burglar":"Diebstahlsicherung","camera":"IP-Kamera","clock":"Uhr","closed":"Geschlossen","co":"Kohlenmonoxid","co2":"Kohlendioxid","co2_alarm":"Kohlendioxid-Warnung","co2_level":"Kohlendioxidgehalt","co_alarm":"Kohlenmonoxid-Warnung","co_level":"Kohlenmonoxid","color_switch":"Farbschalter","contact_sensor":"Kontaktsensor","cool":"Kühlung","current":"Wasserfluss","dew_point":"Taupunkt","dimmer":"Dimmer","direction":"Richtung","distance":"Entfernung","door_lock":"Türschloss","door_window":"Türen und Fenster","electrical_conductivity":"Leitfähigkeit","electrical_resistivity":"Widerstandskoeffizient","emergency":"Notfall","false":"Nicht ausgelöst","fire_sensor":"Feuersensor","first":"Der erste","flood":"Überflutet","freeze":"Kühlung","frequency":"Häufigkeit","full":"Alle","general":"Allgemein","general_purpose":"Universelle Erkennung","general_purpose_alarm":"Allgemeiner Alarm","general_purpose_value":"Allzweckstatus","general_trigger":"Gemeinsames Ereignis","glass_break":"Defekter Sensor","going_to_low_battery":"gehen die Batterie aus","heat":"Heizung","heat_alarm":"Wärmealarm","humidity":"Luftfeuchtigkeit","is":"Status ist","key_fob":"Schlüsselanhänger","keypad":"Tastatur","loudness":"Volumen","low":"Niedrig","luminance":"Licht","luminiscence":"Licht","main_powered":"Hauptantrieb","meter":"Messgerät","moisture":"Niederschlag","motion":"Bewegungserkennung","motor":"Motor","multi_switch":"Mehrkanalschalter","n_a":"N/A","no_b":"Normal","normal":"Normal","not_used":"Unbekannt","off_b":"Aus","on_b":"Öffnen","open":"Öffnen","power":"Macht","power_management":"Energieverwaltung","quake":"Beben","rain_rate":"Regengeschwindigkeit","relative_humidity":"Relative Luftfeuchtigkeit","remote_ontrol":"Fernbedienung","reserved":"Halte es","resume":"Fortsetzen","return_first_alarm_on_supported_list":"Erste Warnung","rotation":"Drehen","security_repeater":"Sicherheits-Repeater","seismic_intensity":"Intensität des Erdbebens","seismic_magnitude":"Größe","sensor":"Sensor","smoke":"Rauch","smoke_alarm":"Rauchwarnung","smoke_test":"Rauchtest","soil_temperature":"Öltemperatur","solar_radiation":"Sonnenstrahlung","switch":"Schalter","switch_all":"Der ganze Schalter","switch_to":"wechseln zu","system":"System","tamper":"Sabotage","tank_capacity":"Kapazität","target_temperature":"Zieltemperatur","temperature":"Temperatur","thermostat":"Thermostat","thermostat_mode":"Klimamodus","thermostat_setpoint":"Stellen Sie die Temperatur ein","tide_level":"Gezeiten","tilt":"Gekippt","time":"Zeit","true":"Auslösung","ultraviolet":"Ultraviolett","velocity":"Geschwindigkeit","ventilation":"Lüftungsanlagen","vibration":"Vibration","voltage":"Spannung","water":"Wasser","water_leak":"Überflutet","water_leak_alarm":"Hochwasseralarm","water_temperature":"Wassertemperatur","weight":"Gewicht","yes_b":"Auslösung"},"support":{"array":{"last_word_connector":" und ","two_words_connector":" und ","words_connector":", "}},"time":{"am":"vormittags","formats":{"default":"%A, %d. %B %Y, %H:%M Uhr","long":"%A, %d. %B %Y, %H:%M Uhr","short":"%d. %B, %H:%M Uhr"},"pm":"nachmittags"},"timezones":{"Abu Dhabi":"Abu Dhabi","Adelaide":"Adelaide","Alaska":"Alaska","Almaty":"Almaty","American Samoa":"Amerikanisch-Samoa","Amsterdam":"Amsterdam","Arizona":"Arizona","Astana":"Astana","Athens":"Athen","Atlantic Time (Canada)":"Atlantic Time (Kanada)","Auckland":"Auckland","Azores":"Azoren","Baghdad":"Bagdad","Baku":"Baku","Bangkok":"Bangkok","Beijing":"Peking","Belgrade":"Belgrad","Berlin":"Berlin","Bern":"Bern","Bogota":"Bogotá","Brasilia":"Brasília","Bratislava":"Bratislava","Brisbane":"Brisbane","Brussels":"Brüssel","Bucharest":"Bukarest","Budapest":"Budapest","Buenos Aires":"Buenos Aires","Cairo":"Kairo","Canberra":"Canberra","Cape Verde Is.":"Kapverdische Inseln","Caracas":"Caracas","Casablanca":"Casablanca","Central America":"Zentralamerika","Central Time (US & Canada)":"Central Time (USA u. Kanada)","Chatham Is.":"Chatham-Inseln","Chennai":"Chennai","Chihuahua":"Chihuahua","Chongqing":"Chongqing","Copenhagen":"Kopenhagen","Darwin":"Darwin","Dhaka":"Dhaka","Dublin":"Dublin","Eastern Time (US & Canada)":"Eastern Time (USA u. Kanada)","Edinburgh":"Edinburgh","Ekaterinburg":"Jekaterinburg","Fiji":"Fidschi","Georgetown":"Georgetown","Greenland":"Grönland","Guadalajara":"Guadalajara","Guam":"Guam","Hanoi":"Hanoi","Harare":"Harare","Hawaii":"Hawaii","Helsinki":"Helsinki","Hobart":"Hobart","Hong Kong":"Hongkong","Indiana (East)":"Indiana (Ost)","International Date Line West":"Internationale Datumsgrenze","Irkutsk":"Irkutsk","Islamabad":"Islamabad","Istanbul":"Istanbul","Jakarta":"Jakarta","Jerusalem":"Jerusalem","Kabul":"Kabul","Kaliningrad":"Kaliningrad","Kamchatka":"Kamtschatka","Karachi":"Karatschi","Kathmandu":"Kathmandu","Kolkata":"Kalkutta","Krasnoyarsk":"Krasnojarsk","Kuala Lumpur":"Kuala Lumpur","Kuwait":"Kuwait","Kyiv":"Kiew","La Paz":"La Paz","Lima":"Lima","Lisbon":"Lissabon","Ljubljana":"Ljubljana","London":"London","Madrid":"Madrid","Magadan":"Magadan","Marshall Is.":"Marshallinseln","Mazatlan":"Mazatlán","Melbourne":"Melbourne","Mexico City":"Mexiko-Stadt","Mid-Atlantic":"Mittelatlantik","Midway Island":"Midwayinseln","Minsk":"Minsk","Monrovia":"Monrovia","Monterrey":"Monterrey","Montevideo":"Montevideo","Moscow":"Moskau","Mountain Time (US & Canada)":"Mountain Time (USA u. Kanada)","Mumbai":"Mumbai","Muscat":"Maskat","Nairobi":"Nairobi","New Caledonia":"Neukaledonien","New Delhi":"Neu-Delhi","Newfoundland":"Neufundland","Novosibirsk":"Nowosibirsk","Nuku\'alofa":"Nuku\'alofa","Osaka":"Osaka","Pacific Time (US & Canada)":"Pacific Time (USA u. Kanada)","Paris":"Paris","Perth":"Perth","Port Moresby":"Port Moresby","Prague":"Prag","Pretoria":"Pretoria","Quito":"Quito","Rangoon":"Rangun","Riga":"Riga","Riyadh":"Riad","Rome":"Rom","Samara":"Samara","Samoa":"Samoa","Santiago":"Santiago","Sapporo":"Sapporo","Sarajevo":"Sarajevo","Saskatchewan":"Saskatchewan","Seoul":"Seoul","Singapore":"Singapur","Skopje":"Skopje","Sofia":"Sofia","Solomon Is.":"Salomonen","Srednekolymsk":"Srednekolymsk","Sri Jayawardenepura":"Sri Jayawardenepura","St. Petersburg":"Sankt Petersburg","Stockholm":"Stockholm","Sydney":"Sydney","Taipei":"Taipei","Tallinn":"Tallinn","Tashkent":"Taschkent","Tbilisi":"Tiflis","Tehran":"Teheran","Tijuana":"Tijuana","Tokelau Is.":"Tokelau","Tokyo":"Tokio","UTC":"UTC","Ulaan Bataar":"Ulaanbaatar","Ulaanbaatar":"Ulaanbaatar","Urumqi":"Ürümqi","Vienna":"Wien","Vilnius":"Vilnius","Vladivostok":"Wladiwostok","Volgograd":"Wolgograd","Warsaw":"Warschau","Wellington":"Wellington","West Central Africa":"West-Zentralafrika","Yakutsk":"Jakutsk","Yerevan":"Jerewan","Zagreb":"Zagreb","Zurich":"Zürich"},"trigger":{"action":"Gehen Sie wie folgt","add_trigger":"Erstellen Verknüpfung","auto":"Auto","conditional_device":"Bitte Bedingungen für Geräte","delete_trigger":"löschen Verknüpfung","manual":"Handbuch","no_trigger_input":"Set Verknüpfungsname","scene_condition":"Bitte wählen Sie die Szene aus, in der die Automatisierung wirksam werden soll. Wenn keines ausgewählt ist, funktioniert diese Automatisierung in jeder Szene","trigger_input":"Verknüpfungsname","virtual_device_help":"Sie können eine URL eingeben, wenn dieser Trigger passiert, wird diese URL aufgerufen. Auto: Diese URL wird automatisch aufgerufen, wenn das Ereignis eintritt. Handbuch: Diese URL wird manuell vom Benutzer aufgerufen, wie zum Beispiel das Öffnen der Webcam.","when":"Wenn die folgenden Bedingungen auftreten"},"user":{"action":"Betriebs-","add_user":"Benutzer hinzufügen","add_users":"Bitte geben Sie E-Mail oder Telefonnummer","admin":"Administrator","country":"Land / Region","de":"Deutschland","delete_user":"Entfernen Sie den Benutzer","edit":"Berechtigungen bearbeiten","en":"Vereinigte Staaten","es":"Spanien","fr":"Frankreich","not_country":"Land / Region darf nicht leer sein","not_user_input":"Benutzer können nicht leer sein！","not_user_room":"Die Zimmer können nicht leer sein！","pt":"Portugal","ru":"Russland","user":"Benutzer","zh":"China"},"wifi":{"add_desp":"Bearbeitung, bitte einen Moment.","add_error":"Wenn dies fehlgeschlagen ist, drücken Sie bitte die Taste unten, um es erneut zu versuchen.","add_success":"Ihr Controller wurde erfolgreich hinzugefügt.","add_title":"WIFI wurde erfolgreich konfiguriert.","next_wifi_desp":"Während des nächsten Schritts fordert Ihr Mobiltelefon möglicherweise auf, zum Mobilfunknetz zu wechseln oder das WLAN ohne Internetverbindung weiter zu verwenden. Bitte wählen Sie, das WLAN weiterhin zu verwenden.","reload_desp":"Ihr Controller wird bald neu gestartet. Es dauert bis zu 3 Minuten, bis der Neustart abgeschlossen ist.","retry":"wiederholen","setting_desp":"Einen Moment bitte, Controller WIFI einrichten","setting_error":"Bitte gehen Sie zu Ihrer Handy-Systemeinstellung und verbinden Sie Ihr WIFI mit dem Zugangspunkt PRESEN_CONTROLLER. Drücken Sie dann die Taste unten, um es erneut zu versuchen!","wifi_link_error":"Es kann keine Verbindung zu Ihrem Controller hergestellt werden, und das WLAN-Setup ist fehlgeschlagen. Sie können den Controller neu starten und in den WLAN-Setup-Modus wechseln. Versuchen Sie es dann erneut."}}'));
I18n.translations.en = I18n.extend((I18n.translations.en || {}), JSON.parse('{"action":{"action":"Actions","action_device":"Please choose target","action_input":"Action name can not be empty！","action_name":"Action name","activate":"Activate","add_action":"Add Action","add_action_type":"Please enter the action name:","add_favorite":"Add to favorites","change_action":"Activate this action:","current_action":"Current Action","delete_action":"Are you sure to remove this action?","execute":"Execute","favorite":"Favorite actions","remove_favorite":"Remove from favorites"},"automation":{"add_automation":"Add Automation","automation_type":"Automation type","by_sunrise":"Sunrise/Sunset","city":"City","current_located_city":"Current location","current_location":"Current location","do":"Target","everyday":"Everyday","gps_auto_desp":"Need to get your GPS location in background mode to make the GPS automations work, we only use your GPS for this purpose, nothing else, please make sure you have granted GPS Always use access.","if":"When","locating":"Locating...","only_app":"Only support in Presen App","relocate":"Relocate","select_conditions":"And","select_location":"Select location","select_target_type":"Do","select_two_controller":"Only support devices under the same controller.","sunrise":"Sunrise","sunset":"Sunset","workday":"Workday"},"dashboard":{"change_pin":"Change PIN","enter_pin":"Enter your controller PIN","no_initial_password":"You need to login using the owner\'s account of this controller to set up the PIN first.","pin_error":"PIN is incorrect.","set_pin":"Setup your controller PIN","show_password":"Show password"},"date":{"abbr_day_names":["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],"abbr_month_names":[null,"Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],"day_names":["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],"formats":{"default":"%Y-%m-%d","long":"%B %d, %Y","short":"%b %d"},"month_names":[null,"January","February","March","April","May","June","July","August","September","October","November","December"],"order":["year","month","day"]},"datetime":{"distance_in_words":{"about_x_hours":{"one":"about 1 hour","other":"about %{count} hours"},"about_x_months":{"one":"about 1 month","other":"about %{count} months"},"about_x_years":{"one":"about 1 year","other":"about %{count} years"},"almost_x_years":{"one":"almost 1 year","other":"almost %{count} years"},"half_a_minute":"half a minute","less_than_x_minutes":{"one":"less than a minute","other":"less than %{count} minutes"},"less_than_x_seconds":{"one":"less than 1 second","other":"less than %{count} seconds"},"over_x_years":{"one":"over 1 year","other":"over %{count} years"},"x_days":{"one":"1 day","other":"%{count} days"},"x_minutes":{"one":"1 minute","other":"%{count} minutes"},"x_months":{"one":"1 month","other":"%{count} months"},"x_seconds":{"one":"1 second","other":"%{count} seconds"},"x_years":{"one":"1 year","other":"%{count} years"}},"prompts":{"day":"Day","hour":"Hour","minute":"Minute","month":"Month","second":"Seconds","year":"Year"}},"device":{"FLiRS":"FLiRS Device","add_dsk_desp":"Support multiple devices to add at the same time, please scan the code or manually input DSK, click Next","add_these_devices":"Add these devices","added_devices":"Added devices","battery":"Battery","battery_operated":"Battery operated remote control","camera":"IP Camera","camera_image":"IP camera screenshot URL","camera_image_desp":"Please input your IP camera\'s screenshot capturing URL. If this URL need authentication to access, you also need to specify the username and password in this URL, you may need to refer your IP camera\'s manual to get this.","camera_name":"IP camera name","camera_name_error":"IP camera name cannot be empty","camera_screenshot_url_error":"IP camera image link cannot be empty","camera_video":"IP camera video stream URL","camera_video_desp":"Please input your IP camera\'s video stream URL and select the correct video format. If this URL need authentication to access, you also need to specify the username and password in this URL, you may need to refer your IP camera\'s manual to get this.","camera_video_url_error":"IP camera video link cannot be empty","change_color":"Change Color","cloud_add_camera":"You can only add IP camera in the controller management page","delay":"Delay","delete_field_device":"Remove as failed device","delete_type":"Please select the device type you want to remove:","device":"Device","device_active":"Device is active","device_dead":"Device is dead","device_information":"Device Info","device_interviewed":"Device is interviewed","device_name_error":"Device name has already been taken","device_not_interviewed":"Device is not fully interviewed","device_operating":"Device is operating","device_sleeping":"Device is sleeping","device_state":"Device online/offline","device_state_desp":"Receive notifications when this device is online/offline","dsk_next":"Please scan the code first or enter DSK manually","edit_device":"Change device name","force_interview":"Force Interview","full":"FULL","interview":"Interview","is_awake":"Is Awake","is_sleeping":"Is Sleeping","main":"Main Powered","manufacturer":"Manufacturer","mark_failed":"Mark as Failed","mark_failed_alert":"This device has been marked as failed, which will take up to 30 seconds to take effect, and then you can REMOVE this device if you want. Are you sure to do this?","mark_failed_delete":"This will remove the device from the netwotk directly, which will take up to one minute to take effect! Are you sure to do this?","notify_battery":"Low battery","notify_battery_desp":"Receive notifications when runs on low battery","notify_desp":"Receive notificatios when device\'s states change, you can specify the scenes that you want to take effect. If your controller supports Local siren feature, you can also turn on it","off_b":"OFF","on_b":"ON","operating":"Operating","operating_time":"Operating Time","play":"Play","playback":"Playback","power_source_bat_percent_remaining":"Battery Percent Remaining","power_source_description":"Power Source Description","power_source_order":"Power Source Order","power_source_status":{"active":"Active","name":"Power Source Status","standby":"Standby","unavailable":"Unavailable","unspecified":"Unspecified"},"product_name":"Product Name","remove_camera_device_hint":"To delete an IP camera device, go to the device details page (click on the device name to enter) to delete","remove_directly":"Remove directly","seconds":"Second","security":"Security","set_to":"Setting","setting":"Setting","setup_device":"Please select the target","smart_add_desp":"设备自动添加，等待时间较长,预计需要{{time}}分钟，可以离开此页面，但是请保持添加的设备通电","thread_network_channel":"Thread Channel","thread_network_data_version":"Thread Data Version","thread_network_extended_pan_id":"Thread ExtendedPanId","thread_network_leader_router_id":"Thread Leader RouterId","thread_network_mesh_local_prefix":"Thread Mesh Local Prefix","thread_network_name":"Thread Network Name","thread_network_pan_id":"Thread PanId","thread_network_partition_id":"Thread PartitionId","thread_network_routing_role":{"end_device":"EndDevice","leader":"Leader","name":"Thread Routing Role","reed":"REED","router":"Router","sleepy_end_device":"SleepyEndDevice","unassigned":"Unassigned","unspecified":"Unspecified"},"thread_network_stable_data_version":"Thread Stable Data Version","thread_network_weighting":"Thread Leader Weight","value":"Value","when":"When"},"errors":{"connection_refused":"Oops! Failed to connect to the Web Console middleware.\\nPlease make sure a rails development server is running.\\n","format":"%{attribute} %{message}","messages":{"accepted":"must be accepted","blank":"can\'t be blank","confirmation":"doesn\'t match %{attribute}","empty":"can\'t be empty","equal_to":"must be equal to %{count}","even":"must be even","exclusion":"is reserved","greater_than":"must be greater than %{count}","greater_than_or_equal_to":"must be greater than or equal to %{count}","inclusion":"is not included in the list","invalid":"is invalid","less_than":"must be less than %{count}","less_than_or_equal_to":"must be less than or equal to %{count}","model_invalid":"Validation failed: %{errors}","not_a_number":"is not a number","not_an_integer":"must be an integer","odd":"must be odd","other_than":"must be other than %{count}","present":"must be blank","required":"must exist","taken":"has already been taken","too_long":{"one":"is too long (maximum is 1 character)","other":"is too long (maximum is %{count} characters)"},"too_short":{"one":"is too short (minimum is 1 character)","other":"is too short (minimum is %{count} characters)"},"wrong_length":{"one":"is the wrong length (should be 1 character)","other":"is the wrong length (should be %{count} characters)"}},"template":{"body":"There were problems with the following fields:","header":{"one":"1 error prohibited this %{model} from being saved","other":"%{count} errors prohibited this %{model} from being saved"}},"unacceptable_request":"A supported version is expected in the Accept header.\\n","unavailable_session":"Session %{id} is no longer available in memory.\\n\\nIf you happen to run on a multi-process server (like Unicorn or Puma) the process\\nthis request hit doesn\'t store %{id} in memory. Consider turning the number of\\nprocesses/workers to one (1) or using a different server in development.\\n"},"event":{"download_video":"Download Video","filter_events":"Filter events","inner":"inner","my_device":"Devices","my_room":"Rooms","open_app":"View Photo","outer":"outer","save_image":"Save Image"},"global":{"account":"ACCOUNT SETTINGS","account_setting":"Account Settings","action":"Action","activate_action":"Run this Action?","activate_scene":"Activate this Scene?","activate_sure":"Are you sure to do this?","add_device_s2_desp":"Please input the first five digits of the device key, you can refer to the deivce manual to get the device key","add_device_s2_title":"Please press buttons on the device following the device manual to add the device","add_device_special_desp":"Make sure that the device has been excluded and/or reset to factory default, you can only add one device at a time. Once it added your device, you need to start the Add Device procedure again to add another.","add_device_wait":"Please wait","add_smart_desp":"Please input the full device key, which should be 40 digits, you can refer to the deivce manual to get the device key","add_zigbee_smart_desp":"Please input the device EUI64 address and the Install Code, both of them should be printed on your device\'s instruction manual. EUI64 addres has 16 characters and Install Code is 36.","advanced_setting":"Advanced Settings","advanced_success":"The modification is successful, and the setting information will be updated in the next data report.","agree":"I agree to the","alexa_link_error":"Link with Alexa failed, please try again!","alexa_link_success":"You have Linked with Alexa and enabled the Presen Smart Home skill successfully.","all":"All","auto":"Automations","away":"Away","by_device":"Device","centigrade":"Centigrade","change_datacenter":"Change controller datacenter","choose_alarm_ringtone":"To choose this will play an alarm sound on your controller, you need to press the main button on the controller to dismiss the alarm","choose_ringtone":"Choose ringtone","click_help":"Check help info","click_to_find_more":"Click to find more","cloud_required":"You need to connect to internet connection to use this feature, currently you are connecting to ethernet.","community":"Community","connecting":"Connecting...","controller_offline":"Controller is Offline, please check your network and try again!","controller_setting":"CONTROLLER SETTINGS","copyright_policy":"Copyright Policy","ctl_update":"There\'s new updates available for your controller, you can go to [Settings -> Controller Management ->  choose your current controller -> Upgrade Controller] to upgrade, or click the [OK] button to go to upgrade screen directly.","dark":"Dark","dashboard":"Dashboard","datacenter_setting":"Datacenter Settings","default":"Default","delete_desp":"Please input \\"YES\\" to continue","device_count":"Devices","device_failed":"This device is unresponsive, you can try to turn it off and on, or trigger it to test if it\'s malfunctional or running out of battery. If it keeps unresponsive for a long time, you should try to reset it or remove it.","device_not_support":"This attribute doesn\'t support this!","download_alert":"Sent to your registered mailbox","dsk_error":"Device key is invalid","dsk_input":"The device key must be 40 digits","enter_your":"Please enter your","error_desp":"We may have a problem, please try again!","error_title":"Sorry, connection failed","every_day":"Every Day","fahrenheit":"Fahrenheit","force_login":"For security reason, you have been logged out, please sign in again!","ftt":"433M","grpc_link_error":"Connect failed, please refresh to try again","grpc_unlink":"Connect failed, trying to reconnect...","has_subscribed":"You are already subscribed","home":"Home","i_agree":"You need to agree the","input_key":"Input device key","input_ssid":"Please enter SSID or connect wifi","input_wifi_password":"Please enter the wifi password!","ins_key":"Install Code","is_alive":"Is Alive","light":"Light","link_with_alexa":"Link with Alexa","location_desp":"Use your location to make GPS automation work","login_demo":"Login With Demo Account","logout":"Logout","matter_thread":"Matter Thread","matter_wifi":"Matter Wifi","my_smarthome":"MY SMART HOME","need_login":"Please login now","no_advanced_setting":"No advanced settings obtained.","no_data":"No data!","not_ready":"failed to start","off_sn":"Control and organize devices within one controller.","on_sn":"When turned on, you can manage all the devices of the controllers at the same time, and all the devices can work together, for instance, you can create an Action to control multilple devices with different controllers.","open_license":"Open source license","open_wifi":"Open WIFI","or":"OR","please_add_controller":"Please add controller","please_add_home":"Please add home first","privacy":"Terms of Service","privacy_statement":"Privacy Statement","quick":"FAVORITES","release_log":"Release Log","remove_device_title":"Please press buttons on the device following the device manual to remove the device","reset_controller_offline":"Your controller is Offline, you need to manually press and hold the Reset button on your controller to force your controller back to factory state.","s2_add":"Add S2","s2_device":"Device S2","scan_key":"Scan device key","scene":"Scene","scene_delete":"Default scene can not be removed","select_add_controller":"Select the controller you want to add device","select_controller":"select controller","select_this_controller":"Switch to this controller","select_this_home":"Switch to this home","service":"Services","setting_wifi":"Setup WIFI","setting_wifi_desp":"WIFI 5G and WIFI in restaurant and airport required web based authentication is not supported","sleep":"Sleep","sn_global":"Global Mode","sn_normal":"Single Controller Mode","sn_setting":"Controller Management","ssid":"SSID","subscribed_success":"You subscribed successfully","successful":"Thank you! Form submitted successfully!","system_with":"Follow system","target":"Target","temperature_setting":"Temperature Settings","terms_of_service":"Terms Of Service","theme":"Theme","time_out":"Operation timeout, please try again!","timezone_setting":"Timezone Settings","type":"Type","use":"Use","user_role_desp":"You can share the access of current controller with other people, just input the email you want to share. The people you shared with can only use what you have created, such as control your devices, activate scenes, run actions, but can\'t make changes of your data, such as adding or removing devices, creating or editing scenes etc, and of course they can\'t reset or reboot your controller. After the email was added, the other user need to restart the app to take effect.","vacation":"Vacation","wifi_connection":"WIFI connection","wifi_link_btn":"The network led is on","wifi_link_desp":"Please press the top button on the controller until the network led is always on to continue.","wifi_password":"WIFI password","wired_connection":"Wired connection","zigbee":"Zigbee","zwave":"Z-Wave","zwave_delete_db":"If this device is lost or malfunctional you can remove this device directly. It will appear again if it continues to communicate with your controller","zwave_delete_field":"Remove this failed device, this operation may fail if this device is not recognized as a failed device"},"guard":{"enter_guard_mode":"Enter Guard Mode","exit_guard_mode":"Exit Guard Mode","guard_device_desp":"Choose lights/switches you want to turn on.","guard_gps_desp":"Please choose your home location, your switches/lights will be automatically turned on around this location\'s sunset time, and then turned off after 3 - 5 hours.","guard_h1":"Presen can help you guard your home when you are away","guard_h2":"Smart Alerts","guard_h2_deps":"When you switch to Guard Mode, Presen can send you notifications if the motion sensor or smoke sensor is triggered, or door is opened.","guard_h3":"Away Lighting","guard_h3_deps":"Presen can turn on and off your lights automatically to make it look like someone is home when you are away.","guard_mode":"Guard Mode"},"help":{"cookie_hint":"This site uses cookies in order to improve your user experience. Detailed information on the use of cookies on this website is provided in our %s. By using this website, you consent to the use of cookies. %s","privacy":"Privacy","yes_i_agree":"Yes, I agree"},"helpers":{"page_entries_info":{"entry":{"one":"entry","other":"entries","zero":"entries"},"more_pages":{"display_entries":"Displaying %{entry_name} <b>%{first}&nbsp;-&nbsp;%{last}</b> of <b>%{total}</b> in total"},"one_page":{"display_entries":{"one":"Displaying <b>1</b> %{entry_name}","other":"Displaying <b>all %{count}</b> %{entry_name}","zero":"No %{entry_name} found"}}},"select":{"prompt":"Please select"},"submit":{"create":"Create %{model}","submit":"Save %{model}","update":"Update %{model}"}},"home":{"433_device":"433M Device","a":"","add_433_device":"Add 433M device","add_433_node_type_doorlock":"Doorlock","add_433_node_type_other":"Others","add_433_node_type_pir":"PIR","add_433_node_type_smoke_sensor":"Smoke Sensor","add_433_node_type_water_sensor":"Water Sensor","add_433_waiting_for_ports":"Checking the Serial Port that the Controller support, please make sure your Controller have Serial Port USB devices plugged in...","add_and_remove_device":"Add or remove device","add_controller":"Add controller","add_device":"Add device","add_device_433_progress_error":"Not found any 433M port on your controller","add_device_433_progress_loading":"Checking","add_device_fail":"Failed to add device, please try again!","add_device_success":"Add device successfully!","add_device_type":"Please choose the protocol which your device support:","add_home":"Add home","add_qiock":"Add to Quick Control","add_success":"Added successfully!","cancel":"Cancel","card_1":"State","card_2":"Uptime","card_3":"Devices","cellphone":"Cellphone","change_controller":"Change controller","click_try_again":"TRY AGAIN","close_hint":"Close","company_location":"Ningbo, China","complete_automation_desp":"We support four kinds of automations to make your home smarter, which can also work together, you can customize kinds of options to meet different scenarios.","complete_automation_title":"Complete and handy automation support","confirm":"Confirm","controller":"Controller","controller_frequency":"Controller Frequency","controller_information":"Controller Info","controller_input":"Controller name","controller_sn_enter":"Please enter your controller SN.","controller_state":"CONTROLLER STATE","current_controller":"Current controller","customers_title":"Smart Home Customers","datacenter_desp":"Datacenters cover Asia, North America and Europe","day":"Day(s)","delete_success":"Remove successfully!","detect_device":"Detecting device...","device":"Devices","device_input":"Device name","device_management":"System","device_name":"Device name","edit":"Edit","edit_controller":"Change Controller Name","edit_device":"Change device name","edit_home":"Edit Home","edit_success":"Changes have been saved!","email":"Email","error":"Error","event":"Events","failed":"Operation failed!","favorite":"Favorite scenes","feedback":"Feedback","forget_password":"Forgot password?","global_datacenter":"Global Datacenter","good_afternoon":"Good afternoon","good_evening":"Good evening","good_morning":"Good morning","have_notify":"You have unchecked events","have_version":"New version available!","home":"Home","home_has_not_user":"The home has not this user","home_name":"Home name","how_to_setup":"How to Setup","indoor_humidity":"Indoor humidity","indoor_temp":"Indoor temperature","ip":"IP address","ipc_sn_enter":"Please enter your IP Camera SN.","lan_ip":"LAN IP","language":"Language","language_for_mobile":"Language","local_network":"LAN","locale_and_time_zone_config":"Language and Time Zone","location_title":"Location","login_name":"Email","memory":"Memory","name":"Name","nb_sn_enter":"Please enter your device SN.","need_set_locale_and_time_zone":"Please set language and time zone first","network_issue":"Network issue","network_issue_desp":"Network issue, please try again!","next":"Next","no_controller":"No controller data available, you need to add a controller first, and power on your controller and connect it to the internet.","no_controller_input":"Controller name can not be empty!","no_device":"No indoor information","not_device_input":"Device name can not be empty!","not_user":"User does not exist!","off_line":"Offline","on_line":"Online","password":"Password","presen_slogon_2":"Build Your Smart Home With <span style=\'style-color\'>Ease</span>","profile":"Profile","qiock_control":"Quick control","recent_event":"LATEST EVENTS","recently_devices":"Recently used devices","refresh":"Refresh Controller","refund_money":"Refund","remove_433_device_hint":"You can go to the device detail page (clicking device name) to remove 433M device.","remove_btn":"Remove","remove_device":"Remove device","remove_device_fail":"Failed to remove device, please try again!","remove_device_success":"Remove device successfully!","remove_qiock":"Remove from Quick Control","remove_zwave":"Remove Z-Wave Device","reset":"Reset","room":"Rooms","routine":"Routines","save":"Save","scan":"Scan code","scene":"Scenes","select_433_device":"The following devices have been detected, please select the device that you want to add","select_433_device_error":"Please select device","select_433_node_type":"Please select the device type for this device","select_433_port":"Please select the serial device that you want to add","select_433_protocol":"Please select the protocol that your device support","setting":"Settings","setting_btn":"Setting","setup_desp":"Presen App will help you install and setup your Presen Controller easily.","setup_step_1":"Plug In Presen Controller","setup_step_1_desp":"Plug in your Presen Controler, and prepare your WiFi SSID and password.","setup_step_2":"Download Presen App","setup_step_2_desp":"Download Presen App at your iOS/Android device, register an account, then add your controller.","setup_step_3":"Add Devices","setup_step_3_desp":"Add yours devices, or setup a scene, an action, or bind with your Alexa/Google Assistant, then it\'s done.","setup_title":"Simple to <span style=\'style-color\'>Setup & Use</span> Presen Controller","sign":"Login","sign_out":"Logout","sign_up":"Sign Up","skip":"Skip","skip_vs":"Skip this version","sn":"SN.","sn_error":"Controller SN. is not valid!","sn_used":"The controller SN. has been used!","state":"State","storage":"Storage","submit":"Submit","subscribe":"Subscribe Newsletter","subscribe_desp":"Subscribe our product news, industry news and promotion.","success":"Done","successful":"Operation has completed successfully!","system":"System","time_zone":"Choose your time zone","token":"OTP","trigger":"Triggers","try_again":"Please try again later!","user":"Users","user_has":"User has already existed!","user_user":"You can not add your self!","version":"Version","voice":"Voice","vs_update":"update now?","warning_message":"Hint","we_are_serving":"We Are Serving","we_provide_ease":"We Will Provide <span class=\'style-color\'>Convenience</span> In Your Home","wide_network":"WAN","wifi":"Wi-Fi","your_home":"Your Home","z_device":"Z-WAVE Device"},"index":{"433m":"Compatibility","433m_desp":"Presen support Z-Wave, Zigbee, regular RF(433M, 345M, 319M) and Tuya, they can work together to build your home security and automation.","about":"About Us","about_about":"About","about_desp":"Presen is a simpler, smarter smart home. We focus hundreds of trusted brands on one mobile app, so you can easily monitor, control and automate your smart home wherever you are.","about_desp_three":"Now imagine if you don\'t need to imagine. Because it uses Presen, it is already available. Things such as lights, doors, air conditioners, and switches can now work better for you, making you feel safer, more controlled, more efficient, and happier. With Presen technology, unlimited possibilities will be driven by your own needs and creativity.","about_desp_two":"Just think about it if it knows when you can\'t go home and how can you rest assured. Imagine if they always knew what you needed and when it was needed. Imagine if it knew you knew yourself better than you.","about_name":"About Presen","about_title":"What is Presen","alerting":"Notification & Alert","alerting_desp":"You can create different rules to receive different kinds of notifications base on your needs.","app":"APP","app_desp":"Discover how to use the Presen App","automation":"Automation","automation_desp":"With Triggers, Actions and Scenes support, you can create many kinds of scenarios according to your needs.","build":"Build a Smart Home","camera_desp":"Support IP security camera and works with other kinds of smart devices.","camera_title":"IP Camera Support","change":"Change","community_desp":"Share and discover how other people are using Presen","complete_title":"Complete smart home automation system","configuration_samples":"Configuration Samples","contact_address":"Contact Us","d_auto_desp":"You can create automations based on devices change events, such as when the motion sensor is triggered, turn on the hallway light.","device_automation":"Devices Automation","documentation":"Help Center","five_desp":"Requires internet connection for registration, devices adding/removing and automation creation.","for_live":"For Life","get_app":"App","gps_automation":"GPS Automation","gps_desp_1":"You can create an automation to activate the Home Scene automatically when you are back home.","gps_desp_2":"We need to track your GPS location on your cellphone to make this feature to work.","has_account":"have an account?","help_you":"Manage everything in your home","home":"Home","local_desp":"When you are at home, you can still control your devices and use automation features even when the internet connection is offline.","local_title":"Offline Control","no_account":"have no account?","or_sign_up":"Create an account now","or_sign_up_type":"or signup with your ","our":"Us","our_app":"App","presen_support":"Presen Support","product":"Products","product_desp":"On the road, from your office or while lying on a tropical beach, you can control your home anywhere. You can have complete control of your house from the screen of your PC, smartphone or any other internet-enabled device. ","product_name":"Complete home control and automation","product_title":"Complete automation platform including local controllers, cloud servers and App those all work together seamlessly.","remote_desp":"You can control your home from anywhere, even at work or on a vacation.","remote_loacl":"Remote and Offline Control","remote_title":"Remote Control","routines":"Automation","routines_desp":"With Automation, Scene and Action support, you can create kinds of usage cases according to your need.","security":"Security","security_desp":"Presen brings your data and home security to a new generation. All data tranfering between your home and cloud is encrypted.","sharing":"Home Sharing","sharing_desp":"All your family members can have their own accounts to control your home with different access.","start_guide":"Start Guide","sun_automation":"Sunrise/Sunset Automation","sun_desp":"You can control your devices, scenes and actions automatically when sunrise or sunset.","support":"Support","time_automation":"Time Automation","time_desp":"Create automation based on your routine.","voice":"Voice Control","voice_desp":"Support Amazon Alexa, Google Assistant, IFTTT and TmallGenie.","vs_eight":"With TmallGenie support","vs_five":"Works without internet & Offline Control","vs_four":"Cloud Control","vs_nine":"Guard Mode","vs_one":"Compatible with standard protocols","vs_seven":"Complete App features","vs_six":"Fast & local automations","vs_three":"With Google Assistant support","vs_title":"Compare with Other Home Automation Solutions","vs_two":"With Alexa support","we_well":"Make live simple","work_with":"Presen Works with"},"ipc":{"add_home":"Pin to Home","alarm":"Alarm settings","alarm_day":"Alarm day","alarm_no_week":"Not set","alarm_setting":"Alarm settings","alarm_time":"Alarm time","alarm_timeInterval":"Alarm minimal interval","all_day_video":"7/24 recording","animal":"Animal","brightness":"Brightness","camera_setting":"Settings","car":"Vehicle","cloud_play":"Cloud live","contrast":"Contrast","device_offine":"Device is Offline, please check the power and network connection.","factory":"Reset","factory_desp":"Continue to reset this camera to factory default? All camera settings will be lost and you need to reconfigure the WiFi of the camera after reset.","flip":"On","horizontal_flip":"Flip horizontal","human_setting":"Human tracking","human_switch":"Human tracking","human_tracking":"Human tracking","interval_time":"{{time}} min","ip_address":"IP address","ipc_alarm_setting":"IPC Do Not Disturb","ipc_alarm_type":"Type","ipc_header":"Live","ipc_title":"Security Cameras","ipc_video_null":"Clip has been staled or deleted.","last_time":"Updated at","local_network_error":"Make sure the camera and your cellphone are connected to the same WiFi network.","local_play":"LAN live","local_play_back":"LAN playback","mic_volume":"Mic volume","motion_area":"Tracking zone","motion_sensitivity":"Motion sensitivity","motion_setting":"Motion setting","motion_switch":"Motion tracking","motion_tracking":"Motion tracking","motion_type":"Tracking targets","motion_video":"Motion recording","network_info":"Network","night_auto":"Auto","night_black":"Gray","night_color":"Color","night_light":"Night light","night_mode":"Night vision mode","night_switch":"Fill light","no_flip":"Off","noise_3d":"3D noise reduction","operation_failed":"Operation failed, please try again later!","other":"Other","package":"Package","person":"Person","reboot":"Restart","reboot_desp":"Continue to restart this camera?","reset_defaults":"Set to default","reset_defaults_desp":"Are you sure to set all configurations ablove to default?","reset_defaults_success":"Set to default successfully","resolution":"Resolution","resolution_hd":"High","resolution_sd":"Standard","saturation":"Saturation","scene_condition":"Specify the scene you don\'t want to receive notifications. If no scene is selected, means all IPC alarm notifications will be send.","sd_reset":"Fromat SD card","sd_reset_desp":"Continue to format SD card? Everything on this card will be lost.","sd_setting":"SD recording type","sd_storage_remaining":"Remaining {{remaining}} GB","sd_switch":"SD recording","select_one":"Please select one at least.","sharpness":"Sharpness","speed_up_connection":"Speed-Up Connection","speed_up_connection_desp":"When is on, will try to connect to the camera using LAN to speed up the connection for live mode when you are on the same WiFi network with the camra, but it may result in unstable issues due to the network connectivity. When off, will always connect via cloud.","switch_all":"IPC alarm notification","switch_all_desp":"When off, all IPC alarm notifications will be disabled, but you can still check the alarm clips in the Events.","type":"Type","update":"Update","update_desp":"Continue to update this camera? make sure the camera is connected to the internet and power is on during update, will be restarted after update is done.","update_title":"Update camera","vertical_flip":"Flip vertical","video_setting":"Video settings","volume":"Speaker volume","wifi":"WiFi","wired":"Wired","zoom":"Zoom in"},"mongoid":{"attributes":{"c/action":{"name":"Action name"},"c/node":{"c_display_name":"Device name"},"c/scene":{"name":"Scene name"}},"errors":{"format":"%{attribute} %{message}","messages":{"accepted":"must be accepted","ambiguous_relationship":{"message":"Ambiguous associations %{candidates} defined on %{klass}.","resolution":"On the %{name} association on %{inverse} you must add an :inverse_of option to specify the exact association on %{klass} that is the opposite of %{name}.","summary":"When Mongoid attempts to set an inverse document of an association in memory, it needs to know which association it belongs to. When setting %{name}, Mongoid looked on the class %{inverse} for a matching association, but multiples were found that could potentially match: %{candidates}."},"blank":"can\'t be blank","blank_in_locale":"can\'t be blank in %{location}","callbacks":{"message":"Calling %{method} on %{klass} resulted in a false return from a callback.","resolution":"Double check all before callbacks to make sure they are not unintentionally returning false.","summary":"If a before callback returns false when using Document.create!, Document#save!, or Document#update_attributes! this error will get raised since the document did not actually get saved."},"calling_document_find_with_nil_is_invalid":{"message":"Calling Document.find with nil is invalid.","resolution":"Most likely this is caused by passing parameters directly through to the find, and the parameter either is not present or the key from which it is accessed is incorrect.","summary":"Document.find expects the parameters to be 1 or more ids, and will return a single document if 1 id is provided, otherwise an array of documents if multiple ids are provided."},"confirmation":"doesn\'t match %{attribute}","criteria_argument_required":{"message":"Calling Criteria methods with nil arguments is not allowed.","resolution":"Invoke Criteria methods with non-nil arguments or omit the respective invocations.","summary":"Arguments to Criteria methods cannot be nil, and most Criteria methods require at least one argument. Only logical operations (and, or, nor and not), `all\' and `where\' can be called without arguments. The method that was called with nil argument was: %{query_method}."},"delete_restriction":{"message":"Cannot delete %{document} because of dependent \'%{relation}\'.","resolution":"Don\'t attempt to delete the parent %{document} when it has children, or change the dependent option on the association.","summary":"When defining \'%{relation}\' with a :dependent => :restrict, Mongoid will raise an error when attempting to delete the %{document} when the child \'%{relation}\' still has documents in it."},"document_not_destroyed":{"message":"%{klass} with id %{id} was not destroyed.","resolution":"Check the before/after destroy callbacks to ensure that the return values are truthy for the chain to continue.","summary":"When calling %{klass}#destroy! and a callback halts the destroy callback chain by returning a false value, the deletion will not actually occur."},"document_not_found":{"message":"Document(s) not found for class %{klass} with id(s) %{missing}.","resolution":"Search for an id that is in the database or set the Mongoid.raise_not_found_error configuration option to false, which will cause a nil to be returned instead of raising this error when searching for a single id, or only the matched documents when searching for multiples.","summary":"When calling %{klass}.find with an id or array of ids, each parameter must match a document in the database or this error will be raised. The search was for the id(s): %{searched} (%{total} total) and the following ids were not found: %{missing}."},"document_with_attributes_not_found":{"message":"Document not found for class %{klass} with attributes %{attributes}.","resolution":"Search for attributes that are in the database or set the Mongoid.raise_not_found_error configuration option to false, which will cause a nil to be returned instead of raising this error.","summary":"When calling %{klass}.find_by with a hash of attributes, all attributes provided must match a document in the database or this error will be raised."},"eager_load":{"message":"Eager loading :%{name} is not supported since it is a polymorphic belongs_to association.","resolution":"Don\'t attempt to perform this action and have patience, maybe this will be supported in the future.","summary":"Mongoid cannot currently determine the classes it needs to eager load when the association is polymorphic. The parents reside in different collections so a simple id lookup is not sufficient enough."},"empty":"can\'t be empty","empty_config_file":{"message":"Empty configuration file: %{path}.","resolution":"Ensure your configuration file contains the correct contents. Please consult the following page with respect to Mongoid\'s configuration: https://docs.mongodb.com/mongoid/current/reference/configuration/","summary":"Your mongoid.yml configuration file appears to be empty."},"equal_to":"must be equal to %{count}","even":"must be even","exclusion":"is reserved","greater_than":"must be greater than %{count}","greater_than_or_equal_to":"must be greater than or equal to %{count}","in_memory_collation_not_supported":{"message":"A collation option cannot be applied when querying documents in-memory.","resolution":"Remove the collation option from the query.","summary":"The query being run against documents in memory has a collation option set. A collation option is only supported if the query is executed on a MongoDB server with version >= 3.4."},"inclusion":"is not included in the list","invalid":"is invalid","invalid_collection":{"message":"Access to the collection for %{klass} is not allowed.","resolution":"For access to the collection that the embedded document is in, use %{klass}#_root.collection, or do not attempt to persist an embedded document without a parent set.","summary":"%{klass}.collection was called, and %{klass} is an embedded document - it resides within the collection of the root document of the hierarchy."},"invalid_config_file":{"message":"Invalid configuration file: %{path}.","resolution":"Ensure your configuration file contains the correct contents. Please consult the following page with respect to Mongoid\'s configuration: https://docs.mongodb.com/mongoid/current/reference/configuration/","summary":"Your mongoid.yml configuration file does not contain the correct file structure."},"invalid_config_option":{"message":"Invalid configuration option: %{name}.","resolution":"Remove the invalid option or fix the typo. If you were expecting the option to be there, please consult the following page with repect to Mongoid\'s configuration:\\n\\n   http://mongoid.org/en/mongoid/docs/installation.html","summary":"A invalid configuration option was provided in your mongoid.yml, or a typo is potentially present. The valid configuration options are: %{options}."},"invalid_dependent_strategy":{"message":"Invalid dependent strategy: %{invalid_strategy}.","resolution":"Change the dependent strategy to one of the valid types.","summary":"An invalid dependent strategy was defined for the association: %{association}. The valid strategies are: %{valid_strategies}."},"invalid_field":{"message":"Defining a field named \'%{name}\' is not allowed.","resolution":"Use Mongoid.destructive_fields to see what names are not allowed, and don\'t use these names. These include names that also conflict with core Ruby methods on Object, Module, Enumerable, or included gems that inject methods into these or Mongoid internals.","summary":"Defining this field would override the method \'%{name}\', which would cause issues with expectations around the original method and cause extremely hard to debug issues. The original method was defined in:\\n   Object: %{origin}\\n   File: %{file}\\n   Line: %{line}"},"invalid_field_option":{"message":"Invalid option :%{option} provided for field :%{name}.","resolution":"When defining the field :%{name} on \'%{klass}\', please provide valid options for the field. These are currently: %{valid}. If you meant to define a custom field option, please do so first like so:\\n\\n   Mongoid::Fields.option :%{option} do |model, field, value|\\n     # Your logic here...\\n   end\\n   class %{klass}\\n     include Mongoid::Document\\n     field :%{name}, %{option}: true\\n   end\\n\\n","summary":"Mongoid requires that you only provide valid options on each field definition in order to prevent unexpected behavior later on."},"invalid_includes":{"message":"Invalid includes directive: %{klass}.includes(%{args})","resolution":"Ensure that each parameter passed to %{klass}.includes is a valid name of an association on the %{klass} model. These are: %{relations}.","summary":"Eager loading in Mongoid only supports providing arguments to %{klass}.includes that are the names of associations on the %{klass}."},"invalid_index":{"message":"Invalid index specification on %{klass}: %{spec}, %{options}","resolution":"Ensure that the index conforms to the correct syntax and has the correct options.\\n\\n Valid options are:\\n   background: true|false\\n   database: \'database_name\'\\n   drop_dups: true|false\\n   name: \'index_name\'\\n   sparse: true|false\\n   unique: true|false\\n   min: 1\\n   max: 1\\n   bits: 26\\n   key: 26\\n   bucket_size : 1\\n   sphere_version : 1\\n   text_version : 1\\n   version : 1\\n   weights: { content: 1, title: 2 }\\n   expire_after_seconds: number_of_seconds\\n   partial_filter_expression\\n   storage_engine\\n   language_override\\n   default_language\\n   collation\\n Valid types are: 1, -1, \'2d\', \'2dsphere\', \'geoHaystack\', \'text\', \'hashed\'\\n\\n Example:\\n   class Band\\n     include Mongoid::Document\\n     index({ name: 1, label: -1 }, { sparse: true })\\n     index({ location: \'2d\' }, { background: true })\\n   end\\n\\n","summary":"Indexes in Mongoid are defined as a hash of field name and direction/2d pairs, with a hash for any additional options."},"invalid_options":{"message":"Invalid option :%{invalid} provided to association :%{name}.","resolution":"Valid options are: %{valid}, make sure these are the ones you are using.","summary":"Mongoid checks the options that are passed to the association macros to ensure that no ill side effects occur by letting something slip by."},"invalid_path":{"message":"Having a root path assigned for %{klass} is invalid.","resolution":"Most likely your embedded model, %{klass} is also referenced via a has_many from a root document in another collection. Double check the association definitions and fix any instances where embedded documents are improperly referenced from other collections.","summary":"Mongoid has two different path objects for determining the location of a document in the database, Root and Embedded. This error is raised when an embedded document somehow gets a root path assigned."},"invalid_persistence_option":{"message":"Invalid persistence option :%{invalid}.","resolution":"Valid options are: %{valid}, make sure these are the ones you are using.","summary":"The options used to change the persistence context must be one of the valid options for a mongo client, or a collection name."},"invalid_relation":{"message":"Defining an association named \'%{name}\' is not allowed.","resolution":"Use Mongoid.destructive_fields to see what names are not allowed, and don\'t use these names. These include names that also conflict with core Ruby methods on Object, Module, Enumerable, or included gems that inject methods into these or Mongoid internals.","summary":"Defining this association would override the method \'%{name}\', which would cause issues with expectations around the original method and cause extremely hard to debug issues. The original method was defined in:\\n   Object: %{origin}\\n   File: %{file}\\n   Line: %{line}"},"invalid_relation_option":{"message":"Invalid association option :%{option} for association \'%{name}\' on class %{klass}.","resolution":"Valid options are: %{valid_options}, make sure you use only those.","summary":"An invalid option was provided for an association."},"invalid_scope":{"message":"Defining a scope of value %{value} on %{klass} is not allowed.","resolution":"Change the scope to be a proc wrapped critera.\\n\\n Example:\\n   class Band\\n     include Mongoid::Document\\n     scope :inactive, ->{ where(active: false) }\\n   end\\n\\n","summary":"Scopes in Mongoid must be procs that wrap criteria objects."},"invalid_session_nesting":{"message":"A session was started while another session was being used.","resolution":"Only use one session at a time; sessions cannot be nested.","summary":"Sessions cannot be nested. Only one session can be used in a thread at once."},"invalid_session_use":{"message":"A session was attempted to be used with a model whose client cannot use that session.","resolution":"Only execute operations on the model class or instances of the model through which the session was created. Otherwise, ensure that all models on which operations are executed in the session block share the same driver client. For example, a model may have a different client specified in its \'store_in\' options.\\n\\n","summary":"Sessions are started via driver clients (Model#mongo_client) and, in most cases, driver clients are shared across models. When different models have their own clients, a session cannot be obtained via one model and used for operations on another model."},"invalid_set_polymorphic_relation":{"message":"The %{name} attribute can\'t be set to an instance of %{other_klass} as %{other_klass} has multiple associations referencing %{klass} as %{name}.","resolution":"Set the values from the parent, or redefine the association with only a single definition in the parent.","summary":"If the parent class of a polymorphic association has multiple definitions for the same association, the values must be set from the parent side and not the child side since Mongoid cannot determine from the child side which association to go in."},"invalid_storage_options":{"message":"Invalid options passed to %{klass}.store_in: %{options}.","resolution":"Change the options passed to store_in to match the documented API, and ensure all keys in the options hash are symbols.\\n\\n Example:\\n   class Band\\n     include Mongoid::Document\\n     store_in collection: \'artists\', database: \'music\'\\n   end\\n\\n","summary":"The :store_in macro takes only a hash of parameters with the keys :database, :collection, or :client."},"invalid_storage_parent":{"message":"Invalid store_in call on class %{klass}.","resolution":"Remove the store_in call on class %{klass}, as it will use its parent store configuration. Or remove the hierarchy extension for this class.","summary":"The :store_in macro can only be called on a base Mongoid Document"},"invalid_time":{"message":"\'%{value}\' is not a valid Time.","resolution":"Make sure to pass parsable values to the field setter for Date, DateTime, and Time objects. When this is a String it needs to be valid for Time.parse. Other objects must be valid to pass to Time.local.","summary":"Mongoid tries to serialize the values for Date, DateTime, and Time into proper UTC times to store in the database. The provided value could not be parsed."},"invalid_value":{"message":"Value of type %{value_class} cannot be written to a field of type %{field_class}","resolution":"Verify if the value to be set correspond to field definition","summary":"Tried to set a value of type %{value_class} to a field of type %{field_class}"},"inverse_not_found":{"message":"When adding a(n) %{klass} to %{base}#%{name}, Mongoid could not determine the inverse foreign key to set. The attempted key was \'%{inverse}\'.","resolution":"If an inverse is not required, like a belongs_to or has_and_belongs_to_many, ensure that :inverse_of => nil is set on the association. If the inverse is needed, most likely the inverse cannot be figured out from the names of the associations and you will need to explicitly tell Mongoid on the association what the inverse is.\\n\\n Example:\\n   class Car\\n     include Mongoid::Document\\n     has_one :engine, class_name: \\"Motor\\", inverse_of: :machine\\n   end\\n\\n   class Motor\\n     include Mongoid::Document\\n     belongs_to :machine, class_name: \\"Car\\", inverse_of: :engine\\n   end","summary":"When adding a document to an association, Mongoid attempts to link the newly added document to the base of the association in memory, as well as set the foreign key to link them on the database side. In this case Mongoid could not determine what the inverse foreign key was."},"less_than":"must be less than %{count}","less_than_or_equal_to":"must be less than or equal to %{count}","message_title":"message","mixed_client_configuration":{"message":"Both uri and standard configuration options defined for client: \'%{name}\'.","resolution":"Provide either only a uri as configuration or only standard options.","summary":"Instead of simply giving uri or standard options a preference order, Mongoid assumes that you have made a mistake in your configuration and requires that you provide one or the other, but not both. The options that were provided were: %{config}."},"mixed_relations":{"message":"Referencing a(n) %{embedded} document from the %{root} document via a non-embedded association is not allowed since the %{embedded} is embedded.","resolution":"Consider not embedding %{embedded}, or do the key storage and access in a custom manner in the application code.","summary":"In order to properly access a(n) %{embedded} from %{root} the reference would need to go through the root document of %{embedded}. In a simple case this would require Mongoid to store an extra foreign key for the root, in more complex cases where %{embedded} is multiple levels deep a key would need to be stored for each parent up the hierarchy."},"model_invalid":"Validation failed: %{errors}","nested_attributes_metadata_not_found":{"message":"Could not find metadata for association \'%{name}\' on model: %{klass}.","resolution":"Make sure that there is an association defined named \'%{name}\' on %{klass} or that the association definition comes before the accepts_nested_attributes_for macro in the model - order matters so that Mongoid has access to the metadata.\\n\\n Example:\\n   class Band\\n     include Mongoid::Document\\n     has_many :albums\\n     accepts_nested_attributes_for :albums\\n   end\\n\\n","summary":"When defining nested attributes for an association, Mongoid needs to access the metadata for the association \'%{name}\' in order if add autosave functionality to it, if applicable. Either no association named \'%{name}\' could be found, or the association had not been defined yet."},"no_client_config":{"message":"No configuration could be found for a client named \'%{name}\'.","resolution":"Double check your mongoid.yml to make sure under the clients key that a configuration exists for \'%{name}\'. If you have set the configuration programatically, ensure that \'%{name}\' exists in the configuration hash.","summary":"When attempting to create the new client, Mongoid could not find a client configuration for the name: \'%{name}\'. This is necessary in order to know the host, port, and options needed to connect."},"no_client_database":{"message":"No database provided for client configuration: :%{name}.","resolution":"If configuring via a mongoid.yml, ensure that within your :%{name} section a :database value for the client\'s default database is defined.\\n\\n Example:\\n   development:\\n     clients:\\n       %{name}:\\n         database: my_app_db\\n         hosts:\\n           - localhost:27017\\n\\n","summary":"Each client configuration must provide a database so Mongoid knows where the default database to persist to. What was provided was: %{config}."},"no_client_hosts":{"message":"No hosts provided for client configuration: :%{name}.","resolution":"If configuring via a mongoid.yml, ensure that within your :%{name} section a :hosts value for the client hosts is defined.\\n\\n Example:\\n   development:\\n     clients:\\n       %{name}:\\n         database: my_app_db\\n         hosts:\\n           - localhost:27017\\n\\n","summary":"Each client configuration must provide hosts so Mongoid knows where the database server is located. What was provided was: %{config}."},"no_clients_config":{"message":"No clients configuration provided.","resolution":"Double check your mongoid.yml to make sure that you have a top-level clients key with at least 1 default client configuration for it. You can regenerate a new mongoid.yml for assistance via `rails g mongoid:config`.\\n\\n Example:\\n   development:\\n     clients:\\n       default:\\n         database: mongoid_dev\\n         hosts:\\n           - localhost:27017\\n\\n","summary":"Mongoid\'s configuration requires that you provide details about each client that can be connected to, and requires in the clients config at least 1 default client to exist."},"no_default_client":{"message":"No default client configuration is defined.","resolution":"If configuring via a mongoid.yml, ensure that within your :clients section a :default client is defined.\\n\\n Example:\\n   development:\\n     clients:\\n       default:\\n         hosts:\\n           - localhost:27017\\n\\n","summary":"The configuration provided settings for: %{keys}, but Mongoid requires a :default to be defined at minimum."},"no_environment":{"message":"Could not load the configuration since no environment was defined.","resolution":"Make sure some environment is set from the mentioned options. Mongoid cannot load configuration from the yaml without knowing which environment it is in, and we have considered defaulting to development an undesireable side effect of this not being defined.","summary":"Mongoid attempted to find the appropriate environment but no Rails.env, Sinatra::Base.environment, RACK_ENV, or MONGOID_ENV could be found."},"no_map_reduce_output":{"message":"No output location was specified for the map/reduce operation.","resolution":"Provide the location that the output of the operation is to go by chaining an #out call to the map/reduce.\\n\\n Example:\\n   Band.map_reduce(map, reduce).out(inline: 1)\\n\\n Valid options for the out function are:\\n   inline:  1\\n   merge:   \'collection-name\'\\n   replace: \'collection-name\'\\n   reduce:  \'collection-name\'\\n\\n","summary":"When executing a map/reduce, you must provide the output location of the results. The attempted command was: %{command}."},"no_metadata":{"message":"Metadata not found for document of type %{klass}.","resolution":"Ensure that your associations on the %{klass} model are all properly defined, and that the inverse associations are also properly defined. Embedded associations must have both the parent (embeds_one/embeds_many) and the inverse (embedded_in) present in order to work properly.","summary":"Mongoid sets the metadata of an association on the document when it is either loaded from within the association, or added to one. The presence of the metadata is required in order to provide various functionality around associations. Most likely you are getting this error because the document is embedded and was attempted to be persisted without being associated with a parent, or the association was not properly defined."},"no_parent":{"message":"Cannot persist embedded document %{klass} without a parent document.","resolution":"Ensure that you\'ve set the parent association if instantiating the embedded document directly, or always create new embedded documents via the parent association.","summary":"If the document is embedded, in order to be persisted it must always have a reference to its parent document. This is most likely caused by either calling %{klass}.create or %{klass}.create! without setting the parent document as an attribute."},"not_a_number":"is not a number","not_an_integer":"must be an integer","odd":"must be odd","other_than":"must be other than %{count}","present":"must be blank","readonly_attribute":{"message":"Attempted to set the readonly attribute \'%{name}\' with the value: %{value}.","resolution":"Don\'t define \'%{name}\' as readonly, or do not attempt to update its value after the document is persisted.","summary":"Attributes flagged as readonly via Model.attr_readonly can only have values set when the document is a new record."},"readonly_document":{"message":"Attempted to persist the readonly document \'%{klass}\'.","resolution":"Don\'t attempt to persist documents that are flagged as readonly.","summary":"Documents loaded from the database using #only cannot be persisted."},"record_invalid":"Validation failed: %{errors}","required":"must exist","resolution_title":"resolution","restrict_dependent_destroy":{"has_many":"Cannot delete record because dependent %{record} exist","has_one":"Cannot delete record because a dependent %{record} exists"},"scope_overwrite":{"message":"Cannot create scope :%{scope_name}, because of existing method %{model_name}.%{scope_name}.","resolution":"Change the name of the scope so it does not conflict with the already defined method %{model_name}, or set the configuration option Mongoid.scope_overwrite_exception to false, which is its default. In this case a warning will be logged.","summary":"When defining a scope that conflicts with a method that already exists on the model, this error will get raised if Mongoid.scope_overwrite_exception is set to true."},"sessions_not_supported":{"message":"Sessions are not supported by the connected server(s).","resolution":"Verify that all servers in your deployment are at least version 3.6 or don\'t attempt to use sessions with older server versions.","summary":"A session was attempted to be used with a MongoDB server version that doesn\'t support sessions. Sessions are supported in MongoDB server versions 3.6 and higher."},"summary_title":"summary","taken":"has already been taken","too_long":{"one":"is too long (maximum is 1 character)","other":"is too long (maximum is %{count} characters)"},"too_many_nested_attribute_records":{"message":"Accepting nested attributes for %{association} is limited to %{limit} records.","resolution":"The limit is set as an option to the macro, for example: accepts_nested_attributes_for :%{association}, limit: %{limit}. Consider raising this limit or making sure no more are sent than the set value.","summary":"More documents were sent to be processed than the allowed limit."},"too_short":{"one":"is too short (minimum is 1 character)","other":"is too short (minimum is %{count} characters)"},"unknown_attribute":{"message":"Attempted to set a value for \'%{name}\' which is not allowed on the model %{klass}.","resolution":"You can include Mongoid::Attributes::Dynamic if you expect to be writing values for undefined fields often.","summary":"Without including Mongoid::Attributes::Dynamic in your model and the attribute does not already exist in the attributes hash, attempting to call %{klass}#%{name}= for it is not allowed. This is also triggered by passing the attribute to any method that accepts an attributes hash, and is raised instead of getting a NoMethodError."},"unknown_model":{"message":"Attempted to instantiate an object of the unknown Model \'%{klass}\'.","resolution":"The _type field is a reserved one used by Mongoid to determine the class for instantiating an object. Please don\'t save data in this field or ensure that any values in this field correspond to valid Models.","summary":"A document with the value \'%{value}\' at the key \'_type\' was used to instantiate a model object but Mongoid cannot find this Class."},"unsaved_document":{"message":"Attempted to save %{document} before the parent %{base}.","resolution":"Make sure to only use create or create! when the parent document %{base} is persisted.","summary":"You cannot call create or create! through the association (%{document}) whose parent (%{base}) is not already saved. This would cause the database to be out of sync since the child could potentially reference a nonexistent parent."},"unsupported_javascript":{"message":"Executing Javascript $where selector on an embedded criteria is not supported.","resolution":"Please provide a standard hash to #where when the criteria is for an embedded association.","summary":"Mongoid only supports providing a hash of arguments to #where criterion on embedded documents. Since %{klass} is embedded, the expression %{javascript} is not allowed."},"validations":{"message":"Validation of %{document} failed.","resolution":"Try persisting the document with valid data or remove the validations.","summary":"The following errors were found: %{errors}"},"wrong_length":{"one":"is the wrong length (should be 1 character)","other":"is the wrong length (should be %{count} characters)"}}}},"nb":{"add_device_success":"The device will not be listed here until it\'s powered up."},"number":{"currency":{"format":{"delimiter":",","format":"%u%n","precision":2,"separator":".","significant":false,"strip_insignificant_zeros":false,"unit":"$"}},"format":{"delimiter":",","precision":3,"separator":".","significant":false,"strip_insignificant_zeros":false},"human":{"decimal_units":{"format":"%n %u","units":{"billion":"Billion","million":"Million","quadrillion":"Quadrillion","thousand":"Thousand","trillion":"Trillion","unit":""}},"format":{"delimiter":"","precision":3,"significant":true,"strip_insignificant_zeros":true},"storage_units":{"format":"%n %u","units":{"byte":{"one":"Byte","other":"Bytes"},"eb":"EB","gb":"GB","kb":"KB","mb":"MB","pb":"PB","tb":"TB"}}},"nth":{},"percentage":{"format":{"delimiter":"","format":"%n%"}},"precision":{"format":{"delimiter":""}}},"order":{"status":{"cancel":"Cancelled","done":"Paid","pending":"Pending"}},"pay":{"activate":"Activate","amount":"Amount","auto_pay":"Automatic renewal","cancel_sub":"Cancel","cancel_sub_desp":"Are you sure to cancel the subscription?","change_success":"Change subscription successfully, it may take a few minutes to sync the state.","charge":"Subscribe","cloud_storage":"Cloud storage","customer_title":"Billing and Receipt","daily":"Test Only","days":"Valid: {{day}} days","download_billing":"Download billing","download_success":"Billing has been sent to your email.","full_name":"Full Name","input_full_name":"Please enter your full name","monthly":"Monthly","now_sub_status":"Current subscription","order":"Order","order_pice":"Order total","paid_price":"Paid total","pause":"Suspend","pay":"Subscribe","pay_info":"Payment","pay_info_desp":"Your full name and email will only be used for billing and will not be shared with any third parties.","payment_record":"Payment record","refund_money":"Refund total","reset_sub":"Restore","select_item":"Current","setting_wait":"Operation done, it may take a few minutes to take effect.","sub":{"daily":{"desp":"1 Camera，clips keep {{file_days}} days","title":"Test Only"},"monthly":{"desp":"1 Camera, clips keep {{file_days}} days","title":"Monthly"},"trial":{"desp":"1 Camera，clips keep {{file_days}} days","title":"Free Trail"},"yearly":{"desp":"1 Camera，clips keep {{file_days}} days","title":"Yearly"}},"sub_status":"Subscription state","sub_time":"Expiration date","sub_type":"Plans","success":"Paid successfully, it maybe take a few minutes to sync the order state.","title":"Payment information","trial":"Trial","unknown":"You have no subscription yet or your subscription is expired.","up_sub":"Change subscription","yearly":"Yearly"},"payment":{"errors":{"not_found_payment_record":"Payment record not found."},"status":{"cancel":"Cancelled","failed":"Payment failed","pending":"Pending","success":"Paid"}},"permissions":{"camera_block":"Camera permission is blocked.","camera_error":"Camera permission is not available on this device.","camera_title":"You have disabled the APP camera permission, and the camera service is limited. Please enable the location service in the Settings","location_block":"Location permission is blocked.","location_error":"Location permission is not available on this device.","location_title":"You have disabled the APP location permission, and the location service is limited. Please enable the location service in the Settings","media_block":"Media Library permission is blocked.","media_error":"Media Library permission is not available on this device.","media_title":"You have disabled the APP Media Library permission, and the Media Library service is limited. Please enable the location service in the Settings","notify_error":"Notification not enabled"},"room":{"add_room":"Add Room","all_off":"All OFF","all_on":"All ON","desp":"EVENT means there\'s device state changes in this room in the last 24 hours.","device_in_room":"Devices in this room","event":"EVENT","filter_controller":"Filter by controllers","filter_room":"Filter by rooms","light_color":"Light Color","normal":"OK","room_name":"Room name","room_no_device":"Please select one device at least!","room_no_name":"Room name can not be empty!","room_switch_hint":"Control all swtich devices in this room","setting_room":"Rooms Setting"},"routine":{"add":"Add Routine","address":"Address","call_url":"Call a HTTP URL","call_url_desp":"This HTTP URL will be send a GET request when this automation is activated","cond_action":"Run an action","cond_device":"Control devices","cond_scene":"Activate a scene","cond_url":"Run an external url","edit":"Edit Routine","get_to":"Arrival","is_cyclic":"Repeat","last":"Back","leave":"Leave","no_google":"Not support GPS automation for Android device without Google Service.","one_cond":"Once","please_address":"Please select the GPS location","please_date":"Please select the date","please_external":"Please input external url!","please_leave":"Please select arriving or leaving","please_time":"Please select time","please_to":"Please select what you want to do!","please_type":"Please select the routine type","routine":"Routines","routine_cond":"When","routine_desp":"Routine can turn on a Scene or Devices base on the time or GPS location (need work with Presen App)","routine_location":"GPS location","routine_type":"Routine type","run_multiple":"Repeat","run_once":"Once","select_action":"Select action","select_device":"Select devices","select_scene":"Select scene","test_app_url":"Test in browser","time":"Time","to_location":"GPS","to_time":"Time","ui":"This feature need to work with Presen App and Presen Cloud, please visit https://www.presensmarthome.com for more help."},"scene":{"activate":"Activate","add_scene":"Add Scene","add_scene_type":"Please enter the scene name:","change_scene":"Activate the scene","current_scene":"Current scene","delete_scene":"Are you sure to remove this scene?","scene_device":"Please choose target!","scene_input":"Name can not be empty!","scene_name":"Scene name","select_icon":"Select Icon"},"services":{"alexa":"Control your devices, scenes and actions with Amazon Alexa enabled devices.","alexa_desp":"Such as \\"Alexa, turn on home scene\\".","ali":"Control your devices, scenes and actions using TmallGenie.","ali_desp":"Such as \\"天猫精灵, 打开卧室的灯 \\".","google":"Control your devices, scenes and actions using Google Assistant supported devices.","google_desp":"Such as \\"Hey Google, activate home\\".","ifttt":"Work with IFTTT to link with more 3rd services.","ifttt_desp":"Such as when motion sensor is triggered, you will get a call."},"session":{"60_s":"Your can re-send the One-time password in 60 seconds!","account":"Account","address_empty":"Please select address!","cellphone_email_type":"Please enter a valid email address or cellphone number!","cellphone_input":"Please enter your cellphone!","change_china":"Switch to Chinese version","change_other":"Switch to universal version","change_password":"Change Password","change_username":"Change Username","confir_input":"Please enter your password again!","email":"The Email already exists!","email_input":"Please enter your email!","email_or_phone":"Email","email_type":"Please enter a valid email address!","get_token":"OTP","is_cellphone":"This cellphone number has already been taken!","is_email":"Email has already been taken!","login_name_input":"Email","login_phone":"Cellphone","login_to_email":"Login use email","login_to_phone":"Login use cellphone","new_input":"Pleas enter your new password!","new_password":"New password","no_name_desp":"Username can not be empty!","no_user_name":"Email/cellphone can not be empty!","not_eamil":"Email do not exist!","not_email_or_phone":"Please enter your email!","not_input_phone":"Please enter your phone!","not_phone":"Cellphone do not exist!","not_token":"One-time password is invalid!","old_error":"Old password is incorrect!","old_input":"Please enter your old password!","old_password":"Old password","password_changed_success":"Your password has been changed!","password_confirmation":"Password Confirmation","password_input":"Password","password_length":"Password needs at least 8 characters and contain uppercase, lowercase and number.","please_enter_password":"Please enter your password!","register":"Sign Up","reset_password":"Reset Password","select_country":"Please select area","send":"Send","send_token":"A One-time password has been send to your mailbox, please check your mailbox.","signup_success_login":"Account created successfully, please login now!","token_input":"A verification code has been sent to your email, please check and enter your verification code below to continue.","token_send":"A One-time password has been send to your mailbox.","token_send_phone":"A One-time password has been send to your Phone.","two_password":"Passwords do not match!","user_name":"Username","wrong_name":"The email/cellphone or password is invalid!"},"setting":{"add":"Add","advanced_setting":"ADVANCED SETTINGS","background_image":"Background Image","backup":"Backup","backup_desp":"Backup the controller data.","backup_message":"Please do not power off the controller when backing up!","base":"BASIC SETTINGS","change_notification":"Change notification","check_device":"Please select the attributes of the devices you DO NOT want to receive change notifications.","check_device_for_urgent":"Only receive urgent notifications when following devices changes.","check_scene":"Do not receive notifications when your Home is in the following checked scenes, if no scene is selected, it means all device change notifications will NOT be send no matter what scene it is.","check_scene_for_urgent":"Only receive urgent notifications when in the following scenes.","close_account":"Remove Account","close_desp":"After removal your account, you will be logged out immediately and you will not be able to login again！","default_reset_all_parameters":"Default Reset All Parameters","delete":"Delete","download_data":"Download Data","download_desp":"Download your personal user data in CSV format, the file will be sent to your email box shortly.","end_time":"End","friday":"Friday","home_setting":"Home Management","inform":"Notifications","local_siren":"Local siren","login_close_desp":"This account has been removed. To reuse this account, please contact us!","monday":"Monday","new_update_version":"New version:","next_day":"Next Day","not_end_time":"and end time is:","not_start_time":"By default the start time is:","notify":"Notification Center","notify_by_device":"Devices Notification","notify_by_time":"Do Not Disturb","notify_desp":"Global notification control, you will not receive any notifications if it\'s off","notify_device_noti_desp":"You will not receive following devices\' change notifications","notify_dnd_desp":"Setup the time you don\'t want to receive notifications","notify_time":"Time","notify_urgent_device":"Urgent Notification","notify_urgent_noti_desp":"You will receive a special notification when these devices change. (Urgent Notification will ignore the Do Not Disturb and Devices Notification settings)","photo_btn":"Choose from your photos","place_end_time":"Please select the end time!","place_select_period":"Please select time！","place_start_time":"Please select start time！","property":"Attributes","record":"Restore","record_desp":"Restore the controller from a previous backup file.","record_error":"Restore failed, please try again!","record_message":"Please do not power off the controller when restoring!","record_success":"Controller has been reset successfully!","reset":"Reset Controller","reset_desp":"Reset the controller to factory state, all the data will be purged.\\n\\nIncluding: devices data, timezone setting, WiFi setting, controller PIN and rooms/scenes/actions/automations etc.","reset_home":"Reset Home Data","reset_home_desp":"This will reset all your controller(s) to factory state, and all your data will be purged.\\n\\nIncluding: devices data, timezone setting, WiFi setting, controller PIN and rooms/scenes/actions/automations etc.","reset_message":"All the data will be purged!","reset_success":"Your controller has been reset successfully.","reset_title":"Reset controller?","restart":"Restart Controller","restart_desp":"It will takes up to 30 seconds to finsih the rebooting","restart_message":"Are you sure to restart the controller？","restart_title":"Restart controller?","saturday":"Saturday","select_date":"Day","select_device_scene":"Please select device or scene！","select_period":"Select time","start_end":"The end time is less than the start time!","starting_time":"Start","sunday":"Sunday","system_notification_state":"Receive push notification. Enable to receive device change notifications, scene activation notifications etc..","thursday":"Thursday","tuesday":"Tuesday","update_desp":"It will take about 5 minutes to finish the update, the time required to download may vary, it depends on your network speed.\\n\\nContinue to update?","update_desp_one":"New version available: ","update_failed_in_progress":"There is an updating  already in progress.","update_failed_other":"Update failed, please try again later!","update_failed_up_to_date":"There is no need to update, you are up to date.","update_online":"Upgrade Controller","update_title":"Downloading...","updated":"Your controller is up to date!","updating":"Upgrading...","upgrade_desp":"Controller is updating, please don\'t turn off the power of your controller, it will take a few seconds to finish.\\n\\nController will reboot when the update is completed, and you will get a notification.","upgrade_success_to":"The controller has been upgraded to","use_background_image":"Use background image","wednesday":"Wednesday","widget_desp":"You can only choose upto 4 scenes and actions in widget.","widget_login":"Need to login to get your personal scenes and actions.","widget_setting":"Widget Setup"},"spec":{"FrameChange":"Motion Detection","HumanMotion":"Human Motion","access_control":"Access Control","air_flow":"Air Flow","air_temperature":"Air Temperature","alarm":"Alarm","alarm_s":"AlarmSensor","angle_position":"Angle Position","animal_detected":"motion detected Animal","app_url":"APP URL","appliance":"Appliance","atmospheric_pressure":"Atmospheric Pressure","auto":"Auto","aux":"Aux","auxiliary":"Auxiliary","barometric_pressure":"Barometric Pressure","battery":"Battery","burglar":"Burglar","camera":"IP Camera","car_detected":"motion detected Vehicle","clock":"Clock","close":"Close","co":"CO","co2":"CO2","co2_alarm":"CO2 Alarm","co2_level":"CO2 Level","co_alarm":"CO Alarm","co_level":"CO Level","color_switch":"Color Switch","color_temperature":"Color Temperature","configuration_parameters":"Configuration Parameters","contact_sensor":"Contact Sensor","cool":"Cool","cooling_setpoint":"Cooling Setpoint","current":"Current","dew_point":"Dew Point","dimmer":"Dimmer","direction":"Direction","distance":"Distance","door_lock":"DoorLock","door_window":"Door/Window","electrical_conductivity":"Electrical Conductivity","electrical_resistivity":"Electrical Resistivity","emergency":"Emergency","executed":"executed","false":"No","fan_mode":"Fan Mode","fire_sensor":"Fire Sensor","first":"First","flood":"Water Leak","flow_measurement":"Flow Measurement","frame_change":"Picture Frame Change","freeze":"Freeze","frequency":"Frequency","full":"FULL","general":"General","general_purpose":"General Purpose","general_purpose_alarm":"General Purpose Alarm","general_purpose_value":"General Purpose Value","general_trigger":"General Trigger","glass_break":"Glass Break","going_to_low_battery":"low battery","gu_zhang":"Fault","heat":"Heat","heat_alarm":"Heat Alarm","heating_setpoint":"Heating Setpoint","home_security":"Home Security","human_motion":"Human Motion","humidity":"Humidity","is":"is","key_fob":"Key Fob","keypad":"keypad","lock":"Lock","loudness":"Loudness","low":"Low","luminance":"Luminance","luminiscence":"Luminance","main_powered":"Main Powered","meter":"Meter","moisture":"Moisture","motion":"Motion","motor":"Motor","multi_switch":"MultiSwitch","n_a":"N/A","no_b":"No","normal":"Normal","not_used":"N/A","off_b":"OFF","on_b":"ON","open":"Open","open_close":"OpenClose","package_detected":"detected Package","person_detected":"motion detected Person","power":"Power","power_management":"Power Management","quake":"Quake","rain_rate":"Rain Rate","relative_humidity":"Relative Humidity","remote_ontrol":"Remote Control","reserved":"Reserved","resume":"Resume","return_first_alarm_on_supported_list":"Return first Alarm on supported list","rock_setting":"Rock Setting","rotation":"Rotation","security_repeater":"Security Repeater","seismic_intensity":"Seismic Intensity","seismic_magnitude":"Seismic Magnitude","sensor":"Sensor","smoke":"Smoke","smoke_alarm":"Smoke Alarm","smoke_test":"Smoke Test","soil_temperature":"Soil Temperature","solar_radiation":"Solar Radiation","switch":"Switch","switch_all":"Switch All","switch_to":"switched to","system":"System","tamper":"Tamper","tank_capacity":"Tank Capacity","target_temperature":"Target Temperature","temperature":"Temperature","temperature_unit":"Temperature Unit","thermostat":"Thermostat","thermostat_mode":"Thermostat Mode","thermostat_setpoint":"Thermostat Setpoint","tide_level":"Tide Level","tilt":"Tilt","time":"Time","true":"Yes","ultraviolet":"Ultraviolet","unlock":"Unlock","velocity":"Velocity","ventilation":"Ventilation","vibration":"Vibration","voltage":"Voltage","water":"Water","water_leak":"Water Leak","water_leak_alarm":"Water Leak Alarm","water_temperature":"Water Temperature","weight":"Weight","window_covering":"Curtain","yes_b":"Yes"},"subscription":{"errors":{"already_subscribed":"You has already subscribed, you can try to change another subscription plan.","already_trailed":"Your trail has expired, please subscribe.","invalid_type":"Invalid subscription type.","no_subscription":"You has no subscription yet.","same_price_id":"You must change to a different subscription plan."}},"support":{"array":{"last_word_connector":", and ","two_words_connector":" and ","words_connector":", "}},"time":{"am":"am","formats":{"default":"%a, %d %b %Y %H:%M:%S %z","long":"%B %d, %Y %H:%M","short":"%d %b %H:%M"},"pm":"pm"},"timezones":{"Abu Dhabi":"Abu Dhabi","Adelaide":"Adelaide","Alaska":"Alaska","Almaty":"Almaty","American Samoa":"American Samoa","Amsterdam":"Amsterdam","Arizona":"Arizona","Astana":"Astana","Athens":"Athens","Atlantic Time (Canada)":"Atlantic Time (Canada)","Auckland":"Auckland","Azores":"Azores","Baghdad":"Baghdad","Baku":"Baku","Bangkok":"Bangkok","Beijing":"Beijing","Belgrade":"Belgrade","Berlin":"Berlin","Bern":"Bern","Bogota":"Bogota","Brasilia":"Brasilia","Bratislava":"Bratislava","Brisbane":"Brisbane","Brussels":"Brussels","Bucharest":"Bucharest","Budapest":"Budapest","Buenos Aires":"Buenos Aires","Cairo":"Cairo","Canberra":"Canberra","Cape Verde Is.":"Cape Verde Is.","Caracas":"Caracas","Casablanca":"Casablanca","Central America":"Central America","Central Time (US & Canada)":"Central Time (US & Canada)","Chatham Is.":"Chatham Is.","Chennai":"Chennai","Chihuahua":"Chihuahua","Chongqing":"Chongqing","Copenhagen":"Copenhagen","Darwin":"Darwin","Dhaka":"Dhaka","Dublin":"Dublin","Eastern Time (US & Canada)":"Eastern Time (US & Canada)\\"","Edinburgh":"Edinburgh","Ekaterinburg":"Ekaterinburg","Fiji":"Fiji","Georgetown":"Georgetown","Greenland":"Greenland","Guadalajara":"Guadalajara","Guam":"Guam","Hanoi":"Hanoi","Harare":"Harare","Hawaii":"Hawaii","Helsinki":"Helsinki","Hobart":"Hobart","Hong Kong":"Hong Kong","Indiana (East)":"Indiana (East)","International Date Line West":"International Date Line West","Irkutsk":"Irkutsk","Islamabad":"Islamabad","Istanbul":"Istanbul","Jakarta":"Jakarta","Jerusalem":"Jerusalem","Kabul":"Kabul","Kaliningrad":"Kaliningrad","Kamchatka":"Kamchatka","Karachi":"Karachi","Kathmandu":"Kathmandu","Kolkata":"Kolkata","Krasnoyarsk":"Krasnoyarsk","Kuala Lumpur":"Kuala Lumpur","Kuwait":"Kuwait","Kyiv":"Kyiv","La Paz":"La Paz","Lima":"Lima","Lisbon":"Lisbon","Ljubljana":"Ljubljana","London":"London","Madrid":"Madrid","Magadan":"Magadan","Marshall Is.":"Marshall Is.","Mazatlan":"Mazatlan","Melbourne":"Melbourne","Mexico City":"Mexico City","Mid-Atlantic":"Mid-Atlantic","Midway Island":"Midway Island","Minsk":"Minsk","Monrovia":"Monrovia","Monterrey":"Monterrey","Montevideo":"Montevideo","Moscow":"Moscow","Mountain Time (US & Canada)":"Mountain Time (US & Canada)","Mumbai":"Mumbai","Muscat":"Muscat","Nairobi":"Nairobi","New Caledonia":"New Caledonia","New Delhi":"New Delhi","Newfoundland":"Newfoundland","Novosibirsk":"Novosibirsk","Nuku\'alofa":"Nuku\'alofa","Osaka":"Osaka","Pacific Time (US & Canada)":"Pacific Time (US & Canada)","Paris":"Paris","Perth":"Perth","Port Moresby":"Port Moresby","Prague":"Prague","Pretoria":"Pretoria","Quito":"Quito","Rangoon":"Rangoon","Riga":"Riga","Riyadh":"Riyadh","Rome":"Rome","Samara":"Samara","Samoa":"Samoa","Santiago":"Santiago","Sapporo":"Sapporo","Sarajevo":"Sarajevo","Saskatchewan":"Saskatchewan","Seoul":"Seoul","Singapore":"Singapore","Skopje":"Skopje","Sofia":"Sofia","Solomon Is.":"Solomon Is.","Srednekolymsk":"Srednekolymsk","Sri Jayawardenepura":"Sri Jayawardenepura","St. Petersburg":"St. Petersburg","Stockholm":"Stockholm","Sydney":"Sydney","Taipei":"Taipei","Tallinn":"Tallinn","Tashkent":"Tashkent","Tbilisi":"Tbilisi","Tehran":"Tehran","Tijuana":"Tijuana","Tokelau Is.":"Tokelau Is.","Tokyo":"Tokyo","UTC":"UTC","Ulaan Bataar":"Ulaan Bataar","Ulaanbaatar":"Ulaanbaatar","Urumqi":"Urumqi","Vienna":"Vienna","Vilnius":"Vilnius","Vladivostok":"Vladivostok","Volgograd":"Volgograd","Warsaw":"Warsaw","Wellington":"Wellington","West Central Africa":"West Central Africa","Yakutsk":"Yakutsk","Yerevan":"Yerevan","Zagreb":"Zagreb","Zurich":"Zurich"},"timezones_all":{"Africa/Abidjan":"Africa/Abidjan","Africa/Accra":"Africa/Accra","Africa/Addis_Ababa":"Africa/Addis_Ababa","Africa/Algiers":"Africa/Algiers","Africa/Asmara":"Africa/Asmara","Africa/Bamako":"Africa/Bamako","Africa/Bangui":"Africa/Bangui","Africa/Banjul":"Africa/Banjul","Africa/Bissau":"Africa/Bissau","Africa/Blantyre":"Africa/Blantyre","Africa/Brazzaville":"Africa/Brazzaville","Africa/Bujumbura":"Africa/Bujumbura","Africa/Cairo":"Africa/Cairo","Africa/Casablanca":"Africa/Casablanca","Africa/Ceuta":"Africa/Ceuta","Africa/Conakry":"Africa/Conakry","Africa/Dakar":"Africa/Dakar","Africa/Dar_es_Salaam":"Africa/Dar_es_Salaam","Africa/Djibouti":"Africa/Djibouti","Africa/Douala":"Africa/Douala","Africa/El_Aaiun":"Africa/El_Aaiun","Africa/Freetown":"Africa/Freetown","Africa/Gaborone":"Africa/Gaborone","Africa/Harare":"Africa/Harare","Africa/Johannesburg":"Africa/Johannesburg","Africa/Juba":"Africa/Juba","Africa/Kampala":"Africa/Kampala","Africa/Khartoum":"Africa/Khartoum","Africa/Kigali":"Africa/Kigali","Africa/Kinshasa":"Africa/Kinshasa","Africa/Lagos":"Africa/Lagos","Africa/Libreville":"Africa/Libreville","Africa/Lome":"Africa/Lome","Africa/Luanda":"Africa/Luanda","Africa/Lubumbashi":"Africa/Lubumbashi","Africa/Lusaka":"Africa/Lusaka","Africa/Malabo":"Africa/Malabo","Africa/Maputo":"Africa/Maputo","Africa/Maseru":"Africa/Maseru","Africa/Mbabane":"Africa/Mbabane","Africa/Mogadishu":"Africa/Mogadishu","Africa/Monrovia":"Africa/Monrovia","Africa/Nairobi":"Africa/Nairobi","Africa/Ndjamena":"Africa/Ndjamena","Africa/Niamey":"Africa/Niamey","Africa/Nouakchott":"Africa/Nouakchott","Africa/Ouagadougou":"Africa/Ouagadougou","Africa/Porto-Novo":"Africa/Porto-Novo","Africa/Sao_Tome":"Africa/Sao_Tome","Africa/Tripoli":"Africa/Tripoli","Africa/Tunis":"Africa/Tunis","Africa/Windhoek":"Africa/Windhoek","America/Adak":"America/Adak","America/Anchorage":"America/Anchorage","America/Anguilla":"America/Anguilla","America/Antigua":"America/Antigua","America/Araguaina":"America/Araguaina","America/Argentina/Buenos_Aires":"America/Argentina/Buenos_Aires","America/Argentina/Catamarca":"America/Argentina/Catamarca","America/Argentina/Cordoba":"America/Argentina/Cordoba","America/Argentina/Jujuy":"America/Argentina/Jujuy","America/Argentina/La_Rioja":"America/Argentina/La_Rioja","America/Argentina/Mendoza":"America/Argentina/Mendoza","America/Argentina/Rio_Gallegos":"America/Argentina/Rio_Gallegos","America/Argentina/Salta":"America/Argentina/Salta","America/Argentina/San_Juan":"America/Argentina/San_Juan","America/Argentina/San_Luis":"America/Argentina/San_Luis","America/Argentina/Tucuman":"America/Argentina/Tucuman","America/Argentina/Ushuaia":"America/Argentina/Ushuaia","America/Aruba":"America/Aruba","America/Asuncion":"America/Asuncion","America/Atikokan":"America/Atikokan","America/Bahia":"America/Bahia","America/Bahia_Banderas":"America/Bahia_Banderas","America/Barbados":"America/Barbados","America/Belem":"America/Belem","America/Belize":"America/Belize","America/Blanc-Sablon":"America/Blanc-Sablon","America/Boa_Vista":"America/Boa_Vista","America/Bogota":"America/Bogota","America/Boise":"America/Boise","America/Cambridge_Bay":"America/Cambridge_Bay","America/Campo_Grande":"America/Campo_Grande","America/Cancun":"America/Cancun","America/Caracas":"America/Caracas","America/Cayenne":"America/Cayenne","America/Cayman":"America/Cayman","America/Chicago":"America/Chicago","America/Chihuahua":"America/Chihuahua","America/Costa_Rica":"America/Costa_Rica","America/Creston":"America/Creston","America/Cuiaba":"America/Cuiaba","America/Curacao":"America/Curacao","America/Danmarkshavn":"America/Danmarkshavn","America/Dawson":"America/Dawson","America/Dawson_Creek":"America/Dawson_Creek","America/Denver":"America/Denver","America/Detroit":"America/Detroit","America/Dominica":"America/Dominica","America/Edmonton":"America/Edmonton","America/Eirunepe":"America/Eirunepe","America/El_Salvador":"America/El_Salvador","America/Fort_Nelson":"America/Fort_Nelson","America/Fortaleza":"America/Fortaleza","America/Glace_Bay":"America/Glace_Bay","America/Godthab":"America/Godthab","America/Goose_Bay":"America/Goose_Bay","America/Grand_Turk":"America/Grand_Turk","America/Grenada":"America/Grenada","America/Guadeloupe":"America/Guadeloupe","America/Guatemala":"America/Guatemala","America/Guayaquil":"America/Guayaquil","America/Guyana":"America/Guyana","America/Halifax":"America/Halifax","America/Havana":"America/Havana","America/Hermosillo":"America/Hermosillo","America/Indiana/Indianapolis":"America/Indiana/Indianapolis","America/Indiana/Knox":"America/Indiana/Knox","America/Indiana/Marengo":"America/Indiana/Marengo","America/Indiana/Petersburg":"America/Indiana/Petersburg","America/Indiana/Tell_City":"America/Indiana/Tell_City","America/Indiana/Vevay":"America/Indiana/Vevay","America/Indiana/Vincennes":"America/Indiana/Vincennes","America/Indiana/Winamac":"America/Indiana/Winamac","America/Inuvik":"America/Inuvik","America/Iqaluit":"America/Iqaluit","America/Jamaica":"America/Jamaica","America/Juneau":"America/Juneau","America/Kentucky/Louisville":"America/Kentucky/Louisville","America/Kentucky/Monticello":"America/Kentucky/Monticello","America/Kralendijk":"America/Kralendijk","America/La_Paz":"America/La_Paz","America/Lima":"America/Lima","America/Los_Angeles":"America/Los_Angeles","America/Lower_Princes":"America/Lower_Princes","America/Maceio":"America/Maceio","America/Managua":"America/Managua","America/Manaus":"America/Manaus","America/Marigot":"America/Marigot","America/Martinique":"America/Martinique","America/Matamoros":"America/Matamoros","America/Mazatlan":"America/Mazatlan","America/Menominee":"America/Menominee","America/Merida":"America/Merida","America/Metlakatla":"America/Metlakatla","America/Mexico_City":"America/Mexico_City","America/Miquelon":"America/Miquelon","America/Moncton":"America/Moncton","America/Monterrey":"America/Monterrey","America/Montevideo":"America/Montevideo","America/Montserrat":"America/Montserrat","America/Nassau":"America/Nassau","America/New_York":"America/New_York","America/Nipigon":"America/Nipigon","America/Nome":"America/Nome","America/Noronha":"America/Noronha","America/North_Dakota/Beulah":"America/North_Dakota/Beulah","America/North_Dakota/Center":"America/North_Dakota/Center","America/North_Dakota/New_Salem":"America/North_Dakota/New_Salem","America/Ojinaga":"America/Ojinaga","America/Panama":"America/Panama","America/Pangnirtung":"America/Pangnirtung","America/Paramaribo":"America/Paramaribo","America/Phoenix":"America/Phoenix","America/Port-au-Prince":"America/Port-au-Prince","America/Port_of_Spain":"America/Port_of_Spain","America/Porto_Velho":"America/Porto_Velho","America/Puerto_Rico":"America/Puerto_Rico","America/Punta_Arenas":"America/Punta_Arenas","America/Rainy_River":"America/Rainy_River","America/Rankin_Inlet":"America/Rankin_Inlet","America/Recife":"America/Recife","America/Regina":"America/Regina","America/Resolute":"America/Resolute","America/Rio_Branco":"America/Rio_Branco","America/Santarem":"America/Santarem","America/Santiago":"America/Santiago","America/Santo_Domingo":"America/Santo_Domingo","America/Sao_Paulo":"America/Sao_Paulo","America/Scoresbysund":"America/Scoresbysund","America/Sitka":"America/Sitka","America/St_Barthelemy":"America/St_Barthelemy","America/St_Johns":"America/St_Johns","America/St_Kitts":"America/St_Kitts","America/St_Lucia":"America/St_Lucia","America/St_Thomas":"America/St_Thomas","America/St_Vincent":"America/St_Vincent","America/Swift_Current":"America/Swift_Current","America/Tegucigalpa":"America/Tegucigalpa","America/Thule":"America/Thule","America/Thunder_Bay":"America/Thunder_Bay","America/Tijuana":"America/Tijuana","America/Toronto":"America/Toronto","America/Tortola":"America/Tortola","America/Vancouver":"America/Vancouver","America/Whitehorse":"America/Whitehorse","America/Winnipeg":"America/Winnipeg","America/Yakutat":"America/Yakutat","America/Yellowknife":"America/Yellowknife","Antarctica/Casey":"Antarctica/Casey","Antarctica/Davis":"Antarctica/Davis","Antarctica/DumontDUrville":"Antarctica/DumontDUrville","Antarctica/Macquarie":"Antarctica/Macquarie","Antarctica/Mawson":"Antarctica/Mawson","Antarctica/McMurdo":"Antarctica/McMurdo","Antarctica/Palmer":"Antarctica/Palmer","Antarctica/Rothera":"Antarctica/Rothera","Antarctica/Syowa":"Antarctica/Syowa","Antarctica/Troll":"Antarctica/Troll","Antarctica/Vostok":"Antarctica/Vostok","Arctic/Longyearbyen":"Arctic/Longyearbyen","Asia/Aden":"Asia/Aden","Asia/Almaty":"Asia/Almaty","Asia/Amman":"Asia/Amman","Asia/Anadyr":"Asia/Anadyr","Asia/Aqtau":"Asia/Aqtau","Asia/Aqtobe":"Asia/Aqtobe","Asia/Ashgabat":"Asia/Ashgabat","Asia/Atyrau":"Asia/Atyrau","Asia/Baghdad":"Asia/Baghdad","Asia/Bahrain":"Asia/Bahrain","Asia/Baku":"Asia/Baku","Asia/Bangkok":"Asia/Bangkok","Asia/Barnaul":"Asia/Barnaul","Asia/Beirut":"Asia/Beirut","Asia/Bishkek":"Asia/Bishkek","Asia/Brunei":"Asia/Brunei","Asia/Chita":"Asia/Chita","Asia/Choibalsan":"Asia/Choibalsan","Asia/Colombo":"Asia/Colombo","Asia/Damascus":"Asia/Damascus","Asia/Dhaka":"Asia/Dhaka","Asia/Dili":"Asia/Dili","Asia/Dubai":"Asia/Dubai","Asia/Dushanbe":"Asia/Dushanbe","Asia/Famagusta":"Asia/Famagusta","Asia/Gaza":"Asia/Gaza","Asia/Hebron":"Asia/Hebron","Asia/Ho_Chi_Minh":"Asia/Ho_Chi_Minh","Asia/Hong_Kong":"Asia/Hong_Kong","Asia/Hovd":"Asia/Hovd","Asia/Irkutsk":"Asia/Irkutsk","Asia/Jakarta":"Asia/Jakarta","Asia/Jayapura":"Asia/Jayapura","Asia/Jerusalem":"Asia/Jerusalem","Asia/Kabul":"Asia/Kabul","Asia/Kamchatka":"Asia/Kamchatka","Asia/Karachi":"Asia/Karachi","Asia/Kathmandu":"Asia/Kathmandu","Asia/Khandyga":"Asia/Khandyga","Asia/Kolkata":"Asia/Kolkata","Asia/Krasnoyarsk":"Asia/Krasnoyarsk","Asia/Kuala_Lumpur":"Asia/Kuala_Lumpur","Asia/Kuching":"Asia/Kuching","Asia/Kuwait":"Asia/Kuwait","Asia/Macau":"Asia/Macau","Asia/Magadan":"Asia/Magadan","Asia/Makassar":"Asia/Makassar","Asia/Manila":"Asia/Manila","Asia/Muscat":"Asia/Muscat","Asia/Nicosia":"Asia/Nicosia","Asia/Novokuznetsk":"Asia/Novokuznetsk","Asia/Novosibirsk":"Asia/Novosibirsk","Asia/Omsk":"Asia/Omsk","Asia/Oral":"Asia/Oral","Asia/Phnom_Penh":"Asia/Phnom_Penh","Asia/Pontianak":"Asia/Pontianak","Asia/Pyongyang":"Asia/Pyongyang","Asia/Qatar":"Asia/Qatar","Asia/Qyzylorda":"Asia/Qyzylorda","Asia/Riyadh":"Asia/Riyadh","Asia/Sakhalin":"Asia/Sakhalin","Asia/Samarkand":"Asia/Samarkand","Asia/Seoul":"Asia/Seoul","Asia/Shanghai":"Asia/Shanghai","Asia/Singapore":"Asia/Singapore","Asia/Srednekolymsk":"Asia/Srednekolymsk","Asia/Taipei":"Asia/Taipei","Asia/Tashkent":"Asia/Tashkent","Asia/Tbilisi":"Asia/Tbilisi","Asia/Tehran":"Asia/Tehran","Asia/Thimphu":"Asia/Thimphu","Asia/Tokyo":"Asia/Tokyo","Asia/Tomsk":"Asia/Tomsk","Asia/Ulaanbaatar":"Asia/Ulaanbaatar","Asia/Urumqi":"Asia/Urumqi","Asia/Ust-Nera":"Asia/Ust-Nera","Asia/Vientiane":"Asia/Vientiane","Asia/Vladivostok":"Asia/Vladivostok","Asia/Yakutsk":"Asia/Yakutsk","Asia/Yangon":"Asia/Yangon","Asia/Yekaterinburg":"Asia/Yekaterinburg","Asia/Yerevan":"Asia/Yerevan","Atlantic/Azores":"Atlantic/Azores","Atlantic/Bermuda":"Atlantic/Bermuda","Atlantic/Canary":"Atlantic/Canary","Atlantic/Cape_Verde":"Atlantic/Cape_Verde","Atlantic/Faroe":"Atlantic/Faroe","Atlantic/Madeira":"Atlantic/Madeira","Atlantic/Reykjavik":"Atlantic/Reykjavik","Atlantic/South_Georgia":"Atlantic/South_Georgia","Atlantic/St_Helena":"Atlantic/St_Helena","Atlantic/Stanley":"Atlantic/Stanley","Australia/Adelaide":"Australia/Adelaide","Australia/Brisbane":"Australia/Brisbane","Australia/Broken_Hill":"Australia/Broken_Hill","Australia/Currie":"Australia/Currie","Australia/Darwin":"Australia/Darwin","Australia/Eucla":"Australia/Eucla","Australia/Hobart":"Australia/Hobart","Australia/Lindeman":"Australia/Lindeman","Australia/Lord_Howe":"Australia/Lord_Howe","Australia/Melbourne":"Australia/Melbourne","Australia/Perth":"Australia/Perth","Australia/Sydney":"Australia/Sydney","Europe/Amsterdam":"Europe/Amsterdam","Europe/Andorra":"Europe/Andorra","Europe/Astrakhan":"Europe/Astrakhan","Europe/Athens":"Europe/Athens","Europe/Belgrade":"Europe/Belgrade","Europe/Berlin":"Europe/Berlin","Europe/Bratislava":"Europe/Bratislava","Europe/Brussels":"Europe/Brussels","Europe/Bucharest":"Europe/Bucharest","Europe/Budapest":"Europe/Budapest","Europe/Busingen":"Europe/Busingen","Europe/Chisinau":"Europe/Chisinau","Europe/Copenhagen":"Europe/Copenhagen","Europe/Dublin":"Europe/Dublin","Europe/Gibraltar":"Europe/Gibraltar","Europe/Guernsey":"Europe/Guernsey","Europe/Helsinki":"Europe/Helsinki","Europe/Isle_of_Man":"Europe/Isle_of_Man","Europe/Istanbul":"Europe/Istanbul","Europe/Jersey":"Europe/Jersey","Europe/Kaliningrad":"Europe/Kaliningrad","Europe/Kiev":"Europe/Kiev","Europe/Kirov":"Europe/Kirov","Europe/Lisbon":"Europe/Lisbon","Europe/Ljubljana":"Europe/Ljubljana","Europe/London":"Europe/London","Europe/Luxembourg":"Europe/Luxembourg","Europe/Madrid":"Europe/Madrid","Europe/Malta":"Europe/Malta","Europe/Mariehamn":"Europe/Mariehamn","Europe/Minsk":"Europe/Minsk","Europe/Monaco":"Europe/Monaco","Europe/Moscow":"Europe/Moscow","Europe/Oslo":"Europe/Oslo","Europe/Paris":"Europe/Paris","Europe/Podgorica":"Europe/Podgorica","Europe/Prague":"Europe/Prague","Europe/Riga":"Europe/Riga","Europe/Rome":"Europe/Rome","Europe/Samara":"Europe/Samara","Europe/San_Marino":"Europe/San_Marino","Europe/Sarajevo":"Europe/Sarajevo","Europe/Saratov":"Europe/Saratov","Europe/Simferopol":"Europe/Simferopol","Europe/Skopje":"Europe/Skopje","Europe/Sofia":"Europe/Sofia","Europe/Stockholm":"Europe/Stockholm","Europe/Tallinn":"Europe/Tallinn","Europe/Tirane":"Europe/Tirane","Europe/Ulyanovsk":"Europe/Ulyanovsk","Europe/Uzhgorod":"Europe/Uzhgorod","Europe/Vaduz":"Europe/Vaduz","Europe/Vatican":"Europe/Vatican","Europe/Vienna":"Europe/Vienna","Europe/Vilnius":"Europe/Vilnius","Europe/Volgograd":"Europe/Volgograd","Europe/Warsaw":"Europe/Warsaw","Europe/Zagreb":"Europe/Zagreb","Europe/Zaporozhye":"Europe/Zaporozhye","Europe/Zurich":"Europe/Zurich","Indian/Antananarivo":"Indian/Antananarivo","Indian/Chagos":"Indian/Chagos","Indian/Christmas":"Indian/Christmas","Indian/Cocos":"Indian/Cocos","Indian/Comoro":"Indian/Comoro","Indian/Kerguelen":"Indian/Kerguelen","Indian/Mahe":"Indian/Mahe","Indian/Maldives":"Indian/Maldives","Indian/Mauritius":"Indian/Mauritius","Indian/Mayotte":"Indian/Mayotte","Indian/Reunion":"Indian/Reunion","Pacific/Apia":"Pacific/Apia","Pacific/Auckland":"Pacific/Auckland","Pacific/Bougainville":"Pacific/Bougainville","Pacific/Chatham":"Pacific/Chatham","Pacific/Chuuk":"Pacific/Chuuk","Pacific/Easter":"Pacific/Easter","Pacific/Efate":"Pacific/Efate","Pacific/Enderbury":"Pacific/Enderbury","Pacific/Fakaofo":"Pacific/Fakaofo","Pacific/Fiji":"Pacific/Fiji","Pacific/Funafuti":"Pacific/Funafuti","Pacific/Galapagos":"Pacific/Galapagos","Pacific/Gambier":"Pacific/Gambier","Pacific/Guadalcanal":"Pacific/Guadalcanal","Pacific/Guam":"Pacific/Guam","Pacific/Honolulu":"Pacific/Honolulu","Pacific/Kiritimati":"Pacific/Kiritimati","Pacific/Kosrae":"Pacific/Kosrae","Pacific/Kwajalein":"Pacific/Kwajalein","Pacific/Majuro":"Pacific/Majuro","Pacific/Marquesas":"Pacific/Marquesas","Pacific/Midway":"Pacific/Midway","Pacific/Nauru":"Pacific/Nauru","Pacific/Niue":"Pacific/Niue","Pacific/Norfolk":"Pacific/Norfolk","Pacific/Noumea":"Pacific/Noumea","Pacific/Pago_Pago":"Pacific/Pago_Pago","Pacific/Palau":"Pacific/Palau","Pacific/Pitcairn":"Pacific/Pitcairn","Pacific/Pohnpei":"Pacific/Pohnpei","Pacific/Port_Moresby":"Pacific/Port_Moresby","Pacific/Rarotonga":"Pacific/Rarotonga","Pacific/Saipan":"Pacific/Saipan","Pacific/Tahiti":"Pacific/Tahiti","Pacific/Tarawa":"Pacific/Tarawa","Pacific/Tongatapu":"Pacific/Tongatapu","Pacific/Wake":"Pacific/Wake","Pacific/Wallis":"Pacific/Wallis"},"trigger":{"action":"Do these","add_trigger":"Add Trigger","auto":"Auto","conditional_device":"Please choose conditions!","delete_trigger":"Are you sure to remove this trigger: ","execute":"is executed","manual":"Manual","no_trigger_input":"Please enter the name!","scene_condition":"Please select the Scene that you want the automation to take effect. If none selected, that will mean all are checked.","trigger_input":"Trigger name","virtual_device_help":"You can input a URL, when this trigger happens this URL will be called. Auto: this URL will be called automatically when event happens; Manual: this URL will be called manually by user, such as opening webcam.","when":"When these happen"},"tuya":{"audio":"Audio","camera_photo_saved":"Photo has been saved into system photo album successfully","camera_record_end":"Video has been saved into system photo album successfully","direction":"Direction","hd":"HD/SD","high":"High","low":"Low","middle":"Middle","motion":"Motion","motion_sensitivity":"Motion sensitivity","motion_switch":"Motion detection","motion_tracking":"Motion tracking","normal":"Normal","ok_s":"OK","pir":"PIR","pir_switch":"PIR detection","record":"Record","screenshot":"Screenshot","sd_error":"Error","sd_formating":"Formating","sd_no":"No SD card","sd_state":"Onboard recording","sd_storage":"SD storage","sd_storage_error":"Storage space is full","sound_sensitivity":"Sound sensitivity","sound_switch":"Sound detection","souud":"Sound","speak":"Speak","wifi_signal":"WiFi signal"},"user":{"action":"Actions","add_user":"Add User","add_users":"Please enter user email","admin":"Administrator","country":"Country/Region","de":"Germany","delete_user":"Remove user","edit":"Change Access","en":"United States","es":"Spain","fr":"France","not_country":"Country/Region can not be empty!","not_user_input":"Please enter your email.","not_user_room":"Please select one room at least!","pt":"Portugal","ru":"Russia","user":"User","zh":"China"},"views":{"pagination":{"first":"&laquo; First","last":"Last &raquo;","next":"Next &rsaquo;","previous":"&lsaquo; Prev","truncate":"&hellip;"}},"wifi":{"add_desp":"Adding your controller, one moment please...","add_error":"Failed, please press the button below to try again.","add_success":"Your controller has been added successfully.","add_title":"WIFI has been configured successfully.","add_tuya_wifi":"Please connect your phone to the Wi-Fi hotspot of the network device and click OK","ipc_setting_desp":"Setting up your camera WIFI, one moment please...","ipc_setting_error":"Please go to your cellphone System Setting and connect your WIFI to access point newway manually, then press the button below to try again!","next_wifi_desp":"During the next step, your cellphone may prompt to switch to cellular network or keep using the WiFi without internet connection, please choose to keep using the WiFi.","reload_desp":"Your controller will be restarted soon, it will take up to 30 seconds.","retry":"Retry","setting_desp":"Setting up your controller WIFI, one moment please...","setting_error":"Please go to your cellphone System Setting and connect your WIFI to access point PRESEN_CONTROLLER manually, then press the button below to try again!","wifi_link_error":"Can\'t connect to your controller and WiFi setup failed, you can restart your controller and enter WiFi Setup mode, then try again.","wired_net_add":"Add your controller","wired_net_btn":"Add controller manually","wired_net_desp":"Please plug in the ethernet cable to your controller, then plug in the power cable, and make sure your cellphone and your controller are in the same LAN network. "}}'));
I18n.translations.es = I18n.extend((I18n.translations.es || {}), JSON.parse('{"action":{"action":"Comportamiento","action_device":"Por favor, configure los dispositivos de destino！","action_input":"Nombre de la acción no puede estar vacío！","action_name":"Nombre de la acción","activate":"Activar","add_action":"Añadir acción","add_action_type":"Por favor ingrese el nombre de la acción:","add_favorite":"Agregar a atajos?","change_action":"Activa esta acción:","current_action":"Acción actual","delete_action":"¿Estás seguro de eliminar esta acción?","favorite":"ACCIONES FAVORITAS","remove_favorite":"Eliminar de accesos directos"},"automation":{"automation_type":"Tipo de automatización","by_sunrise":"Al amanecer/atardecer","city":"Ciudad","current_located_city":"Ciudad local actual","current_location":"Ubicación actual","do":"Hacer","if":"Si","locating":"Localizando...","new_automation":"Nueva automatización","only_app":"Solo soporte en la aplicación Presen","relocate":"Trasladarse","select_conditions":"Seleccionar condiciones","select_location":"Seleccionar ubicación","select_target_type":"Seleccionar tipo de objetivo","sunrise":"amanecer","sunset":"Puesta de sol"},"dashboard":{"change_pin":"Cambiar PIN","enter_pin":"Ingrese el PIN de su controlador","set_pin":"Configura el PIN de tu controlador","show_password":"Mostrar contraseña"},"date":{"abbr_day_names":["dom","lun","mar","mié","jue","vie","sáb"],"abbr_month_names":[null,"ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],"day_names":["domingo","lunes","martes","miércoles","jueves","viernes","sábado"],"formats":{"default":"%-d/%-m/%Y","long":"%-d de %B de %Y","short":"%-d de %b"},"month_names":[null,"enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"],"order":["day","month","year"]},"datetime":{"distance_in_words":{"about_x_hours":{"one":"alrededor de 1 hora","other":"alrededor de %{count} horas"},"about_x_months":{"one":"alrededor de 1 mes","other":"alrededor de %{count} meses"},"about_x_years":{"one":"alrededor de 1 año","other":"alrededor de %{count} años"},"almost_x_years":{"one":"casi 1 año","other":"casi %{count} años"},"half_a_minute":"medio minuto","less_than_x_minutes":{"one":"menos de 1 minuto","other":"menos de %{count} minutos"},"less_than_x_seconds":{"one":"menos de 1 segundo","other":"menos de %{count} segundos"},"over_x_years":{"one":"más de 1 año","other":"más de %{count} años"},"x_days":{"one":"1 día","other":"%{count} días"},"x_minutes":{"one":"1 minuto","other":"%{count} minutos"},"x_months":{"one":"1 mes","other":"%{count} meses"},"x_seconds":{"one":"1 segundo","other":"%{count} segundos"},"x_years":{"one":"1 año","other":"%{count} años"}},"prompts":{"day":"Día","hour":"Hora","minute":"Minutos","month":"Mes","second":"Segundos","year":"Año"}},"device":{"FLiRS":"Dispositivo FLiRS","battery":"Batería","battery_operated":"Mando a distancia con pilas","camera":"cámara IP","camera_image":"Enlace de imagen de la cámara IP","camera_image_desp":"Por favor ingrese la URL de captura de pantalla de su cámara IP. Si esta URL necesita autenticación para acceder, también debe especificar el nombre de usuario y la contraseña en esta URL, es posible que deba consultar el manual de su cámara IP para obtener esto.","camera_name":"Nombre de la cámara IP","camera_name_error":"El nombre de la cámara IP no puede estar vacío","camera_screenshot_url_error":"El enlace de imagen de la cámara IP no puede estar vacío","camera_video":"Enlace de video de cámara IP","camera_video_desp":"Ingrese la URL de la transmisión de video de su cámara IP y seleccione el formato de video correcto. Si esta URL necesita autenticación para acceder, también debe especificar el nombre de usuario y la contraseña en esta URL, es posible que deba consultar el manual de su cámara IP para obtener esto.","camera_video_url_error":"El enlace de video de la cámara IP no puede estar vacío","change_color":"Cambiar el color","cloud_add_camera":"Agregar una cámara IP, por favor agregue en el controlador","conf_apply_battery":"Debemos despertar para activar la configuración del dispositivo de la batería se han cambiado","delay":"retrasado","delete_field_device":"Eliminar como dispositivo fallido","delete_type":"¿Qué tipo de dispositivo que desea eliminar？","device":"equipo","device_active":"El dispositivo está activo","device_dead":"El dispositivo está muerto","device_information":"Información del dispositivo","device_interviewed":"El dispositivo es entrevistado","device_not_interviewed":"El dispositivo no es totalmente entrevistado","device_operating":"El dispositivo está funcionando","device_sleeping":"El dispositivo está durmiendo","edit_device":"Modificar el nombre del dispositivo","force_interview":"Fuerza Entrevista","full":"todos","interview":"Entrevista","is_awake":"Está despierto","is_sleeping":"Está durmiendo","main":"Principal alimentado","mark_failed":"Marcados con error","mark_failed_alert":"Los controles controlador de nodo si el claro, entonces se marca como fracaso. Este proceso tarda unos 30 segundos. Entonces, si es necesario, puede utilizar la función de \'Borrar\' para eliminar los nodos.","mark_failed_delete":"¿Está seguro de que quiere borrar el dispositivo? Para eliminar un nodo de controlador de red, sin necesidad de más medidas. Este proceso puede tardar un minuto.","no_device_name":"El nombre del dispositivo no puede estar vacía!","off_b":"cerrar","on_b":"abierto","operating":"Operando","operating_time":"Tiempo de funcionamiento","play":"Jugar","remove_camera_device_hint":"Para eliminar un dispositivo de cámara IP, vaya a la página de detalles del dispositivo (haga clic en el nombre del dispositivo para ingresar) para eliminar","remove_directly":"Eliminar directamente","seconds":"segundo","set_to":"establecer","setting":"establecer","setup_device":"Por favor, configurar el dispositivo de destino","value":"valor","value_changed_to":"Cambiar el valor","value_not_stored_indevice":"Pero no en la memoria del dispositivo","when":"cuando"},"errors":{"format":"%{attribute} %{message}","messages":{"accepted":"debe ser aceptado","blank":"no puede estar en blanco","confirmation":"no coincide","empty":"no puede estar vacío","equal_to":"debe ser igual a %{count}","even":"debe ser par","exclusion":"está reservado","greater_than":"debe ser mayor que %{count}","greater_than_or_equal_to":"debe ser mayor que o igual a %{count}","inclusion":"no está incluido en la lista","invalid":"no es válido","less_than":"debe ser menor que %{count}","less_than_or_equal_to":"debe ser menor que o igual a %{count}","model_invalid":"La validación falló: %{errors}","not_a_number":"no es un número","not_an_integer":"debe ser un entero","odd":"debe ser impar","other_than":"debe ser distinto de %{count}","present":"debe estar en blanco","required":"debe existir","taken":"ya está en uso","too_long":{"one":"es demasiado largo (1 carácter máximo)","other":"es demasiado largo (%{count} caracteres máximo)"},"too_short":{"one":"es demasiado corto (1 carácter mínimo)","other":"es demasiado corto (%{count} caracteres mínimo)"},"wrong_length":{"one":"no tiene la longitud correcta (1 carácter exactos)","other":"no tiene la longitud correcta (%{count} caracteres exactos)"}},"template":{"body":"Se encontraron problemas con los siguientes campos:","header":{"one":"No se pudo guardar este/a %{model} porque se encontró 1 error","other":"No se pudo guardar este/a %{model} porque se encontraron %{count} errores"}}},"event":{"inner":"interior","my_device":"mi dispositivo","my_room":"mi habitación","open_app":"Abre la app","outer":"exterior"},"global":{"account":"CONFIGURACIÓN DE CUENTA","account_setting":"Configuración de la cuenta","activate_action":"¿Ejecutar esta acción?","activate_scene":"¿Activar esta escena?","activate_sure":"¿Estás seguro de hacer esto?","add_device_s2_desp":"Ingrese los primeros cinco dígitos de la clave del dispositivo, puede consultar el manual del dispositivo para obtener la clave del dispositivo","add_device_s2_title":"Presione los botones en el dispositivo siguiendo el manual del dispositivo para agregar el dispositivo","add_device_wait":"Por favor espere","add_smart_desp":"Ingrese la clave completa del dispositivo, que debe tener 40 dígitos, puede consultar el manual del dispositivo para obtener la clave del dispositivo","add_zigbee_smart_desp":"Ingrese la dirección MAC del dispositivo y el código de instalación, ambos deben estar impresos en el contenedor de su dispositivo o en su manual de instrucciones. Las direcciones MAC tienen 16 caracteres y el código de instalación es 36.","advanced_setting":"Configuración avanzada","agree":"Estoy de acuerdo con la","alexa_link_error":"Falló el enlace con Alexa, ¡inténtalo de nuevo!","alexa_link_success":"Se ha vinculado con Alexa y ha habilitado la habilidad Presen Smart Home.","all":"Todos","auto":"Automatización","away":"Lejos","by_device":"Por cambios de dispositivo","centigrade":"Centígrado","change_datacenter":"Cambiar el centro de datos del controlador","choose_alarm_ringtone":"To choose this will play an alarm sound on your controller, you need to press the main button on the controller to dismss the alarm","choose_ringtone":"Elegir tono de llamada","click_help":"Consultar información de ayuda","click_to_find_more":"Haga clic para encontrar más","cloud_required":"Necesita conectarse a la conexión a Internet para usar esta función, actualmente se está conectando a ethernet.","community":"comunidad","connecting":"Conectando...","controller_setting":"CONFIGURACIÓN DEL CONTROLADOR","copyright_policy":"política de derechos de autor","dashboard":"Tablero","datacenter_setting":"Configuración del centro de datos","default":"Predeterminado","device_failed":"Este dispositivo no responde, puede intentar apagarlo, encenderlo / apagarlo o activarlo para probar si funciona mal o tiene poca batería. Si no responde durante mucho tiempo, debe intentar restablecerlo o eliminarlo.","device_not_support":"Este atributo no es compatible con esto!","download_alert":"Enviado a su buzón registrado","dsk_error":"La clave del dispositivo no es válida","dsk_input":"La clave del dispositivo debe tener 40 dígitos","enter_your":"Por favor introduzca su","error_desp":"Es posible que tengamos un problema. ¡Inténtalo de nuevo!","error_title":"Lo sentimos, la conexión falló","every_day":"Todos los días","fahrenheit":"Fahrenheit","grpc_link_error":"Falló la conexión, actualice para volver a intentarlo","grpc_unlink":"Falló la conexión, intentando volver a conectar ...","home":"Inicio","i_agree":"Necesitas aceptar el","input_key":"Clave del dispositivo de entrada","input_ssid":"Ingrese SSID o conecte wifi","input_wifi_password":"Por favor, introduzca la contraseña wifi!","ins_key":"Instalar clave","is_alive":"IS Alive","link_with_alexa":"Enlace con Alexa","login_demo":"iniciar sesión con cuenta demo","logout":"cerrar sesión","my_smarthome":"Mi casa inteligente","need_login":"Por favor inicie sesión ahora","no_data":"¡sin datos!","off_sn":"Controle y organice dispositivos dentro de un controlador.","on_sn":"Cuando está activado, puede administrar todos los dispositivos de los controladores al mismo tiempo, y todos los dispositivos pueden trabajar juntos, por ejemplo, puede crear una Acción para controlar dispositivos múltiples con diferentes controladores.","open_license":"Licencia de código abierto","open_wifi":"Abrir WIFI","or":"o","privacy":"Términos de servicio","privacy_statement":"declaracion de privacidad","quick":"Atajos","release_log":"Actualizar registro","remove_device_title":"Presione los botones en el dispositivo siguiendo el manual del dispositivo para eliminar el dispositivo","s2_add":"S2 Agregar","s2_device":"Dispositivo S2","scan_key":"Escanear clave de dispositivo","scene":"Escena","scene_delete":"La escena por defecto no puede ser eliminada","select_controller":"seleccionar controlador","select_this_controller":"Cambiar a este controlador","service":"Servicios","setting_wifi":"Configurar WIFI","setting_wifi_desp":"WIFI 5G y WIFI en restaurante y aeropuerto requieren autenticación basada en web no es compatible","sleep":"Dormir","sn_global":"modo global","sn_normal":"modo de controlador único","sn_setting":"Gestión del controlador","ssid":"SSID","target":"Objetivo","temperature_setting":"Configuraciones de temperatura","terms_of_service":"términos de servicio","time_out":"¡Tiempo de espera de operación, por favor intente nuevamente!","timezone_setting":"Configuración de zona horaria","type":"Tipo","use":"utilizar","user_role_desp":"Puede compartir el acceso del controlador actual con otras personas, simplemente ingrese el correo electrónico que desea compartir aquí. Las personas con las que compartió solo pueden usar sus datos, como controlar sus dispositivos, activar escenas, ejecutar acciones, pero no pueden realizar cambios en sus datos, como agregar o quitar dispositivos, crear o editar escenas, etc. no puede reiniciar o reiniciar su controlador. Después de agregar el correo electrónico, el otro usuario debe reiniciar la aplicación para que surta efecto.","vacation":"Vacaciones","wifi_connection":"conexión WIFI","wifi_link_btn":"El led de red está encendido","wifi_link_desp":"Presione el botón superior en el controlador hasta que el led de red esté siempre encendido para continuar","wifi_password":"contraseña WIFI","wired_connection":"Conexión por cable","zwave_delete_db":"si este dispositivo se pierde o funciona mal, puede eliminarlo directamente. Aparecerá nuevamente si continúa comunicándose con su controlador","zwave_delete_field":"Elimine este dispositivo fallido, esta operación puede fallar si este dispositivo no se reconoce como un dispositivo fallido"},"guard":{"enter_guard_mode":"Ingrese al modo de guardia","exit_guard_mode":"Salga del modo de guardia","guard_h1":"Presen puede ayudarlo a proteger su hogar cuando no está","guard_h2":"ALERTAS INTELIGENTES","guard_h2_deps":"Cuando cambia al modo Guardia, Presen puede enviarle notificaciones si se activa el sensor de movimiento o el sensor de humo o se abre la puerta.","guard_h3":"ILUMINACIÓN LEJOS","guard_h3_deps":"Presen puede encender y apagar las luces automáticamente para que parezca que alguien está en casa cuando usted no está.","guard_mode":"Modo de guardia"},"help":{"cookie_hint":"Este sitio utiliza cookies para mejorar tu experiencia de usuario. Información detallada sobre el uso de cookies en este sitio web se proporciona en nuestra %s. Mediante el uso de este sitio web, usted da su consentimiento para el uso de cookies. %s","privacy":"Privacidad","yes_i_agree":"Sí, estoy de acuerdo"},"helpers":{"select":{"prompt":"Por favor seleccione"},"submit":{"create":"Crear %{model}","submit":"Guardar %{model}","update":"Actualizar %{model}"}},"home":{"433_device":"433 equipos","a":"más","add_433_device":"Añadir dispositivo 433M","add_433_node_type_doorlock":"Doorlock","add_433_node_type_other":"Others","add_433_node_type_pir":"PIR","add_433_node_type_smoke_sensor":"Smoke Sensor","add_433_node_type_water_sensor":"Water Sensor","add_433_waiting_for_ports":"Comprobando el puerto serie que soporta el controlador, asegúrese de que su controlador tiene puertos USB de serie conectados ...","add_controller":"Añadir controlador","add_device":"Agregar dispositivo","add_device_433_progress_error":"No se encontró ningún puerto 433M en su controlador","add_device_433_progress_loading":"Comprobación","add_device_type":"¿Qué tipo de dispositivo que desea agregar？","add_qiock":"Add to Quick Control","add_success":"Añadir el éxito！","cancel":"cancelado","card_1":"estado de funcionamiento del controlador","card_2":"tiempo de funcionamiento del controlador","card_3":"Número de dispositivos","cellphone":"número de teléfono","change_controller":"Cambio de controlador","confirm":"confirmar","controller":"controlador","controller_information":"Información del controlador","controller_input":"nombre del controlador","controller_sn_enter":"Por favor, introduzca el número de serie del controlador","controller_state":"Estado del controlador","current_controller":"El controlador de corriente","day":"día","delete_success":"Eliminado correctamente!","detect_device":"equipos de ensayo","device":"equipo","device_input":"Nombre del dispositivo","device_management":"Gestión de dispositivos","device_name":"Nombre del dispositivo","edit":"editar","edit_controller":"Modificar el nombre del controlador","edit_device":"Modificar el nombre del dispositivo","edit_success":"Modificar el éxito！","email":"buzón","error":"error","event":"noticias","failed":"La operación ha fallado！","favorite":"ESCENAS FAVORITAS","forget_password":"¿Ha olvidado su contraseña","good_afternoon":"Buenas tardes","good_evening":"Buena noches","good_morning":"Buenos días","have_notify":"Tienes un evento sin marcar","have_version":"Hay una nueva versión！","home":"casa","indoor_humidity":"Humedad interior","indoor_temp":"Temperatura interior","ip":"IP","language":"idioma","language_for_mobile":"idioma","local_network":"LAN","login_name":"E-mail o número de teléfono","name":"nombre","network_issue":"Problema de red","network_issue_desp":"Su red es demasiado lenta, puede intentar reiniciar la aplicación o cambiar a una red rápida","next":"El siguiente paso","no_controller":"No hay datos de controlador disponibles, primero debe agregar un controlador en la aplicación Presen, encender su controlador y conectarlo a Internet, luego esperar unos segundos para actualizar la pantalla.","no_controller_input":"El nombre del controlador no puede estar vacía！","no_device":"No hay información interior","not_device_input":"El nombre del dispositivo no puede estar vacía！","not_user":"El usuario no existe！","off_line":"desconectado","on_line":"En línea","password":"contraseña","profile":"configuración","qiock_control":"Quick control","recent_event":"Los acontecimientos recientes","recently_devices":"Dispositivos recientes","refresh":"Actualiza el controlador","remove_433_device_hint":"Puede ir a la página de detalles del dispositivo (haciendo clic en el nombre del dispositivo) para eliminar el dispositivo 433M.","remove_btn":"borrar","remove_device":"eliminar dispositivo","remove_qiock":"Remove from Quick Control","room":"habitación","routine":"Rutinas","save":"presentar","scene":"escena","select_433_device":"Se han detectado los siguientes dispositivos, seleccione el dispositivo que desea agregar","select_433_node_type":"Selecciona el tipo de dispositivo para este dispositivo","select_433_port":"Seleccione el dispositivo serie que desea agregar a","select_433_protocol":"Seleccione el protocolo que su dispositivo admite","setting":"establecer","setting_btn":"establecer","sign":"acceder","sign_out":"dejar","sign_up":"registro","sn":"Número de serie","sn_error":"número de serie no existe！","sn_used":"El número de serie ya está en uso！","state":"estado","success":"éxito","successful":"El funcionamiento satisfactorio!","time_zone":"Elija su zona horaria","token":"código de verificación","trigger":"vínculo","try_again":"Inténtelo de nuevo más tarde！","user":"usuario","user_has":"El usuario ya existe！","user_user":"No se puede añadir su propio！","version":"El número de versión","voice":"Voz","vs_update":"Si actualizar de inmediato","warning_message":"rápido","wide_network":"WAN","z_device":"equipos Zwave"},"index":{"433m":"Z-Wave y RF regular","433m_desp":"Presen admite los sensores regulares de RF (433M, 345M, 319M) de algunas marcas famosas además de los dispositivos Z-Wave, y pueden trabajar juntos para desarrollar la seguridad y automatización de su hogar.","about":"sobre nosotros","about_about":"en","about_desp":"Presen es una casa inteligente más simple e inteligente. Enfocamos cientos de marcas confiables en una aplicación móvil, para que pueda monitorear, controlar y automatizar fácilmente su hogar inteligente donde sea que se encuentre.","about_desp_three":"Ahora imagina si no necesitas imaginar. Debido a que usa Presen, ya está disponible. Cosas como luces, puertas, aires acondicionados e interruptores ahora pueden funcionar mejor para usted, haciéndolo sentir más seguro, más controlado, más eficiente y más feliz. Con la tecnología Presen, sus propias necesidades y creatividad impulsarán posibilidades ilimitadas.","about_desp_two":"Piénselo si sabe cuándo no puede ir a su casa y cómo puede estar tranquilo. Imagínese si siempre supieran lo que necesitaban y cuándo era necesario. Imagina si supiera que te conocías mejor que tú.","about_name":"Acerca de Presen","about_our":"Nos centramos en el hogar inteligente","about_title":"Qué es Presen","alerting":"AVISO 24/7","alerting_desp":"Las funciones Presen Events and Notifications pueden personalizarse fácilmente para enviar mensajes de emergencia y notificación a su teléfono móvil","ali_desp":"Control por voz en los dispositivos seriales de 天猫精灵, controle su hogar solo con su voz","app":"APP","automation":"FÁCIL AUTOMATIZACIÓN","automation_desp":"Con el soporte Triggers and Scenes, puede crear muchos tipos de escenarios de acuerdo a sus necesidades","build":"Construir la mejor casa inteligente","change":"cambio","contact_address":"dirección","for_live":"La vida en","get_app":"Obtener aplicaciones gratuitas","has_account":"Ya tiene su cuenta？","help_you":"Le ayudan a gestionar toda la casa","home":"casa","no_account":"No tiene cuenta？","or_sign_up":"o registrarse","or_sign_up_type":"O utilizar su","our":"nosotros","our_app":"mi App","product":"Característica","product_desp":"En el camino, desde su oficina o mientras está acostado en una playa tropical, puede controlar su hogar en cualquier lugar. Puede tener el control total de su casa desde la pantalla de su PC, teléfono inteligente o cualquier otro dispositivo habilitado para Internet.","product_desp_1":"introducción de largo","product_name":"Completa control y automatización del hogar","product_name_1":"El primer producto","product_title":"Plataforma de automatización completa que incluye controladores locales, servidores en la nube, aplicaciones de usuario y administración que funcionan en conjunto sin problemas.","product_title_1":"breve introducción","remote":"CONTROL REMOTO","remote_desp":"Controle su casa desde su teléfono inteligente o cualquier otro dispositivo habilitado para Internet, incluso sin conexión a Internet en su LAN, con conexión segura a Internet","routines":"RUTINAS","routines_desp":" Presen Routines le permite crear fácilmente sus propios escenarios de automatización","security":"MEJOR SEGURIDAD","security_desp":"Presen le ofrece una nueva generación de protección de seguridad y protección del hogar","sharing":"COMPARTIR EL HOGAR","sharing_desp":"Todos los miembros de su familia pueden tener sus propias cuentas para controlar su hogar con diferentes accesos","voice":"CONTROL DE VOZ","voice_desp":"Control por voz en los dispositivos seriales de Amazon Echo, controle su hogar solo con su voz","we_well":"Vamos a crear una economía inteligente de su casa","work_with":"obras Presen"},"mongoid":{"attributes":{"c/action":{"name":"Nombre de acción"},"c/node":{"c_display_name":"Nombre del dispositivo"},"c/scene":{"name":"Nombre de la escena"}},"errors":{"format":"%{attribute} %{message}","messages":{"accepted":"debe ser aceptado","blank":"no puede estar en blanco","confirmation":"no coincide","empty":"no puede estar vacío","equal_to":"debe ser igual a %{count}","even":"debe ser par","exclusion":"está reservado","greater_than":"debe ser mayor que %{count}","greater_than_or_equal_to":"debe ser mayor que o igual a %{count}","inclusion":"no está incluido en la lista","invalid":"no es válido","less_than":"debe ser menor que %{count}","less_than_or_equal_to":"debe ser menor que o igual a %{count}","model_invalid":"La validación falló: %{errors}","not_a_number":"no es un número","not_an_integer":"debe ser un entero","odd":"debe ser impar","other_than":"debe ser distinto de %{count}","present":"debe estar en blanco","record_invalid":"La validación falló: %{errors}","required":"debe existir","restrict_dependent_destroy":{"has_many":"No se puede eliminar el registro porque existen %{record} dependientes","has_one":"No se puede eliminar el registro porque existe un %{record} dependiente"},"taken":"ya está en uso","too_long":{"one":"es demasiado largo (1 carácter máximo)","other":"es demasiado largo (%{count} caracteres máximo)"},"too_short":{"one":"es demasiado corto (1 carácter mínimo)","other":"es demasiado corto (%{count} caracteres mínimo)"},"wrong_length":{"one":"no tiene la longitud correcta (1 carácter exactos)","other":"no tiene la longitud correcta (%{count} caracteres exactos)"}}}},"number":{"currency":{"format":{"delimiter":".","format":"%n %u","precision":2,"separator":",","significant":false,"strip_insignificant_zeros":false,"unit":"€"}},"format":{"delimiter":".","precision":3,"separator":",","significant":false,"strip_insignificant_zeros":false},"human":{"decimal_units":{"format":"%n %u","units":{"billion":"mil millones","million":{"one":"millón","other":"millones"},"quadrillion":"mil billones","thousand":"mil","trillion":{"one":"billón","other":"billones"},"unit":""}},"format":{"delimiter":"","precision":1,"significant":true,"strip_insignificant_zeros":true},"storage_units":{"format":"%n %u","units":{"byte":{"one":"Byte","other":"Bytes"},"gb":"GB","kb":"KB","mb":"MB","tb":"TB"}}},"percentage":{"format":{"delimiter":"","format":"%n %"}},"precision":{"format":{"delimiter":""}}},"room":{"add_room":"Añadir otra habitación","desp":"EVENTO significa que hay cambios de estado del dispositivo en esta sala en las últimas 24 horas.","device_in_room":"El equipo de la sala","event":"evento","filter_room":"Filtrar por habitaciones","normal":"normal","room_name":"Nombre de la sala","room_no_device":"No hay ningún dispositivo seleccionado！","room_no_name":"Nombre de la sala no puede estar vacía！","room_switch_hint":"Controlar todos los dispositivos de swtich en esta sala","setting_room":"Ajuste de la habitación"},"routine":{"add":"Agregar una rutina","address":"Dirección","cond_action":"Enciende una acción","cond_device":"Encienda los dispositivos","cond_scene":"Enciende una escena","edit":"Editar rutina","get_to":"Llegada","is_cyclic":"Repetir","last":"Espalda","leave":"Salir","one_cond":"Una vez","please_address":"Por favor seleccione la ubicación del GPS","please_date":"Por favor seleccione la fecha","please_leave":"Seleccione llegar o salir","please_time":"Por favor seleccione hora","please_to":"Seleccione lo que desea activar","please_type":"Por favor seleccione el tipo de rutina","routine":"Rutinas","routine_cond":"Cuando","routine_desp":"La rutina puede activar una base de Escena o Dispositivos en la hora o la ubicación del GPS (es necesario trabajar con la aplicación Presen)","routine_location":"Ubicación del GPS","routine_type":"Tipo de rutina","run_multiple":"Repetir","run_once":"Vez","select_action":"Seleccionar acción","select_device":"Seleccionar dispositivos","select_scene":"Seleccionar escena","time":"Hora","to_location":"Por ubicación de GPS","to_time":"A tiempo","ui":"Esta función debe funcionar con Presen App y Presen Cloud, visite https://www.presensmarthome.com"},"scene":{"activate":"activación","add_scene":"Añadir escena","add_scene_type":"Por favor, introduzca el nombre de la escena:","change_scene":"Ya sea para cambiar la escena","current_scene":"escena actual","delete_scene":"eliminar las escenas","scene_device":"Por favor, añadir un dispositivo！","scene_input":"nombre de la escena no puede estar vacía！","scene_name":"nombre de la escena"},"services":{"alexa":"Controle sus dispositivos y automatizaciones con dispositivos habilitados para Amazon Alexa.","alexa_desp":"Por ejemplo, \\"Alexa, turn on home scene\\".","ali":"Controla tus dispositivos y las automatizaciones usan TmallGenie.","ali_desp":"Como \\"天猫精灵, 打开卧室的灯 \\".","google":"Controla tus dispositivos y automatizaciones con los dispositivos compatibles con Google Assistant.","google_desp":"Como \\"Hey Google, activate home\\".","ifttt":"Trabaje con IFTTT para vincular con más servicios 3.","ifttt_desp":"Por ejemplo, cuando se activa el sensor de movimiento, recibirá una llamada."},"session":{"60_s":"Volver a intentar después de 60 segundos！","cellphone_input":" Por favor, introduzca su número de teléfono móvil!","change_password":"Cambiar contraseña","confir_input":"Por favor, confirme su contraseña！","email":"¡El buzón ya existe!","email_input":"Por favor, introduzca el correo electrónico！","email_or_phone":"E-mail o número de teléfono","email_type":"formato de correo electrónico no es correcta！","get_token":"código de verificación","is_cellphone":"número de teléfono ya existe！","is_email":"E-mail ya existe！","login_name_input":"Por favor, acceder al número de correo o teléfono","new_input":"Por favor, introduzca una nueva contraseña！","new_password":"nueva contraseña","no_user_name":"Nombre de usuario no puede estar vacía！","not_eamil":"Buzón no existe！","not_email_or_phone":"Por favor, acceder a su correo electrónico o número","not_phone":"El número de teléfono no existe！","not_token":"Código de verificación no es correcta！","old_error":"La contraseña antigua no es correcta！","old_input":"Por favor, introduzca su contraseña antigua！","old_password":"Contrsñ","password_confirmation":"confirmar contraseña","password_input":"Por favor, introduzca su contraseña","password_length":"La contraseña no puede ser inferior a seis！","please_enter_password":"Por favor, introduzca su contraseña","register":"registro","reset_password":"Restablecer contraseña","send":"enviar","send_eamil":"Enviar código de verificación para el correo electrónico o número","send_token":"Se ha enviado un token a su buzón, verifíquelo.","signup_success_login":"Cuenta creada correctamente, ingresa ahora","token_input":"Se ha enviado un código de verificación a su correo electrónico, verifique e ingrese su código de verificación a continuación para continuar!","token_send":"Código de verificación mandó correctamente！","two_password":"Las dos contraseñas no coinciden！","user_name":"Nombre de usuario","wrong_name":"nombre de usuario o contraseña es incorrecta！"},"setting":{"add":"Agregado","advanced_setting":"Configuración avanzada","backup":"Controlador de copia de seguridad","backup_desp":"Controlador de copia de seguridad","backup_message":"Por favor, no apague el controlador de respaldo de energía。。。","base":"Configuración básica","change_notification":"Notificación de cambio","check_device":"Por favor, seleccione los atributos de los dispositivos que no desea recibir las notificaciones de cambios.","check_device_for_urgent":"Sólo recibirá las notificaciones urgentes al seguir los cambios de dispositivos.","check_scene":"No reciben notificaciones cuando su casa se encuentra en las siguientes escenas marcadas, si no se ha seleccionado ninguna escena, significa que todas las notificaciones de cambio de dispositivo no será no enviar importa qué escena es.","check_scene_for_urgent":"Sólo recibirá las notificaciones urgentes cuando en las siguientes escenas.","close_account":"Cerrar cuenta","close_desp":"¡Los usuarios no pueden iniciar sesión después de cerrar la cuenta! Si necesita volver a abrir su cuenta, contáctenos!","download_data":"Descargar datos","download_desp":"Descargar datos personales","end_time":"Hora de finalización","friday":"viernes","inform":"darse cuenta","local_siren":"Sirena local","login_close_desp":"La cuenta ha sido cerrada. Para abrir una cuenta, ¡contáctenos!","monday":"lunes","not_end_time":"La hora de finalización está configurada para:","not_start_time":"Si no necesita tiempo, configure la hora de inicio para:","notify":"Centro de notificaciones","notify_by_device":"Dispositivos de notificación","notify_by_time":"No molestar","notify_desp":"Control global de la notificación, no recibirá notificaciones de cualquier si está apagado.","notify_device_noti_desp":"Usted no recibirá notificaciones de cambio de dispositivos siguientes","notify_dnd_desp":"El tiempo que no desea recibir notificaciones de configuración","notify_time":"Hora","notify_urgent_device":"Notificación urgente","notify_urgent_noti_desp":"Usted recibirá una notificación especial al cambian de estos dispositivos. (Notificación urgente ignorará la configuración de dispositivos de notificación y punto no molestar)","place_end_time":"Por favor selecciona la hora de finalización！","place_select_period":"Por favor selecciona el tiempo！","place_start_time":"Por favor, seleccione hora de inicio！","property":"Propiedades","record":"Restaurar Controller","record_desp":"Restaurar Controller","record_error":"El controlador no pudo restaurar! Por favor, inténtelo de nuevo！","record_message":"Por favor, no desconecte la alimentación para restablecer el control。。。","record_success":"El éxito de reducción de controlador！","reset":"controlador de reajuste","reset_desp":" El controlador se restablece a los valores de fábrica. Nota: Todas las habitaciones y otros datos se borrarán!","reset_message":"El controlador borrará todos los datos。。。。","reset_success":"Su controlador se ha reiniciado con éxito","reset_title":"Restablecer controlador?","restart":"controlador de reinicio","restart_desp":"Reiniciar el controlador, reiniciar tarda unos 5 minutos! Después de terminado el reinicio por favor, actualice la página","restart_message":"¿Seguro que desea reiniciar el controlador","restart_title":"Reiniciar el controlador?","saturday":"sábado","select_date":"Seleccione fecha","select_device_scene":"Por favor seleccione dispositivo o escena！","select_period":"período de selección","start_end":"¡La hora de finalización es menor que la hora de inicio!","starting_time":"Tiempo de empezar","sunday":"domingo","thursday":"jueves","tuesday":"martes","update_desp":"El proceso de actualización no apague la mejora de poder tarda unos 5 a 7 minutos! Después de que la actualización se ha completado por favor, actualice la página","update_desp_one":"Una nueva versión:","update_failed_in_progress":"Hay una actualización ya en progreso.","update_failed_other":"Actualización fallida, por favor intente nuevamente más tarde.","update_failed_up_to_date":"No es necesario actualizar, está actualizado.","update_online":"Actualizar controlador","update_title":"Actualizando ...","updated":"El sistema actual hasta la fecha, ninguna actualización!","updating":"Estamos actualizando。。。","upgrade_desp":"El controlador se está actualizando, no apague la alimentación del controlador, tardará unos 5 minutos en finalizar, depende de la velocidad de su red. El controlador se reiniciará cuando haya terminado y recibirá una notificación.","upgrade_success_to":"El controlador ha sido actualizado a","wednesday":"miércoles"},"spec":{"access_control":"Control de acceso","air_flow":"Flujo de aire","air_temperature":"Presión","alarm":"Alarma","alarm_s":"Alarma de inducción","angle_position":"Ángulo","app_url":"Desencadenar URL","appliance":"Electrodomésticos","atmospheric_pressure":"Presión","auto":"Automático","aux":"Equipo auxiliar","auxiliary":"Auxiliar","barometric_pressure":"Presión","battery":"Batería","burglar":"Antirrobo","camera":"Camara ip","clock":"Reloj","closed":"Cerrado","co":"Monóxido de carbono","co2":"Dióxido de carbono","co2_alarm":"Advertencia de dióxido de carbono","co2_level":"Nivel de dióxido de carbono","co_alarm":"Advertencia de monóxido de carbono","co_level":"Nivel de monóxido de carbono","color_switch":"Interruptor de color","contact_sensor":"Sensor de contacto","cool":"Refrigeración","current":"Flujo de agua","dew_point":"Punto de rocío","dimmer":"Atenuador","direction":"Dirección","distance":"Distancia","door_lock":"Cerradura de puerta","door_window":"Puertas y ventanas","electrical_conductivity":"Conductividad","electrical_resistivity":"Coeficiente de resistencia","emergency":"Emergencia","false":"No activado","fire_sensor":"Sensor de fuego","first":"El primero","flood":"Inundado","freeze":"Refrigeración","frequency":"Frecuencia","full":"Todo","general":"General","general_purpose":"Detección universal","general_purpose_alarm":"Alarma general","general_purpose_value":"Estado de propósito general","general_trigger":"Evento común","glass_break":"Sensor roto","going_to_low_battery":"va a quedarse sin batería","heat":"Calefacción","heat_alarm":"Alarma de calor","humidity":"Humedad","is":"El estado es","key_fob":"Llavero","keypad":"Teclado","loudness":"Volumen","low":"Bajo","luminance":"Luz","luminiscence":"Luz","main_powered":"Principal accionado","meter":"Instrumento de medición","moisture":"Lluvia","motion":"Detección de movimiento","motor":"Motor","multi_switch":"Interruptor multicanal","n_a":"N/A","no_b":"Normal","normal":"Normal","not_used":"Desconocido","off_b":"Apagado","on_b":"Abierto","open":"Abierto","power":"Poder","power_management":"Administración de energía","quake":"Terremoto","rain_rate":"Velocidad de lluvia","relative_humidity":"Humedad relativa","remote_ontrol":"Teledirigido","reserved":"Guárdelo","resume":"Currículum","return_first_alarm_on_supported_list":"Primera advertencia","rotation":"Girar","security_repeater":"Repetidor de seguridad","seismic_intensity":"Intensidad del terremoto","seismic_magnitude":"Magnitud","sensor":"Sensor","smoke":"Humo","smoke_alarm":"Advertencia de humo","smoke_test":"Prueba de humo","soil_temperature":"Temperatura del aceite","solar_radiation":"Radiación solar","switch":"Cambiar","switch_all":"Todo el cambio","switch_to":"cambiar a","system":"Sistema","tamper":"Sabotaje","tank_capacity":"Capacidad","target_temperature":"Temperatura objetivo","temperature":"Temperatura","thermostat":"Termostato","thermostat_mode":"Modo de aire acondicionado","thermostat_setpoint":"Establecer la temperatura","tide_level":"Marea","tilt":"Inclinado","time":"Tiempo","true":"Disparado","ultraviolet":"UV","velocity":"Speed","ventilation":"Equipo de ventilación","vibration":"Vibración","voltage":"Voltaje","water":"Agua","water_leak":"Inundado","water_leak_alarm":"Alarma de inundación","water_temperature":"Temperatura del agua","weight":"Peso","yes_b":"Disparado"},"support":{"array":{"last_word_connector":" y ","two_words_connector":" y ","words_connector":", "}},"time":{"am":"am","formats":{"default":"%A, %-d de %B de %Y %H:%M:%S %z","long":"%-d de %B de %Y %H:%M","short":"%-d de %b %H:%M"},"pm":"pm"},"timezones":{"Abu Dhabi":"Abu Dhabi","Adelaide":"Adelaida","Alaska":"Alaska","Almaty":"Almaty","American Samoa":"Samoa Americana","Amsterdam":"Amsterdam","Arizona":"Arizona","Astana":"Astana","Athens":"Atenas","Atlantic Time (Canada)":"Hora del Atlántico (Canadá)","Auckland":"Auckland","Azores":"Azores","Baghdad":"Bagdad","Baku":"Bakú","Bangkok":"Bangkok","Beijing":"Beijing","Belgrade":"Belgrado","Berlin":"Berlín","Bern":"Berna","Bogota":"Bogotá","Brasilia":"Brasilia","Bratislava":"Bratislava","Brisbane":"Brisbane","Brussels":"Bruselas","Bucharest":"Bucarest","Budapest":"Budapest","Buenos Aires":"Buenos Aires","Cairo":"El Cairo","Canberra":"Canberra","Cape Verde Is.":"Isla Cabo Verde","Caracas":"Caracas","Casablanca":"Casablanca","Central America":"América Central","Central Time (US & Canada)":"Hora central (EE.UU. y Canadá)","Chatham Is.":"Isla Chatham","Chennai":"Chennai","Chihuahua":"Chihuahua","Chongqing":"Chongqing","Copenhagen":"Copenhague","Darwin":"Darwin","Dhaka":"Dhaka","Dublin":"Dublín","Eastern Time (US & Canada)":"Hora del Este (EE.UU. y Canadá)","Edinburgh":"Edimburgo","Ekaterinburg":"Ekaterinburg","Fiji":"Fiji","Georgetown":"Georgetown","Greenland":"Groenlandia","Guadalajara":"Guadalajara","Guam":"Guam","Hanoi":"Hanoi","Harare":"Harare","Hawaii":"Hawai","Helsinki":"Helsinki","Hobart":"Hobart","Hong Kong":"Hong Kong","Indiana (East)":"Indiana (Este)","International Date Line West":"Línea de fecha internacional del oeste","Irkutsk":"Irkutsk","Islamabad":"Islamabad","Istanbul":"Estambul","Jakarta":"Yakarta","Jerusalem":"Jerusalén","Kabul":"Kabul","Kaliningrad":"Kaliningrado","Kamchatka":"Kamchatka","Karachi":"Karachi","Kathmandu":"Katmandú","Kolkata":"Kolkata","Krasnoyarsk":"Krasnoyarsk","Kuala Lumpur":"Kuala Lumpur","Kuwait":"Kuwait","Kyev":"Kyev","Kyiv":"Kiev","La Paz":"La Paz","Lima":"Lima","Lisbon":"Lisboa","Ljubljana":"Ljubljana","London":"Londres","Madrid":"Madrid","Magadan":"Magadan","Marshall Is.":"Islas Marshall","Mazatlan":"Mazatlán","Melbourne":"Melbourne","Mexico City":"Ciudad de México","Mid-Atlantic":"Atlántico medio","Midway Island":"Isla de Midway","Minsk":"Minsk","Monrovia":"Monrovia","Monterrey":"Monterrey","Montevideo":"Montevideo","Moscow":"Moscú","Mountain Time (US & Canada)":"Hora de las Montañas (EE.UU. y Canadá)","Mumbai":"Mumbai","Muscat":"Moscatel","Nairobi":"Nairobi","New Caledonia":"Nueva Caledonia","New Delhi":"Nueva Delhi","Newfoundland":"Terranova","Novosibirsk":"Novosibirsk","Nuku\'alofa":"Nuku\'alofa","Osaka":"Osaka","Pacific Time (US & Canada)":"Hora del Pacífico (EE.UU. y Canadá)","Paris":"París","Perth":"Perth","Port Moresby":"Port Moresby","Prague":"Praga","Pretoria":"Pretoria","Quito":"Quito","Rangoon":"Rangún","Riga":"Riga","Riyadh":"Riad","Rome":"Roma","Samara":"Samara","Samoa":"Samoa","Santiago":"Santiago","Sapporo":"Sapporo","Sarajevo":"Sarajevo","Saskatchewan":"Saskatchewan","Seoul":"Seúl","Singapore":"Singapur","Skopje":"Skopje","Sofia":"Sofía","Solomon Is.":"Islas Salomón","Srednekolymsk":"Srednekolimsk","Sri Jayawardenepura":"Sri Jayawardenepura","St. Petersburg":"San Petersburgo","Stockholm":"Estocolmo","Sydney":"Sydney","Taipei":"Taipei","Tallinn":"Tallin","Tashkent":"Tashkent","Tbilisi":"Tbilisi","Tehran":"Teherán","Tijuana":"Tijuana","Tokelau Is.":"Isla de Tokelau","Tokyo":"Tokio","UTC":"UTC","Ulaan Bataar":"Ulán Bator","Ulaanbaatar":"Ulaanbaatar","Urumqi":"Urumqi","Vienna":"Viena","Vilnius":"Vilnius","Vladivostok":"Vladivostok","Volgograd":"Volgogrado","Warsaw":"Varsovia","Wellington":"Wellington","West Central Africa":"Centro-Oeste de África","Yakutsk":"Yakutsk","Yerevan":"Ereván","Zagreb":"Zagreb","Zurich":"Zúrich"},"trigger":{"action":"Haga lo siguiente","add_trigger":"La creación de vinculación","auto":"Auto","conditional_device":"Por favor, establece las condiciones para el equipo","delete_trigger":"eliminar la vinculación","manual":"Manual","no_trigger_input":"Establecer nombre de vinculación","scene_condition":"Seleccione la escena en la que desea que la automatización surta efecto. Si ninguno está marcado, esta automatización funcionará en cualquier escena","trigger_input":"nombre de vinculación","virtual_device_help":"Puede ingresar una URL, cuando se produzca este desencadenamiento, se llamará a esta URL. Auto: esta URL se llamará automáticamente cuando sucede el evento; Manual: esta URL será llamada manualmente por el usuario, como la apertura de la webcam.","when":"Cuando se producen las siguientes condiciones"},"user":{"action":"operativo","add_user":"Agregar usuario","add_users":"Por favor escriba el correo electrónico o número de teléfono","admin":"administrador","country":"País / Región","de":"Alemania","delete_user":"Eliminar el usuario","edit":"Editar permisos","en":"Estados Unidos","es":"España","fr":"Francia","not_country":"País / región no puede estar vacía","not_user_input":"Los usuarios no pueden estar vacía！","not_user_room":"Las habitaciones no puede estar vacía！","pt":"Portugal","ru":"Rusia","user":"usuario","zh":"China"},"wifi":{"add_desp":"Procesando, un momento por favor.","add_error":"Falló, presione el botón a continuación para volver a intentarlo.","add_success":"Su controlador ha sido agregado exitosamente.","add_title":"WIFI se ha configurado correctamente.","next_wifi_desp":"Durante el siguiente paso, su teléfono celular puede solicitar cambiar a la red celular o seguir usando el WiFi sin conexión a Internet, elija seguir usando el WiFi.","reload_desp":"Su controlador se reiniciará pronto, tardará hasta 3 minutos en finalizar el reinicio.","retry":"procesar de nuevo","setting_desp":"Un momento por favor, configurando el controlador WIFI","setting_error":"¡Vaya a la configuración del sistema de su teléfono celular y conecte su WIFI al punto de acceso PRESEN_CONTROLLER, luego presione el botón a continuación para volver a intentarlo!","wifi_link_error":"No se puede conectar a su controlador y la configuración de WiFi falló, puede reiniciar su controlador e ingresar al modo de Configuración de WiFi, luego intente nuevamente."}}'));
I18n.translations.fr = I18n.extend((I18n.translations.fr || {}), JSON.parse('{"action":{"action":"actes","action_device":"Veuillez configurer les périphériques cibles！","action_input":"Le nom de l\'action ne peut pas être vide！","action_name":"Nom de l\'action","activate":"Activer","add_action":"Ajouter une action","add_action_type":"Veuillez entrer le nom de l\'action:","add_favorite":"Ajouter aux raccourcis?","change_action":"Activer cette action:","current_action":"Action en cours","delete_action":"Êtes-vous sûr de vouloir supprimer cette action?","favorite":"ACTIONS PRÉFÉRÉES","remove_favorite":"Supprimer des raccourcis"},"automation":{"automation_type":"Type d\'automatisation","by_sunrise":"Par lever / coucher de soleil","city":"Ville","current_located_city":"Ville actuellement localisée","current_location":"Localisation actuelle","do":"Faire","if":"Si","locating":"Localisation ...","new_automation":"Nouvelle automatisation","only_app":"Prise en charge uniquement dans l\'application Presen","relocate":"Relocaliser","select_conditions":"Sélectionnez les conditions","select_location":"Sélectionnez l\'emplacement","select_target_type":"Sélectionnez le type de cible","sunrise":"lever du soleil","sunset":"Le coucher du soleil"},"dashboard":{"change_pin":"Changer le code PIN","enter_pin":"Entrez le code PIN de votre contrôleur","set_pin":"Configurer le code PIN de votre contrôleur","show_password":"Montrer le mot de passe"},"date":{"abbr_day_names":["dim","lun","mar","mer","jeu","ven","sam"],"abbr_month_names":[null,"jan.","fév.","mar.","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc."],"day_names":["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"],"formats":{"default":"%d/%m/%Y","long":"%e %B %Y","short":"%e %b"},"month_names":[null,"janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"],"order":["day","month","year"]},"datetime":{"distance_in_words":{"about_x_hours":{"one":"environ une heure","other":"environ %{count} heures"},"about_x_months":{"one":"environ un mois","other":"environ %{count} mois"},"about_x_years":{"one":"environ un an","other":"environ %{count} ans"},"almost_x_years":{"one":"presqu\'un an","other":"presque %{count} ans"},"half_a_minute":"une demi-minute","less_than_x_minutes":{"one":"moins d\'une minute","other":"moins de %{count} minutes","zero":"moins d\'une minute"},"less_than_x_seconds":{"one":"moins d\'une seconde","other":"moins de %{count} secondes","zero":"moins d\'une seconde"},"over_x_years":{"one":"plus d\'un an","other":"plus de %{count} ans"},"x_days":{"one":"1 jour","other":"%{count} jours"},"x_minutes":{"one":"1 minute","other":"%{count} minutes"},"x_months":{"one":"1 mois","other":"%{count} mois"},"x_seconds":{"one":"1 seconde","other":"%{count} secondes"},"x_years":{"one":"un an","other":"%{count} ans"}},"prompts":{"day":"Jour","hour":"Heure","minute":"Minute","month":"Mois","second":"Seconde","year":"Année"}},"device":{"FLiRS":"Dispositif FLiRS","battery":"Batterie","battery_operated":"Télécommande à piles","camera":"caméra IP","camera_image":"Lien image caméra IP","camera_image_desp":"Veuillez saisir l\'URL de capture d\'écran de votre caméra IP. Si cette URL nécessite une authentification pour pouvoir accéder, vous devez également spécifier le nom d\'utilisateur et le mot de passe dans cette URL. Vous devrez peut-être consulter le manuel de votre caméra IP pour l\'obtenir.","camera_name":"Nom de la caméra IP","camera_name_error":"Le nom de la caméra IP ne peut pas être vide","camera_screenshot_url_error":"Le lien de l\'image de la caméra IP ne peut pas être vide","camera_video":"Lien vidéo caméra IP","camera_video_desp":"Veuillez entrer l\'URL du flux vidéo de votre caméra IP et sélectionnez le format vidéo approprié. Si cette URL nécessite une authentification pour pouvoir accéder, vous devez également spécifier le nom d\'utilisateur et le mot de passe dans cette URL. Vous devrez peut-être consulter le manuel de votre caméra IP pour l\'obtenir.","camera_video_url_error":"Le lien vidéo de la caméra IP ne peut pas être vide","change_color":"Changer de couleur","cloud_add_camera":"Ajouter une caméra IP, s\'il vous plaît ajouter dans le contrôleur","conf_apply_battery":"Il faut se réveiller pour activer les paramètres de l\'appareil de la batterie ont été modifiés","delay":"différé","delete_field_device":"Supprimer comme appareil défectueux","delete_type":"Quel type de périphérique que vous souhaitez supprimer？","device":"équipement","device_active":"Le périphérique est actif","device_dead":"Le périphérique est mort","device_information":"Informations sur l\'appareil","device_interviewed":"L\'appareil est interviewé","device_not_interviewed":"Le périphérique n\'est pas entièrement interrogé","device_operating":"Le périphérique fonctionne","device_sleeping":"Le périphérique est en train de dormir","edit_device":"Modifier le nom du périphérique","force_interview":"Interview de force","full":"tous","interview":"Entretien","is_awake":"Est réveillé","is_sleeping":"Dort","main":"Principal alimenté","mark_failed":"Marqué comme ayant échoué","mark_failed_alert":"Le contrôleur vérifie node si clair, il est marqué comme un échec. Ce processus prend environ 30 secondes. Ensuite, le cas échéant, vous pouvez utiliser la fonction « Supprimer » pour supprimer des nœuds.","mark_failed_delete":"Etes-vous sûr de vouloir supprimer l\'appareil? Pour supprimer un nœud du contrôleur de réseau, sans la nécessité d\'une action plus loin. Ce processus peut prendre une minute.","no_device_name":"Le nom de l\'appareil ne peut pas être vide!","off_b":"fermer","on_b":"ouvert","operating":"En fonctionnement","operating_time":"Temps de fonctionnement","play":"Jouer","remove_camera_device_hint":"Pour supprimer un périphérique de caméra IP, accédez à la page de détails du périphérique (cliquez sur le nom du périphérique à saisir) pour le supprimer.","remove_directly":"Supprimer directement","seconds":"deuxième","set_to":"installer","setting":"installer","setup_device":"S\'il vous plaît définir le dispositif cible","value":"valeur","value_changed_to":"Modifiez la valeur","value_not_stored_indevice":"Mais pas dans la mémoire de l\'appareil","when":"quand"},"errors":{"format":"%{attribute} %{message}","messages":{"accepted":"doit être accepté(e)","blank":"doit être rempli(e)","confirmation":"ne concorde pas avec %{attribute}","empty":"doit être rempli(e)","equal_to":"doit être égal à %{count}","even":"doit être pair","exclusion":"n\'est pas disponible","greater_than":"doit être supérieur à %{count}","greater_than_or_equal_to":"doit être supérieur ou égal à %{count}","inclusion":"n\'est pas inclus(e) dans la liste","invalid":"n\'est pas valide","less_than":"doit être inférieur à %{count}","less_than_or_equal_to":"doit être inférieur ou égal à %{count}","model_invalid":"Validation échouée : %{errors}","not_a_number":"n\'est pas un nombre","not_an_integer":"doit être un nombre entier","odd":"doit être impair","other_than":"doit être différent de %{count}","present":"doit être vide","required":"doit exister","taken":"n\'est pas disponible","too_long":{"one":"est trop long (pas plus d\'un caractère)","other":"est trop long (pas plus de %{count} caractères)"},"too_short":{"one":"est trop court (au moins un caractère)","other":"est trop court (au moins %{count} caractères)"},"wrong_length":{"one":"ne fait pas la bonne longueur (doit comporter un seul caractère)","other":"ne fait pas la bonne longueur (doit comporter %{count} caractères)"}},"template":{"body":"Veuillez vérifier les champs suivants : ","header":{"one":"Impossible d\'enregistrer ce(tte) %{model} : 1 erreur","other":"Impossible d\'enregistrer ce(tte) %{model} : %{count} erreurs"}}},"event":{"inner":"interne","my_device":"mon appareil","my_room":"ma chambre","open_app":"Ouvrir application","outer":"extérieur"},"global":{"Choose_Ringtone":"Choisir une sonnerie","account":"REGLAGES COMPTE","account_setting":"Paramètres du compte","activate_action":"Exécuter cette action?","activate_scene":"Activer cette scène?","activate_sure":"Êtes-vous sûr de faire cela?","add_device_s2_desp":"Veuillez saisir les cinq premiers chiffres de la clé de périphérique, vous pouvez vous reporter au manuel deivce pour obtenir la clé de périphérique","add_device_s2_title":"S\'il vous plaît appuyez sur les boutons sur le périphérique suivant le manuel du périphérique pour ajouter le périphérique","add_device_wait":"Veuillez patienter","add_smart_desp":"Veuillez saisir la clé de périphérique complète, qui doit comporter 40 chiffres. Vous pouvez vous reporter au manuel de deivce pour obtenir la clé de périphérique","add_zigbee_smart_desp":"Veuillez saisir l\'adresse MAC de l\'appareil et le code d\'installation, les deux doivent être imprimés sur l\'emballage de votre appareil ou son manuel d\'instructions. Les adresses MAC ont 16 caractères et le code d\'installation est 36.","advanced_setting":"Paramètres avancés","agree":"je suis d\'accord avec le","alexa_link_error":"La liaison avec Alexa a échoué, veuillez réessayer!","alexa_link_success":"Vous vous êtes lié à Alexa et avez activé la compétence Presen Smart Home.","all":"Tout","auto":"Automatisation","away":"Loin","by_device":"Par changement d\'appareil","centigrade":"Centigrade","change_datacenter":"Change datacenter controller","choose_alarm_ringtone":"Pour choisir cela déclenchera une alarme sonore sur votre contrôleur, vous devez appuyer sur le bouton principal sur le contrôleur pour démonter l\'alarme","click_help":"Vérifier les informations d\'aide","click_to_find_more":"Cliquez pour en savoir plus","cloud_required":"Vous devez vous connecter à une connexion Internet pour utiliser cette fonctionnalité. Actuellement, vous vous connectez à Ethernet.","community":"communauté","connecting":"De liaison...","controller_setting":"PARAMÈTRES DU CONTRÔLEUR","copyright_policy":"politique de copyright","dashboard":"Tableau de bord","datacenter_setting":"Paramètres du centre de données","default":"Défaut","device_failed":"Cet appareil ne répond pas, vous pouvez essayer de le rallumer ou de l\'éteindre, ou de le déclencher pour tester s\'il est défectueux ou si la batterie est faible. S\'il ne répond plus pendant longtemps, vous devez essayer de le réinitialiser ou de le supprimer.","device_not_support":"Cet attribut ne supporte pas ça!","download_alert":"Envoyé dans votre boîte aux lettres enregistrée","dsk_error":"La clé de périphérique n\'est pas valide","dsk_input":"La clé de périphérique doit comporter 40 chiffres","enter_your":"S\'il vous plait entrez votre","error_desp":"Nous pouvons avoir un problème, veuillez réessayer!","error_title":"Désolé, la connexion a échoué","every_day":"Tous les jours","fahrenheit":"Fahrenheit","grpc_link_error":"La connexion a échoué, veuillez actualiser pour essayer à nouveau","grpc_unlink":"La connexion a échoué, en essayant de se reconnecter ...","home":"Accueil","i_agree":"Vous devez accepter le","input_key":"Clé du périphérique d\'entrée","input_ssid":"S\'il vous plaît entrez SSID ou connectez-vous wifi","input_wifi_password":"S\'il vous plaît entrer le mot de passe wifi!","ins_key":"Installer la clé","is_alive":"IS Alive","link_with_alexa":"Lien avec Alexa","login_demo":"se connecter avec un compte démo","logout":"Se déconnecter","my_smarthome":"Ma maison intelligente","need_login":"Veuillez vous connecter maintenant","no_data":"pas de données!","off_sn":"Contrôlez et organisez les périphériques au sein d\'un seul contrôleur.","on_sn":"Lorsque cette option est activée, vous pouvez gérer tous les périphériques des contrôleurs en même temps. Tous les périphériques peuvent fonctionner ensemble. Par exemple, vous pouvez créer une action pour contrôler des périphériques multiples avec différents contrôleurs.","open_license":"Licence Open Source","open_wifi":"Open WIFI","or":"ou","privacy":"Conditions d\'utilisation","privacy_statement":"déclaration de confidentialité","quick":"Raccourcis","release_log":"Journal de mise à jour","remove_device_title":"S\'il vous plaît appuyez sur les boutons sur le périphérique suivant le manuel du périphérique pour supprimer le périphérique","s2_add":"S2 Add","s2_device":"Appareil S2","scan_key":"Touche de numérisation","scene":"Scène","scene_delete":"La scène par défaut ne peut pas être supprimée","select_controller":"select controller","select_this_controller":"Basculer sur ce contrôleur","service":"Prestations de service","setting_wifi":"Configuration WIFI","setting_wifi_desp":"L\'authentification Web requise dans les restaurants et les aéroports WIFI 5G et WIFI n\'est pas prise en charge","sleep":"Le sommeil","sn_global":"mode global","sn_normal":"mode contrôleur unique","sn_setting":"Controller Management","ssid":"SSID","target":"Cible","temperature_setting":"Paramètres de température","terms_of_service":"conditions d\'utilisation","time_out":"Délai d\'expiration de l\'opération, veuillez réessayer!","timezone_setting":"Paramètres du fuseau horaire","type":"Type","use":"utilisation","user_role_desp":"Vous pouvez partager l\'accès du contrôleur actuel avec d\'autres personnes, entrez simplement l\'e-mail que vous souhaitez partager ici. Les personnes avec lesquelles vous avez partagé peuvent uniquement utiliser vos données, telles que contrôler vos appareils, activer des scènes, exécuter des actions, mais ne peuvent pas modifier vos données, telles que l\'ajout ou la suppression d\'appareils, la création ou la modification de scènes, etc., et bien sûr, elles ne peut pas réinitialiser ou redémarrer votre contrôleur. Une fois l\'e-mail ajouté, l\'autre utilisateur doit redémarrer l\'application pour prendre effet.","vacation":"Vacances","wifi_connection":"connexion WIFI","wifi_link_btn":"La led réseau est allumée","wifi_link_desp":"Veuillez appuyer sur le bouton du haut du contrôleur jusqu\'à ce que le voyant réseau soit toujours allumé pour continuer","wifi_password":"mot de passe WIFI","wired_connection":"Connexion filaire","zwave_delete_db":"Si ce périphérique est perdu ou fonctionne mal, vous pouvez le supprimer directement. Il réapparaîtra s\'il continue à communiquer avec votre contrôleur","zwave_delete_field":"Supprimer ce périphérique en échec, cette opération peut échouer si ce périphérique n\'est pas reconnu comme un périphérique en panne"},"guard":{"enter_guard_mode":"Entrer en mode garde","exit_guard_mode":"Quitter le mode garde","guard_h1":"Presen peut vous aider à garder votre maison en votre absence","guard_h2":"ALERTES INTELLIGENTES","guard_h2_deps":"Lorsque vous passez en mode garde, Presen peut vous envoyer des notifications si le détecteur de mouvement ou le détecteur de fumée est déclenché ou si la porte est ouverte.","guard_h3":"ÉCLAIRAGE LOIN","guard_h3_deps":"Presen peut allumer et éteindre vos lumières automatiquement pour donner l\'impression que quelqu\'un est à la maison lorsque vous êtes absent.","guard_mode":"Mode garde"},"help":{"cookie_hint":"Ce site utilise des cookies afin d’améliorer votre expérience d’utilisateur. Des informations détaillées sur l’utilisation des cookies sur ce site Web sont fournies dans notre %s. En utilisant ce site, vous consentez à l’utilisation de cookies. %s","privacy":"Vie privée","yes_i_agree":"Oui, je suis d’accord"},"helpers":{"select":{"prompt":"Veuillez sélectionner"},"submit":{"create":"Créer un(e) %{model}","submit":"Enregistrer ce(tte) %{model}","update":"Modifier ce(tte) %{model}"}},"home":{"433_device":"433 équipements","a":"plus","add_433_device":"Ajouter un périphérique 433M","add_433_node_type_doorlock":"Doorlock","add_433_node_type_other":"Others","add_433_node_type_pir":"PIR","add_433_node_type_smoke_sensor":"Smoke Sensor","add_433_node_type_water_sensor":"Water Sensor","add_433_waiting_for_ports":"En vérifiant le port série que le contrôleur prend en charge, assurez-vous que votre contrôleur dispose de périphériques USB de port série ...","add_controller":"Ajouter Controller","add_device":"Ajouter un périphérique","add_device_433_progress_error":"Aucun port 433M trouvé sur votre contrôleur","add_device_433_progress_loading":"Vérification","add_device_type":"Quel type de périphérique que vous souhaitez ajouter？","add_qiock":"Add to Quick Control","add_success":"Ajouter le succès！","cancel":"annulé","card_1":"Contrôleur état de fonctionnement","card_2":"durée du contrôleur","card_3":"Nombre d\'appareils","cellphone":"Numéro de téléphone","change_controller":"contrôleur Changement","confirm":"confirmer","controller":"contrôleur","controller_information":"Informations sur le contrôleur","controller_input":"Nom du contrôleur","controller_sn_enter":"S\'il vous plaît entrer le numéro de série du contrôleur","controller_state":"Statut du contrôleur","current_controller":"Le régulateur de courant","day":"jour","delete_success":"Supprimé avec succès!","detect_device":"Matériel d\'essais","device":"équipement","device_input":"Nom du périphérique","device_management":"gestion des périphériques","device_name":"Nom du périphérique","edit":"éditer","edit_controller":"Modifier le nom du contrôleur","edit_device":"Modifier le nom du périphérique","edit_success":"Modifier le succès！","email":"boîte aux lettres","error":"erreur","event":"nouvelles","failed":"L\'opération a échoué！","favorite":"SCÈNES FAVORITES","forget_password":"Vous avez oublié votre mot de passe","good_afternoon":"bonne après-midi","good_evening":"Bonsoir","good_morning":"Bonjour","have_notify":"Vous avez décoché un événement","have_version":"Il y a une nouvelle version！","home":"maison","indoor_humidity":"Humidité intérieure","indoor_temp":"Température intérieure","ip":"IP","language":"langue","language_for_mobile":"langue","local_network":"LAN","login_name":"E-mail ou numéro de téléphone","name":"nom","network_issue":"Problème de réseau","network_issue_desp":"Votre réseau est trop lent, vous pouvez essayer de redémarrer l\'application ou passer à un réseau rapide","next":"L\'étape suivante","no_controller":"Aucune donnée de contrôleur disponible, vous devez d\'abord ajouter un contrôleur dans l\'application actuelle, puis allumer votre contrôleur et le connecter à Internet, puis patientez quelques secondes pour actualiser l\'écran.","no_controller_input":"Le nom du contrôleur ne peut pas être vide！","no_device":"Aucune information intérieure","not_device_input":"Le nom de l\'appareil ne peut pas être vide！","not_user":"L\'utilisateur n\'existe pas！","off_line":"Hors ligne","on_line":"en ligne","password":"mot de passe","profile":"configuration","qiock_control":"Quick control","recent_event":"Les événements récents","recently_devices":"Appareils récemment","refresh":"Actualiser le contrôleur","remove_433_device_hint":"Vous pouvez accéder à la page de détail du périphérique (en cliquant sur le nom du périphérique) pour supprimer le périphérique 433M.s","remove_btn":"effacer","remove_device":"supprimer périphérique","remove_qiock":"Remove from Quick Control","room":"chambre","routine":"Routines","save":"soumettre","scene":"scène","select_433_device":"Les périphériques suivants ont été détectés, sélectionnez le périphérique que vous souhaitez ajouter","select_433_node_type":"Sélectionnez le type d\'appareil pour cet appareil","select_433_port":"Sélectionnez le périphérique série que vous souhaitez ajouter à","select_433_protocol":"Sélectionnez le protocole que votre appareil prend en charge","setting":"installer","setting_btn":"installer","sign":"Se connecter","sign_out":"quitter","sign_up":"inscription","sn":"numéro de série","sn_error":"Le numéro de série n\'existe pas！","sn_used":"Le numéro de série est déjà utilisé！","state":"état","success":"succès","successful":"Opération réussie!","time_zone":"Choisissez votre fuseau horaire","token":"Code de vérification","trigger":"lien","try_again":"Réessayez plus tard！","user":"utilisateur","user_has":"L\'utilisateur existe déjà！","user_user":"Vous ne pouvez pas ajouter votre propre！","version":"Le numéro de version","voice":"Voix","vs_update":"Que mettre à jour immédiatement","warning_message":"rapide","wide_network":"WAN","z_device":"équipement ZWAVE"},"index":{"433m":"Z-Wave et RF régulière","433m_desp":"Presen prend en charge les capteurs RF (433M, 345M, 319M) habituels de certaines grandes marques en plus des dispositifs Z-Wave, et ils peuvent travailler ensemble pour renforcer la sécurité et l\'automatisation de votre maison","about":"A propos de nous","about_about":"sur","about_desp":"Presen est une maison intelligente plus simple et plus intelligente. Nous concentrons des centaines de marques de confiance sur une seule application mobile, de sorte que vous pouvez facilement surveiller, contrôler et automatiser votre maison intelligente où que vous soyez.","about_desp_three":"Maintenant, imaginez si vous n\'avez pas besoin d\'imaginer. Parce qu\'il utilise Presen, il est déjà disponible. Des choses comme les lumières, les portes, les climatiseurs et les interrupteurs peuvent désormais mieux fonctionner pour vous, vous permettant de vous sentir plus en sécurité, mieux contrôlé, plus efficace et plus heureux. Avec la technologie Presen, les possibilités illimitées seront dictées par vos propres besoins et votre créativité.","about_desp_two":"Pensez-y seulement si vous savez quand vous ne pouvez pas rentrer à la maison et comment pouvez-vous être assuré. Imaginez s\'ils ont toujours su ce dont vous aviez besoin et quand c\'était nécessaire. Imagine si tu savais que tu te connaissais mieux que toi.","about_name":"À propos de Presen","about_our":"Nous nous concentrons sur la maison intelligente","about_title":"Qu\'est-ce que Presen","alerting":"ALERTE 24/7","alerting_desp":"Les fonctions Presen Events et Notifications peuvent être facilement personnalisées pour envoyer des messages d\'urgence et de notification à votre téléphone portable","ali_desp":"Contrôle vocal sur les périphériques série 天猫精灵, contrôlez votre maison uniquement avec votre voix","app":"APP","automation":"AUTOMATISATION FACILE","automation_desp":"Avec le support de Triggers and Scenes, vous pouvez créer de nombreux types de scénarios en fonction de vos besoins","build":"Construire la meilleure maison intelligente","change":"changement","contact_address":"adresse","for_live":"vivre dans","get_app":"Application gratuite","has_account":"Vous avez déjà un compte？","help_you":"Vous aider à gérer toute la maison","home":"maison","no_account":"Pas de compte？","or_sign_up":"ou inscrivez-vous","or_sign_up_type":"Ou utilisez votre","our":"nous","our_app":"My APP","product":"Fonctionnalité","product_desp":"Sur la route, depuis votre bureau ou allongé sur une plage tropicale, vous pouvez contrôler votre maison n\'importe où. Vous pouvez avoir le contrôle complet de votre maison depuis l\'écran de votre PC, smartphone ou tout autre appareil connecté à Internet.","product_desp_1":"longue introduction","product_name":"Contrôle total de la maison et automatisation","product_name_1":"Le premier produit","product_title":"Plate-forme d\'automatisation complète comprenant des contrôleurs locaux, des serveurs cloud, des applications utilisateur et d\'administration qui fonctionnent ensemble de manière transparente.","product_title_1":"Brève introduction","remote":"TÉLÉCOMMANDE","remote_desp":"Contrôlez votre maison à partir de votre smartphone ou de tout autre appareil connecté à Internet, même sans Internet dans votre réseau local, avec une connexion Internet sécurisée","routines":"ROUTINES","routines_desp":"Presen Routines vous permet de créer facilement vos propres scénarios d\'automatisation","security":"MEILLEURE SÉCURITÉ","security_desp":"Presen vous apporte une nouvelle génération de sécurité à la maison et de sécurité","sharing":"PARTAGE À LA MAISON","sharing_desp":"Tous les membres de votre famille peuvent avoir leurs propres comptes pour contrôler votre maison avec un accès différent","voice":"COMMANDE VOCALE","voice_desp":"Contrôle vocal sur les périphériques série Amazon Echo, contrôlez votre maison uniquement avec votre voix","we_well":"Nous allons créer une maison intelligente","work_with":"œuvres Presen"},"mongoid":{"attributes":{"c/action":{"name":"Nom de l\'action"},"c/node":{"c_display_name":"Nom de l\'appareil"},"c/scene":{"name":"Nom de scène"}},"errors":{"format":"%{attribute} %{message}","messages":{"accepted":"doit être accepté(e)","blank":"doit être rempli(e)","confirmation":"ne concorde pas avec %{attribute}","empty":"doit être rempli(e)","equal_to":"doit être égal à %{count}","even":"doit être pair","exclusion":"n\'est pas disponible","greater_than":"doit être supérieur à %{count}","greater_than_or_equal_to":"doit être supérieur ou égal à %{count}","inclusion":"n\'est pas inclus(e) dans la liste","invalid":"n\'est pas valide","less_than":"doit être inférieur à %{count}","less_than_or_equal_to":"doit être inférieur ou égal à %{count}","model_invalid":"Validation échouée : %{errors}","not_a_number":"n\'est pas un nombre","not_an_integer":"doit être un nombre entier","odd":"doit être impair","other_than":"doit être différent de %{count}","present":"doit être vide","record_invalid":"La validation a échoué : %{errors}","required":"doit exister","restrict_dependent_destroy":{"has_many":"Vous ne pouvez pas supprimer l\'enregistrement parce que les %{record} dépendants existent","has_one":"Vous ne pouvez pas supprimer l\'enregistrement car un(e) %{record} dépendant(e) existe"},"taken":"n\'est pas disponible","too_long":{"one":"est trop long (pas plus d\'un caractère)","other":"est trop long (pas plus de %{count} caractères)"},"too_short":{"one":"est trop court (au moins un caractère)","other":"est trop court (au moins %{count} caractères)"},"wrong_length":{"one":"ne fait pas la bonne longueur (doit comporter un seul caractère)","other":"ne fait pas la bonne longueur (doit comporter %{count} caractères)"}}}},"number":{"currency":{"format":{"delimiter":" ","format":"%n %u","precision":2,"separator":",","significant":false,"strip_insignificant_zeros":false,"unit":"€"}},"format":{"delimiter":" ","precision":3,"separator":",","significant":false,"strip_insignificant_zeros":false},"human":{"decimal_units":{"format":"%n %u","units":{"billion":"milliard","million":"million","quadrillion":"million de milliards","thousand":"millier","trillion":"billion","unit":""}},"format":{"delimiter":"","precision":3,"significant":true,"strip_insignificant_zeros":true},"storage_units":{"format":"%n %u","units":{"byte":{"one":"octet","other":"octets"},"gb":"Go","kb":"ko","mb":"Mo","tb":"To"}}},"percentage":{"format":{"delimiter":"","format":"%n%"}},"precision":{"format":{"delimiter":""}}},"room":{"add_room":"Ajouter une chambre","desp":"EVENT signifie qu\'il y a des changements d\'état de l\'appareil dans cette pièce au cours des dernières 24 heures.","device_in_room":"L\'équipement dans la chambre","event":"événement","filter_room":"Filtrer par chambres","normal":"normal","room_name":"Nom de la chambre","room_no_device":"Aucun dispositif est sélectionné！","room_no_name":"Nom de la salle ne peut pas être vide！","room_switch_hint":"Contrôler tous les appareils swtich dans cette salle","setting_room":"Réglage de la chambre"},"routine":{"add":"Ajouter une routine","address":"Adresse","cond_action":"Activer une action","cond_device":"Activer les appareils","cond_scene":"Activer une scène","edit":"Modifier la routine","get_to":"Arrivée","is_cyclic":"Répétez","last":"Arrière","leave":"Laisser","one_cond":"Une fois que","please_address":"Veuillez sélectionner l\'emplacement GPS","please_date":"Veuillez sélectionner la date","please_leave":"Veuillez sélectionner l\'arrivée ou la sortie","please_time":"Veuillez sélectionner l\'heure","please_to":"Veuillez sélectionner ce que vous voulez activer","please_type":"Veuillez sélectionner le type de routine","routine":"Routines","routine_cond":"Quand","routine_desp":"Routine peut activer une scène ou une base de périphériques sur l\'heure ou l\'emplacement GPS (besoin de travailler avec Presen App)","routine_location":"Localisation GPS","routine_type":"Type de routine","run_multiple":"Répétez","run_once":"Fois","select_action":"Sélectionnez une action","select_device":"Sélectionnez les appareils","select_scene":"Sélectionnez une scène","time":"Temps","to_location":"Par localisation GPS","to_time":"Par heure","ui":"Cette fonctionnalité doit fonctionner avec Presen App et Presen Cloud, s\'il vous plaît visitez https://www.presensmarthome.com"},"scene":{"activate":"activation","add_scene":"Ajouter scène","add_scene_type":"S\'il vous plaît entrez le nom de scène:","change_scene":"Que ce soit pour changer la scène","current_scene":"Scène actuelle","delete_scene":"supprimer les scènes","scene_device":"S\'il vous plaît ajouter un périphérique！","scene_input":"Nom de la scène ne peut être vide！","scene_name":"Nom de la scène"},"services":{"alexa":"Contrôlez vos appareils et automatisations avec les appareils compatibles Amazon Alexa.","alexa_desp":"Tels que \\"Alexa, turn on home scene\\".","ali":"Contrôlez vos appareils et les automatisations utilisent TmallGenie.","ali_desp":"Comme \\"天猫精灵, 打开卧室的灯 \\".","google":"Contrôlez vos appareils et automatisations à l’aide d’appareils pris en charge par Google Assistant.","google_desp":"Comme \\"Hey Google, activate home\\".","ifttt":"Collaborez avec IFTTT pour établir un lien avec d’autres services du 3e.","ifttt_desp":"Par exemple, lorsque le capteur de mouvement est déclenché, vous obtiendrez un appel."},"session":{"60_s":"Réessayez au bout de 60 secondes！","cellphone_input":"S\'il vous plaît entrer votre numéro de portable!","change_password":"Changer mot de passe","confir_input":"S\'il vous plaît confirmer votre mot de passe sur！","email":"La boîte aux lettres existe déjà!","email_input":"S\'il vous plaît entrer l\'e-mail！","email_or_phone":"E-mail ou numéro de téléphone","email_type":"le format E-mail est incorrect！","get_token":"Code de vérification","is_cellphone":"numéro de téléphone existe déjà！","is_email":"E-mail existe déjà！","login_name_input":"S\'il vous plaît accéder au numéro de boîte aux lettres ou par téléphone","new_input":"S\'il vous plaît entrer un nouveau mot de passe！","new_password":"nouveau mot de passe","no_user_name":"Nom d\'utilisateur ne peut pas être vide！","not_eamil":"Boîte aux lettres n\'existe pas！","not_email_or_phone":"S\'il vous plaît accéder à votre numéro de e-mail","not_phone":"Le numéro de téléphone n\'existe pas！","not_token":"Le code de vérification n\'est pas correct！","old_error":"L\'ancien mot de passe est incorrect！","old_input":"S\'il vous plaît entrer votre ancien mot de passe！","old_password":"ancien mot de passe","password_confirmation":"confirmer mot de passe","password_input":"S\'il vous plaît entrer votre mot de passe","password_length":"Mot de passe ne peut pas être inférieure à six！","please_enter_password":"s\'il vous plait entrez votre mot de passe","register":"inscription","reset_password":"Password reset","send":"envoyer","send_eamil":"Envoyer le code de vérification au numéro de courriel","send_token":"Un jeton a été envoyé dans votre boîte aux lettres. Veuillez vérifier votre boîte aux lettres.","signup_success_login":"Compte créé avec succès, connectez-vous maintenant","token_input":"Un code de vérification a été envoyé à votre adresse e-mail. Veuillez vérifier et entrer votre code de vérification ci-dessous pour continuer.","token_send":"Le code de vérification envoyé avec succès！","two_password":"Les deux mots de passe ne correspondent pas！","user_name":"Nom d\'utilisateur","wrong_name":"Nom d\'utilisateur ou mot de passe est incorrect！"},"setting":{"add":"Ajouté","advanced_setting":"Paramètres avancés","backup":"Contrôleur de sauvegarde","backup_desp":"Sauvegarde du contrôleur","backup_message":"S\'il vous plaît ne pas éteindre le contrôleur de sauvegarde de puissance。。。","base":"Réglages de base","change_notification":"Notification de modification","check_device":"Veuillez sélectionner les attributs des dispositifs que ne pas vouloir recevoir les notifications de modifications inutiles.","check_device_for_urgent":"Seulement recevoir des notifications urgentes en suivant les changements de périphériques.","check_scene":"Ne recevez pas de notifications lorsque votre maison est dans les scènes suivantes vérifiées, si aucune scène n’est sélectionnée, cela signifie que toutes les notifications de modifications de périphérique ne sera pas envoyer n’importe quelle scène il est.","check_scene_for_urgent":"Seulement recevoir les notifications urgentes dans les scènes suivantes.","close_account":"Fermer le compte","close_desp":"Les utilisateurs ne peuvent pas se connecter après la fermeture du compte! Si vous avez besoin de rouvrir votre compte, contactez-nous!","download_data":"Télécharger les données","download_desp":"Télécharger les données personnelles","end_time":"Heure de fin","friday":"Vendredi","inform":"Remarquer","local_siren":"Sirène locale","login_close_desp":"Le compte a été fermé. Pour ouvrir un compte, contactez-nous!","monday":"Lundi","not_end_time":"L\'heure de fin est définie sur:","not_start_time":"Si vous n\'avez pas besoin de temps, réglez l\'heure de début sur:","notify":"Centre de notifications","notify_by_device":"Dispositifs Notification","notify_by_time":"Ne pas déranger","notify_desp":"Contrôle notification globale, vous ne recevrez pas de notifications si il est éteint.","notify_device_noti_desp":"Vous ne recevrez pas de notifications de modification des appareils suivants","notify_dnd_desp":"Programme d’installation le temps que vous ne souhaitez pas recevoir des notifications","notify_time":"Temps","notify_urgent_device":"Urgent Notification","notify_urgent_noti_desp":"Vous recevrez une notification spéciale lorsque ces appareils changent. (Notification urgente ignorera les paramètres Dot pas déranger et de dispositifs de Notification)","place_end_time":"S\'il vous plaît sélectionner l\'heure de fin！","place_select_period":"S\'il vous plaît sélectionnez l\'heure！","place_start_time":"S\'il vous plaît sélectionner l\'heure de début！","property":"Propriétés","record":"Restore Controller","record_desp":"Restore Controller","record_error":"Le contrôleur Échec de la restauration! S\'il vous plaît essayer à nouveau！","record_message":"S\'il vous plaît ne mettez pas hors tension pour rétablir le contrôle。。。","record_success":"Le succès de la réduction du contrôleur!","reset":"Réinitialiser le contrôleur","reset_desp":"Le contrôleur est remis à zéro aux paramètres d\'usine. Note: Toutes les chambres et d\'autres données seront effacées!","reset_message":"Le contrôleur supprime toutes les données。。。。","reset_success":"Votre contrôleur a été réinitialisé avec succès.","reset_title":"Réinitialiser le contrôleur?","restart":"restart Controller","restart_desp":"Redémarrez le contrôleur, le redémarrage prend environ 5 minutes! Après le redémarrage est terminé, s\'il vous plaît rafraîchir la page","restart_message":"Voulez-vous vraiment réinitialiser le contrôleur？","restart_title":"Redémarrer le contrôleur?","saturday":"samedi","select_date":"Sélectionner une date","select_device_scene":"S\'il vous plaît sélectionnez l\'appareil ou la scène！","select_period":"période de sélection","start_end":"L\'heure de fin est inférieure à l\'heure de début!","starting_time":"Heure de départ","sunday":"dimanche","thursday":"Jeudi","tuesday":"Mardi","update_desp":"Le processus de mise à niveau ne désactivez pas la mise à niveau de puissance prend environ 5 à 7 minutes! Après la mise à niveau terminée, s\'il vous plaît rafraîchir la page ","update_desp_one":"Une nouvelle version:","update_failed_in_progress":"Une mise à jour est déjà en cours.","update_failed_other":"La mise à jour a échoué, veuillez réessayer plus tard!","update_failed_up_to_date":"Il n\'est pas nécessaire de mettre à jour, vous êtes à jour.","update_online":"Mise à niveau du contrôleur","update_title":"Mise à jour ...","updated":"Le système actuel jusqu\'à ce jour, aucune mise à jour!","updating":"Nous sommes mis à jour。。。","upgrade_desp":"Le contrôleur est en cours de mise à jour, veuillez ne pas couper l\'alimentation de votre contrôleur, cela prendra environ 5 minutes pour terminer, cela dépend de la vitesse de votre réseau. Le contrôleur redémarrera une fois terminé et vous recevrez une notification.","upgrade_success_to":"Le contrôleur a été mis à niveau vers","wednesday":"Mercredi"},"spec":{"access_control":"Contrôle d\'accès","air_flow":"Flux d\'air","air_temperature":"Pression","alarm":"Alarme","alarm_s":"Alarme d\'induction","angle_position":"Angle","app_url":"URL de déclenchement","appliance":"Appareils ménagers","atmospheric_pressure":"Pression","auto":"Automatique","aux":"Équipement auxiliaire","auxiliary":"Auxiliaire","barometric_pressure":"Pression","battery":"Batterie","burglar":"Anti-vol","camera":"Caméra IP","clock":"Horloge","closed":"Fermé","co":"Monoxyde de carbone","co2":"Dioxyde de carbone","co2_alarm":"Avertissement de dioxyde de carbone","co2_level":"Niveau de dioxyde de carbone","co_alarm":"Avertissement de monoxyde de carbone","co_level":"Niveau de monoxyde de carbone","color_switch":"Commutateur de couleur","contact_sensor":"Capteur de contact","cool":"Réfrigération","current":"Écoulement de l\'eau","dew_point":"Point de rosée","dimmer":"Gradateur","direction":"Direction","distance":"Distance","door_lock":"Serrure de porte","door_window":"Portes et fenêtres","electrical_conductivity":"Conductivité","electrical_resistivity":"Coefficient de résistance","emergency":"Urgence","false":"Non déclenché","fire_sensor":"Capteur de feu","first":"Le premier","flood":"Inondé","freeze":"Réfrigération","frequency":"Fréquence","full":"Tout","general":"Général","general_purpose":"Détection universelle","general_purpose_alarm":"Alarme générale","general_purpose_value":"Statut général","general_trigger":"Événement commun","glass_break":"Capteur cassé","going_to_low_battery":"va manquer de batterie","heat":"Chauffage","heat_alarm":"Alarme de chaleur","humidity":"Humidité","is":"Le statut est","key_fob":"Fob clé","keypad":"Clavier","loudness":"Volume","low":"Faible","luminance":"Lumière","luminiscence":"Lumière","main_powered":"Principal alimenté","meter":"Instrument de mesure","moisture":"Pluie","motion":"Détection de mouvement","motor":"Moteur","multi_switch":"Interrupteur multicanal","n_a":"N/A","no_b":"Normal","normal":"Normal","not_used":"Unknown","off_b":"Off","on_b":"Ouvrir","open":"Ouvrir","power":"Puissance","power_management":"Gestion de l\'alimentation","quake":"Tremblement de terre","rain_rate":"Vitesse de pluie","relative_humidity":"Humidité relative","remote_ontrol":"Télécommande","reserved":"Garde le","resume":"CV","return_first_alarm_on_supported_list":"Premier avertissement","rotation":"Faire pivoter","security_repeater":"Répéteur de sécurité","seismic_intensity":"Intensité sismique","seismic_magnitude":"Magnitude","sensor":"Capteur","smoke":"Fumée","smoke_alarm":"Avertissement de fumée","smoke_test":"Test de fumée","soil_temperature":"Température de l\'huile","solar_radiation":"Rayonnement solaire","switch":"Commutateur","switch_all":"L\'ensemble du commutateur","switch_to":"basculer vers","system":"Système","tamper":"Tamper","tank_capacity":"Capacité","target_temperature":"Température cible","temperature":"Température","thermostat":"Thermostat","thermostat_mode":"Mode climatisation","thermostat_setpoint":"Réglez la température","tide_level":"Niveau de la marée","tilt":"Inclinaison","time":"Temps","true":"Déclenché","ultraviolet":"UV","velocity":"Vitesse","ventilation":"Équipement de ventilation","vibration":"Vibration","voltage":"Tension","water":"Eau","water_leak":"Inondé","water_leak_alarm":"Alarme d\'inondation","water_temperature":"Température de l\'eau","weight":"Poids","yes_b":"Déclenché"},"support":{"array":{"last_word_connector":" et ","two_words_connector":" et ","words_connector":", "}},"time":{"am":"am","formats":{"default":"%d %B %Y %Hh %Mmin %Ss","long":"%A %d %B %Y %Hh%M","short":"%d %b %Hh%M"},"pm":"pm"},"timezones":{"Abu Dhabi":"Abou Dabi","Adelaide":"Adélaïde","Alaska":"Alaska","Almaty":"Almaty","American Samoa":"Samoa américaines","Amsterdam":"Amsterdam","Arizona":"Arizona","Astana":"Astana","Athens":"Athènes","Atlantic Time (Canada)":"Atlantic Time (Canada)","Auckland":"Auckland","Azores":"Açores","Baghdad":"Bagdad","Baku":"Bakou","Bangkok":"Bangkok","Beijing":"Pékin","Belgrade":"Belgrade","Berlin":"Berlin","Bern":"Berne","Bogota":"Bogotá","Brasilia":"Brasilia","Bratislava":"Bratislava","Brisbane":"Brisbane","Brussels":"Bruxelles","Bucharest":"Bucarest","Budapest":"Budapest","Buenos Aires":"Buenos Aires","Cairo":"Caire","Canberra":"Canberra","Cape Verde Is.":"Cap-Vert","Caracas":"Caracas","Casablanca":"Casablanca","Central America":"Amérique centrale","Central Time (US & Canada)":"Central Time (É.-U. et Canada)","Chatham Is.":"Îles Chatham","Chennai":"Chennai","Chihuahua":"Chihuahua","Chongqing":"Chongqing","Copenhagen":"Copenhague","Darwin":"Darwin","Dhaka":"Dacca","Dublin":"Dublin","Eastern Time (US & Canada)":"Heure de l\'Est (É.-U. et Canada)","Edinburgh":"Édimbourg","Ekaterinburg":"Iekaterinbourg","Fiji":"Fidji","Georgetown":"Georgetown","Greenland":"Groenland","Guadalajara":"Guadalajara","Guam":"Guam","Hanoi":"Hanoï","Harare":"Harare","Hawaii":"Hawaï","Helsinki":"Helsinki","Hobart":"Hobart","Hong Kong":"Hong Kong","Indiana (East)":"Indiana (est)","International Date Line West":"International Date Line West","Irkutsk":"Irkoutsk","Islamabad":"Islamabad","Istanbul":"Istanbul","Jakarta":"Jakarta","Jerusalem":"Jérusalem","Kabul":"Kaboul","Kaliningrad":"Kaliningrad","Kamchatka":"Kamtchatka","Karachi":"Karachi","Kathmandu":"Katmandou","Kolkata":"Calcutta","Krasnoyarsk":"Krasnoïarsk","Kuala Lumpur":"Kuala Lumpur","Kuwait":"Koweït","Kyiv":"Kiev","La Paz":"La Paz","Lima":"Lima","Lisbon":"Lisbonne","Ljubljana":"Ljubljana","London":"Londres","Madrid":"Madrid","Magadan":"Magadan","Marshall Is.":"Îles Marshall","Mazatlan":"Mazatlán","Melbourne":"Melbourne","Mexico City":"Mexico","Mid-Atlantic":"Mid-Atlantic","Midway Island":"Îles Midway","Minsk":"Minsk","Monrovia":"Monrovia","Monterrey":"Monterrey","Montevideo":"Montevideo","Moscow":"Moscou","Mountain Time (US & Canada)":"Mountain Time (É.-U. et Canada)","Mumbai":"Bombay","Muscat":"Mascate","Nairobi":"Nairobi","New Caledonia":"Nouvelle-Calédonie","New Delhi":"New Delhi","Newfoundland":"Terre-Neuve","Novosibirsk":"Novossibirsk","Nuku\'alofa":"Nuku\'alofa","Osaka":"Osaka","Pacific Time (US & Canada)":"Pacific Time (É.-U. et Canada)","Paris":"Paris","Perth":"Perth","Port Moresby":"Port Moresby","Prague":"Prague","Pretoria":"Pretoria","Quito":"Quito","Rangoon":"Rangoun","Riga":"Riga","Riyadh":"Riyad","Rome":"Rome","Samara":"Samara","Samoa":"Samoa","Santiago":"Santiago","Sapporo":"Sapporo","Sarajevo":"Sarajevo","Saskatchewan":"Saskatchewan","Seoul":"Séoul","Singapore":"Singapour","Skopje":"Skopje","Sofia":"Sofia","Solomon Is.":"Îles Salomon","Srednekolymsk":"Srednekolymsk","Sri Jayawardenepura":"Sri Jayawardenapura","St. Petersburg":"Saint-Pétersbourg","Stockholm":"Stockholm","Sydney":"Sydney","Taipei":"Taipei","Tallinn":"Tallinn","Tashkent":"Tachkent","Tbilisi":"Tbilissi","Tehran":"Téhéran","Tijuana":"Tijuana","Tokelau Is.":"Tokelau","Tokyo":"Tokyo","UTC":"UTC","Ulaan Bataar":"Oulan-Bator","Ulaanbaatar":"Oulan-Bator","Urumqi":"Ürümqi","Vienna":"Vienne","Vilnius":"Vilnius","Vladivostok":"Vladivostok","Volgograd":"Volgograd","Warsaw":"Varsovie","Wellington":"Wellington","West Central Africa":"Heure d\'Afrique occidentale","Yakutsk":"Iakoutsk","Yerevan":"Erevan","Zagreb":"Zagreb","Zurich":"Zurich"},"trigger":{"action":"Effectuez les opérations suivantes","add_trigger":"lien Création","auto":"Auto","conditional_device":"S\'il vous plaît fixer des conditions pour l\'équipement","delete_trigger":"supprimer lien","manual":"Manuel","no_trigger_input":"Définir le nom de liaison","scene_condition":"Veuillez sélectionner la scène dans laquelle vous souhaitez que l\'automatisation prenne effet. Si aucune n\'est cochée, cette automatisation fonctionnera dans n\'importe quelle scène","trigger_input":"nom de liaison","virtual_device_help":"Vous pouvez entrer une URL, lorsque ce déclencheur se produit, cette URL sera appelée. Auto: cette URL sera appelée automatiquement lorsque l\'événement se produit; Manuel: cette URL sera appelée manuellement par l\'utilisateur, comme l\'ouverture de la webcam.","when":"Lorsque les conditions suivantes sont réunies"},"user":{"action":"d\'exploitation","add_user":"Ajouter un utilisateur","add_users":"S\'il vous plaît entrer le numéro de courriel ou par téléphone","admin":"administrateur","country":"Pays/Région","de":"Allemagne","delete_user":"Supprimez l\'utilisateur","edit":"Modifier les autorisations","en":"États-Unis","es":"Espagne","fr":"France","not_country":"Pays/région ne peut pas être vide","not_user_input":"Les utilisateurs ne peuvent pas être vide！","not_user_room":"Les chambres ne peuvent pas être vide！","pt":"Portugal","ru":"Russie","user":"utilisateur","zh":"Chine"},"wifi":{"add_desp":"Traitement, un instant s\'il vous plaît.","add_error":"Échec, veuillez appuyer sur le bouton ci-dessous pour réessayer.","add_success":"Votre contrôleur a été ajouté avec succès.","add_title":"Le WIFI a été configuré avec succès.","next_wifi_desp":"Au cours de l\'étape suivante, votre téléphone portable peut vous demander de passer au réseau cellulaire ou de continuer à utiliser le WiFi sans connexion Internet, veuillez choisir de continuer à utiliser le WiFi.","reload_desp":"Votre contrôleur va redémarrer bientôt, il faudra jusqu\'à 3 minutes pour terminer le redémarrage.","retry":"retenter","setting_desp":"Un instant s\'il vous plaît, configuration du contrôleur WIFI","setting_error":"Veuillez vous rendre dans les paramètres de votre système de téléphone portable et connecter votre WIFI au point d\'accès PRESEN_CONTROLLER, puis appuyez sur le bouton ci-dessous pour réessayer!","wifi_link_error":"Impossible de se connecter à votre contrôleur et la configuration WiFi a échoué, vous pouvez redémarrer votre contrôleur et passer en mode de configuration WiFi, puis réessayer."}}'));
I18n.translations.he = I18n.extend((I18n.translations.he || {}), JSON.parse('{"timezones":{"Abu Dhabi":"אבו דאבי","Adelaide":"אדלייד","Alaska":"אלסקה","Almaty":"אלמאטי","American Samoa":"סמואה האמריקנית","Amsterdam":"אמסטרדם","Arizona":"אריזונה","Astana":"אסטנה","Athens":"אתונה","Atlantic Time (Canada)":"זמן אטלנטי (קנדה)","Auckland":"אוקלנד","Azores":"איים האזוריים","Baghdad":"בגדד","Baku":"באקו","Bangkok":"בנגקוק","Beijing":"בייג\'ינג","Belgrade":"בלגרד","Berlin":"ברלין","Bern":"ברן","Bogota":"בוגוטה","Brasilia":"ברזיליה","Bratislava":"ברטיסלבה","Brisbane":"בריסביין","Brussels":"בריסל","Bucharest":"בוקרשט","Budapest":"בודפשט","Buenos Aires":"בואנוס איירס","Cairo":"קהיר","Canberra":"קנברה","Cape Verde Is.":"אי קייפ ורדה","Caracas":"קראקס","Casablanca":"קזבלנקה","Central America":"מרכז אמריקה","Central Time (US & Canada)":"זמן מרכז (ארה\\"ב וקנדה)","Chatham Is.":"איי צ\'טהאם","Chennai":"צ\'נאי","Chihuahua":"צ\'יוואווה","Chongqing":"צ\'ונגצ\'ינג","Copenhagen":"קופנהגן","Darwin":"דרווין","Dhaka":"דאקה","Dublin":"דבלין","Eastern Time (US & Canada)":"שעון החוף מזרחי (ארה\\"ב וקנדה)","Edinburgh":"אדינבורו","Ekaterinburg":"יקטרינבורג","Fiji":"פיג\'י","Georgetown":"ג\'ורג\'טאון","Greenland":"גרינלנד","Guadalajara":"גוודלחרה","Guam":"גואם","Hanoi":"האנוי","Harare":"הרארה","Hawaii":"הוואי","Helsinki":"הלסינקי","Hobart":"הוברט","Hong Kong":"הונג קונג","Indiana (East)":"אינדיאנה (מזרח)","International Date Line West":"קו התאריך הבינלאומי מערב","Irkutsk":"אירקוטסק","Islamabad":"איסלמבאד","Istanbul":"איסטנבול","Jakarta":"ג\'קרטה","Jerusalem":"ירושלים","Kabul":"קאבול","Kaliningrad":"קלינינגרד","Kamchatka":"קמצ\'טקה","Karachi":"קראצ\'י","Kathmandu":"קטמנדו","Kolkata":"כלכותה","Krasnoyarsk":"קרסנויארסק","Kuala Lumpur":"קואלה לומפור","Kuwait":"כווית","Kyev":"קייב","Kyiv":"קייב","La Paz":"לה פאס","Lima":"לימה","Lisbon":"ליסבון","Ljubljana":"לובליאנה","London":"לונדון","Madrid":"מדריד","Magadan":"מגדן","Marshall Is.":"איי מרשל","Mazatlan":"מזטלן","Melbourne":"מלבורן","Mexico City":"מקסיקו סיטי","Mid-Atlantic":"אמצע האטלנטי","Midway Island":"איי מידוויי","Minsk":"מינסק","Monrovia":"מונרוביה","Monterrey":"מונטריי","Montevideo":"מונטווידאו","Moscow":"מוסקבה","Mountain Time (US & Canada)":"זמן הרים (ארה\\"ב וקנדה)","Mumbai":"מומבאי","Muscat":"מוסקט","Nairobi":"ניירובי","New Caledonia":"קלדוניה החדשה","New Delhi":"ניו דלהי","Newfoundland":"ניופאונדלנד","Novosibirsk":"נובוסיבירסק","Nuku\'alofa":"נוקואלופה","Osaka":"אוסקה","Pacific Time (US & Canada)":"שעון החוף המערבי (ארה\\"ב וקנדה)","Paris":"פריז","Perth":"פרת\'","Port Moresby":"פורט מורסבי","Prague":"פראג","Pretoria":"פרטוריה","Quito":"קיטו","Rangoon":"רנגון","Riga":"ריגה","Riyadh":"ריאד","Rome":"רומא","Samara":"סמרה","Samoa":"סמואה","Santiago":"סנטיאגו","Sapporo":"סאפורו","Sarajevo":"סרייבו","Saskatchewan":"ססקצ\'ואן","Seoul":"סיאול","Singapore":"סינגפור","Skopje":"סקופיה","Sofia":"סופיה","Solomon Is.":"איי שלמה","Srednekolymsk":"סרדנקולימסק","Sri Jayawardenepura":"סרי ג\'אווארדנפורה","St. Petersburg":"סנט פטרסבורג","Stockholm":"שטוקהולם","Sydney":"סידני","Taipei":"טייפה","Tallinn":"טאלין","Tashkent":"טשקנט","Tbilisi":"טביליסי","Tehran":"טהרן","Tijuana":"טיחואנה","Tokelau Is.":"איי טוקלאו","Tokyo":"טוקיו","UTC":"זמן UTC","Ulaan Bataar":"אולאן באטאר","Ulaanbaatar":"אולן בטור","Urumqi":"אורומקי","Vienna":"וינה","Vilnius":"וילנה","Vladivostok":"ולדיווסטוק","Volgograd":"וולגוגרד","Warsaw":"ורשה","Wellington":"וולינגטון","West Central Africa":"מערב מרכז אפריקה","Yakutsk":"יאקוטסק","Yerevan":"ירוואן","Zagreb":"זאגרב","Zurich":"ציריך"}}'));
I18n.translations.it = I18n.extend((I18n.translations.it || {}), JSON.parse('{"timezones":{"Abu Dhabi":"Abu Dhabi","Adelaide":"Adelaide","Alaska":"Alaska","Almaty":"Almaty","American Samoa":"Samoa Americane","Amsterdam":"Amsterdam","Arizona":"Arizona","Astana":"Astana","Athens":"Atene","Atlantic Time (Canada)":"Fuso orario atlantico (Canada)","Auckland":"Auckland","Azores":"Azzorre","Baghdad":"Baghdad","Baku":"Baku","Bangkok":"Bangkok","Beijing":"Pechino","Belgrade":"Belgrado","Berlin":"Berlino","Bern":"Berna","Bogota":"Bogotà","Brasilia":"Brasilia","Bratislava":"Bratislava","Brisbane":"Brisbane","Brussels":"Bruxelles","Bucharest":"Bucarest","Budapest":"Budapest","Buenos Aires":"Buenos Aires","Cairo":"Il Cairo","Canberra":"Canberra","Cape Verde Is.":"Isole di Capo Verde","Caracas":"Caracas","Casablanca":"Casablanca","Central America":"America Centrale","Central Time (US & Canada)":"Fuso orario centrale (USA e Canada)","Chatham Is.":"Isole Chatham","Chennai":"Chennai","Chihuahua":"Chihuahua","Chongqing":"Chongqing","Copenhagen":"Copenhagen","Darwin":"Darwin","Dhaka":"Dacca","Dublin":"Dublino","Eastern Time (US & Canada)":"Fuso orario orientale (USA e Canada)","Edinburgh":"Edimburgo","Ekaterinburg":"Ekaterinburg","Fiji":"Figi","Georgetown":"Georgetown","Greenland":"Groenlandia","Guadalajara":"Guadalajara","Guam":"Guam","Hanoi":"Hanoi","Harare":"Harare","Hawaii":"Hawaii","Helsinki":"Helsinki","Hobart":"Hobart","Hong Kong":"Hong Kong","Indiana (East)":"Indiana (est)","International Date Line West":"Linea del cambiamento di data ovest","Irkutsk":"Irkutsk","Islamabad":"Islamabad","Istanbul":"Istanbul","Jakarta":"Giacarta","Jerusalem":"Gerusalemme","Kabul":"Kabul","Kaliningrad":"Kaliningrad","Kamchatka":"Kamčatka","Karachi":"Karachi","Kathmandu":"Katmandu","Kolkata":"Calcutta","Krasnoyarsk":"Krasnojarsk","Kuala Lumpur":"Kuala Lumpur","Kuwait":"Kuwait","Kyiv":"Kiev","La Paz":"La Paz","Lima":"Lima","Lisbon":"Lisbona","Ljubljana":"Lubiana","London":"Londra","Madrid":"Madrid","Magadan":"Magadan","Marshall Is.":"Isole Marshall","Mazatlan":"Mazatlan","Melbourne":"Melbourne","Mexico City":"Città del Messico","Mid-Atlantic":"Atlantico centrale","Midway Island":"Isola di Midway","Minsk":"Minsk","Monrovia":"Monrovia","Monterrey":"Monterrey","Montevideo":"Montevideo","Moscow":"Mosca","Mountain Time (US & Canada)":"Fuso orario delle Montagne Rocciose (USA e Canada)","Mumbai":"Mumbai","Muscat":"Mascate","Nairobi":"Nairobi","New Caledonia":"Nuova Caledonia","New Delhi":"Nuova Delhi","Newfoundland":"Terranova","Novosibirsk":"Novosibirsk","Nuku\'alofa":"Nuku\'alofa","Osaka":"Osaka","Pacific Time (US & Canada)":"Fuso orario del Pacifico (USA e Canada)","Paris":"Parigi","Perth":"Perth","Port Moresby":"Port Moresby","Prague":"Praga","Pretoria":"Pretoria","Quito":"Quito","Rangoon":"Rangoon","Riga":"Riga","Riyadh":"Riyad","Rome":"Roma","Samara":"Samara","Samoa":"Samoa","Santiago":"Santiago","Sapporo":"Sapporo","Sarajevo":"Sarajevo","Saskatchewan":"Saskatchewan","Seoul":"Seul","Singapore":"Singapore","Skopje":"Skopje","Sofia":"Sofia","Solomon Is.":"Isole Salomone","Srednekolymsk":"Srednekolymsk","Sri Jayawardenepura":"Sri Jayawardenapura","St. Petersburg":"San Pietroburgo","Stockholm":"Stoccolma","Sydney":"Sydney","Taipei":"Taipei","Tallinn":"Tallinn","Tashkent":"Tashkent","Tbilisi":"Tbilisi","Tehran":"Teheran","Tijuana":"Tijuana","Tokelau Is.":"Isole Tokelau","Tokyo":"Tokyo","UTC":"UTC","Ulaan Bataar":"Ulan Bator","Ulaanbaatar":"Ulaanbaatar","Urumqi":"Ürümqi","Vienna":"Vienna","Vilnius":"Vilnius","Vladivostok":"Vladivostok","Volgograd":"Volgograd","Warsaw":"Varsavia","Wellington":"Wellington","West Central Africa":"Africa centrale occidentale","Yakutsk":"Jakutsk","Yerevan":"Erevan","Zagreb":"Zagabria","Zurich":"Zurigo"}}'));
I18n.translations.ja = I18n.extend((I18n.translations.ja || {}), JSON.parse('{"timezones":{"Abu Dhabi":"アブダビ","Adelaide":"アデレード","Alaska":"アラスカ","Almaty":"アルマトイ","American Samoa":"米領サモア","Amsterdam":"アムステルダム","Arizona":"アリゾナ","Astana":"アスタナ","Athens":"アテネ","Atlantic Time (Canada)":"大西洋時間（カナダ）","Auckland":"オークランド","Azores":"アゾレス","Baghdad":"バグダッド","Baku":"バクー","Bangkok":"バンコク","Beijing":"北京","Belgrade":"ベオグラード","Berlin":"ベルリン","Bern":"ベルン","Bogota":"ボゴタ","Brasilia":"ブラジリア","Bratislava":"ブラチスラバ","Brisbane":"ブリスベン","Brussels":"ブリュッセル","Bucharest":"ブカレスト","Budapest":"ブダペスト","Buenos Aires":"ブエノスアイレス","Cairo":"カイロ","Canberra":"キャンベラ","Cape Verde Is.":"カーボベルデ諸島","Caracas":"カラカス","Casablanca":"カサブランカ","Central America":"中央アメリカ","Central Time (US & Canada)":"中部時間（米国とカナダ）","Chatham Is.":"チャタム島","Chennai":"チェンナイ","Chihuahua":"チワワ","Chongqing":"重慶","Copenhagen":"コペンハーゲン","Darwin":"ダーウィン","Dhaka":"ダッカ","Dublin":"ダブリン","Eastern Time (US & Canada)":"東部時間（米国とカナダ）","Edinburgh":"エディンバラ","Ekaterinburg":"エカテリンブルク","Fiji":"フィジー","Georgetown":"ジョージタウン","Greenland":"グリーンランド","Guadalajara":"グアダラハラ","Guam":"グアム","Hanoi":"ハノイ","Harare":"ハラレ","Hawaii":"ハワイ","Helsinki":"ヘルシンキ","Hobart":"ホバート","Hong Kong":"香港","Indiana (East)":"インディアナ（東）","International Date Line West":"日付変更線西側","Irkutsk":"イルクーツク","Islamabad":"イスラマバード","Istanbul":"イスタンブール","Jakarta":"ジャカルタ","Jerusalem":"エルサレム","Kabul":"カブール","Kaliningrad":"カリーニングラード","Kamchatka":"カムチャツカ","Karachi":"カラチ","Kathmandu":"カトマンズ","Kolkata":"コルカタ","Krasnoyarsk":"クラスノヤルスク","Kuala Lumpur":"クアラルンプール","Kuwait":"クウェート","Kyiv":"キエフ","La Paz":"ラパス","Lima":"リマ","Lisbon":"リスボン","Ljubljana":"リュブリャナ","London":"ロンドン","Madrid":"マドリード","Magadan":"マガダン","Marshall Is.":"マーシャル諸島","Mazatlan":"マサトラン","Melbourne":"メルボルン","Mexico City":"メキシコシティ","Mid-Atlantic":"中部大西洋","Midway Island":"ミッドウェー島","Minsk":"ミンスク","Monrovia":"モンロビア","Monterrey":"モントレー","Montevideo":"モンテビデオ","Moscow":"モスクワ","Mountain Time (US & Canada)":"山岳部時間（米国とカナダ）","Mumbai":"ムンバイ","Muscat":"マスカット","Nairobi":"ナイロビ","New Caledonia":"ニューカレドニア","New Delhi":"ニューデリー","Newfoundland":"ニューファンドランド","Novosibirsk":"ノボシビルスク","Nuku\'alofa":"ヌクアロファ","Osaka":"大阪","Pacific Time (US & Canada)":"太平洋時間（米国とカナダ）","Paris":"パリ","Perth":"パース","Port Moresby":"ポートモレスビー","Prague":"プラハ","Pretoria":"プレトリア","Quito":"キト","Rangoon":"ヤンゴン","Riga":"リガ","Riyadh":"リヤド","Rome":"ローマ","Samara":"サマーラ","Samoa":"サモア","Santiago":"サンティアゴ","Sapporo":"札幌","Sarajevo":"サラエボ","Saskatchewan":"サスカチュワン","Seoul":"ソウル","Singapore":"シンガポール","Skopje":"スコピエ","Sofia":"ソフィア","Solomon Is.":"ソロモン諸島","Srednekolymsk":"スレドネコリムスク","Sri Jayawardenepura":"スリジャヤワルダナプラ","St. Petersburg":"サンクトペテルブルク","Stockholm":"ストックホルム","Sydney":"シドニー","Taipei":"台北","Tallinn":"タリン","Tashkent":"タシュケント","Tbilisi":"トビリシ","Tehran":"テヘラン","Tijuana":"ティファナ","Tokelau Is.":"トケラウ諸島","Tokyo":"東京","UTC":"UTC","Ulaan Bataar":"ウランバートル","Ulaanbaatar":"ウランバートル","Urumqi":"ウルムチ","Vienna":"ウィーン","Vilnius":"ビリニュス","Vladivostok":"ウラジオストク","Volgograd":"ボルゴグラード","Warsaw":"ワルシャワ","Wellington":"ウェリントン","West Central Africa":"西部中央アフリカ","Yakutsk":"ヤクーツク","Yerevan":"エレバン","Zagreb":"ザグレブ","Zurich":"チューリッヒ"}}'));
I18n.translations.ko = I18n.extend((I18n.translations.ko || {}), JSON.parse('{"timezones":{"Abu Dhabi":"아부다비","Adelaide":"애들레이드","Alaska":"알래스카","Almaty":"알마티","American Samoa":"미국령 사모아","Amsterdam":"암스테르담","Arizona":"애리조나","Astana":"아스타나","Athens":"아테네","Atlantic Time (Canada)":"대서양 시간대 (캐나다)","Auckland":"오클랜드","Azores":"아소르스","Baghdad":"바그다드","Baku":"바쿠","Bangkok":"방콕","Beijing":"베이징","Belgrade":"베오그라드","Berlin":"베를린","Bern":"베른","Bogota":"보고타","Brasilia":"브라질리아","Bratislava":"브라티슬라바","Brisbane":"브리즈번","Brussels":"브뤼셀","Bucharest":"부쿠레슈티","Budapest":"부다페스트","Buenos Aires":"부에노스아이레스","Cairo":"카이로","Canberra":"캔버라","Cape Verde Is.":"카보베르데 제도","Caracas":"카라카스","Casablanca":"카사블랑카","Central America":"중앙 아메리카","Central Time (US & Canada)":"중부 시간대 (미국 & 캐나다)","Chatham Is.":"채텀 아일랜드","Chennai":"첸나이","Chihuahua":"치와와","Chongqing":"충칭","Copenhagen":"코펜하겐","Darwin":"다윈","Dhaka":"다카","Dublin":"더블린","Eastern Time (US & Canada)":"동부 시간대 (미국 & 캐나다)","Edinburgh":"에든버러","Ekaterinburg":"예카테린부르크","Fiji":"피지","Georgetown":"조지타운","Greenland":"그린란드","Guadalajara":"과달라하라","Guam":"괌","Hanoi":"하노이","Harare":"하라레","Hawaii":"하와이","Helsinki":"헬싱키","Hobart":"호바트","Hong Kong":"홍콩","Indiana (East)":"인디애나 (동쪽)","International Date Line West":"날짜 변경선 서쪽","Irkutsk":"이르쿠츠크","Islamabad":"이슬라마바드","Istanbul":"이스탄불","Jakarta":"자카르타","Jerusalem":"예루살렘","Kabul":"카불","Kaliningrad":"칼리닌그라드","Kamchatka":"캄차카","Karachi":"카라치","Kathmandu":"카트만두","Kolkata":"콜카타","Krasnoyarsk":"크라스노야르스크","Kuala Lumpur":"쿠알라룸푸르","Kuwait":"쿠웨이트","Kyiv":"키예프","La Paz":"라파스","Lima":"리마","Lisbon":"리스본","Ljubljana":"류블랴냐","London":"런던","Madrid":"마드리드","Magadan":"마가단","Marshall Is.":"마셜 제도","Mazatlan":"마사티안","Melbourne":"멜버른","Mexico City":"멕시코시티","Mid-Atlantic":"중앙 대서양","Midway Island":"미드웨이 섬","Minsk":"민스크","Monrovia":"몬로비아","Monterrey":"몬테레이","Montevideo":"몬테비데오","Moscow":"모스크바","Mountain Time (US & Canada)":"산악 시간대 (미국 & 캐나다)","Mumbai":"뭄바이","Muscat":"무스카트","Nairobi":"나이로비","New Caledonia":"누벨칼레도니","New Delhi":"뉴델리","Newfoundland":"뉴펀들랜드","Novosibirsk":"노보시비르스크","Nuku\'alofa":"누쿠알로파","Osaka":"오사카","Pacific Time (US & Canada)":"태평양 시간대 (미국 & 캐나다)","Paris":"파리","Perth":"퍼스","Port Moresby":"포트모르즈비","Prague":"프라하","Pretoria":"프리토리아","Quito":"키토","Rangoon":"양곤","Riga":"리가","Riyadh":"리야드","Rome":"로마","Samara":"사마라","Samoa":"사모아","Santiago":"산티아고","Sapporo":"삿포로","Sarajevo":"사라예보","Saskatchewan":"서스캐처원","Seoul":"서울","Singapore":"싱가포르","Skopje":"스코페","Sofia":"소피아","Solomon Is.":"솔로몬 제도","Srednekolymsk":"스레드네콜림스크","Sri Jayawardenepura":"스리자야와르데네푸라","St. Petersburg":"상트페테르부르크","Stockholm":"스톡홀름","Sydney":"시드니","Taipei":"타이페이","Tallinn":"탈린","Tashkent":"타슈켄트","Tbilisi":"트빌리시","Tehran":"테헤란","Tijuana":"티후아나","Tokelau Is.":"토켈라우 제도","Tokyo":"도쿄","UTC":"UTC","Ulaan Bataar":"울란바토르","Ulaanbaatar":"울란바토르","Urumqi":"우루무치","Vienna":"빈","Vilnius":"빌뉴스","Vladivostok":"블라디보스토크","Volgograd":"볼고그라드","Warsaw":"바르샤뱌","Wellington":"웰링턴","West Central Africa":"서부 중앙 아프리카","Yakutsk":"야쿠츠크","Yerevan":"예레반","Zagreb":"자그레브","Zurich":"취리히"}}'));
I18n.translations.nl = I18n.extend((I18n.translations.nl || {}), JSON.parse('{"timezones":{"Abu Dhabi":"Abu Dhabi","Adelaide":"Adelaide","Alaska":"Alaska","Almaty":"Almaty","American Samoa":"Amerikaans-Samoa","Amsterdam":"Amsterdam","Arizona":"Arizona","Astana":"Astana","Athens":"Athene","Atlantic Time (Canada)":"Atlantische Tijd (Canada)","Auckland":"Auckland","Azores":"Azoren","Baghdad":"Bagdad","Baku":"Bakoe","Bangkok":"Bangkok","Beijing":"Beijing","Belgrade":"Belgrado","Berlin":"Berlijn","Bern":"Bern","Bogota":"Bogota","Brasilia":"Brazilië","Bratislava":"Bratislava","Brisbane":"Brisbane","Brussels":"Brussel","Bucharest":"Boekarest","Budapest":"Boedapest","Buenos Aires":"Buenos Aires","Cairo":"Kairo","Canberra":"Canberra","Cape Verde Is.":"Kaapverdische Eilanden.","Caracas":"Caracas","Casablanca":"Casablanca","Central America":"Midden-Amerika","Central Time (US & Canada)":"Centrale Tijd (VS & Canada)","Chatham Is.":"Chatham Eilanden","Chennai":"Chennai","Chihuahua":"Chihuahua","Chongqing":"Chongqing","Copenhagen":"Kopenhagen","Darwin":"Darwin","Dhaka":"Dhaka","Dublin":"Dublin","Eastern Time (US & Canada)":"Oostelijke Tijd (VS & Canada)","Edinburgh":"Edinburgh","Ekaterinburg":"Jekaterinenburg","Fiji":"Fiji","Georgetown":"Georgetown","Greenland":"Groenland","Guadalajara":"Guadalajara","Guam":"Guam","Hanoi":"Hanoi","Harare":"Harare","Hawaii":"Hawaii","Helsinki":"Helsinki","Hobart":"Hobart","Hong Kong":"Hong Kong","Indiana (East)":"Indiana (Oosten)","International Date Line West":"Internationale datumgrens West","Irkutsk":"Irkoetsk","Islamabad":"Islamabad","Istanbul":"Istanboel","Jakarta":"Jakarta","Jerusalem":"Jeruzalem","Kabul":"Kaboel","Kaliningrad":"Kaliningrad","Kamchatka":"Kamtsjatka","Karachi":"Karachi","Kathmandu":"Kathmandu","Kolkata":"Calcutta","Krasnoyarsk":"Krasnojarsk","Kuala Lumpur":"Kuala Lumpur","Kuwait":"Koeweit","Kyiv":"Kiev","La Paz":"La Paz","Lima":"Vijf","Lisbon":"Lissabon","Ljubljana":"Ljubljana","London":"Londen","Madrid":"Madrid","Magadan":"Magadan","Marshall Is.":"Marshall Eilanden.","Mazatlan":"Mazatlan","Melbourne":"Melbourne","Mexico City":"Mexico-Stad","Mid-Atlantic":"Medio Atlantische Oceaan","Midway Island":"Midway Eiland","Minsk":"Minsk","Monrovia":"Monrovia","Monterrey":"Monterrey","Montevideo":"Montevideo","Moscow":"Moskou","Mountain Time (US & Canada)":"Bergtijd (VS & Canada)","Mumbai":"Mumbai","Muscat":"Muscat","Nairobi":"Nairobi","New Caledonia":"Nieuw-Caledonië","New Delhi":"Nieuw Delhi","Newfoundland":"Newfoundland","Novosibirsk":"Novosibirsk","Nuku\'alofa":"Nuku\'alofa","Osaka":"Osaka","Pacific Time (US & Canada)":"Pacifische Tijd (V.S. & Canada)","Paris":"Parijs","Perth":"Perth","Port Moresby":"Port Moresby","Prague":"Praag","Pretoria":"Pretoria","Quito":"Quito","Rangoon":"Rangoon","Riga":"Riga","Riyadh":"Riyad","Rome":"Rome","Samara":"Samara","Samoa":"Samoa","Santiago":"Santiago","Sapporo":"Sapporo","Sarajevo":"Sarajevo","Saskatchewan":"Saskatchewan","Seoul":"Seoel","Singapore":"Singapore","Skopje":"Skopje","Sofia":"Sofia","Solomon Is.":"Solomon Eilanden","Srednekolymsk":"Srednekolymsk","Sri Jayawardenepura":"Sri Jayawardenepura","St. Petersburg":"Sint-Petersburg","Stockholm":"Stockholm","Sydney":"Sydney","Taipei":"Taipei","Tallinn":"Tallin","Tashkent":"Tasjkent","Tbilisi":"Tbilisi","Tehran":"Teheran","Tijuana":"Tijuana","Tokelau Is.":"Tokelau Eilanden","Tokyo":"Tokyo","UTC":"UTC","Ulaan Bataar":"Ulaan Bataar","Ulaanbaatar":"Ulaanbaatar","Urumqi":"Urumqi","Vienna":"Wenen","Vilnius":"Vilnius","Vladivostok":"Vladivostok","Volgograd":"Volgograd","Warsaw":"Warschau","Wellington":"Wellington","West Central Africa":"West Centraal-Afrika","Yakutsk":"Yakutsk","Yerevan":"Yerevan","Zagreb":"Zagreb","Zurich":"Zurich"}}'));
I18n.translations.pl = I18n.extend((I18n.translations.pl || {}), JSON.parse('{"timezones":{"Abu Dhabi":"Abu Dabi","Adelaide":"Adelajda","Alaska":"Alaska","Almaty":"Ałma-Ata","American Samoa":"Samoa Amerykańskie","Amsterdam":"Amsterdam","Arizona":"Arizona","Astana":"Astana","Athens":"Ateny","Atlantic Time (Canada)":"Czas atlantycki (Kanada)","Auckland":"Auckland","Azores":"Azory","Baghdad":"Bagdad","Baku":"Baku","Bangkok":"Bangkok","Beijing":"Pekin","Belgrade":"Belgrad","Berlin":"Berlin","Bern":"Berno","Bogota":"Bogota","Brasilia":"Brasília","Bratislava":"Bratysława","Brisbane":"Brisbane","Brussels":"Bruksela","Bucharest":"Bukareszt","Budapest":"Budapeszt","Buenos Aires":"Buenos Aires","Cairo":"Kair","Canberra":"Canberra","Cape Verde Is.":"Wyspy Zielonego Przylądka","Caracas":"Caracas","Casablanca":"Casablanca","Central America":"Ameryka Środkowa","Central Time (US & Canada)":"Czas centralny (USA i Kanada)","Chatham Is.":"Wyspy Chatham","Chennai":"Chennai","Chihuahua":"Chihuahua","Chongqing":"Chongqing","Copenhagen":"Kopenhaga","Darwin":"Darwin","Dhaka":"Dhaka","Dublin":"Dublin","Eastern Time (US & Canada)":"Czas wschodni (USA i Kanada)","Edinburgh":"Edynburg","Ekaterinburg":"Jekaterynburg","Fiji":"Fidżi","Georgetown":"Georgetown","Greenland":"Grenlandia","Guadalajara":"Guadalajara","Guam":"Guam","Hanoi":"Hanoi","Harare":"Harare","Hawaii":"Hawaje","Helsinki":"Helsinki","Hobart":"Hobart","Hong Kong":"Hongkong","Indiana (East)":"Indiana (Wschodnia)","International Date Line West":"Linia zmiany daty","Irkutsk":"Irkuck","Islamabad":"Islamabad","Istanbul":"Stambuł","Jakarta":"Dżakarta","Jerusalem":"Jerozolima","Kabul":"Kabul","Kaliningrad":"Kaliningrad","Kamchatka":"Kamczatka","Karachi":"Karaczi","Kathmandu":"Katmandu","Kolkata":"Kalkuta","Krasnoyarsk":"Krasnojarsk","Kuala Lumpur":"Kuala Lumpur","Kuwait":"Kuwejt","Kyiv":"Kijów","La Paz":"La Paz","Lima":"Lima","Lisbon":"Lizbona","Ljubljana":"Lublana","London":"Londyn","Madrid":"Madryt","Magadan":"Magadan","Marshall Is.":"Wyspy Marshalla","Mazatlan":"Mazatlan","Melbourne":"Melbourne","Mexico City":"Meksyk","Mid-Atlantic":"Środkowoatlantycki","Midway Island":"Wyspa Midway","Minsk":"Mińsk","Monrovia":"Monrovia","Monterrey":"Monterrey","Montevideo":"Montevideo","Moscow":"Moskwa","Mountain Time (US & Canada)":"Czas górski (USA i Kanada)","Mumbai":"Bombaj","Muscat":"Muskat","Nairobi":"Nairobi","New Caledonia":"Nowa Kaledonia","New Delhi":"Nowe Delhi","Newfoundland":"Nowa Funlandia","Novosibirsk":"Nowosybirsk","Nuku\'alofa":"Nuku\'alofa","Osaka":"Osaka","Pacific Time (US & Canada)":"Czas pacyficzny (USA i Kanada)","Paris":"Paryż","Perth":"Perth","Port Moresby":"Port Moresby","Prague":"Praga","Pretoria":"Pretoria","Quito":"Quito","Rangoon":"Rangun","Riga":"Ryga","Riyadh":"Rijad","Rome":"Rzym","Samara":"Samara","Samoa":"Samoa","Santiago":"Santiago","Sapporo":"Sapporo","Sarajevo":"Sarajewo","Saskatchewan":"Saskatchewan","Seoul":"Seul","Singapore":"Singapur","Skopje":"Skopje","Sofia":"Sofia","Solomon Is.":"Wyspy Salomona","Srednekolymsk":"Sriedniekołymsk","Sri Jayawardenepura":"Sri Jayawardenepura","St. Petersburg":"Petersburg","Stockholm":"Sztokholm","Sydney":"Sydney","Taipei":"Tajpej","Tallinn":"Tallinn","Tashkent":"Taszkient","Tbilisi":"Tbilisi","Tehran":"Teheran","Tijuana":"Tijuana","Tokelau Is.":"Wyspy Tokelau","Tokyo":"Tokio","UTC":"UTC","Ulaan Bataar":"Ułan Bator","Ulaanbaatar":"Ułan Bator","Urumqi":"Urumczi","Vienna":"Wiedeń","Vilnius":"Wilno","Vladivostok":"Władywostok","Volgograd":"Wołgograd","Warsaw":"Warszawa","Wellington":"Wellington","West Central Africa":"Afryka Środkowo-Zachodnia","Yakutsk":"Jakuck","Yerevan":"Erywań","Zagreb":"Zagrzeb","Zurich":"Zurych"}}'));
I18n.translations.pt = I18n.extend((I18n.translations.pt || {}), JSON.parse('{"action":{"action":"Ações","action_device":"Por favor configure dispositivos alvo！","action_input":"Nome da ação não pode estar vazio！","action_name":"Nome da ação","activate":"Ativar","add_action":"Adicionar ação","add_action_type":"Por favor insira o nome da ação:","add_favorite":"Adicionar aos atalhos?","change_action":"Quer mudar a ação","current_action":"Ação Atual","delete_action":"apagar ação","favorite":"AÇÕES FAVORITAS","remove_favorite":"Remover dos atalhos"},"automation":{"automation_type":"Tipo de automação","by_sunrise":"Ao nascer / pôr do sol","city":"Cidade","current_located_city":"Cidade localizada atual","current_location":"Localização atual","do":"Faz","if":"E se","locating":"Localizando ...","new_automation":"Nova automação","only_app":"Suporte apenas no Presen App","relocate":"Realocar","select_conditions":"Selecionar condições","select_location":"Selecionar local","select_target_type":"Selecione o tipo de destino","sunrise":"Nascer do sol","sunset":"Pôr do sol"},"dashboard":{"change_pin":"Alterar PIN","enter_pin":"Digite o PIN do seu controlador","set_pin":"Configure o PIN do seu controlador","show_password":"Mostrar senha"},"date":{"abbr_day_names":["Dom","Seg","Ter","Qua","Qui","Sex","Sáb"],"abbr_month_names":[null,"Jan","Fev","Mar","Abr","Mai","Jun","Jul","Ago","Set","Out","Nov","Dez"],"day_names":["Domingo","Segunda-feira","Terça-feira","Quarta-feira","Quinta-feira","Sexta-feira","Sábado"],"formats":{"default":"%d/%m/%Y","long":"%d de %B de %Y","short":"%d de %B"},"month_names":[null,"Janeiro","Fevereiro","Março","Abril","Maio","Junho","Julho","Agosto","Setembro","Outubro","Novembro","Dezembro"],"order":["day","month","year"]},"datetime":{"distance_in_words":{"about_x_hours":{"one":"aproximadamente 1 hora","other":"aproximadamente %{count} horas"},"about_x_months":{"one":"aproximadamente 1 mês","other":"aproximadamente %{count} meses"},"about_x_years":{"one":"aproximadamente 1 ano","other":"aproximadamente %{count} anos"},"almost_x_years":{"one":"quase 1 ano","other":"quase %{count} anos"},"half_a_minute":"meio minuto","less_than_x_minutes":{"one":"menos de um minuto","other":"menos de %{count} minutos"},"less_than_x_seconds":{"one":"menos de 1 segundo","other":"menos de %{count} segundos"},"over_x_years":{"one":"mais de 1 ano","other":"mais de %{count} anos"},"x_days":{"one":"1 dia","other":"%{count} dias"},"x_minutes":{"one":"1 minuto","other":"%{count} minutos"},"x_months":{"one":"1 mês","other":"%{count} meses"},"x_seconds":{"one":"1 segundo","other":"%{count} segundos"},"x_years":{"one":"1 ano","other":"%{count} anos"}},"prompts":{"day":"Dia","hour":"Hora","minute":"Minuto","month":"Mês","second":"Segundo","year":"Ano"}},"device":{"FLiRS":"Dispositivo FLiRS","battery":"Bateria","battery_operated":"Controle remoto com bateria","camera":"Câmera IP","camera_image":"Link de imagem da câmera IP","camera_image_desp":"Por favor, insira a captura de tela de captura da sua câmera IP. Se esse URL precisar de autenticação para acessar, você também precisará especificar o nome de usuário e a senha nesse URL. Talvez seja necessário consultar o manual da sua câmera IP para obter isso.","camera_name":"Nome da câmera IP","camera_name_error":"O nome da câmera IP não pode estar vazio","camera_screenshot_url_error":"O link da imagem da câmera IP não pode estar vazio","camera_video":"Link de vídeo da câmera IP","camera_video_desp":"Por favor, insira o URL do fluxo de vídeo da sua câmera IP e selecione o formato de vídeo correto. Se esse URL precisar de autenticação para acessar, você também precisará especificar o nome de usuário e a senha nesse URL. Talvez seja necessário consultar o manual da câmera para obter isso.","camera_video_url_error":"O link de vídeo da câmera IP não pode estar vazio","change_color":"Mudar cor","cloud_add_camera":"Adicione uma câmera IP, por favor, adicione no controlador","conf_apply_battery":"Precisamos acordar para ativar as configurações do dispositivo de bateria foram alterados","delay":"atrasado","delete_field_device":"Remover como dispositivo com falha","delete_type":"Que tipo de dispositivo que deseja excluir？","device":"equipamento","device_active":"O dispositivo está ativo","device_dead":"Dispositivo está morto ","device_information":"Informações do dispositivo","device_interviewed":"Dispositivo é entrevistado","device_not_interviewed":"O dispositivo não é totalmente entrevistado","device_operating":"Dispositivo está operando","device_sleeping":"O dispositivo está dormindo","edit_device":"Modificar o nome do dispositivo","force_interview":"Força Entrevista","full":"tudo","interview":"Entrevista","is_awake":"Está acordado","is_sleeping":"Está dormindo","main":"Principal Alimentado","mark_failed":"Marcada como falha","mark_failed_alert":"O controlador verifica se o nó claro, então ele é marcado como falha. Este processo leva cerca de 30 segundos. Depois, se necessário, você pode usar a função \'Excluir\' para excluir nós.","mark_failed_delete":"Tem certeza de que deseja excluir o dispositivo? Para remover um nó do controlador de rede, sem a necessidade de novas medidas. Este processo pode demorar um minuto.","no_device_name":"O nome do dispositivo não pode estar vazio!","off_b":"fechar","on_b":"aberto","operating":"Operativo","operating_time":"Tempo operacional","play":"Jogar","remove_camera_device_hint":"Para excluir um dispositivo de câmera IP, vá para a página de detalhes do dispositivo (clique no nome do dispositivo a ser inserido) para excluir","remove_directly":"Remover diretamente","seconds":"segundo","set_to":"estabelecer","setting":"estabelecer","setup_device":"Por favor, defina o dispositivo de destino","value":"valor","value_changed_to":"Alterar o valor","value_not_stored_indevice":"Mas não na memória do dispositivo","when":"quando"},"errors":{"format":"%{attribute} %{message}","messages":{"accepted":"tem de ser aceite","blank":"não pode estar em branco","confirmation":"não coincide com a confirmação","empty":"não pode estar vazio","equal_to":"tem de ser igual a %{count}","even":"tem de ser par","exclusion":"é reservado","greater_than":"tem de ser maior que %{count}","greater_than_or_equal_to":"tem de ser maior ou igual a %{count}","inclusion":"não está incluído na lista","invalid":"é inválido","less_than":"tem de ser menor que %{count}","less_than_or_equal_to":"tem de ser menor ou igual a %{count}","model_invalid":"A validação falhou: %{errors}","not_a_number":"não é um número","not_an_integer":"tem de ser um inteiro","odd":"tem de ser ímpar","other_than":"tem de ser diferente de %{count}","present":"não pode estar em branco","required":"é obrigatório","taken":"não está disponível","too_long":"é demasiado grande (o máximo é de %{count} caracteres)","too_short":"é demasiado pequeno (o mínimo é de %{count} caracteres)","wrong_length":"comprimento errado (deve ter %{count} caracteres)"},"template":{"body":"Por favor, verifique os seguintes campos:","header":{"one":"1 erro impediu guardar este %{model}","other":"%{count} erros impediram guardar este %{model}"}}},"event":{"inner":"interior","my_device":"My Device","my_room":"meu quarto","open_app":"Open APP","outer":"exterior"},"global":{"account":"CONFIGURAÇÕES DE CONTA","account_setting":"Configurações da conta","activate_action":"Executar esta ação?","activate_scene":"Ativar esta cena?","activate_sure":"Você tem certeza de fazer isso?","add_device_s2_desp":"Por favor, insira os cinco primeiros dígitos da chave do dispositivo, você pode consultar o manual para obter a chave do dispositivo","add_device_s2_title":"Por favor, pressione os botões no dispositivo seguindo o manual do dispositivo para adicionar o dispositivo","add_device_wait":"Aguarde","add_smart_desp":"Por favor, insira a chave completa do dispositivo, que deve ter 40 dígitos, você pode consultar o manual do departamento para obter a chave do dispositivo","add_zigbee_smart_desp":"Digite o endereço MAC do dispositivo e o código de instalação, ambos devem ser impressos no invólucro do dispositivo ou no manual de instruções. Os endereços MAC têm 16 caracteres e o código de instalação é 36.","advanced_setting":"Configurações avançadas","agree":"eu concordo com o","alexa_link_error":"O link com o Alexa falhou, tente novamente!","alexa_link_success":"Você se vinculou ao Alexa e ativou a habilidade Presen Smart Home.","all":"Todos","auto":"Automação","away":"Longe","by_device":"Por alterações no dispositivo","centigrade":"Centígrado","change_datacenter":"Alterar datacenter do controlador","choose_alarm_ringtone":"Para escolher isso tocará um alarme no seu controlador, você precisa pressionar o botão principal no controlador para desativar o alarme","choose_ringtone":"Escolha o toque","click_help":"Verifique as informações da ajuda","click_to_find_more":"Clique para saber mais","cloud_required":"Você precisa se conectar à conexão de internet para usar esse recurso, atualmente você está se conectando à ethernet.","community":"comunidade","connecting":"Conectando...","controller_setting":"CONFIGURAÇÕES DO CONTROLADOR","copyright_policy":"política de direitos autorais","dashboard":"painel de controle","datacenter_setting":"Configurações do datacenter","default":"Padrão","device_failed":"Este dispositivo não responde, você pode tentar desligá-lo ou ligá-lo / desligá-lo ou acioná-lo para testar se está com defeito ou com bateria fraca. Se ele não responder por muito tempo, tente redefini-lo ou removê-lo.","device_not_support":"Este atributo não suporta isso!","download_alert":"Enviado para sua caixa de correio registrada","dsk_error":"A chave do dispositivo é inválida","dsk_input":"A chave do dispositivo deve ter 40 dígitos","enter_your":"Digite seu","error_desp":"Podemos ter um problema, tente novamente!","error_title":"Desculpe, a conexão falhou","every_day":"Todo dia","fahrenheit":"Fahrenheit","grpc_link_error":"Falha na conexão, atualize para tentar novamente","grpc_unlink":"Falha na conexão, tentando reconectar ...","home":"Home","i_agree":"Você precisa concordar com o","input_key":"Chave do dispositivo de entrada","input_ssid":"Insira o SSID ou conecte o wifi","input_wifi_password":"Por favor, digite a senha do wifi!","ins_key":"Chave de instalação","is_alive":"IS Alive","link_with_alexa":"Vincular com Alexa","login_demo":"faça o login com uma conta demo","logout":"sair","my_smarthome":"Minha casa inteligente","need_login":"Por favor faça o login agora","no_data":"sem dados!","off_sn":"Controlar e organizar dispositivos dentro de um controlador.","on_sn":"Quando ativado, você pode gerenciar todos os dispositivos dos controladores ao mesmo tempo e todos os dispositivos podem trabalhar juntos, por exemplo, você pode criar uma Ação para controlar dispositivos múltiplos com controladores diferentes.","open_license":"Licença de código aberto","open_wifi":"WIFI aberto","or":"ou","privacy":"Termos de serviço","privacy_statement":"declaração de privacidade","quick":"Atalhos","release_log":"Atualizar log","remove_device_title":"Por favor, pressione os botões no dispositivo seguindo o manual do dispositivo para removê-lo","s2_add":"S2 Add","s2_device":"Dispositivo S2","scan_key":"Digitalizar chave do dispositivo","scene":"Cena","scene_delete":"Cena padrão não pode ser removida","select_controller":"selecione o controlador","select_this_controller":"Alterne para este controlador","service":"Serviços","setting_wifi":"Setup WIFI","setting_wifi_desp":"WIFI 5G e WIFI no restaurante e aeroporto não são necessários para autenticação na web","sleep":"Dormir","sn_global":"modo global","sn_normal":"modo de controlador único","sn_setting":"Gerenciamento do controlador","ssid":"SSID","target":"Alvo","temperature_setting":"Configurações de temperatura","terms_of_service":"termos de serviço","time_out":"Tempo limite de operação, tente novamente!","timezone_setting":"Configurações do fuso horário","type":"Tipo","use":"usar","user_role_desp":"Você pode compartilhar o acesso do controlador atual com outras pessoas, basta inserir o email que deseja compartilhar aqui. As pessoas com quem você compartilhou só podem usar seus dados, como controlar seus dispositivos, ativar cenas, executar ações, mas não podem fazer alterações em seus dados, como adicionar ou remover dispositivos, criar ou editar cenas etc., e é claro que elas Não é possível redefinir ou reiniciar seu controlador. Depois que o email foi adicionado, o outro usuário precisa reiniciar o aplicativo para entrar em vigor.","vacation":"Férias","wifi_connection":"conexão WIFI","wifi_link_btn":"O led da rede está ativado","wifi_link_desp":"Por favor, pressione o botão superior no controlador até que o led da rede esteja sempre ligado para continuar","wifi_password":"senha WIFI","wired_connection":"Conexão com fio","zwave_delete_db":"f este dispositivo estiver perdido ou com defeito, você pode removê-lo diretamente. Ele aparecerá novamente se continuar se comunicando com seu controlador","zwave_delete_field":"Remova este dispositivo com falha, esta operação poderá falhar se este dispositivo não for reconhecido como um dispositivo com falha"},"guard":{"enter_guard_mode":"Entre no modo de guarda","exit_guard_mode":"Sair do modo de proteção","guard_h1":"Presen pode ajudá-lo a proteger sua casa quando estiver ausente","guard_h2":"ALERTAS INTELIGENTES","guard_h2_deps":"Quando você muda para o Modo de proteção, o Presen pode enviar notificações se o sensor de movimento ou de fumaça for acionado ou a porta for aberta.","guard_h3":"AWAY ILUMINAÇÃO","guard_h3_deps":"O Presen pode ligar e desligar suas luzes automaticamente para parecer que alguém está em casa quando você estiver ausente.","guard_mode":"Modo de guarda"},"help":{"cookie_hint":"Este site usa cookies para melhorar a sua experiência de usuário. Informações detalhadas sobre o uso de cookies neste website são fornecidas em nossa %s. Ao utilizar este site, você concorda com o uso de cookies. %s","privacy":"Privacidade","yes_i_agree":"Sim, concordo..."},"helpers":{"select":{"prompt":"Por favor seleccione"},"submit":{"create":"Criar %{model}","submit":"Gravar %{model}","update":"Actualizar %{model}"}},"home":{"433_device":"433 equipamentos","a":"mais","add_433_device":"Adicionar dispositivo 433M","add_433_node_type_doorlock":"Doorlock","add_433_node_type_other":"Others","add_433_node_type_pir":"PIR","add_433_node_type_smoke_sensor":"Smoke Sensor","add_433_node_type_water_sensor":"Water Sensor","add_433_waiting_for_ports":"Ao verificar a porta serial que o controlador suporta, verifique se o seu controlador possui dispositivos USB de porta serial conectados ...","add_controller":"Adicionar Controlador","add_device":"Adicionar dispositivo","add_device_433_progress_error":"Não encontrou nenhuma porta 433M no seu controlador","add_device_433_progress_loading":"Verificar","add_device_type":"Que tipo de dispositivo que você deseja adicionar?","add_qiock":"Add to Quick Control","add_success":"Adicionar sucesso！","cancel":"cancelado","card_1":"estado de funcionamento do controlador","card_2":"tempo de funcionamento do controlador","card_3":"Número de dispositivos","cellphone":"número de telefone","change_controller":"Alterar Controlador","confirm":"confirmar","controller":"controlador","controller_information":"Informações controlador","controller_input":"nome do controlador","controller_sn_enter":"Por favor, indique o número de série do controlador","controller_state":"Estado controlador","current_controller":"O controlador de corrente","day":"dia","delete_success":"Excluído com sucesso!","detect_device":"equipamento de testes","device":"equipamento","device_input":"Nome do dispositivo","device_management":"Device Management","device_name":"Nome do dispositivo","edit":"editar","edit_controller":"Modificar o nome do controlador","edit_device":"Modificar o nome do dispositivo","edit_success":"Modificar sucesso！","email":"caixa de correio","error":"erro","event":"notícia","failed":"A operação falhou！","favorite":"CENA FAVORITA","forget_password":"Esqueceu sua senha","good_afternoon":"Boa tarde","good_evening":"Boa noite","good_morning":"Bom Dia","have_notify":"Você desmarcou o evento","have_version":"Existe uma nova versão！","home":"casa","indoor_humidity":"Umidade interna","indoor_temp":"Temp interior","ip":"IP","language":"idioma","language_for_mobile":"idioma","local_network":"LAN","login_name":"E-mail ou número de telefone","name":"nome","network_issue":"Problema de rede","network_issue_desp":"Sua rede é muito lenta, você pode tentar reiniciar o aplicativo ou mudar para uma rede rápida","next":"O próximo passo","no_controller":"Nenhum dado do controlador disponível, você precisa primeiro adicionar um controlador no Present Present, ligar o controlador e conectá-lo à Internet. Depois, aguarde alguns segundos para atualizar a tela.","no_controller_input":"O nome do controlador não pode estar vazio！","no_device":"Nenhuma informação interna","not_device_input":"O nome do dispositivo não pode estar vazio！","not_user":"Usuário não existe！","off_line":"off-line","on_line":"on-line","password":"senha","profile":"configuração","qiock_control":"Quick control","recent_event":"Os acontecimentos recentes","recently_devices":"Dispositivos recentemente","refresh":"Atualize o controlador","remove_433_device_hint":"Você pode acessar a página de detalhes do dispositivo (clicar no nome do dispositivo) para remover o dispositivo 433M.","remove_btn":"excluir","remove_device":"excluir dispositivo","remove_qiock":"Remove from Quick Control","room":"quarto","routine":"Rotinas","save":"submeter","scene":"cena","select_433_device":"Os seguintes dispositivos já foram detectados, selecione o dispositivo que deseja adicionar","select_433_node_type":"Selecione o tipo de dispositivo para este dispositivo","select_433_port":"Selecione o dispositivo serial que deseja adicionar a","select_433_protocol":"Selecione o protocolo que o dispositivo suporta","setting":"estabelecer","setting_btn":"estabelecer","sign":"logar","sign_out":"desistir","sign_up":"inscrição","sn":"número de série","sn_error":"número de série não existe！","sn_used":"O número de série já está em uso！","state":"estado","success":"sucesso","successful":"operação bem sucedida!","time_zone":"Escolha o seu fuso horário","token":"código de verificação","trigger":"acoplamento","try_again":"Tente novamente mais tarde！","user":"usuário","user_has":"Usuário já existe！","user_user":"Você não pode adicionar seus próprios！","version":"O número de versão","voice":"Voz","vs_update":"Seja atualizado imediatamente","warning_message":"pronto","wide_network":"WAN","z_device":"equipamentos ZWAVE"},"index":{"433m":"Z-Wave e RF Regular","433m_desp":"Presen suporta alguns sensores RF (433M, 345M, 319M) de marcas famosas, além de dispositivos Z-Wave, e eles podem trabalhar juntos para construir sua segurança e automação residencial.","about":"Quem Somos","about_about":"em","about_desp":"Presen é uma casa inteligente mais simples e inteligente. Concentramos centenas de marcas confiáveis em um aplicativo móvel, para que você possa monitorar, controlar e automatizar facilmente sua casa inteligente onde quer que esteja.","about_desp_three":"Agora imagine se você não precisa imaginar. Porque usa Presen, já está disponível. Coisas como luzes, portas, aparelhos de ar condicionado e switches agora podem funcionar melhor para você, fazendo você se sentir mais seguro, mais controlado, mais eficiente e mais feliz. Com a tecnologia Presen, possibilidades ilimitadas serão conduzidas por suas próprias necessidades e criatividade.","about_desp_two":"Basta pensar nisso se souber quando você não pode ir para casa e como você pode ter certeza. Imagine se eles sempre souberam o que você precisava e quando era necessário. Imagine se soubesse que se conhecia melhor do que você.","about_name":"Sobre Presen","about_our":"Nós nos concentramos na casa inteligente","about_title":"O que é Presen","alerting":"24/7 ALERTANDO","alerting_desp":"Os recursos Presen Events and Notifications podem ser facilmente personalizados para enviar mensagens de emergência e notificação para o seu celular","ali_desp":"Controle de voz sobre os dispositivos seriais do 天猫精灵, controle sua casa apenas com sua voz","app":"APP","automation":"AUTOMAÇÃO FÁCIL","automation_desp":"Com o suporte a Triggers e Scenes, você pode criar vários tipos de cenários de acordo com suas necessidades","build":"Construir o melhor casa inteligente","change":"mudança","contact_address":"endereço","for_live":"viver em","get_app":"Get Free App","has_account":"Já tem uma conta？","help_you":"Ajudá-lo a gerenciar toda a casa","home":"casa","no_account":"Não tem conta？","or_sign_up":"ou registar-se","or_sign_up_type":"Ou utilize a sua","our":"nós","our_app":"My App","product":"Característica","product_desp":"Na estrada, em seu escritório ou em uma praia tropical, você pode controlar sua casa em qualquer lugar. Você pode ter controle total de sua casa a partir da tela do seu PC, smartphone ou qualquer outro dispositivo habilitado para internet.","product_desp_1":"longa introdução","product_name":"Controle e automação domésticos completos","product_name_1":"O primeiro produto","product_title":"Plataforma de automação completa, incluindo controladores locais, servidores em nuvem, aplicativos de usuário e de administração que funcionam juntos de forma integrada.","product_title_1":"Breve introdução","remote":"CONTROLE REMOTO","remote_desp":"Controle a sua casa a partir do seu smartphone ou de qualquer outro dispositivo com acesso à internet, mesmo sem internet na sua LAN, com conexão segura à Internet","routines":"ROTINAS","routines_desp":"As Rotinas Presen permitem que você crie facilmente seus próprios cenários de automação","security":"MELHOR SEGURANÇA","security_desp":"Presen traz para você uma nova geração de proteção de segurança e proteção em casa","sharing":"COMPARTILHAMENTO DE CASA","sharing_desp":"Todos os membros da sua família podem ter suas próprias contas para controlar sua casa com acesso diferente","voice":"CONTROLE DE VOZ","voice_desp":"Controle de voz sobre os dispositivos seriais do Amazon Echo, controle sua casa apenas com sua voz","we_well":"Vamos criar um smart sua casa","work_with":"obras Presen"},"mongoid":{"attributes":{"c/action":{"name":"Nome da ação"},"c/node":{"c_display_name":"Nome do dispositivo"},"c/scene":{"name":"Nome da cena"}},"errors":{"format":"%{attribute} %{message}","messages":{"accepted":"tem de ser aceite","blank":"não pode estar em branco","confirmation":"não coincide com a confirmação","empty":"não pode estar vazio","equal_to":"tem de ser igual a %{count}","even":"tem de ser par","exclusion":"é reservado","greater_than":"tem de ser maior que %{count}","greater_than_or_equal_to":"tem de ser maior ou igual a %{count}","inclusion":"não está incluído na lista","invalid":"é inválido","less_than":"tem de ser menor que %{count}","less_than_or_equal_to":"tem de ser menor ou igual a %{count}","model_invalid":"A validação falhou: %{errors}","not_a_number":"não é um número","not_an_integer":"tem de ser um inteiro","odd":"tem de ser ímpar","other_than":"tem de ser diferente de %{count}","present":"não pode estar em branco","record_invalid":"A validação falhou: %{errors}","required":"é obrigatório","restrict_dependent_destroy":{"has_many":"Não pode ser eliminado por existirem dependências de %{record}","has_one":"Não pode ser eliminado por existir uma dependência de %{record}"},"taken":"não está disponível","too_long":"é demasiado grande (o máximo é de %{count} caracteres)","too_short":"é demasiado pequeno (o mínimo é de %{count} caracteres)","wrong_length":"comprimento errado (deve ter %{count} caracteres)"}}},"number":{"currency":{"format":{"delimiter":" ","format":"%n %u","precision":2,"separator":",","significant":false,"strip_insignificant_zeros":false,"unit":"€"}},"format":{"delimiter":".","precision":3,"separator":",","significant":false,"strip_insignificant_zeros":false},"human":{"decimal_units":{"format":"%n %u","units":{"billion":{"one":"mil milhões","other":"mil milhões"},"million":{"one":"milhão","other":"milhões"},"quadrillion":{"one":"mil biliões","other":"mil biliões"},"thousand":"mil","trillion":{"one":"bilião","other":"biliões"},"unit":""}},"format":{"delimiter":"","precision":1,"significant":true,"strip_insignificant_zeros":true},"storage_units":{"format":"%n %u","units":{"byte":{"one":"Byte","other":"Bytes"},"gb":"GB","kb":"KB","mb":"MB","tb":"TB"}}},"percentage":{"format":{"delimiter":""}},"precision":{"format":{"delimiter":""}}},"room":{"add_room":"Adicionar outro quarto","desp":"EVENTO significa que há alterações de estado do dispositivo nesta sala nas últimas 24 horas.","device_in_room":"O equipamento na sala","event":"evento","filter_room":"Filtrar por quartos","normal":"normal","room_name":"Nome da sala","room_no_device":"Nenhum dispositivo é selecionado！","room_no_name":"Quarto nome não pode estar vazio！","room_switch_hint":"Controlar todos os dispositivos de swtich nesta sala","setting_room":"Definir o quarto"},"routine":{"add":"Adicione uma rotina","address":"Endereço","cond_action":"Ativar uma ação","cond_device":"Ligar dispositivos","cond_scene":"Ligue uma cena","edit":"Editar rotina","get_to":"Chegada","is_cyclic":"Repita","last":"Costas","leave":"Sair","one_cond":"Uma vez","please_address":"Por favor, selecione o local do GPS","please_date":"Por favor, selecione a data","please_leave":"Por favor, selecione chegar ou sair","please_time":"Por favor selecione o tempo","please_to":"Por favor, selecione o que você deseja ativar","please_type":"Por favor, selecione o tipo de rotina","routine":"Rotinas","routine_cond":"Quando","routine_desp":"A rotina pode ativar uma cena ou base de dispositivos na hora ou na localização do GPS (é necessário trabalhar com o Presen App)","routine_location":"Localização GPS","routine_type":"Tipo de rotina","run_multiple":"Repita","run_once":"Vez","select_action":"Selecione ação","select_device":"Selecione dispositivos","select_scene":"Selecione cena","time":"Tempo","to_location":"Por localização GPS","to_time":"Por tempo","ui":"Este recurso precisa trabalhar com Presen App e Presen Cloud, visite https://www.presensmarthome.com"},"scene":{"activate":"ativação","add_scene":"Adicionar cena","add_scene_type":"Digite o nome da cena:","change_scene":"Quer mudar a cena","current_scene":"Cena atual","delete_scene":"apagar cenas","scene_device":"Por favor, adicione um dispositivo！","scene_input":"nome da cena não pode estar vazio！","scene_name":"nome da cena"},"services":{"alexa":"Controle seus dispositivos e automações com dispositivos habilitados para Amazon Alexa.","alexa_desp":"Como \\"Alexa, turn on home scene\\".","ali":"Controle seus dispositivos e automações usam tmallGenie.","ali_desp":"Como \\"天猫精灵, 打开卧室的灯 \\".","google":"Controle seus dispositivos e automações usando dispositivos suportados pelo Google Assistente.","google_desp":"Como \\"Hey Google, activate home\\".","ifttt":"Trabalhe com ifttt para vincular com mais 3º serviços.","ifttt_desp":"Como quando o sensor de movimento é acionado, você receberá uma chamada."},"session":{"60_s":"Tente novamente após 60 segundos！","cellphone_input":"Digite seu número de telemóvel!","change_password":"Change Password","confir_input":"Por favor, confirme a sua senha！","email":"A caixa de correio já existe!","email_input":"Digite o e-mail！","email_or_phone":"E-mail ou número de telefone","email_type":"formato de E-mail não é correto！","get_token":"código de verificação","is_cellphone":"número de telefone já existe！","is_email":"E-mail já existe！","login_name_input":"Favor acessar o número do correio ou telefone","new_input":"Por favor insira uma nova senha！","new_password":"nova senha","no_user_name":"Nome de usuário não pode estar vazio！","not_eamil":"O caixa de correio não existe！","not_email_or_phone":"Favor acessar o seu e-mail ou número","not_phone":"O número de telefone não existe！","not_token":"O código de verificação não está correto！","old_error":"A senha antiga não está correta！","old_input":"Por favor, indique a sua senha antiga！","old_password":"Senha antiga","password_confirmation":"confirmar password","password_input":"Digite sua senha","password_length":"A senha não pode ser inferior a seis！","please_enter_password":"Por favor, insira sua senha","register":"inscrição","reset_password":"Password Reset","send":"enviar","send_eamil":"Enviar código de confirmação para o número de e-mail","send_token":"Um token foi enviado para sua caixa de correio, verifique sua caixa de correio.","signup_success_login":"Conta criada com sucesso, faça o login agora","token_input":"Um código de verificação foi enviado para seu e-mail. Verifique e insira seu código de verificação abaixo para continuar","token_send":"código de verificação enviado com sucesso！","two_password":" As duas senhas não combinam！","user_name":"Nome de Usuário","wrong_name":"nome de usuário ou senha está incorreta！"},"setting":{"add":"Adicionado","advanced_setting":"Configurações avançadas","backup":"Controlador de backup","backup_desp":"Backup Controller","backup_message":"Por favor, não desligue o controlador de backup de energia。。。","base":"Configurações básicas","change_notification":"Alterar notificação","check_device":"Por favor, selecione os atributos dos dispositivos que você não deseja receber notificações de alteração.","check_device_for_urgent":"Só receba notificações urgentes quando alterações de dispositivos a seguir.","check_scene":"Não receber notificações quando sua casa é nas cenas seguintes verificadas, se nenhuma cena é seleccionada, isso significa que todas as notificações de alteração de dispositivo não será enviar não importa qual cena é.","check_scene_for_urgent":"Só receba notificações urgentes quando nas cenas seguintes.","close_account":"Fechar conta","close_desp":"Os usuários não podem entrar depois de fechar a conta! Se você precisar reabrir sua conta, entre em contato conosco!","download_data":"Download de dados","download_desp":"Download de dados pessoais","end_time":"Fim do tempo","friday":"Sexta-feira","inform":"Aviso prévio","local_siren":"Sirene local","login_close_desp":"A conta foi fechada. Para abrir uma conta, entre em contato conosco!","monday":"Segunda-feira","not_end_time":"O tempo de término está definido para:","not_start_time":"Se você não precisar de tempo, defina a hora de início para:","notify":"Centro de notificação","notify_by_device":"Notificação de dispositivos","notify_by_time":"Não perturbe","notify_desp":"Controle de notificação global, você não receberá quaisquer notificações se é folga.","notify_device_noti_desp":"Você não receberá notificações de alteração dos seguintes dispositivos","notify_dnd_desp":"Configurar o tempo que você não deseja receber notificações","notify_time":"Tempo","notify_urgent_device":"Notificação urgente","notify_urgent_noti_desp":"Você receberá uma notificação especial quando alterar estes dispositivos. (Notificação urgente irá ignorar as configurações de ponto não perturbe e dispositivos de notificação)","place_end_time":"Selecione o horário final！","place_select_period":"Selecione a hora!","place_start_time":"Selecione a hora de início!","property":"Propriedades","record":"Restaurar Controller","record_desp":"Restaurar Controller","record_error":"O controlador de restauração falhou! Por favor, tente novamente！","record_message":"Por favor, não desligue a energia para restaurar o controle。。。","record_success":"O sucesso redução controlador！","reset":"controlador de reset","reset_desp":"O controlador é redefinido para os padrões de fábrica. Nota: Todos os quartos e outros dados serão apagados!","reset_message":"O controlador irá apagar todos os dados。。。。","reset_success":"Seu controlador foi redefinido com sucesso.","reset_title":"Redefinir controlador?","restart":"Reiniciar Controlador","restart_desp":"Reinicie o controlador, reiniciar leva cerca de 5 minutos! Após a reinicialização estiver concluída, por favor, atualize a página","restart_message":"Tem certeza de que deseja redefinir o controlador？","restart_title":"Reiniciar o controlador?","saturday":"sábado","select_date":"Selecione a data","select_device_scene":"Selecione o dispositivo ou a cena!","select_period":"período de seleção","start_end":"O tempo de término é menor do que o tempo de início!","starting_time":"Hora de início","sunday":"domingo","thursday":"Quinta-feira","tuesday":"terça","update_desp":"O processo de atualização não desligue a atualização de poder leva cerca de 5 a 7 minutos! Após a conclusão da atualização, por favor, atualize a página","update_desp_one":"Uma nova versão:","update_failed_in_progress":"Já existe uma atualização em andamento.","update_failed_other":"Falha na atualização. Tente novamente mais tarde!","update_failed_up_to_date":"Não há necessidade de atualizar, você está atualizado.","update_online":"Atualizar Controller","update_title":"Atualizando ...","updated":"O actual sistema até à data, não há upgrade!","updating":"Estamos atualizado。。。","upgrade_desp":"O controlador está atualizando, por favor, não desligue a energia do seu controlador, isso levará cerca de 5 minutos para terminar, depende da velocidade da sua rede. O controlador será reiniciado quando terminar e você receberá uma notificação.","upgrade_success_to":"O controlador foi atualizado para","wednesday":"Quarta-feira"},"spec":{"access_control":"Controle de acesso","air_flow":"Fluxo de ar","air_temperature":"Pressão","alarm":"Alarme","alarm_s":"Alarme de indução","angle_position":"Ângulo","app_url":"URL de disparo","appliance":"Aparelhos","atmospheric_pressure":"Pressão","auto":"Automático","aux":"Equipamento auxiliar","auxiliary":"Auxiliar","barometric_pressure":"Pressão","battery":"Bateria","burglar":"Anti-roubo","camera":"Câmera IP","clock":"Relógio","closed":"Fechadas","co":"Monóxido de carbono","co2":"Dióxido de carbono","co2_alarm":"Aviso de dióxido de carbono","co2_level":"Nível de dióxido de carbono","co_alarm":"Aviso de monóxido de carbono","co_level":"Nível de monóxido de carbono","color_switch":"Interruptor de cores","contact_sensor":"Sensor de contato","cool":"Refrigeração","current":"Fluxo de água","dew_point":"Radiação solar","dimmer":"Dimmer","direction":"Direção","distance":"Distância","door_lock":"Fechadura da porta","door_window":"Portas e janelas","electrical_conductivity":"Condutividade","electrical_resistivity":"Coeficiente de resistência","emergency":"Emergência","false":"Não ativado","fire_sensor":"Sensor de incêndio","first":"O primeiro","flood":"Inundado","freeze":"Refrigeração","frequency":"Frequência","full":"Todos","general":"Geral","general_purpose":"Detecção universal","general_purpose_alarm":"Alarme geral","general_purpose_value":"Status de propósito geral","general_trigger":"Evento comum","glass_break":"Sensor quebrado","going_to_low_battery":"vai ficar sem bateria","heat":"Aquecimento","heat_alarm":"Alarme de calor","humidity":"Umidade","is":"Status é","key_fob":"Fob-chave","keypad":"Teclado","loudness":"Volume","low":"Baixo","luminance":"Luz","luminiscence":"Luz","main_powered":"Principal Alimentado","meter":"Instrumento de medição","moisture":"Precipitação","motion":"Detecção de movimento","motor":"Motor","multi_switch":"Interruptor multicanal","n_a":"N/A","no_b":"Normal","normal":"Normal","not_used":"Desconhecido","off_b":"Off","on_b":"Aberto","open":"Aberto","power":"Eletricidade","power_management":"Gerenciamento de energia","quake":"Terremoto","rain_rate":"Velocidade da chuva","relative_humidity":"Umidade relativa","remote_ontrol":"Controle remoto","reserved":"Mantê-lo","resume":"Currículo","return_first_alarm_on_supported_list":"Primeiro aviso","rotation":"Girar","security_repeater":"Repetidor de segurança","seismic_intensity":"Intensidade do terremoto","seismic_magnitude":"Magnitude","sensor":"Sensor","smoke":"Fumaça","smoke_alarm":"Aviso de fumaça","smoke_test":"Teste de fumaça","soil_temperature":"Temperatura do óleo","solar_radiation":"Radiação solar","switch":"Alternar","switch_all":"Toda a mudança","switch_to":"troque para","system":"Sistema","tamper":"Tamper","tank_capacity":"Capacidade","target_temperature":"Temperatura alvo","temperature":"Temperatura","thermostat":"Termostato","thermostat_mode":"Modo de ar condicionado","thermostat_setpoint":"Defina a temperatura","tide_level":"Nível da maré","tilt":"Inclinado","time":"Hora","true":"Triggered","ultraviolet":"Ultravioleta","velocity":"Velocidade","ventilation":"Equipamento de ventilação","vibration":"Vibração","voltage":"Voltagem","water":"Água","water_leak":"Inundado","water_leak_alarm":"Alarme de inundação","water_temperature":"Temperatura da água","weight":"Peso","yes_b":"Triggered"},"support":{"array":{"last_word_connector":", e","two_words_connector":" e ","words_connector":", "}},"time":{"am":"am","formats":{"default":"%A, %d de %B de %Y, %H:%Mh","long":"%A, %d de %B de %Y, %H:%Mh","short":"%d/%m, %H:%M hs"},"pm":"pm"},"timezones":{"Abu Dhabi":"Abu Dabi","Adelaide":"Adelaide","Alaska":"Alasca","Almaty":"Almaty","American Samoa":"Samoa Americana","Amsterdam":"Amsterdã","Arizona":"Arizona","Astana":"Astana","Athens":"Atenas","Atlantic Time (Canada)":"Hora do Atlântico (Canadá)","Auckland":"Auckland","Azores":"Açores","Baghdad":"Bagdá","Baku":"Baku","Bangkok":"Bangoc","Beijing":"Pequim","Belgrade":"Belgrado","Berlin":"Berlim","Bern":"Berna","Bogota":"Bogotá","Brasilia":"Brasília","Bratislava":"Bratislava","Brisbane":"Brisbane","Brussels":"Bruxelas","Bucharest":"Bucareste","Budapest":"Budapeste","Buenos Aires":"Buenos Aires","Cairo":"Cairo","Canberra":"Canberra","Cape Verde Is.":"Ilha de Cabo Verde","Caracas":"Caracas","Casablanca":"Casablanca","Central America":"América Central","Central Time (US & Canada)":"Hora da Região Central (EUA e Canadá)","Chatham Is.":"Ilha Chatham","Chennai":"Chennai","Chihuahua":"Chihuahua","Chongqing":"Chongqing","Copenhagen":"Copenhague","Darwin":"Darwin","Dhaka":"Daca","Dublin":"Dublin","Eastern Time (US & Canada)":"Hora da Região Leste (EUA e Canadá)","Edinburgh":"Edimburgo","Ekaterinburg":"Ekaterinburg","Fiji":"Fiji","Georgetown":"Georgetown","Greenland":"Groenlândia","Guadalajara":"Guadalajara","Guam":"Guam","Hanoi":"Hanoi","Harare":"Harare","Hawaii":"Havaí","Helsinki":"Helsinque","Hobart":"Hobart","Hong Kong":"Hong Kong","Indiana (East)":"Indiana (Leste)","International Date Line West":"Linha Internacional de Data - Oeste","Irkutsk":"Irkutsk","Islamabad":"Islamabad","Istanbul":"Istambul","Jakarta":"Jakarta","Jerusalem":"Jerusalém","Kabul":"Cabul","Kaliningrad":"Kaliningrado","Kamchatka":"Kamchatka","Karachi":"Karachi","Kathmandu":"Kathmandu","Kolkata":"Kolkata","Krasnoyarsk":"Krasnoyarsk","Kuala Lumpur":"Kuala Lumpur","Kuwait":"Kuwait","Kyiv":"Kyiv","La Paz":"La Paz","Lima":"Lima","Lisbon":"Lisboa","Ljubljana":"Liubliana","London":"Londres","Madrid":"Madri","Magadan":"Magadan","Marshall Is.":"Ilhas Marshall","Mazatlan":"Mazatlan","Melbourne":"Melbourne","Mexico City":"Cidade do México","Mid-Atlantic":"Atlântico Médio","Midway Island":"Ilhas Midway","Minsk":"Minsk","Monrovia":"Monróvia","Monterrey":"Monterrey","Montevideo":"Montevidéu","Moscow":"Moscou","Mountain Time (US & Canada)":"Hora das Montanhas (EUA e Canadá)","Mumbai":"Mumbai","Muscat":"Muscat","Nairobi":"Nairobi","New Caledonia":"Nova Caledônia","New Delhi":"Nova Deli","Newfoundland":"Newfoundland","Novosibirsk":"Novosibirsk","Nuku\'alofa":"Nuku\'alofa","Osaka":"Osaka","Pacific Time (US & Canada)":"Hora do Pacífico (EUA e Canadá)","Paris":"Paris","Perth":"Perth","Port Moresby":"Port Moresby","Prague":"Praga","Pretoria":"Pretória","Quito":"Quito","Rangoon":"Rangoon","Riga":"Riga","Riyadh":"Riad","Rome":"Roma","Samara":"Samara","Samoa":"Samoa","Santiago":"Santiago","Sapporo":"Sapporo","Sarajevo":"Sarajevo","Saskatchewan":"Saskatchewan","Seoul":"Seul","Singapore":"Cingapura","Skopje":"Skopje","Sofia":"Sofia","Solomon Is.":"Ilhas Salomão","Srednekolymsk":"Srednekolymsk","Sri Jayawardenepura":"Sri Jayawardenepura","St. Petersburg":"São Petersburgo","Stockholm":"Estocolmo","Sydney":"Sydney","Taipei":"Taipei","Tallinn":"Tallinn","Tashkent":"Tashkent","Tbilisi":"Tbilisi","Tehran":"Teerã","Tijuana":"Tijuana","Tokelau Is.":"Ilha Tokelau","Tokyo":"Tóquio","UTC":"UTC","Ulaan Bataar":"Ulaan Bataar","Ulaanbaatar":"Ulaanbaatar","Urumqi":"Urumqi","Vienna":"Viena","Vilnius":"Vilnius","Vladivostok":"Vladivostok","Volgograd":"Volgograd","Warsaw":"Varsóvia","Wellington":"Wellington","West Central Africa":"Oeste da África Central","Yakutsk":"Yakutsk","Yerevan":"Yerevan","Zagreb":"Zagreb","Zurich":"Zurique"},"trigger":{"action":"Faça o seguinte","add_trigger":"linkage criando","auto":"Auto","conditional_device":"Por favor, definir condições para equipamentos","delete_trigger":"excluir linkage","manual":"Manual","no_trigger_input":"Definir o nome do linkage","scene_condition":"Por favor, selecione a cena em que você deseja que a automação entre em vigor. Se nenhum estiver marcado, essa automação funcionará em qualquer cena","trigger_input":"nome linkage","virtual_device_help":"Você pode inserir um URL, quando esse gatilho acontece, esse URL será chamado. Auto: este URL será chamado automaticamente quando o evento acontecer; Manual: este URL será chamado manualmente pelo usuário, como a abertura da webcam.","when":"Quando as seguintes condições ocorrer"},"user":{"action":"Operating","add_user":"Adicionar Usuário","add_users":"Por favor, indique e-mail ou número de telefone","admin":"administrador","country":"País / Região","de":"Alemanha","delete_user":"Remova o usuário","edit":"Editar permissões","en":"Estados Unidos","es":"Espanha","fr":"França","not_country":"aís / região não pode estar vazio","not_user_input":"Os usuários não pode estar vazio！","not_user_room":"Os quartos não pode estar vazio！","pt":"Portugal","ru":"Rússia","user":"usuário","zh":"China"},"wifi":{"add_desp":"Processando, um momento, por favor.","add_error":"Falha, pressione o botão abaixo para tentar novamente.","add_success":"Seu controlador foi adicionado com sucesso.","add_title":"WIFI foi configurado com sucesso.","next_wifi_desp":"Durante a próxima etapa, o seu celular poderá solicitar a mudança para a rede celular ou continuar usando o Wi-Fi sem conexão com a Internet. Escolha continuar usando o Wi-Fi.","reload_desp":"Seu controlador será reiniciado em breve; levará 3 minutos para concluir a reinicialização.","retry":"tentar novamente","setting_desp":"Um momento, por favor, configurando o controlador WIFI","setting_error":"Por favor, vá para a configuração do seu sistema de telefone celular e conecte seu WIFI ao ponto de acesso PRESEN_CONTROLLER, depois pressione o botão abaixo para tentar novamente!","wifi_link_error":"Não é possível conectar-se ao seu controlador e a configuração do Wi-Fi falhou. Você pode reiniciar o controlador e entrar no modo de Configuração do Wi-Fi e tente novamente."}}'));
I18n.translations.pt_br = I18n.extend((I18n.translations.pt_br || {}), JSON.parse('{"timezones":{"Abu Dhabi":"Abu Dabi","Adelaide":"Adelaide","Alaska":"Alasca","Almaty":"Almaty","American Samoa":"Samoa Americana","Amsterdam":"Amsterdã","Arizona":"Arizona","Astana":"Astana","Athens":"Atenas","Atlantic Time (Canada)":"Hora do Atlântico (Canadá)","Auckland":"Auckland","Azores":"Açores","Baghdad":"Bagdá","Baku":"Baku","Bangkok":"Bangoc","Beijing":"Pequim","Belgrade":"Belgrado","Berlin":"Berlim","Bern":"Berna","Bogota":"Bogotá","Brasilia":"Brasília","Bratislava":"Bratislava","Brisbane":"Brisbane","Brussels":"Bruxelas","Bucharest":"Bucareste","Budapest":"Budapeste","Buenos Aires":"Buenos Aires","Cairo":"Cairo","Canberra":"Canberra","Cape Verde Is.":"Ilha de Cabo Verde","Caracas":"Caracas","Casablanca":"Casablanca","Central America":"América Central","Central Time (US & Canada)":"Hora da Região Central (EUA e Canadá)","Chatham Is.":"Ilha Chatham","Chennai":"Chennai","Chihuahua":"Chihuahua","Chongqing":"Chongqing","Copenhagen":"Copenhague","Darwin":"Darwin","Dhaka":"Daca","Dublin":"Dublin","Eastern Time (US & Canada)":"Hora da Região Leste (EUA e Canadá)","Edinburgh":"Edimburgo","Ekaterinburg":"Ekaterinburg","Fiji":"Fiji","Georgetown":"Georgetown","Greenland":"Groenlândia","Guadalajara":"Guadalajara","Guam":"Guam","Hanoi":"Hanoi","Harare":"Harare","Hawaii":"Havaí","Helsinki":"Helsinque","Hobart":"Hobart","Hong Kong":"Hong Kong","Indiana (East)":"Indiana (Leste)","International Date Line West":"Linha Internacional de Data - Oeste","Irkutsk":"Irkutsk","Islamabad":"Islamabad","Istanbul":"Istambul","Jakarta":"Jakarta","Jerusalem":"Jerusalém","Kabul":"Cabul","Kaliningrad":"Kaliningrado","Kamchatka":"Kamchatka","Karachi":"Karachi","Kathmandu":"Kathmandu","Kolkata":"Kolkata","Krasnoyarsk":"Krasnoyarsk","Kuala Lumpur":"Kuala Lumpur","Kuwait":"Kuwait","Kyiv":"Kyiv","La Paz":"La Paz","Lima":"Lima","Lisbon":"Lisboa","Ljubljana":"Liubliana","London":"Londres","Madrid":"Madri","Magadan":"Magadan","Marshall Is.":"Ilhas Marshall","Mazatlan":"Mazatlan","Melbourne":"Melbourne","Mexico City":"Cidade do México","Mid-Atlantic":"Atlântico Médio","Midway Island":"Ilhas Midway","Minsk":"Minsk","Monrovia":"Monróvia","Monterrey":"Monterrey","Montevideo":"Montevidéu","Moscow":"Moscou","Mountain Time (US & Canada)":"Hora das Montanhas (EUA e Canadá)","Mumbai":"Mumbai","Muscat":"Muscat","Nairobi":"Nairobi","New Caledonia":"Nova Caledônia","New Delhi":"Nova Deli","Newfoundland":"Newfoundland","Novosibirsk":"Novosibirsk","Nuku\'alofa":"Nuku\'alofa","Osaka":"Osaka","Pacific Time (US & Canada)":"Hora do Pacífico (EUA e Canadá)","Paris":"Paris","Perth":"Perth","Port Moresby":"Port Moresby","Prague":"Praga","Pretoria":"Pretória","Quito":"Quito","Rangoon":"Rangoon","Riga":"Riga","Riyadh":"Riad","Rome":"Roma","Samara":"Samara","Samoa":"Samoa","Santiago":"Santiago","Sapporo":"Sapporo","Sarajevo":"Sarajevo","Saskatchewan":"Saskatchewan","Seoul":"Seul","Singapore":"Cingapura","Skopje":"Skopje","Sofia":"Sofia","Solomon Is.":"Ilhas Salomão","Srednekolymsk":"Srednekolymsk","Sri Jayawardenepura":"Sri Jayawardenepura","St. Petersburg":"São Petersburgo","Stockholm":"Estocolmo","Sydney":"Sydney","Taipei":"Taipei","Tallinn":"Tallinn","Tashkent":"Tashkent","Tbilisi":"Tbilisi","Tehran":"Teerã","Tijuana":"Tijuana","Tokelau Is.":"Ilha Tokelau","Tokyo":"Tóquio","UTC":"UTC","Ulaan Bataar":"Ulaan Bataar","Ulaanbaatar":"Ulaanbaatar","Urumqi":"Urumqi","Vienna":"Viena","Vilnius":"Vilnius","Vladivostok":"Vladivostok","Volgograd":"Volgograd","Warsaw":"Varsóvia","Wellington":"Wellington","West Central Africa":"Oeste da África Central","Yakutsk":"Yakutsk","Yerevan":"Yerevan","Zagreb":"Zagreb","Zurich":"Zurique"}}'));
I18n.translations.ru = I18n.extend((I18n.translations.ru || {}), JSON.parse('{"action":{"action":"действия","action_device":"Пожалуйста, настройте целевые устройства！","action_input":"Имя действия не может быть пустым！","action_name":"Название действия","activate":"активировать","add_action":"Добавить действие","add_action_type":"Введите название действия:","add_favorite":"Добавить в ярлыки?","change_action":"Активируйте это действие:","current_action":"Текущие действия","delete_action":"Вы действительно хотите удалить это действие?","favorite":"ЛЮБИМЫЕ ДЕЙСТВИЯ","remove_favorite":"Удалить из ярлыков"},"automation":{"automation_type":"Тип автоматизации","by_sunrise":"На рассвете / закате","city":"город","current_located_city":"Текущий город","current_location":"Текущее местоположение","do":"Делать","if":"Если","locating":"Локационной ...","new_automation":"Новая автоматизация","only_app":"Поддержка только в приложении Presen","relocate":"Смена места жительства","select_conditions":"Выберите условия","select_location":"Выберите место","select_target_type":"Выберите тип цели","sunrise":"Восход","sunset":"Заход солнца"},"dashboard":{"change_pin":"Изменить PIN","enter_pin":"Введите PIN-код вашего контроллера","set_pin":"Настройка PIN-кода вашего контроллера","show_password":"Показать пароль"},"date":{"abbr_day_names":["Вс","Пн","Вт","Ср","Чт","Пт","Сб"],"abbr_month_names":[null,"янв.","февр.","марта","апр.","мая","июня","июля","авг.","сент.","окт.","нояб.","дек."],"day_names":["воскресенье","понедельник","вторник","среда","четверг","пятница","суббота"],"formats":{"default":"%d.%m.%Y","long":"%-d %B %Y","short":"%-d %b"},"month_names":[null,"января","февраля","марта","апреля","мая","июня","июля","августа","сентября","октября","ноября","декабря"],"order":["day","month","year"]},"datetime":{"distance_in_words":{"about_x_hours":{"few":"около %{count} часов","many":"около %{count} часов","one":"около %{count} часа","other":"около %{count} часа"},"about_x_months":{"few":"около %{count} месяцев","many":"около %{count} месяцев","one":"около %{count} месяца","other":"около %{count} месяца"},"about_x_years":{"few":"около %{count} лет","many":"около %{count} лет","one":"около %{count} года","other":"около %{count} лет"},"almost_x_years":{"few":"почти %{count} года","many":"почти %{count} лет","one":"почти 1 год","other":"почти %{count} лет"},"half_a_minute":"меньше минуты","less_than_x_minutes":{"few":"меньше %{count} минут","many":"меньше %{count} минут","one":"меньше %{count} минуты","other":"меньше %{count} минуты"},"less_than_x_seconds":{"few":"меньше %{count} секунд","many":"меньше %{count} секунд","one":"меньше %{count} секунды","other":"меньше %{count} секунды"},"over_x_years":{"few":"больше %{count} лет","many":"больше %{count} лет","one":"больше %{count} года","other":"больше %{count} лет"},"x_days":{"few":"%{count} дня","many":"%{count} дней","one":"%{count} день","other":"%{count} дня"},"x_minutes":{"few":"%{count} минуты","many":"%{count} минут","one":"%{count} минуту","other":"%{count} минуты"},"x_months":{"few":"%{count} месяца","many":"%{count} месяцев","one":"%{count} месяц","other":"%{count} месяца"},"x_seconds":{"few":"%{count} секунды","many":"%{count} секунд","one":"%{count} секунду","other":"%{count} секунды"},"x_years":{"few":"%{count} года","many":"%{count} лет","one":"%{count} год","other":"%{count} года"}},"prompts":{"day":"День","hour":"Часов","minute":"Минут","month":"Месяц","second":"Секунд","year":"Год"}},"device":{"FLiRS":"Устройство FLiRS","battery":"аккумулятор","battery_operated":"Пульт дистанционного управления с батарейным питанием","camera":"айпи камера","camera_image":"Ссылка на изображение IP-камеры","camera_image_desp":"Пожалуйста, введите URL-адрес захвата скриншотов вашей IP-камеры. Если для этого URL требуется аутентификация для доступа, вам также необходимо указать имя пользователя и пароль в этом URL-адресе, возможно, вам понадобится обратиться к руководству вашей IP-камеры, чтобы получить это.","camera_name":"Имя IP-камеры","camera_name_error":"Имя IP-камеры не может быть пустым","camera_screenshot_url_error":"Ссылка на изображение IP-камеры не может быть пустым","camera_video":"Видеокамера IP-камеры","camera_video_desp":"Введите URL видеопотока вашей IP-камеры и выберите нужный формат видео. Если для этого URL требуется аутентификация для доступа, вам также необходимо указать имя пользователя и пароль в этом URL-адресе, возможно, вам понадобится обратиться к руководству вашей IP-камеры, чтобы получить это.","camera_video_url_error":"IP-камера не может быть пустой","change_color":"Сменить цвет","cloud_add_camera":"Добавьте IP-камеру, добавьте в контроллер","conf_apply_battery":"Мы должны проснуться, чтобы активировать настройки устройства батареи были изменены","delay":"задержанный","delete_field_device":"Удалить как неисправное устройство","delete_type":"Какой тип устройства, которое вы хотите удалить？","device":"оборудование","device_active":"Устройство активно","device_dead":"Устройство мертво","device_information":"информация об устройстве","device_interviewed":"Устройство опрошено","device_not_interviewed":"Устройство не полностью опрошено","device_operating":"Устройство работает","device_sleeping":"Устройство спящее","edit_device":"Изменить имя устройства","force_interview":"Force Interview","full":"все","interview":"Интервью","is_awake":"Is Awake","is_sleeping":"Спит","main":"Главная Powered","mark_failed":"Маркированы как не удалось","mark_failed_alert":"Контроллер проверяет узел ли ясно, то он помечен как провал. Этот процесс занимает около 30 секунд. Затем, при необходимости, вы можете использовать «Удалить» функцию для удаления узлов.","mark_failed_delete":"Вы уверены, что хотите удалить устройство? Чтобы удалить узел из контроллера сети, без необходимости дальнейших действий. Этот процесс может занять минуту.","no_device_name":"Имя устройства не может быть пустым!","off_b":"закрыть","on_b":"открытый","operating":"Операционная»","operating_time":"Время работы","play":"Broadcast","remove_camera_device_hint":"Чтобы удалить устройство IP-камеры, перейдите на страницу сведений о устройстве (щелкните по имени устройства для ввода), чтобы удалить","remove_directly":"Удалить напрямую","seconds":"второй","set_to":"устанавливать","setting":"устанавливать","setup_device":"Пожалуйста, установите целевое устройство","value":"значение","value_changed_to":"Изменение значения","value_not_stored_indevice":"Но не в памяти устройства","when":"когда"},"errors":{"format":"%{attribute} %{message}","messages":{"accepted":"нужно подтвердить","blank":"не может быть пустым","confirmation":"не совпадает со значением поля %{attribute}","empty":"не может быть пустым","equal_to":"может иметь лишь значение, равное %{count}","even":"может иметь лишь нечетное значение","exclusion":"имеет зарезервированное значение","greater_than":"может иметь значение большее %{count}","greater_than_or_equal_to":"может иметь значение большее или равное %{count}","inclusion":"имеет непредусмотренное значение","invalid":"имеет неверное значение","less_than":"может иметь значение меньшее чем %{count}","less_than_or_equal_to":"может иметь значение меньшее или равное %{count}","model_invalid":"Возникли ошибки: %{errors}","not_a_number":"не является числом","not_an_integer":"не является целым числом","odd":"может иметь лишь четное значение","other_than":"должно отличаться от %{count}","present":"нужно оставить пустым","required":"не может отсутствовать","taken":"уже существует","too_long":{"few":"слишком большой длины (не может быть больше чем %{count} символа)","many":"слишком большой длины (не может быть больше чем %{count} символов)","one":"слишком большой длины (не может быть больше чем %{count} символ)","other":"слишком большой длины (не может быть больше чем %{count} символа)"},"too_short":{"few":"недостаточной длины (не может быть меньше %{count} символов)","many":"недостаточной длины (не может быть меньше %{count} символов)","one":"недостаточной длины (не может быть меньше %{count} символа)","other":"недостаточной длины (не может быть меньше %{count} символа)"},"wrong_length":{"few":"неверной длины (может быть длиной ровно %{count} символа)","many":"неверной длины (может быть длиной ровно %{count} символов)","one":"неверной длины (может быть длиной ровно %{count} символ)","other":"неверной длины (может быть длиной ровно %{count} символа)"}},"template":{"body":"Проблемы возникли со следующими полями:","header":{"few":"%{model}: сохранение не удалось из-за %{count} ошибок","many":"%{model}: сохранение не удалось из-за %{count} ошибок","one":"%{model}: сохранение не удалось из-за %{count} ошибки","other":"%{model}: сохранение не удалось из-за %{count} ошибки"}}},"event":{"inner":"внутренний","my_device":"Мое устройство","my_room":"Моя комната","open_app":"Открытый APP","outer":"внешний"},"global":{"Choose_ringtone":"Выберите мелодию","account":"НАСТРОЙКИ СЧЕТА","account_setting":"«Настройки аккаунта»","activate_action":"«Запустить это действие?»","activate_scene":"Активировать эту сцену?","activate_sure":"Вы уверены, что делаете это?","add_device_s2_desp":"Пожалуйста, введите первые пять цифр ключа устройства, вы можете обратиться к руководству устройства, чтобы получить ключ устройства","add_device_s2_title":"Пожалуйста, нажмите кнопки на устройстве, следуя инструкции к устройству, чтобы добавить устройство","add_device_wait":"Пожалуйста, подождите","add_smart_desp":"Пожалуйста, введите полный ключ устройства, который должен быть 40 цифрами, вы можете обратиться к руководству устройства, чтобы получить ключ устройства","add_zigbee_smart_desp":"Пожалуйста, введите MAC-адрес устройства и код установки, они должны быть напечатаны на упаковке вашего устройства или в инструкции по эксплуатации. MAC-адрес имеет 16 символов, а код установки - 36.","advanced_setting":"«Расширенные настройки»","agree":"я согласен с","alexa_link_error":"Не удалось установить связь с Alexa, повторите попытку!","alexa_link_success":"Вы связались с Alexa и активировали умение Presen Smart Home.","all":"Все","auto":"автоматизация","away":"прочь","by_device":"По изменениям устройства","centigrade":"стоградусный","change_datacenter":"«Изменить центр данных контроллера»","choose_alarm_ringtone":"Чтобы выбрать это, будет воспроизводиться звуковой сигнал на вашем контроллере, вам нужно нажать главную кнопку на контроллере, чтобы отключить сигнал тревоги.","click_help":"Проверьте справочную информацию","click_to_find_more":"Нажмите, чтобы найти больше","cloud_required":"Для использования этой функции вам необходимо подключиться к интернет-соединению, в настоящее время вы подключаетесь к ethernet.","community":"сообщество","connecting":"соединительный...","controller_setting":"НАСТРОЙКИ КОНТРОЛЛЕРА","copyright_policy":"авторское право","dashboard":"Приборная доска","datacenter_setting":"«Настройки центра обработки данных»","default":"По умолчанию","device_failed":"Это устройство не отвечает, вы можете попытаться выключить его или включить или выключить, или запустить его, чтобы проверить, работает ли он неисправно или низкий уровень заряда батареи. Если он долго не отвечает, вы должны попытаться сбросить его или удалить.","device_not_support":"Этот атрибут не поддерживает это!","download_alert":"Отправлено в ваш зарегистрированный почтовый ящик","dsk_error":"Ключ устройства недействителен","dsk_input":"Ключ устройства должен состоять из 40 цифр","enter_your":"Пожалуйста, введите ваш","error_desp":"У нас может быть проблема, пожалуйста, попробуйте еще раз!","error_title":"Извините, соединение не удалось","every_day":"Каждый день","fahrenheit":"Фаренгейт","grpc_link_error":"Не удалось подключиться, обновите, чтобы повторить попытку","grpc_unlink":"Не удалось подключиться, попытаться восстановить ...","home":"дома","i_agree":"”Вы должны согласиться“","input_key":"«Ключ устройства ввода»","input_ssid":"Пожалуйста, введите SSID или подключите Wi-Fi","input_wifi_password":"Пожалуйста, введите пароль Wi-Fi!","ins_key":"Установить ключ","is_alive":"IS Alive","link_with_alexa":"Связь с Алекса","login_demo":"войти с демо-счет","logout":"выйти","my_smarthome":"“Мой умный дом”","need_login":"Пожалуйста, зарегистрируйтесь сейчас","no_data":"нет данных!","off_sn":"Контролируйте и организуйте устройства в одном контроллере.","on_sn":"Когда эта функция включена, вы можете управлять всеми устройствами контроллеров одновременно, и все устройства могут работать вместе, например, вы можете создать действие для управления несколькими устройствами с разными контроллерами.","open_license":"«Лицензия с открытым исходным кодом»","open_wifi":"«Открыть WIFI»","or":"или же","privacy":"Условия использования","privacy_statement":"Заявление о конфиденциальности","quick":"Ярлыки","release_log":"«Обновление журнала»","remove_device_title":"Пожалуйста, нажмите кнопки на устройстве, следуя инструкции устройства, чтобы удалить устройство","s2_add":"S2 Add","s2_device":"Устройство S2","scan_key":"Ключ устройства сканирования","scene":"Место действия","scene_delete":"Сцена по умолчанию не может быть удалена","select_controller":"«выбрать контроллер»","select_this_controller":"Переключиться на этот контроллер","service":"Сервисы","setting_wifi":"«Настройка WIFI»","setting_wifi_desp":"«WIFI 5G и WIFI в ресторане и аэропорту требуется веб-аутентификация не поддерживается»","sleep":"сон","sn_global":"глобальный режим","sn_normal":"режим одного контроллера","sn_setting":"«Управление контроллером»","ssid":"SSID","target":"цель","temperature_setting":"Настройки температуры","terms_of_service":"условия обслуживания","time_out":"Тайм-аут операции, пожалуйста, попробуйте еще раз!","timezone_setting":"«Настройки часового пояса»","type":"Тип","use":"использование","user_role_desp":"Вы можете поделиться доступом к текущему контроллеру с другими людьми, просто введите адрес электронной почты, которым вы хотите поделиться здесь. Люди, с которыми вы поделились, могут использовать только ваши данные, например, управлять вашими устройствами, активировать сцены, выполнять действия, но не могут вносить изменения в ваши данные, такие как добавление или удаление устройств, создание или редактирование сцен и т. Д., И, конечно, они могут не может сбросить или перезагрузить ваш контроллер. После добавления электронного письма другому пользователю необходимо перезапустить приложение, чтобы оно вступило в силу.","vacation":"отпуск","wifi_connection":"WIFI соединение","wifi_link_btn":"«Индикатор сети включен»","wifi_link_desp":"«Пожалуйста, нажимайте верхнюю кнопку на контроллере, пока светодиод сети не будет всегда включен, чтобы продолжить»","wifi_password":"пароль WIFI","wired_connection":"«Проводное соединение»","zwave_delete_db":"Если это устройство потеряно или неисправно, вы можете удалить это устройство напрямую. Оно появится снова, если оно продолжит связь с вашим контроллером","zwave_delete_field":"Удалить это неисправное устройство, эта операция может завершиться ошибкой, если это устройство не будет распознано как неисправное устройство"},"guard":{"enter_guard_mode":"Войдите в режим охраны","exit_guard_mode":"Выход из режима охраны","guard_h1":"Presen может помочь вам охранять ваш дом, когда вас нет","guard_h2":"УМНЫЕ ПРЕДУПРЕЖДЕНИЯ","guard_h2_deps":"Когда вы переключаетесь в режим охраны, Presen может отправлять вам уведомления, если датчик движения или датчик дыма сработали или дверь открыта.","guard_h3":"ВНЕ ОСВЕЩЕНИЯ","guard_h3_deps":"Presen может автоматически включать и выключать ваши огни, чтобы выглядело, как будто кто-то дома, когда вас нет дома.","guard_mode":"Режим охраны"},"help":{"cookie_hint":"Этот сайт использует cookies для того, чтобы улучшить ваш пользовательский опыт. Подробная информация об использовании куки на этом сайте предоставляется в нашей %s. Используя этот сайт, вы соглашаетесь на использование файлов cookie. %s","privacy":"Конфиденциальность","yes_i_agree":"Да, я согласен"},"helpers":{"select":{"prompt":"Выберите: "},"submit":{"create":"Создать %{model}","submit":"Сохранить %{model}","update":"Сохранить %{model}"}},"home":{"433_device":"433 оборудование","a":"более","add_433_device":"Добавить устройство 433M","add_433_node_type_doorlock":"Doorlock","add_433_node_type_other":"Others","add_433_node_type_pir":"PIR","add_433_node_type_smoke_sensor":"Smoke Sensor","add_433_node_type_water_sensor":"Water Sensor","add_433_waiting_for_ports":"Проверка последовательного порта, который поддерживает контроллер, убедитесь, что у вашего контроллера есть USB-устройства с последовательным портом ...","add_controller":" Добавить контроллер","add_device":"Добавить устройство","add_device_433_progress_error":"Не найден какой-либо порт 433M на вашем контроллере","add_device_433_progress_loading":"проверка","add_device_type":"Какой тип устройства, которое вы хотите добавить？","add_qiock":"Add to Quick Control","add_success":"Добавить успех！","cancel":"отменен","card_1":"Рабочее состояние","card_2":"Время работы контроллера","card_3":"Количество устройств","cellphone":"Телефон","change_controller":"Изменение контроллера","confirm":"подтвердить","controller":"контроллер","controller_information":"контроллер информация","controller_input":"имя контроллера","controller_sn_enter":"Пожалуйста, введите серийный номер контроллера","controller_state":"Состояние контроллера","current_controller":"Регулятор тока","day":"день","delete_success":"Удаляется успешно!","detect_device":"испытательное оборудование","device":"оборудование","device_input":"Имя устройства","device_management":"Управление устройствами","device_name":"Имя устройства","edit":"редактировать","edit_controller":"Изменить имя контроллера","edit_device":"Изменить имя устройства","edit_success":"Изменение успеха！","email":"почтовый ящик","error":"ошибка","event":"новости","failed":"Не удалось выполнить операцию！","favorite":"ЛЮБИМЫЕ СЦЕНЫ","forget_password":"Забыли пароль","good_afternoon":"Добрый день","good_evening":"Добрый вечер","good_morning":"Доброе утро","have_notify":"Вы отметили событие","have_version":"Существует новая версия！","home":"дома","indoor_humidity":"Влажность в помещении","indoor_temp":"Крытый Темп","ip":"IP","language":"язык","language_for_mobile":"язык","local_network":"LAN","login_name":"Электронная почта или номер телефона","name":"имя","network_issue":"Проблема с сетью","network_issue_desp":"Ваша сеть слишком медленная, вы можете попробовать перезапустить приложение или переключиться на быструю сеть","next":"Следующий шаг","no_controller":"Данные контроллера недоступны, вам нужно сначала добавить контроллер в Present App, включить контроллер и подключить его к Интернету, затем подождать несколько секунд, чтобы обновить экран.","no_controller_input":"Имя контроллера не может быть пустым！","no_device":"Нет внутренней информации","not_device_input":"Имя устройства не может быть пустым！","not_user":"Пользователь не существует！","off_line":"Offline","on_line":"Интернет","password":"пароль","profile":"конфигурация","qiock_control":"Quick control","recent_event":"Последние события","recently_devices":"Недавно устройства","refresh":"Обновить контроллер","remove_433_device_hint":"Вы можете перейти на страницу сведений о устройстве (имя устройства), чтобы удалить устройство 433M.","remove_btn":"удалять","remove_device":"Удалить устройство","remove_qiock":"Remove from Quick Control","room":"комната","routine":"Подпрограммы","save":"представить","scene":"сцена","select_433_device":"Следующие устройства обнаружены, выберите устройство, которое вы хотите добавить","select_433_node_type":"Выберите тип устройства для этого устройства.","select_433_port":"Выберите последовательное устройство, которое вы хотите добавить в","select_433_protocol":"Выберите протокол, поддерживаемый вашим устройством.","setting":"устанавливать","setting_btn":"устанавливать","sign":"войти","sign_out":"выход","sign_up":"регистрация","sn":"порядковый номер","sn_error":"Серийный номер не существует！","sn_used":"Серийный номер уже используется！","state":"состояние","success":"успех","successful":"Успешная работа!","time_zone":"Выберите часовой пояс","token":"Проверочный код","trigger":"связь","try_again":"Попробуйте еще раз позже！","user":"пользователь","user_has":"Пользователь уже существует！","user_user":" Вы не можете добавить свои собственные！","version":"Номер версии","voice":"Голос","vs_update":"Быстрое обновление","warning_message":"подсказка","wide_network":"WAN","z_device":"ZWAVE оборудование"},"index":{"433m":"Z-Wave и Regular RF","433m_desp":"Presen поддерживает некоторые традиционные RF-датчики (433M, 345M, 319M) известных известных брендов, помимо устройств Z-Wave, и они могут работать вместе, чтобы построить домашнюю безопасность и автоматизацию","about":"О компании","about_about":"на","about_desp":"Presen - более простой, умный умный дом. Мы фокусируем сотни доверенных брендов на одном мобильном приложении, поэтому вы можете легко контролировать, контролировать и автоматизировать свой умный дом, где бы вы ни находились.","about_desp_three":"Теперь представьте, если вам не нужно представлять. Поскольку он использует Presen, он уже доступен. Такие вещи, как свет, двери, кондиционеры и переключатели, теперь могут работать лучше для вас, что делает вас более безопасным, более контролируемым, более эффективным и счастливым. Благодаря технологии Presen неограниченные возможности будут определяться вашими потребностями и творчеством.","about_desp_two":"Подумайте об этом, если он знает, когда вы не можете вернуться домой и как вы можете быть уверены. Представьте, если они всегда знали, что вам нужно, и когда это было необходимо. Представьте, если бы вы знали, что знаете себя лучше, чем вы.","about_name":"О выставке Presen","about_our":"Мы ориентируемся на умный дом","about_title":"Что такое Presen","alerting":"24/7 ПРЕДУПРЕЖДЕНИЕ","alerting_desp":"Функции Presen Events и Notifications могут быть легко настроены для отправки экстренных сообщений и уведомлений на ваш мобильный телефон","ali_desp":"Управление голосом через последовательные устройства 天猫精灵, управление вашим домом только вашим голосом","app":"APP","automation":"ЛЕГКАЯ АВТОМАТИЗАЦИЯ","automation_desp":"С поддержкой триггеров и сцен вы можете создавать множество сценариев в соответствии с вашими потребностями","build":"Построить лучший умный дом","change":"изменение","contact_address":"адрес","for_live":"Жизнь в","get_app":"Получите бесплатные приложения","has_account":"Уже есть учетная запись？","help_you":"Помочь вам управлять всем домом","home":"дома","no_account":"Нет учетной записи？","or_sign_up":"Или зарегистрируйтесь","or_sign_up_type":"Или использовать свой","our":"нас","our_app":"Мой App","product":"Особенность","product_desp":"В дороге, из своего офиса или во время лежания на тропическом пляже, вы можете управлять своим домом в любом месте. Вы можете полностью контролировать свой дом с экрана вашего ПК, смартфона или любого другого устройства с поддержкой Интернета.","product_desp_1":"Длительное введение","product_name":"Полный домашний контроль и автоматизация","product_name_1":"Первый продукт","product_title":"Полная платформа автоматизации, включая локальные контроллеры, облачные серверы, пользовательские и административные приложения, которые работают без проблем.","product_title_1":"Краткое введение","remote":"ДИСТАНЦИОННОЕ УПРАВЛЕНИЕ","remote_desp":"Управляйте своим домом со своего смартфона или любого другого устройства с поддержкой Интернета, даже без Интернета в вашей локальной сети, с безопасным подключением к Интернету","routines":"ROUTINES","routines_desp":"Presen Routines позволяет вам легко создавать собственные сценарии автоматизации","security":"ЛУЧШАЯ БЕЗОПАСНОСТЬ","security_desp":"Presen предлагает вам новое поколение защиты и безопасности дома","sharing":"РАЗМЕЩЕНИЕ ДОМА","sharing_desp":"Все члены вашей семьи могут иметь свои собственные учетные записи для управления вашим домом с различным доступом","voice":"УПРАВЛЕНИЕ ГОЛОСАМИ","voice_desp":"Управление голосом через последовательные устройства Amazon Echo, управление вашим домом только вашим голосом","we_well":"Мы будем создавать умные дома","work_with":"Presen работы"},"mongoid":{"attributes":{"c/action":{"name":"Название действия"},"c/node":{"c_display_name":"Имя устройства"},"c/scene":{"name":"Название сцены"}},"errors":{"format":"%{attribute} %{message}","messages":{"accepted":"нужно подтвердить","blank":"не может быть пустым","confirmation":"не совпадает со значением поля %{attribute}","empty":"не может быть пустым","equal_to":"может иметь лишь значение, равное %{count}","even":"может иметь лишь нечетное значение","exclusion":"имеет зарезервированное значение","greater_than":"может иметь значение большее %{count}","greater_than_or_equal_to":"может иметь значение большее или равное %{count}","inclusion":"имеет непредусмотренное значение","invalid":"имеет неверное значение","less_than":"может иметь значение меньшее чем %{count}","less_than_or_equal_to":"может иметь значение меньшее или равное %{count}","model_invalid":"Возникли ошибки: %{errors}","not_a_number":"не является числом","not_an_integer":"не является целым числом","odd":"может иметь лишь четное значение","other_than":"должно отличаться от %{count}","present":"нужно оставить пустым","record_invalid":"Возникли ошибки: %{errors}","required":"не может отсутствовать","restrict_dependent_destroy":{"has_many":"Невозможно удалить запись, так как существуют зависимости: %{record}","has_one":"Невозможно удалить запись, так как существует зависимость: %{record}"},"taken":"уже существует","too_long":{"few":"слишком большой длины (не может быть больше чем %{count} символа)","many":"слишком большой длины (не может быть больше чем %{count} символов)","one":"слишком большой длины (не может быть больше чем %{count} символ)","other":"слишком большой длины (не может быть больше чем %{count} символа)"},"too_short":{"few":"недостаточной длины (не может быть меньше %{count} символов)","many":"недостаточной длины (не может быть меньше %{count} символов)","one":"недостаточной длины (не может быть меньше %{count} символа)","other":"недостаточной длины (не может быть меньше %{count} символа)"},"wrong_length":{"few":"неверной длины (может быть длиной ровно %{count} символа)","many":"неверной длины (может быть длиной ровно %{count} символов)","one":"неверной длины (может быть длиной ровно %{count} символ)","other":"неверной длины (может быть длиной ровно %{count} символа)"}}}},"number":{"currency":{"format":{"delimiter":" ","format":"%n %u","precision":2,"separator":",","significant":false,"strip_insignificant_zeros":false,"unit":"руб."}},"format":{"delimiter":" ","precision":3,"separator":",","significant":false,"strip_insignificant_zeros":false},"human":{"decimal_units":{"format":"%n %u","units":{"billion":{"few":"миллиардов","many":"миллиардов","one":"миллиард","other":"миллиардов"},"million":{"few":"миллионов","many":"миллионов","one":"миллион","other":"миллионов"},"quadrillion":{"few":"квадриллионов","many":"квадриллионов","one":"квадриллион","other":"квадриллионов"},"thousand":{"few":"тысяч","many":"тысяч","one":"тысяча","other":"тысяч"},"trillion":{"few":"триллионов","many":"триллионов","one":"триллион","other":"триллионов"},"unit":""}},"format":{"delimiter":"","precision":1,"significant":false,"strip_insignificant_zeros":false},"storage_units":{"format":"%n %u","units":{"byte":{"few":"байта","many":"байт","one":"байт","other":"байта"},"gb":"ГБ","kb":"КБ","mb":"МБ","tb":"ТБ"}}},"percentage":{"format":{"delimiter":"","format":"%n%"}},"precision":{"format":{"delimiter":""}}},"room":{"add_room":"Добавить другой номер","desp":"EVENT означает, что в этой комнате за последние 24 часа происходят изменения состояния устройства.","device_in_room":"Оборудование в номере","event":"событие","filter_room":"Фильтр по номера","normal":"нормаль","room_name":"номер Наименование","room_no_device":"Ни одно устройство не выбрано！","room_no_name":"Название Номер не может быть пустым！","room_switch_hint":"Управление всеми устройствами swtich в этой комнате","setting_room":"Установка номеров"},"routine":{"add":"Добавить рутину","address":"Адрес","cond_action":"Включить действие","cond_device":"Включить устройства","cond_scene":"Включить сцену","edit":"Изменить рутину","get_to":"прибытие","is_cyclic":"Повторите","last":"назад","leave":"Оставлять","one_cond":"однажды","please_address":"Выберите местоположение GPS","please_date":"Выберите дату","please_leave":"Выберите прибытие или выезд","please_time":"Выберите время","please_to":"Выберите, что вы хотите включить.","please_type":"Выберите тип подпрограммы","routine":"Подпрограммы","routine_cond":"когда","routine_desp":"Routine может включить базу Scene или Devices в момент времени или местоположение GPS (необходимо работать с Presen App)","routine_location":"Местоположение GPS","routine_type":"Обычный тип","ru":"Эта функция должна работать с Presen App и Presen Cloud, пожалуйста, посетите https://www.presensmarthome.com","run_multiple":"Повторите","run_once":"Раз","select_action":"Выберите действие","select_device":"Выбрать устройства","select_scene":"Выберите сцену","time":"Время","to_location":"По местоположению GPS","to_time":"По времени"},"scene":{"activate":"активация","add_scene":"Добавить сцену","add_scene_type":"Пожалуйста, введите имя сцены:","change_scene":"Независимо от того, чтобы переключить сцену","current_scene":"Текущая сцена","delete_scene":"Удаление сцен","scene_device":"Пожалуйста, добавьте устройство！","scene_input":"Название сцены не может быть пустым！","scene_name":"название сцены"},"services":{"alexa":"Управляйте устройствами и автоматикой с помощью устройств с поддержкой Amazon Alexa.","alexa_desp":"Такие, как \\"Alexa, turn on home scene\\".","ali":"Управляйте устройствами и автоматиками, используйте TmallGenie.","ali_desp":"Такие, как \\"天猫精灵, 打开卧室的灯 \\".","google":"Управляйте устройствами и автоматикой с помощью поддерживаемых Google Assistant устройств.","google_desp":"Такие, как \\"Hey Google, activate home\\".","ifttt":"Работа с IFTTT для связи с более 3-го сервиса.","ifttt_desp":"Например, когда датчик движения срабатывает, вы получите вызов."},"session":{"60_s":"Повторите попытку через 60 секунд！","cellphone_input":"Пожалуйста, введите номер вашего мобильного телефона!","change_password":"Изменение пароля","confir_input":"Пожалуйста, подтвердите ваш пароль！","email":"Почтовый ящик уже существует!","email_input":"Пожалуйста, введите адрес электронной почты！","email_or_phone":"Электронная почта или номер телефона","email_type":"Формат электронной почты не правильно！","get_token":"Проверочный код","is_cellphone":"Номер телефона уже существует！","is_email":"Электронная почта уже существует！","login_name_input":"Пожалуйста, войдите в почтовый ящик или номер телефона","new_input":"Пожалуйста, введите новый пароль！","new_password":"Новый пароль","no_user_name":"Имя пользователя не может быть пустым！","not_eamil":"Почтовый ящик не существует！","not_email_or_phone":"Пожалуйста, доступ к электронной почте или номер телефона","not_phone":"Номер телефона не существует！","not_token":"Проверочный код не является правильным！","old_error":"Старый пароль не правильно！","old_input":"Пожалуйста, введите свой старый пароль！","old_password":"Старый пароль","password_confirmation":"Подтвердите пароль","password_input":"Пожалуйста, введите ваш пароль","password_length":"Пароль не может быть меньше, чем шесть！","please_enter_password":"Пожалуйста введите ваш","register":"регистрация","reset_password":"Сброс пароля","send":"послать","send_eamil":"Отправить код подтверждения на адрес электронной почты или","send_token":"Токен был отправлен на ваш почтовый ящик, пожалуйста, проверьте ваш почтовый ящик.","signup_success_login":"Аккаунт создан успешно, пожалуйста, войдите сейчас","token_input":"Код подтверждения был отправлен на вашу электронную почту, пожалуйста, проверьте и введите код подтверждения ниже, чтобы продолжить!","token_send":"Проверочный код успешно отправлен！","two_password":"Пароли не совпадают！","user_name":"имя пользователя","wrong_name":"Имя пользователя или пароль неверны！"},"setting":{"add":"добавлять","advanced_setting":"Расширенные настройки","backup":"Резервный контроллер","backup_desp":"Контроллер резервного копирования","backup_message":"Пожалуйста, не выключайте контроллер резервного питания。。。","base":"Основные настройки","change_notification":"Изменить уведомление","check_device":"Пожалуйста, выберите атрибуты устройств, вы не хотите получать уведомления об изменениях.","check_device_for_urgent":"Получаете срочные уведомления, только если после изменения устройства.","check_scene":"Не получать уведомления, когда ваш дом находится в следующих проверенных сцены, если выбран не сцена, это означает, что все уведомления об изменении устройства не будет отправлять неважно какие сцены, это.","check_scene_for_urgent":"Только срочные уведомления когда в следующих сценах.","close_account":"Закрыть аккаунт","close_desp":"Пользователи не могут войти в систему после закрытия учетной записи! Если вам нужно снова открыть свою учетную запись, свяжитесь с нами!","download_data":"Загрузить данные","download_desp":"Скачать персональные данные","end_time":"Время окончания","friday":"пятница","inform":"уведомление","local_siren":"Локальная сирена","login_close_desp":"Учетная запись закрыта. Чтобы открыть счет, свяжитесь с нами!","monday":"понедельник","not_end_time":"Конечное время установлено так:","not_start_time":"Если вам не нужно время, установите время начала:","notify":"Центр уведомлений","notify_by_device":"Уведомление устройства","notify_by_time":"Не беспокоить","notify_desp":"Глобальные уведомления элемента управления, вы не будете получать уведомления, если он выключен.","notify_device_noti_desp":"Вы не будете получать уведомления об изменении следующих устройств","notify_dnd_desp":"Установка времени, вы не хотите получать уведомления о","notify_time":"Время","notify_urgent_device":"Срочное уведомление","notify_urgent_noti_desp":"Вы получите специальное уведомление при изменении этих устройств. (Срочное уведомление будет игнорировать параметры точка не беспокоить и устройства уведомления)","place_end_time":"Выберите время окончания!","place_select_period":"Пожалуйста, выберите время!","place_start_time":"Выберите время начала!","property":"собственности","record":"Восстановление контроллера","record_desp":"Восстановление контроллера","record_error":"Контроллер восстановления не удалось! Пожалуйста, попробуйте еще раз！","record_message":"Пожалуйста, не выключайте питание, чтобы восстановить контроль。。。","record_success":"Успех сокращения контроллера！","reset":"инициализация","reset_desp":"Контроллер сброса к заводским установкам. Примечание: Все номера и другие данные будут удалены!","reset_message":"Контроллер удалит все данные。。。。","reset_success":"Ваш контроллер был успешно сброшен.","reset_title":"Сбросить контроллер?","restart":"Перезагрузка контроллера","restart_desp":"Перезагрузите контроллер, перезагрузка занимает около 5 минут! После завершения перезагрузки, пожалуйста, обновите страницу","restart_message":"Вы уверены, что хотите сбросить контроллер？","restart_title":"Перезагрузить контроллер?","saturday":"суббота","select_date":"Выберите дату","select_device_scene":"Выберите устройство или сцену！","select_period":"период отбора","start_end":"Время окончания меньше времени начала!","starting_time":"Время начала","sunday":"Воскресенье","thursday":"Четверг","tuesday":"вторник","update_desp":"Процесс обновления не выключайте обновление мощности занимает около 5 до 7 минут! После завершения обновления, пожалуйста, обновите страницу ","update_desp_one":"Новая версия","update_failed_in_progress":"Обновление уже выполняется.","update_failed_other":"Не удалось обновить, попробуйте позже!","update_failed_up_to_date":"Нет необходимости обновлять, вы в курсе.","update_online":"Обновление контроллера","update_title":"Обновление ...","updated":"Действующая система до настоящего времени, не обновление!","updating":"Мы модернизированы。。。","upgrade_desp":"Контроллер обновляется, пожалуйста, не выключайте питание вашего контроллера, это займет около 5 минут, это зависит от скорости вашей сети. Контроллер перезагрузится, когда вы закончите, и вы получите уведомление.","upgrade_success_to":"Контроллер был обновлен до","wednesday":"среда"},"spec":{"access_control":"Контроль доступа","air_flow":"воздушный поток","air_temperature":"барометрическое давление","alarm":"предупреждение","alarm_s":"Сигнал индукции","angle_position":"угол","app_url":"URL-адрес триггера","appliance":"Бытовая техника","atmospheric_pressure":"барометрическое давление","auto":"автоматическая","aux":"Вспомогательное оборудование","auxiliary":"помощь","barometric_pressure":"барометрическое давление","battery":"аккумулятор","burglar":"кража","camera":"IP-камера","clock":"часы","closed":"Закрыто","co":"Угарный газ","co2":"Двуокись углерода","co2_alarm":"Предупреждение о двуокиси углерода","co2_level":"Уровень углекислого газа","co_alarm":"Предупреждение о окиси углерода","co_level":"Уровень углекислого газа","color_switch":"Цветовой переключатель","contact_sensor":"Датчик контакта","cool":"охлаждение","current":"Расход воды","dew_point":"Точка росы","dimmer":"Легкий модулятор","direction":"направление","distance":"расстояние","door_lock":"Дверной замок","door_window":"Двери и окна","electrical_conductivity":"проводимость","electrical_resistivity":"Коэффициент сопротивления","emergency":"неотложный","false":"Не запускается","fire_sensor":"Датчик пожара","first":"Первый","flood":"затопленный","freeze":"охлаждение","frequency":"частота","full":"все","general":"общий","general_purpose":"Универсальное обнаружение","general_purpose_alarm":"Общий аварийный сигнал","general_purpose_value":"Статус общего назначения","general_trigger":"Общее событие","glass_break":"Сломанный датчик","going_to_low_battery":"выйдет из батареи","heat":"отопление","heat_alarm":"Тепловая сигнализация","humidity":"влажность","is":"Статус","key_fob":"Ключевой Фоб","keypad":"Клавиатуры","loudness":"объем","low":"низкий","luminance":"освещение","luminiscence":"освещение","main_powered":"Главная Powered","meter":"Измерительный инструмент","moisture":"количество осадков","motion":"Чувствительность движения","motor":"двигатель","multi_switch":"Многоканальный переключатель","n_a":"N/A","no_b":"нормаль","normal":"нормаль","not_used":"неизвестный","off_b":"закрыть","on_b":"открытый","open":"открыто","power":"электрическая мощность","power_management":"Управление питанием","quake":"дрожать","rain_rate":"Скорость дождя","relative_humidity":"Относительная влажность","remote_ontrol":"Дистанционное управление","reserved":"удержание","resume":"Продолжить","return_first_alarm_on_supported_list":"Первое предупреждение","rotation":"вращение","security_repeater":"Повторителер безопасности","seismic_intensity":"Интенсивность землетрясения","seismic_magnitude":"величина","sensor":"индуктор","smoke":"дым","smoke_alarm":"Предупреждение о дыме","smoke_test":"Тест дыма","soil_temperature":"Температура масла","solar_radiation":"Солнечное излучение","switch":"переключатель","switch_all":"Весь переключатель","switch_to":"переключить на","system":"система","tamper":"тампер","tank_capacity":"мощность","target_temperature":"Целевая температура","temperature":"температура","thermostat":"термостат","thermostat_mode":"Режим кондиционирования воздуха","thermostat_setpoint":"Установите температуру","tide_level":"Уровень прилива","tilt":"наклон","time":"время","true":"спусковой крючок","ultraviolet":"ультрафиолетовый","velocity":"скорость","ventilation":"Вентиляционное оборудование","vibration":"Вибрации","voltage":"напряжение","water":"воды","water_leak":"затопленный","water_leak_alarm":"Сигнал о наводнении","water_temperature":"Температура воды","weight":"вес","yes_b":"спусковой крючок"},"support":{"array":{"last_word_connector":" и ","two_words_connector":" и ","words_connector":", "}},"time":{"am":"утра","formats":{"default":"%a, %d %b %Y, %H:%M:%S %z","long":"%d %B %Y, %H:%M","short":"%d %b, %H:%M"},"pm":"вечера"},"timezones":{"Abu Dhabi":"Абу-Даби","Adelaide":"Аделаида","Alaska":"Аляска","Almaty":"Алма-Ата","American Samoa":"Американское Самоа","Amsterdam":"Амстердам","Arizona":"Аризона","Astana":"Астана","Athens":"Афины","Atlantic Time (Canada)":"Североамериканское атлантическое время","Auckland":"Окленд","Azores":"Азорские острова","Baghdad":"Багдад","Baku":"Баку","Bangkok":"Бангкок","Beijing":"Пекин","Belgrade":"Белград","Berlin":"Берлин","Bern":"Берн","Bogota":"Богота","Brasilia":"Бразилиа","Bratislava":"Братислава","Brisbane":"Брисбен","Brussels":"Брюссель","Bucharest":"Бухарест","Budapest":"Будапешт","Buenos Aires":"Буэнос-Айрес","Cairo":"Каир","Canberra":"Канберра","Cape Verde Is.":"Кабо-Верде","Caracas":"Каракас","Casablanca":"Касабланка","Central America":"Центральная Америка","Central Time (US & Canada)":"Североамериканское центральное время","Chatham Is.":"Остров Чатем","Chennai":"Ченнаи","Chihuahua":"Чиуауа","Chongqing":"Чунцин","Copenhagen":"Копенгаген","Darwin":"Дарвин","Dhaka":"Дакка","Dublin":"Дублин","Eastern Time (US & Canada)":"Североамериканское восточное время","Edinburgh":"Эдинбург","Ekaterinburg":"Екатеринбург","Fiji":"Фиджи","Georgetown":"Джорджтаун","Greenland":"Гренландия","Guadalajara":"Гвадалахара","Guam":"Гуам","Hanoi":"Ханой","Harare":"Хараре","Hawaii":"Гавайи","Helsinki":"Хельсинки","Hobart":"Хобарт","Hong Kong":"Гонконг","Indiana (East)":"Индиана","International Date Line West":"Международная линия перемены дат","Irkutsk":"Иркутск","Islamabad":"Исламабад","Istanbul":"Стамбул","Jakarta":"Джакарта","Jerusalem":"Иерусалим","Kabul":"Кабул","Kaliningrad":"Калининград","Kamchatka":"Камчатка","Karachi":"Карачи","Kathmandu":"Катманду","Kolkata":"Калькутта","Krasnoyarsk":"Красноярск","Kuala Lumpur":"Куала-Лумпур","Kuwait":"Кувейт","Kyiv":"Киев","La Paz":"Ла-Пас","Lima":"Лимо","Lisbon":"Лиссабон","Ljubljana":"Любляна","London":"Лондон","Madrid":"Мадрид","Magadan":"Магадан","Marshall Is.":"Маршалловы острова","Mazatlan":"Масатлан","Melbourne":"Мельбурн","Mexico City":"Мехико","Mid-Atlantic":"Центральноатлантическое время","Midway Island":"Остров Мидуэй","Minsk":"Минск","Monrovia":"Монровия","Monterrey":"Монтеррей","Montevideo":"Монтевидео","Moscow":"Москва","Mountain Time (US & Canada)":"Североамериканское горное время","Mumbai":"Бомбей","Muscat":"Маскат","Nairobi":"Найроби","New Caledonia":"Новая Каледония","New Delhi":"Нью-Дели","Newfoundland":"Ньюфаундленд","Novosibirsk":"Новосибирск","Nuku\'alofa":"Нукуалофа","Osaka":"Осака","Pacific Time (US & Canada)":"Североамериканское тихоокеанское время","Paris":"Париж","Perth":"Перт","Port Moresby":"Порт-Морсби","Prague":"Прага","Pretoria":"Претория","Quito":"Кито","Rangoon":"Рангун","Riga":"Рига","Riyadh":"Эр-Рияд","Rome":"Рим","Samara":"Самара","Samoa":"Самоа","Santiago":"Сантьяго","Sapporo":"Саппоро","Sarajevo":"Сараево","Saskatchewan":"Саскачеван","Seoul":"Сейл","Singapore":"Сингапур","Skopje":"Скопье","Sofia":"София","Solomon Is.":"Саломоновы острова","Srednekolymsk":"Среднеколымск","Sri Jayawardenepura":"Шри-Джаяварденепура-Котте","St. Petersburg":"Санкт-Петербург","Stockholm":"Стокгольм","Sydney":"Сидней","Taipei":"Тайпей","Tallinn":"Таллин","Tashkent":"Ташкент","Tbilisi":"Тбилиси","Tehran":"Тегеран","Tijuana":"Тихуана","Tokelau Is.":"Токелау","Tokyo":"Токио","UTC":"Всемирное координированное время (UTC)","Ulaan Bataar":"Улан-Батор","Ulaanbaatar":"Улан-Батор","Urumqi":"Урумчи","Vienna":"Вена","Vilnius":"Вильнюс","Vladivostok":"Владивосток","Volgograd":"Волгоград","Warsaw":"Варшава","Wellington":"Веллингтон","West Central Africa":"Западноафриканское время","Yakutsk":"Якутск","Yerevan":"Ереван","Zagreb":"Загреб","Zurich":"Цюрих"},"trigger":{"action":"Выполните следующие действия","add_trigger":"Создание связи","auto":"авто","conditional_device":" Пожалуйста, создать условия для оборудования","delete_trigger":"Удалить связь","manual":"Руководство","no_trigger_input":"Установить связь имя","scene_condition":"Пожалуйста, выберите сцену, которую вы хотите, чтобы автоматизация вступила в силу. Если ни один не выбран, эта автоматизация будет работать в любой сцене","trigger_input":"Рычажное имя","virtual_device_help":"Вы можете ввести URL-адрес, когда этот триггер произойдет, этот URL-адрес будет вызван. Авто: этот URL-адрес будет вызываться автоматически, когда произойдет событие; Руководство: этот URL-адрес будет вызываться вручную пользователем, например, с открытием веб-камеры.","when":"Когда возникают следующие условия"},"user":{"action":"операционная","add_user":"Добавить пользователя","add_users":"Пожалуйста, введите адрес электронной почты или номер телефона","admin":"администратор","country":"Страна/Регион","de":"Германия","delete_user":"Удалить пользователя","edit":"редактировать","en":"США","es":"Испания","fr":"Франция","not_country":"Страна/регион не может быть пустым","not_user_input":"Пользователи не могут быть пустыми！","not_user_room":"Номера не могут быть пустыми！","pt":"Португалия","ru":"Россия","user":"пользователь","zh":"Китай"},"wifi":{"add_desp":"Обработка, один момент, пожалуйста.","add_error":"Не удалось, нажмите кнопку ниже, чтобы повторить попытку.","add_success":"Ваш контроллер был успешно добавлен.","add_title":"WIFI был успешно настроен.","next_wifi_desp":"На следующем шаге ваш мобильный телефон может запросить переключение на сотовую сеть или продолжать использовать WiFi без подключения к интернету, пожалуйста, выберите продолжать использовать WiFi.","reload_desp":"Ваш контроллер будет перезагружен в ближайшее время, для завершения перезагрузки потребуется до 3 минут.","retry":"повторная попытка","setting_desp":"Один момент, пожалуйста, настройка контроллера WIFI","setting_error":"Перейдите к настройке системы мобильного телефона и подключите WIFI к точке доступа PRESEN_CONTROLLER, затем нажмите кнопку ниже, чтобы повторить попытку!","wifi_link_error":"Не удается подключиться к контроллеру, и настройка Wi-Fi не удалась. Вы можете перезагрузить контроллер и войти в режим настройки WiFi, а затем повторите попытку."}}'));
I18n.translations.zh_cn = I18n.extend((I18n.translations.zh_cn || {}), JSON.parse('{"action":{"action":"动作","action_device":"请添加设备！","action_input":"动作名称不能为空！","action_name":"动作名称","activate":"激活","add_action":"添加动作","add_action_type":"请输入动作名称:","add_favorite":"加入快捷控制","change_action":"是否激活动作:","current_action":"当前动作","delete_action":"是否删除动作","execute":"执行","favorite":"喜欢的动作","remove_favorite":"从快捷控制中删除"},"automation":{"add_automation":"添加自动化","automation_type":"自动化类型","by_sunrise":"日出日落","city":"城市","current_located_city":"当前定位城市","current_location":"当前城市","do":"目标","everyday":"每天","gps_auto_desp":"需要使用你的GPS位置信息来使该种类型的自动化工作，请保证您已经授予了GPS一直运行的权利。","if":"当","locating":"定位中...","only_app":"仅在Presen App中可用","relocate":"重新定位","select_conditions":"并且","select_location":"选择城市","select_target_type":"执行","select_two_controller":"目前只支持操作同一个控制器下的设备","sunrise":"日出","sunset":"日落","workday":"工作日"},"dashboard":{"change_pin":"修改PIN","enter_pin":"请输入您的控制器PIN","no_initial_password":"你需要首先使用该控制的账户登录并且设置初始PIN，然后才能继续使用。","pin_error":"PIN不正确。","set_pin":"请设置您的控制器PIN","show_password":"显示密码"},"date":{"abbr_day_names":["周日","周一","周二","周三","周四","周五","周六"],"abbr_month_names":[null,"1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],"day_names":["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],"formats":{"default":"%Y-%m-%d","long":"%Y年%m月%d日","short":"%m月%d日"},"month_names":[null,"一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],"order":["year","month","day"]},"datetime":{"distance_in_words":{"about_x_hours":{"one":"大约一小时","other":"大约 %{count} 小时"},"about_x_months":{"one":"大约一个月","other":"大约 %{count} 个月"},"about_x_years":{"one":"大约一年","other":"大约 %{count} 年"},"almost_x_years":{"one":"接近一年","other":"接近 %{count} 年"},"half_a_minute":"半分钟","less_than_x_minutes":{"one":"不到一分钟","other":"不到 %{count} 分钟"},"less_than_x_seconds":{"one":"不到一秒","other":"不到 %{count} 秒"},"over_x_years":{"one":"一年多","other":"%{count} 年多"},"x_days":{"one":"一天","other":"%{count} 天"},"x_minutes":{"one":"一分钟","other":"%{count} 分钟"},"x_months":{"one":"一个月","other":"%{count} 个月"},"x_seconds":{"one":"一秒","other":"%{count} 秒"},"x_years":{"one":"一年","other":"%{count} 年"}},"prompts":{"day":"日","hour":"时","minute":"分","month":"月","second":"秒","year":"年"}},"device":{"FLiRS":"红外设备","add_dsk_desp":"支持多个设备同时添加，请扫码或者手动输入DSK，点击下一步","add_these_devices":"开始添加","added_devices":"已添加设备","battery":"电池","battery_operated":"电池供电的远程控制设备","camera":"IP 相机","camera_image":"IP 相机图片链接","camera_image_desp":"请输入您的IP摄像机的屏幕截图捕获URL。 如果此URL需要身份验证才能访问，您还需要在此URL中指定用户名和密码，您可能需要参考IP摄像头的手册来获取此信息。","camera_name":"IP 相机名称","camera_name_error":"IP 相机名称不能为空","camera_screenshot_url_error":"IP 相机图片链接不能为空","camera_video":"IP 相机视频链接","camera_video_desp":"请输入您的IP摄像机的视频流URL并选择正确的视频格式。 如果此URL需要身份验证才能访问，您还需要在此URL中指定用户名和密码，您可能需要参考IP摄像头的手册来获取此信息。","camera_video_url_error":"IP 相机视频链接不能为空","change_color":"改变颜色","cloud_add_camera":"添加IP 相机请在controller添加","delay":"延迟","delete_field_device":"删除故障设备","delete_type":"您想要删除什么类型的设备？","device":"设备","device_active":"设备是活动的","device_dead":"设备没有运行","device_information":"设备信息","device_interviewed":"设备面试通过","device_name_error":"设备名称已经被占用","device_not_interviewed":"设备没有完全通过面试","device_operating":"设备运行中","device_sleeping":"设备正在睡眠","device_state":"设备工作状态","device_state_desp":"接收设备在线/离线状态变化通知","dsk_next":"请先扫码或者手动输入DSK","edit_device":"修改设备名称","force_interview":"重新面试","full":"全","interview":"面试","is_awake":"是否醒着","is_sleeping":"正在睡眠","main":"市电","manufacturer":"制造商","mark_failed":"标记为失败","mark_failed_alert":"控制器将检查节点是否清醒，然后将其标记为失败。 这个过程大约需要30秒。 然后，如果需要，可以使用“删除”功能删除节点。","mark_failed_delete":"您确定要删除设备吗？控制器将从网络中删除节点，而无需进一步操作。 这个过程可能需要一分钟的时间。","notify_battery":"电池电量过低","notify_battery_desp":"在低电量运行时接收通知","notify_desp":"接收设备的状态变化通知，你可以指定通知生效的场景。同时你也可以打开本地告警功能如果你的控制器支持的话","off_b":"关","on_b":"开","operating":"状态","operating_time":"工作时间","play":"播放","playback":"回放","power_source_bat_percent_remaining":"电池剩余电量","power_source_description":"电源描述","power_source_order":"电源首选项","power_source_status":{"active":"活跃","name":"电源状态","standby":"待机","unavailable":"不可用","unspecified":"未指定"},"product_name":"产品名称","remove_camera_device_hint":"删除IP相机设备请进入设备详情页面(点击设备名称进入)进行删除","remove_directly":"直接删除","seconds":"秒","security":"安防","set_to":"设置","setting":"设置","setup_device":"请设置目标设备","smart_add_desp":"设备自动添加，等待时间较长,预计需要{{time}}分钟，可以离开此页面，但是请保持添加的设备通电","thread_network_channel":"Thread信道","thread_network_data_version":"Thread完整数据版本","thread_network_extended_pan_id":"Thread ExtendedPanId","thread_network_leader_router_id":"Thread Leader RouterId","thread_network_mesh_local_prefix":"Thread IPv6 前缀","thread_network_name":"Thread网络名称","thread_network_pan_id":"Thread PanId","thread_network_partition_id":"Thread PartitionId","thread_network_routing_role":{"end_device":"终端设备","leader":"领导设备","name":"Thread路由角色","reed":"符合路由条件的终端设备","router":"路由设备","sleepy_end_device":"睡眠终端设备","unassigned":"未分配","unspecified":"未指定"},"thread_network_stable_data_version":"Thread稳定数据版本","thread_network_weighting":"Thread Leader Weight","value":"值","when":"当"},"errors":{"format":"%{attribute}%{message}","messages":{"accepted":"必须是可被接受的","blank":"不能为空字符","confirmation":"与确认值不匹配","empty":"不能留空","equal_to":"必须等于 %{count}","even":"必须为双数","exclusion":"是保留关键字","greater_than":"必须大于 %{count}","greater_than_or_equal_to":"必须大于或等于 %{count}","inclusion":"不包含于列表中","invalid":"是无效的","less_than":"必须小于 %{count}","less_than_or_equal_to":"必须小于或等于 %{count}","model_invalid":"验证失败: %{errors}","not_a_number":"不是数字","not_an_integer":"必须是整数","odd":"必须为单数","other_than":"长度非法（不可为 %{count} 个字符","present":"必须是空白","required":"必须存在","taken":"已经被使用","too_long":{"one":"过长（最长为一个字符）","other":"过长（最长为 %{count} 个字符）"},"too_short":{"one":"过短（最短为一个字符）","other":"过短（最短为 %{count} 个字符）"},"wrong_length":{"one":"长度非法（必须为一个字符）","other":"长度非法（必须为 %{count} 个字符）"}},"template":{"body":"如下字段出现错误：","header":{"one":"有 1 个错误发生导致「%{model}」无法被保存。","other":"有 %{count} 个错误发生导致「%{model}」无法被保存。"}}},"event":{"download_video":"下载视频","filter_events":"过滤事件","inner":"内部","my_device":"我的设备","my_room":"我的房间","open_app":"查看图片","outer":"外部","save_image":"保存图片"},"global":{"account":"账户设置","account_setting":"账户设置","action":"动作","activate_action":"运行这个动作吗?","activate_scene":"激活这个场景吗?","activate_sure":"确认执行这个操作吗?","add_device_s2_desp":"请输入设备密钥的前五位数字，您可以参考设备说明书获取设备密钥","add_device_s2_title":"请参考设备说明书找到对应的按钮来添加设备","add_device_special_desp":"确保设备已经恢复出厂设置或者从其他网络中移除（针对Z-Wave设备，如果不确定，请首先执行删除操作把Z-Wave设备从之前的网络中移除），每次只能添加一个设备，如需添加另一个请再次执行添加设备操作。","add_device_wait":"请等待","add_smart_desp":"请输入完整的设备密钥，应该是40位数字，您可以参考设备说明书获取设备密钥","add_zigbee_smart_desp":"请输入设备的MAC地址和安装码，你可以在设备的外包装或者说明书中找到它们，MAC地址有16位字符，安装码是36位。","advanced_setting":"高级设置","advanced_success":"修改成功，设置信息将在下次数据上报时更新","agree":"我同意","alexa_link_error":"账号绑定失败，请重试","alexa_link_success":"账号绑定成功，启用Alexa技能成功","all":"所有","auto":"自动化","away":"离家","by_device":"设备变化","centigrade":"摄氏度","change_datacenter":"修改控制器数据中心","choose_alarm_ringtone":"选择后当该设备变化时会在控制器端播放警报声音，你需要点击控制器的主按钮来关闭告警声","choose_ringtone":"选择声音","click_help":"查看帮助信息","click_to_find_more":"点击查看更多","cloud_required":"您需要连接到internet才能使用此功能，目前您正在连接到以太网。","community":"社区论坛","connecting":"连接中...","controller_offline":"控制器离线，请检查网络后重试！","controller_setting":"控制器设置","copyright_policy":"版权政策","ctl_update":"你的控制器有可用的更新，你可以通过 [设置 -> 控制器管理 -> 选择当前控制器 -> 升级控制器]来进行升级，你也可以点击[OK]按钮来直接进入升级页面。","dark":"深色","dashboard":"离线控制","datacenter_setting":"数据中心设置","default":"默认声音","delete_desp":"请输入“YES”来继续","device_count":"个设备","device_failed":"设备未响应，如果是电池设备你可以检查是否电量低，或者可以尝试重启，打开关闭，或者触发等操作，也有可能该设备发生故障。如果该设备长期处于该状态，你可以考虑重置或者删除它。","device_not_support":"该属性不支持!","download_alert":"已发送到您注册的邮箱","dsk_error":"DSK 不正确","dsk_input":"DSK 必须40位","enter_your":"请输入","error_desp":"我们好像遇到了问题，请稍后重试!","error_title":"抱歉，连接失败","every_day":"每天","fahrenheit":"华氏度","force_login":"为了安全原因您已被强制退出，请重新登录!","ftt":"433M","grpc_link_error":"连接错误，请刷新页面！","grpc_unlink":"连接中断! 正在重新连接...","has_subscribed":"您已订阅","home":"在家","i_agree":"你需要同意","input_key":"手动输入DSK","input_ssid":"请输入SSID或者连接wifi","input_wifi_password":"请输入wifi密码！","ins_key":"Install Code","is_alive":"是否是活动的","light":"亮色","link_with_alexa":"绑定Alexa账户","location_desp":"正在使用你的位置来使GPS自动化工作","login_demo":"演示账户登录","logout":"退出","matter_thread":"Matter Thread","matter_wifi":"Matter Wifi","my_smarthome":"我的智能家居","need_login":"请先登录","no_advanced_setting":"无高级设置。","no_data":"没有数据！","not_ready":"模块启动失败","off_sn":"当开关为关闭状态时，将管理所选择控制器的设备","on_sn":"当打开时，您可以同时管理控制器的所有设备，并且所有设备可以一起工作，例如，您可以创建一个Action来使用不同的控制器控制多台设备。","open_license":"开源许可","or":"或","please_add_controller":"请添加控制器","please_add_home":"请先添加家庭","privacy":"服务条款","privacy_statement":"隐私声明","quick":"快捷操作","release_log":"更新日志","remove_device_title":"请参考设备说明书找到对应的按钮来删除设备","reset_controller_offline":"控制器不在线，需要您手工按住控制器上的Reset按钮来强制恢复到出厂状态。","s2_add":"S2 添加","s2_device":"S2 设备","scan_key":"相机扫码二维码","scene":"场景","scene_delete":"默认场景不能被删除","select_add_controller":"请选择你要添加设备的控制器","select_controller":"选择控制器","select_this_controller":"选择这个控制器","select_this_home":"选择这个家庭","service":"第三方服务","setting_wifi":"设置 WIFI","setting_wifi_desp":"目前不支持 5G 频段的Wi-Fi,以及酒店、机场等需要认证的Wi-Fi","sleep":"睡觉","sn_global":"全局模式","sn_normal":"单控制器模式","sn_setting":"控制器管理","ssid":"SSID","subscribed_success":"订阅成功","successful":"操作成功!","system_with":"跟随系统","target":"目标","temperature_setting":"温度设置","terms_of_service":"服务条款","theme":"主题","time_out":"操作超时，请重试！","timezone_setting":"时区设置","type":"类型","use":"使用","user_role_desp":"你可以分享你的控制器给其他用户，只需要输入其他用户的Email即可。被分享的用户只能控制你的设备，激活场景，或者运行动作，但是不能执行任何数据修改操作，比如添加或删除设备，添加或编辑场景等等，同样他们不能重置或者重启你的控制器。添加用户后，另一个用户需要重启App才能生效。","vacation":"假期","wifi_connection":"WIFI 连接","wifi_link_btn":"确认以上操作","wifi_link_desp":"长按设备的配网按钮，直到指示灯常亮，开启设备配网模式。","wifi_password":"WIFI 密码","wired_connection":"有线网络连接","zigbee":"Zigbee","zwave":"Z-Wave","zwave_delete_db":"直接删除设备，如果你认为该设备确实已经出现通信故障，或者丢失，你可以直接删除该设备，如果该设备再次和你的控制器通信的话有可能会再次出现","zwave_delete_field":"删除通信失败的设备，如果该设备没有被识别为失败， 那么该操作可能不会成功"},"guard":{"enter_guard_mode":"打开看家模式","exit_guard_mode":"退出看家模式","guard_device_desp":"请选择您想要打开的灯和开关。","guard_gps_desp":"请选择您家的位置，您家里的开关和灯将会在当地的日落时自动打开，然后3 - 5小时后自动关闭。","guard_h1":"Presen可以在你长期外出的时候更好的保护你的家","guard_h2":"智能告警","guard_h2_deps":"当进入看家模式后，如果有探测到烟雾、动作感应或者门窗感应等被触发，我们会给你的手机发送告警信息。","guard_h3":"开关自动化","guard_h3_deps":"Presen会在你不在家的时候按照日落时间自动打开和关闭你家里的灯，让别人以为你家里还有人。","guard_mode":"看家模式"},"help":{"cookie_hint":"本网站需要使用Cookie来提升用户体验，更多关于Cookie使用的信息请参考我们的%s，使用本网站表示你同意该条款。%s","privacy":"隐私条款","yes_i_agree":"同意"},"helpers":{"select":{"prompt":"请选择"},"submit":{"create":"新增%{model}","submit":"储存%{model}","update":"更新%{model}"}},"home":{"433_device":"433M 设备","a":"个","add_433_device":"添加433M设备","add_433_node_type_doorlock":"门磁","add_433_node_type_other":"其他","add_433_node_type_pir":"红外设备","add_433_node_type_smoke_sensor":"烟雾探测","add_433_node_type_water_sensor":"水淹探测设备","add_433_waiting_for_ports":"正在检查你的串口设备，请保证你的Controller支持该功能，并且串口的USB设置已插入控制器...","add_and_remove_device":"添加/删除设备","add_controller":"添加控制器","add_device":"添加设备","add_device_433_progress_error":"未找到433M的串口设备在你的控制器上","add_device_433_progress_loading":"检查中","add_device_fail":"添加设备失败，请重试！","add_device_success":"添加设备成功！","add_device_type":"您想添加什么类型的设备？","add_home":"新建家庭","add_qiock":"加入快捷控制","add_success":"添加成功！","cancel":"取消","card_1":"运行状态","card_2":"运行时间","card_3":"设备数量","cellphone":"手机号码","change_controller":"更改控制器","click_try_again":"重试","close_hint":"关闭","company_location":"中国，宁波","complete_automation_desp":"我们支持4中自动化模式，并且可以相互结合，支持不同的属性设置，满足您的各种使用场景。","complete_automation_title":"完善和简单易用的自动化功能","confirm":"确认","controller":"控制器","controller_frequency":"控制器频率","controller_information":"控制器信息","controller_input":"控制器名称","controller_sn_enter":"请输入控制器的序列号","controller_state":"控制器状态","current_controller":"当前控制器","customers_title":"智能家居客户","datacenter_desp":"数据中心覆盖亚洲，北美和欧洲","day":"天","delete_success":"删除成功!","detect_device":"检测设备中...","device":"设备","device_input":"设备名称","device_management":"设备管理","device_name":"设备名称","edit":"编辑","edit_controller":"修改控制器名称","edit_device":"修改设备名称","edit_home":"编辑家庭","edit_success":"修改成功！","email":"邮箱","error":"错误","event":"事件","failed":"操作失败！","favorite":"喜欢的场景","feedback":"反馈建议","forget_password":"忘记密码","global_datacenter":"全球数据中心","good_afternoon":"下午好","good_evening":"晚上好","good_morning":"上午好","have_notify":"有新的事件发生","have_version":"有新版本！","home":"首页","home_has_not_user":"这个家庭不存在此用户。","home_name":"家庭名称","how_to_setup":"怎样上手","indoor_humidity":"室内湿度","indoor_temp":"室内温度","ip":"IP","ipc_sn_enter":"请输入IP Camera的序列号","lan_ip":"内网地址","language":"语言","language_for_mobile":"语言","local_network":"局域网","locale_and_time_zone_config":"语言和时区","location_title":"位置","login_name":"邮箱","memory":"内存","name":"名称","nb_sn_enter":"请输入设备序列号","need_set_locale_and_time_zone":"请先设置语言和时区","network_issue":"网络错误","network_issue_desp":"网络异常，请重试","next":"下一步","no_controller":"还没有controller，请首先添加一个控制器，将controller通电并连接网络，等待10秒。","no_controller_input":"控制器名称不能为空！","no_device":"没有室内信息","not_device_input":"设备名称不能为空！","not_user":"用户不存在！","off_line":"离线","on_line":"在线","password":"密码","presen_slogon_2":"<span style=\'style-color\'>轻松</span>构建一个智能的家","profile":"设置","qiock_control":"快捷控制","recent_event":"最近的事件","recently_devices":"最近使用的设备","refresh":"刷新控制器","refund_money":"退款金额","remove_433_device_hint":"删除433M的设备请进入设备详情页面(点击设备名称进入)进行删除","remove_btn":"删除","remove_device":"删除设备","remove_device_fail":"删除设备失败，请重试！","remove_device_success":"删除设备成功！","remove_qiock":"从快捷控制中删除","remove_zwave":"删除Z-Wave设备","reset":"重置","room":"房间","routine":"日常","save":"保存","scan":"扫码","scene":"场景","select_433_device":"已探测到以下设备，请选择你要添加的设备","select_433_device_error":"请选择设备","select_433_node_type":"请选择该设备的类型","select_433_port":"请选择你要使用的串口设备","select_433_protocol":"请选择你的设备所支持的协议类型","setting":"设置","setting_btn":"设置","setup_desp":"使用Presen App能够快速和便捷的安装和配置您的Presen Controller和各种智能设备。","setup_step_1":"插入Presen控制器电源","setup_step_1_desp":"插入控制器电源，准备好您的WiFi和密码。","setup_step_2":"下载Presen App","setup_step_2_desp":"Presen App支持iOS和安卓，也支持在平板上使用，安装后注册一个账号。","setup_step_3":"添加设备","setup_step_3_desp":"添加您的Presen控制器，设备WiFi，然后就可以添加设备，或者设置一个场景和动作，也可以绑定您的Alex或者天猫精灵，大功告成！","setup_title":"只需<span style=\'style-color\'>简单</span>的操作","sign":"登录","sign_out":"退出","sign_up":"注册","skip":"跳过","skip_vs":"忽略此版本","sn":"序列号","sn_error":"序列号不存在！","sn_used":"序列号已被使用！","state":"状态","storage":"空间","submit":"提交","subscribe":"订阅","subscribe_desp":"订阅我们的产品新闻、行业新闻和促销优惠活动。","success":"成功","successful":"操作成功!","system":"系统","time_zone":"选择时区","token":"验证码","trigger":"联动","try_again":"稍后重试！","user":"用户","user_has":"用户已存在！","user_user":"不能添加自己！","version":"版本号","voice":"语音","vs_update":"是否立即更新","warning_message":"提示","we_are_serving":"我们有","we_provide_ease":"我们的目的就是给你的家庭提供<span class=\'style-color\'>方便</span>","wide_network":"广域网","wifi":"无线网","your_home":"你的家","z_device":"ZWAVE 设备"},"index":{"433m":"兼容性","433m_desp":"Presen支持Z-Wave、Zigbee、Tuya以及其他一些无线通讯协议（如433M、345M、319M），它们可以一起工作和相互控制。","about":"关于我们","about_about":"关于","about_desp":"Presen是更简单，更智能的智能家居。 我们将数百种可信赖品牌的产品集中在一个移动应用程序中，因此无论您身在何处，您都可以轻松地监控，控制和自动化您的智能家居。","about_desp_three":"现在，想象一下如果你不需要想象。 因为使用Presen，它已经可以。 诸如灯，门，空调和开关之类的东西现在可以为你更好地工作，让你感觉更安全，更有控制力，更高效，并且更加高兴。 借助Presen技术，无限的可能性将受到您自己的需求和创造力的推动。","about_desp_two":"试想一下，如果它知道你什么时候不能回家，以及如何让你放心。 试想一下，如果他们总是知道你需要什么，什么时候需要它。 试想一下，如果它知道你比你更了解自己。","about_name":"关于Presen","about_title":"什么是Presen","alerting":"通知和告警","alerting_desp":"你可以创建不同的规则来接收不同类型的通知事件。","app":"APP","app_desp":"App使用帮助","automation":"自动化","automation_desp":"联动、场景和日常功能可以让你轻松的根据自己的需求来创建不同的自动化功能。","build":"建立最好的智能家居","camera_desp":"支持安防IP摄像头，并且能够和其他各种智能设备联动控。","camera_title":"支持IP摄像头","change":"生活","community_desp":"分享和发现其他人是如果使用Presen的","complete_title":"完善的智能家居自动化系统","configuration_samples":"配置说明","contact_address":"联系地址","d_auto_desp":"你可以基于设备的变化事件来创建自动化，比如当走廊的动作感应器触发时打开灯。","device_automation":"设备自动化","documentation":"帮助中心","five_desp":"用户注册、设备添加删除和自动化创建需要联网。","for_live":"改变你的","get_app":"APP","gps_automation":"基于GPS的自动化","gps_desp_1":"你可以创建一个自动化当你下班回家时来自动激活Home场景。","gps_desp_2":"我们需要追踪你的手机的GPS位置信息来使该功能工作。","has_account":"已经有一个账号？","help_you":"帮助您管理家中的一切","home":"首页","local_desp":"当你在家时，即使家庭的互联网连接断线你依旧可以控制你的设备和使用自动化功能。","local_title":"离线控制","no_account":"没有账号？","or_sign_up":"或者进行注册","or_sign_up_type":"或者使用您的","our":"我们","our_app":"我的App","presen_support":"Presen 帮助","product":"产品","product_desp":"在路上，从您的办公室或躺在热带海滩上，您可以随时随地控制您的家。 您可以从PC，智能手机或任何其他具有互联网功能的设备的屏幕完全控制您的房屋。","product_name":"完整的家庭控制和自动化","product_title":"完整的自动化平台，包括无缝协同工作的本地控制器，云服务器，App应用程序。","remote_desp":"你可以在任何地方远程控制你的智能家居设备，比如在公司甚至在外度假时。","remote_loacl":"远程控制和离线控制","remote_title":"远程控制","routines":"自动化","routines_desp":"你可以根据自身需求使用自动化、场景和动作来创建各种不同的使用场景。","security":"安全","security_desp":"我们致力于保护您的数据和家庭安全。所有数据的传输都采用行业标准的加密方式进行加密。","sharing":"家庭分享","sharing_desp":"不同的家庭成员可以拥有不同的控制权限。","start_guide":"入门指南","sun_automation":"日出/日落自动化","sun_desp":"你可以在日出或者日落时自动来控制你的设备、场景和动作。","support":"支持","time_automation":"基于时间的自动化","time_desp":"基于你每天的日常规律来创建自动化。","voice":"语音控制","voice_desp":"支持Amazon Alexa、Google Assistant、IFTTT和天猫精灵。","vs_eight":"支持天猫精灵","vs_five":"离线控制","vs_four":"远程控制","vs_nine":"看家模式","vs_one":"支持多种标准通讯协议","vs_seven":"完善的App功能","vs_six":"快速和可靠的本地自动化","vs_three":"支持Google Assistant","vs_title":"与其他家庭智能化产品比较","vs_two":"支持Alexa","we_well":"我们将为您创造一个聪明的家","work_with":"Presen 作品"},"ipc":{"add_home":"添加到首页","alarm":"告警设置","alarm_day":"告警时间重复周期","alarm_no_week":"未设置","alarm_setting":"告警设置","alarm_time":"告警时间设置","alarm_timeInterval":"告警时间间隔","all_day_video":"全天录像","animal":"动物","brightness":"亮度","camera_setting":"设置","car":"车辆","cloud_play":"云端播放","contrast":"对比度","device_offine":"设备离线,请检查设备电源和网络连接","factory":"恢复出厂设置","factory_desp":"确定要恢复出厂设置?重置完成后，设备会自动重启。","flip":"翻转","horizontal_flip":"水平翻转","human_setting":"人形侦测设置","human_switch":"人形侦测开关","human_tracking":"人形追踪","interval_time":"{{time}}分钟","ip_address":"IP 地址","ipc_alarm_setting":"免打扰设置","ipc_alarm_type":"事件类型(可多选)","ipc_header":"设备","ipc_title":"安防摄像机","ipc_video_null":"视频已经过期并被清除!","last_time":"最后更新","local_network_error":"请确保您手机和摄像头处于同一局域网内","local_play":"本地播放","local_play_back":"本地回放","mic_volume":"麦克风音量","motion_area":"侦测区域","motion_sensitivity":"移动侦测灵敏度","motion_setting":"移动侦测设置","motion_switch":"移动侦测开关","motion_tracking":"运动追踪","motion_type":"移动侦测类型","motion_video":"移动侦测录像","network_info":"网络信息","night_auto":"自动夜视","night_black":"黑白夜视","night_color":"全彩夜视","night_light":"夜视灯光","night_mode":"夜视模式","night_switch":"补光灯开关","no_flip":"不翻转","noise_3d":"3D降噪","operation_failed":"操作失败","other":"其他","package":"包裹","person":"人","reboot":"重启设备","reboot_desp":"确定要重启设备?","reset_defaults":"恢复默认","reset_defaults_desp":"确定要恢复默认设置?","reset_defaults_success":"恢复默认设置成功","resolution":"分辨率","resolution_hd":"高清","resolution_sd":"标清","saturation":"饱和度","scene_condition":"指定免打扰的场景，当处于该场景时，您不会收到告警通知, 为空表示所有场景都接收。","sd_reset":"SD卡格式化","sd_reset_desp":"确定要格式化SD卡?","sd_setting":"SD卡录像类型","sd_storage_remaining":"剩余 {{remaining}} GB","sd_switch":"SD卡录像开关","select_one":"请至少选择一个","sharpness":"锐度","speed_up_connection":"加速连接","speed_up_connection_desp":"开启后，App会优先通过局域网连接设备，但是可能会导致设备部分功能无法使用。关闭后，App会优先通过云端连接设备。","switch_all":"IPC告警开关","switch_all_desp":"关闭后，所有IPC的报警都将关闭，但是你仍然可以在事件中查看所有告警视频。","type":"型号","update":"升级","update_desp":"确保设备已连接网，设备升级完成后将自动重启。请等待设备有语音提示！","update_title":"开始升级?","vertical_flip":"垂直翻转","video_setting":"视频设置","volume":"告警音量","wifi":"WiFi","wired":"有线","zoom":"变焦"},"mongoid":{"attributes":{"c/action":{"name":"动作名称"},"c/node":{"c_display_name":"设备名称"},"c/scene":{"name":"场景名称"}},"errors":{"format":"%{attribute}%{message}","messages":{"accepted":"必须是可被接受的","blank":"不能为空字符","confirmation":"与确认值不匹配","empty":"不能留空","equal_to":"必须等于 %{count}","even":"必须为双数","exclusion":"是保留关键字","greater_than":"必须大于 %{count}","greater_than_or_equal_to":"必须大于或等于 %{count}","inclusion":"不包含于列表中","invalid":"是无效的","less_than":"必须小于 %{count}","less_than_or_equal_to":"必须小于或等于 %{count}","model_invalid":"验证失败: %{errors}","not_a_number":"不是数字","not_an_integer":"必须是整数","odd":"必须为单数","other_than":"长度非法（不可为 %{count} 个字符","present":"必须是空白","record_invalid":"验证失败: %{errors}","required":"必须存在","restrict_dependent_destroy":{"has_many":"由于 %{record} 需要此记录，所以无法移除记录","has_one":"由于 %{record} 需要此记录，所以无法移除记录"},"taken":"已经被使用","too_long":{"one":"过长（最长为一个字符）","other":"过长（最长为 %{count} 个字符）"},"too_short":{"one":"过短（最短为一个字符）","other":"过短（最短为 %{count} 个字符）"},"wrong_length":{"one":"长度非法（必须为一个字符）","other":"长度非法（必须为 %{count} 个字符）"}}}},"nb":{"add_device_success":"请确保该设备已上电并处于正常工作状态，否则将暂时无法看到该设备!"},"number":{"currency":{"format":{"delimiter":",","format":"%u %n","precision":2,"separator":".","significant":false,"strip_insignificant_zeros":false,"unit":"CN¥"}},"format":{"delimiter":",","precision":3,"separator":".","significant":false,"strip_insignificant_zeros":false},"human":{"decimal_units":{"format":"%n %u","units":{"billion":"十亿","million":"百万","quadrillion":"千兆","thousand":"千","trillion":"兆","unit":""}},"format":{"delimiter":"","precision":1,"significant":false,"strip_insignificant_zeros":false},"storage_units":{"format":"%n %u","units":{"byte":{"one":"Byte","other":"Bytes"},"gb":"GB","kb":"KB","mb":"MB","tb":"TB"}}},"percentage":{"format":{"delimiter":"","format":"%n%"}},"precision":{"format":{"delimiter":""}}},"order":{"status":{"cancel":"已取消","done":"已付款","pending":"未付款"}},"pay":{"activate":"激活","amount":"金额","auto_pay":"自动续费","cancel_sub":"取消订阅","cancel_sub_desp":"确定要取消订阅?","change_success":"修改成功，状态更新有延迟，请稍后查看","charge":"订阅","cloud_storage":"云存储","customer_title":"账单和收据","daily":"测试","days":"有效期: {{day}}天","download_billing":"下载账单","download_success":"账单已发送至您的邮箱，请查收","full_name":"姓名","input_full_name":"请输入姓名","monthly":"连续包月","now_sub_status":"当前订阅状态","order":"订单管理","order_pice":"订单价格","paid_price":"支付金额","pause":"暂停","pay":"立即支付","pay_info":"支付信息","pay_info_desp":"姓名和邮箱用于支付记录和收据，不会用于其他用途。请确保信息准确。如果输入错误,将无法获取支付和收据信息","payment_record":"支付记录","refund_money":"退款金额","reset_sub":"恢复订阅","select_item":"当前选择","setting_wait":"操作成功！状态更新有延迟，请稍后查看","sub":{"daily":{"desp":"支持1台摄像机，保存过去{{file_days}}天内，因事件触发产生的录像","title":"测试订阅"},"monthly":{"desp":"支持1台摄像机，保存过去{{file_days}}天内，因事件触发产生的录像","title":"月度订阅"},"trial":{"desp":"免费试用7天，支持1台摄像机，保存过去{{file_days}}天内，因事件触发产生的录像","title":"免费试用"},"yearly":{"desp":"支持1台摄像机，保存过去{{file_days}}天内，因事件触发产生的录像","title":"年度订阅"}},"sub_status":"订阅状态","sub_time":"过期时间","sub_type":"订阅类型","success":"支付成功, 状态更新有延迟，请稍后查看","title":"家庭地址信息","trial":"体验","unknown":"未订阅或已过期","up_sub":"修改订阅","yearly":"连续包年"},"payment":{"errors":{"not_found_payment_record":"支付记录不存在"},"status":{"cancel":"已取消","failed":"付款失败","pending":"未付款","success":"已付款"}},"permissions":{"camera_block":"相机权限被阻止。","camera_error":"此设备上的相机权限不可用。","camera_title":"您已经禁用APP相机权限,相机服务受限,请在设置中开启","location_block":"位置权限被阻止。","location_error":"此设备上的位置权限不可用。","location_title":"您已经禁用APP定位权限,定位服务受限,请在设置中开启","media_block":"媒体权限被阻止。","media_error":"此设备上的媒体权限不可用。","media_title":"您已经禁用APP读写手机储存权限,图片文件访问受限,请在设置中开启","notify_error":"通知未开启"},"room":{"add_room":"添加房间","all_off":"全关","all_on":"全开","desp":"事件,表示在过去的24小时内当前房间有设备状态变化","device_in_room":"这个房间中的设备","event":"事件","filter_controller":"根据控制器过滤","filter_room":"根据房间过滤","light_color":"灯光颜色","normal":"正常","room_name":"房间名称","room_no_device":"没有选择设备！","room_no_name":"房间名称不能为空！","room_switch_hint":"直接控制该房间中所有的开关设备","setting_room":"设置房间"},"routine":{"add":"添加日常","address":"地址","call_url":"调用HTTP接口","call_url_desp":"请输入需要调用的HTTP接口，当该自动化运行时会以GET方式向该地址发送请求","cond_action":"执行一个动作","cond_device":"控制设备","cond_scene":"打开一个场景","cond_url":"执行外部链接","edit":"编辑日常","get_to":"到达","is_cyclic":"重复","last":"上一步","leave":"离开","no_google":"暂时不支持，GPS类型的自动化需要您的手机运行Google服务。","one_cond":"执行一次","please_address":"请选择位置","please_date":"请选择日期","please_external":"请输入外部链接!","please_leave":"请选择离开或者返回某地","please_time":"请选择时间","please_to":"请选择执行目标","please_type":"请选择执行类型","routine":"日常","routine_cond":"执行条件","routine_desp":"日常可以根据您设置的时间或者GPS的条件自动完成打开场景或者设备的操作(需要使用Presen App)","routine_location":"地理位置","routine_type":"执行类型","run_multiple":"重复","run_once":"一次","select_action":"选择动作","select_device":"选择设备","select_scene":"选择场景","test_app_url":"在浏览器中打开测试","time":"时间","to_location":"地理位置","to_time":"时间","ui":"该功能需要配合Presen App和Presen Cloud，请访问https://www.presensmarthome.com"},"scene":{"activate":"激活","add_scene":"添加场景","add_scene_type":"请输入场景名称:","change_scene":"是否切换场景","current_scene":"当前场景","delete_scene":"是否删除场景","scene_device":"请添加设备！","scene_input":"名称不能为空！","scene_name":"场景名称","select_icon":"选取图标"},"services":{"alexa":"使用支持Amazon Alexa的设备来控制你的设备、场景和动作。","alexa_desp":"比如 \\"Alexa, turn on home scene\\"。","ali":"使用天猫精灵来控制你的设备、场景和动作。","ali_desp":"比如 \\"天猫精灵, 打开卧室的灯\\"。","google":"使用支持Google Assistant的设备来控制你的设备、场景和动作。","google_desp":"比如 \\" Hey Google, activate home\\"。","ifttt":"启用IFTTT来支持更多自动化功能。","ifttt_desp":"比如当动作感应触发时你会收到一个电话。"},"session":{"60_s":"60秒后重试！","account":"账号","address_empty":"请选择地址","cellphone_email_type":"邮箱或手机号码格式不正确！","cellphone_input":"请输入手机号码!","change_china":"切换到中国版","change_other":"切换到国际版","change_password":"修改密码","change_username":"修改用户名","confir_input":"请输入确认密码！","email":"邮箱已存在！","email_input":"请输入邮箱！","email_or_phone":"邮箱","email_type":"邮箱格式不正确！","get_token":"验证码","is_cellphone":"手机号码已存在！","is_email":"邮箱已存在！","login_name_input":"邮箱","login_phone":"手机号码","login_to_email":"使用邮箱登录","login_to_phone":"是有手机号登录","new_input":"请输入新密码！","new_password":"新密码","no_name_desp":"用户名不能为空","no_user_name":"邮箱或手机号码不能为空！","not_eamil":"邮箱不存在！","not_email_or_phone":"请输入您的邮箱!","not_input_phone":"请输入您的手机号!","not_phone":"手机号不存在！","not_token":"验证码不正确！","old_error":"旧密码不正确！","old_input":"请输入旧密码！","old_password":"旧密码","password_changed_success":"密码修改成功！","password_confirmation":"确认密码","password_input":"密码","password_length":"密码至少需要8个字符，包含大写、小写和数字。","please_enter_password":"请输入你的密码!","register":"注册","reset_password":"重置密码","select_country":"请选择地区","send":"发送","send_token":"验证码已经发送到了您的邮箱，请查收并在下面输入您的验证码以继续","signup_success_login":"账户创建成功，请登录","token_input":"验证码已经发送到了您的邮箱，请查收并在下面输入您的验证码以继续","token_send":"验证码已发送到您的邮箱！","token_send_phone":"验证码已发送到您的手机！","two_password":"两次密码不一致！","user_name":"用户名","wrong_name":"邮箱或密码错误！"},"setting":{"add":"添加","advanced_setting":"高级设置","background_image":"背景图片","backup":"备份控制器","backup_desp":"控制器备份","backup_message":"备份控制器请不要关闭电源!","base":"基础设置","change_notification":"开启通知","check_device":"请选择你不想接收以下哪些设备和属性的变化通知","check_device_for_urgent":"选择哪些设备的变化为紧急通知","check_scene":"只有在处于以下选中场景时，才不接收设备的变化通知，不勾选为全部场景都不接收","check_scene_for_urgent":"仅当处于以下场景时才接收紧急通知","close_account":"删除账号","close_desp":"删除账号后，你将会被退出登录，并且该账号将无法再次使用！","default_reset_all_parameters":"默认重置所有参数","delete":"删除","download_data":"下载数据","download_desp":"下载你的个人数据，包含数据的CSV文件将会被发送到你的个人邮箱","end_time":"结束","friday":"星期五","home_setting":"家庭管理","inform":"通知","local_siren":"本地警告","login_close_desp":"账户已经删除，如需重新启用账号，请联系我们！","monday":"星期一","new_update_version":"新版本:","next_day":"次日","not_end_time":"结束时间设置为:","not_start_time":"如果不需要时间请将开始时间设置为:","notify":"通知中心","notify_by_device":"设备变化通知","notify_by_time":"免打扰时间","notify_desp":"全局控制通知开关，关闭将收不到通知","notify_device_noti_desp":"设置哪些设备你不想要接收变化通知","notify_dnd_desp":"设置你不想要接收通知的时间","notify_time":"时间","notify_urgent_device":"紧急通知","notify_urgent_noti_desp":"设置哪些设备变化时接收紧急通知，紧急通知为一种特殊的通知方式，注意该设置会忽略[免打扰时间]和[忽略设备变化通知]的配置","photo_btn":"从相册中选择","place_end_time":"请选择结束时间！","place_select_period":"请选择时间！","place_start_time":"请选择开始时间！","property":"属性","record":"重制控制器","record_desp":"控制器恢复出厂设置","record_error":"控制器恢复出厂设置失败！请重试！","record_message":"重制控制器请不要关闭电源!","record_success":"控制器已恢复出厂设置！","reset":"重置控制器","reset_desp":"控制器重置为出厂默认值，所有数据将清除。\\n\\n包括：设备、时区、WiFi、场景、房间、动作、自动化、控制器PIN等等","reset_home":"重置家庭","reset_home_desp":"重置你的控制器为出厂默认值，所有数据将清除。\\n\\n包括家庭中：设备、时区、WiFi、场景、房间、动作、自动化、控制器PIN等等","reset_message":"控制器数据将全部删除!","reset_success":"控制器重置成功","reset_title":"确认恢复出厂设置吗？","restart":"重启控制器","restart_desp":"重启控制器， 重启大约需要5分钟！重启完成后请刷新页面","restart_message":"确定要重启控制器吗？","restart_title":"确认重启吗？","saturday":"星期六","select_date":"选择日期","select_device_scene":"请选择设备或场景！","select_period":"选择时间","start_end":"结束时间小于开始时间!","starting_time":"开始","sunday":"星期日","system_notification_state":"接收消息通知。用于接收设备告警，场景激活等等推送通知，关闭后无法收到所有通知","thursday":"星期四","tuesday":"星期二","update_desp":"开始升级吗?","update_desp_one":"有新版本: ","update_failed_in_progress":"已经有更新任务正在进行中","update_failed_other":"更新失败，请稍后重试","update_failed_up_to_date":"已经是最新版本，无需更新","update_online":"升级控制器","update_size":"","update_title":"开始更新...","updated":"当前控制器为最新版本，无需升级!","updating":"正在下载中。。。","upgrade_desp":"控制器正在升级中，请不要关闭控制器电源，只会花费几秒钟的时间。\\n\\n升级成功后控制器会自动重启，并且您会收到通知。","upgrade_success_to":"控制器已经升级到了","use_background_image":"使用背景图片","wednesday":"星期三","widget_desp":"你最多可以选择4个场景和动作在小组件中使用。","widget_login":"需要先登陆来获取您的场景和动作信息。","widget_setting":"小组件设置"},"spec":{"FrameChange":"移动侦测","HumanMotion":"人体移动","access_control":"访问控制","air_flow":"气流","air_temperature":"气压","alarm":"告警","alarm_s":"感应告警","angle_position":"角度","animal_detected":"检测到动物活动","app_url":"触发URL","appliance":"家电","atmospheric_pressure":"气压","auto":"自动","aux":"辅助设备","auxiliary":"辅助","barometric_pressure":"气压","battery":"电池","burglar":"防盗","camera":"IP相机","car_detected":"检测到车辆活动","clock":"时钟","close":"关闭","co":"一氧化碳","co2":"二氧化碳","co2_alarm":"二氧化碳告警","co2_level":"二氧化碳级别","co_alarm":"一氧化碳告警","co_level":"一氧化碳级别","color_switch":"颜色控制","color_temperature":"色温","configuration_parameters":"配置参数","contact_sensor":"接触感应","cool":"制冷","cooling_setpoint":"制冷","current":"水流","dew_point":"露点","dimmer":"调光器","direction":"方向","distance":"距离","door_lock":"门锁","door_window":"门窗","electrical_conductivity":"导电性","electrical_resistivity":"电阻系数","emergency":"紧急","executed":"已运行","false":"未触发","fan_mode":"风扇模式","fire_sensor":"火灾感应","first":"第一个","flood":"水淹","flow_measurement":"流量测量","frame_change":"画面变化","freeze":"制冷","frequency":"频率","full":"全","general":"通用","general_purpose":"通用探测","general_purpose_alarm":"通用告警","general_purpose_value":"通用目标状态","general_trigger":"通用事件","glass_break":"破碎感应","going_to_low_battery":"低电量","gu_zhang":"故障","heat":"制热","heat_alarm":"加热告警","heating_setpoint":"制热","home_security":"家庭安防","human_motion":"人体移动","humidity":"湿度","huo_jing":"火警","is":"状态为","key_fob":"key Fob","keypad":"keypad","lock":"关闭","loudness":"音量","low":"低","luminance":"光照","luminiscence":"光照","main_powered":"市电","meter":"测量仪","moisture":"雨量","motion":"动作感应","motor":"马达","multi_switch":"多路开关","n_a":"N/A","no_b":"正常","normal":"正常","not_used":"未知","off_b":"关","on_b":"开","open":"打开","open_close":"接触感应","package_detected":"检测到包裹放置","person_detected":"检测到人员活动","power":"电力","power_management":"用电管理","quake":"震感","rain_rate":"雨速","relative_humidity":"相对湿度","remote_ontrol":"远程控制","reserved":"保留","resume":"恢复","return_first_alarm_on_supported_list":"首位告警","rock_setting":"摇头设置","rotation":"旋转","security_repeater":"Security Repeater","seismic_intensity":"地震强度","seismic_magnitude":"震级","sensor":"感应器","smoke":"烟雾","smoke_alarm":"烟雾告警","smoke_bei_jing_cuo":"当前背景错","smoke_biao_ding":"正常标定错","smoke_bu_chang_biao_ding":"补偿标定错","smoke_di_dian_ya":"低电压","smoke_gu_zhang_jing_yin":"故障静音","smoke_huo_jing_biao_ding":"火警标定错","smoke_nei_bu_cuo":"内部错","smoke_pu_tong_jing_yin":"普通静音","smoke_shou_ming":"10年寿命到","smoke_test":"烟雾测试","smoke_tui_chu_huo_jing_biao_ding":"退出报警标定错","smoke_tui_chu_jing_yin_zai_ci_bao_jing":"退出静音再次报警标定错","smoke_wu_ran":"污染","smoke_zi_jian":"自检","soil_temperature":"油温","solar_radiation":"太阳辐射","switch":"开关","switch_all":"整体开关","switch_to":"切换为","system":"系统","tamper":"防拆","tank_capacity":"容量","target_temperature":"目标温度","temperature":"温度","temperature_unit":"温度单位","thermostat":"调温器","thermostat_mode":"空调模式","thermostat_setpoint":"设置温度","tide_level":"潮位","tilt":"倾斜","time":"时间","true":"触发","ultraviolet":"紫外线","unlock":"打开","velocity":"速度","ventilation":"通风设备","vibration":"震动","voltage":"电压","water":"水","water_leak":"水淹","water_leak_alarm":"水淹告警","water_temperature":"水温","weight":"重量","window_covering":"窗帘","yes_b":"触发"},"subscription":{"errors":{"already_subscribed":"已订阅，请升级或降级订阅","already_trailed":"您已试用过该服务","invalid_type":"订阅类型不存在","no_subscription":"您还没有订阅","same_price_id":"不能更改相同订阅"}},"support":{"array":{"last_word_connector":", 和 ","two_words_connector":" 和 ","words_connector":", "}},"time":{"am":"上午","formats":{"default":"%Y年%m月%d日 %A %H:%M:%S %Z","long":"%Y年%m月%d日 %H:%M","short":"%m月%d日 %H:%M"},"pm":"下午"},"timezones":{"Abu Dhabi":"阿布达比","Adelaide":"阿得雷德","Alaska":"阿拉斯加","Almaty":"阿拉木图","American Samoa":"美属萨摩亚群岛","Amsterdam":"阿姆斯特丹","Arizona":"亚利桑那州","Astana":"阿斯塔纳","Athens":"雅典","Atlantic Time (Canada)":"大西洋时间(加拿大)","Auckland":"奥克兰","Azores":"亚速群岛","Baghdad":"巴格达","Baku":"巴库","Bangkok":"曼谷","Beijing":"北京","Belgrade":"贝尔格勒","Berlin":"柏林","Bern":"伯恩","Bogota":"波哥大","Brasilia":"巴西利亚","Bratislava":"布拉提斯拉瓦","Brisbane":"布里斯本","Brussels":"布鲁塞尔","Bucharest":"布加勒斯特","Budapest":"布达佩斯","Buenos Aires":"布宜诺斯艾利斯","Cairo":"开罗","Canberra":"坎培拉","Cape Verde Is.":"维德角岛","Caracas":"卡拉卡斯","Casablanca":"达尔贝达","Central America":"中美洲","Central Time (US & Canada)":"中部时区(美国与加拿大)","Chatham Is.":"查塔姆","Chennai":"清奈","Chihuahua":"契瓦瓦","Chongqing":"重庆","Copenhagen":"哥本哈根","Darwin":"达尔文","Dhaka":"达卡","Dublin":"都布林","Eastern Time (US & Canada)":"东部时区(美国与加拿大)","Edinburgh":"爱丁堡","Ekaterinburg":"叶卡捷琳堡","Fiji":"斐济","Georgetown":"乔治城","Greenland":"格陵兰","Guadalajara":"瓜达拉哈拉","Guam":"关岛","Hanoi":"河内","Harare":"哈拉雷","Hawaii":"夏威夷","Helsinki":"赫尔辛基","Hobart":"荷巴特","Hong Kong":"香港","Indiana (East)":"印第安那州(东部)","International Date Line West":"国际换日线(西)","Irkutsk":"伊尔库茨克","Islamabad":"伊斯兰玛巴德","Istanbul":"伊斯坦堡","Jakarta":"雅加达","Jerusalem":"耶路撒冷","Kabul":"喀布尔","Kaliningrad":"加里寧格勒","Kamchatka":"堪察加","Karachi":"喀拉蚩","Kathmandu":"加德满都","Kolkata":"加尔各答","Krasnoyarsk":"克拉斯诺亚尔斯克","Kuala Lumpur":"吉隆坡","Kuwait":"科威特","Kyiv":"基辅","La Paz":"拉巴斯","Lima":"利马","Lisbon":"里斯本","Ljubljana":"卢比安纳","London":"伦敦","Madrid":"马德里","Magadan":"马加丹","Marshall Is.":"马绍尔群岛","Mazatlan":"马萨特兰","Melbourne":"墨尔本","Mexico City":"墨西哥市","Mid-Atlantic":"大西洋中部","Midway Island":"中途岛","Minsk":"明斯克","Monrovia":"蒙罗维亚","Monterrey":"蒙特雷","Montevideo":"蒙得维的亚","Moscow":"莫斯科","Mountain Time (US & Canada)":"山区时区(美国与加拿大)","Mumbai":"孟买","Muscat":"马斯喀特","Nairobi":"奈洛比","New Caledonia":"新喀里多尼亚","New Delhi":"新德里","Newfoundland":"纽芬兰","Novosibirsk":"新西伯利亚","Nuku\'alofa":"努瓜娄发","Osaka":"大阪","Pacific Time (US & Canada)":"太平洋时区(美国与加拿大)","Paris":"巴黎","Perth":"珀斯","Port Moresby":"莫士比港","Prague":"布拉格","Pretoria":"普勒托利亚","Quito":"基多","Rangoon":"仰光","Riga":"里加","Riyadh":"利雅德","Rome":"罗马","Samara":"薩馬拉","Samoa":"萨摩亚","Santiago":"圣地亚哥","Sapporo":"札幌","Sarajevo":"塞拉耶佛","Saskatchewan":"萨斯喀彻温","Seoul":"首尔","Singapore":"新加坡","Skopje":"史高比耶","Sofia":"索菲亚","Solomon Is.":"索罗门群岛","Srednekolymsk":"中科雷姆斯克","Sri Jayawardenepura":"科泰","St. Petersburg":"圣彼得堡","Stockholm":"斯德哥尔摩","Sydney":"雪梨","Taipei":"台北","Tallinn":"塔林","Tashkent":"塔什干","Tbilisi":"提比里斯","Tehran":"德黑兰","Tijuana":"提华纳","Tokelau Is.":"托克劳群岛","Tokyo":"东京","UTC":"世界标準时间","Ulaan Bataar":"乌兰巴托","Ulaanbaatar":"乌兰巴托","Urumqi":"乌鲁木齐","Vienna":"维也纳","Vilnius":"维尔纽斯","Vladivostok":"海参崴","Volgograd":"伏尔加格勒","Warsaw":"华沙","Wellington":"惠灵顿","West Central Africa":"中西非时区","Yakutsk":"雅库茨克","Yerevan":"叶里温","Zagreb":"札格瑞布","Zurich":"苏黎世"},"trigger":{"action":"执行以下操作","add_trigger":"创建联动","auto":"自动","conditional_device":"请设置条件设备","delete_trigger":"是否删除联动","execute":"已运行","manual":"手动","no_trigger_input":"请设置联动名称","scene_condition":"请选择想要该自动化生效的场景，只有处于选中的场景之一时，该自动化才会生效。如果没有选中任何场景，将和选中所有具有一样的效果。","trigger_input":"联动名称","virtual_device_help":"这里可以输入一个URL，当事件发生时该URL会被调用(HTTP GET)。自动代表当该事件发生时该URL会被系统自动调用，间隔时间1小时；手动代表事件发生时需要手工点击来触发，用于查看摄像头等设备。","when":"当以下情况发生时"},"tuya":{"audio":"声音","camera_photo_saved":"视频截图已保存到了系统相册中","camera_record_end":"视频已保存到系统相册中","direction":"控制","hd":"清晰度","high":"高灵敏度","low":"低灵敏度","middle":"中灵敏度","motion":"移动侦测设置","motion_sensitivity":"移动侦测灵敏度","motion_switch":"移动侦测报警开关","motion_tracking":"移动追踪开关","normal":"正常","ok_s":"正常","pir":"人体红外线感应设置","pir_switch":"人体红外线感应","record":"录像","screenshot":"拍照","sd_error":"SD卡异常","sd_formating":"正在格式化","sd_no":"无SD卡","sd_state":"SD卡状态","sd_storage":"SD卡容量","sd_storage_error":"空间不足","sound_sensitivity":"声音侦测灵敏度","sound_switch":"声音侦测报警开关","souud":"声音侦测设置","speak":"对讲","wifi_signal":"WIFI信号强度"},"user":{"action":"操作","add_user":"添加用户","add_users":"请输入邮箱","admin":"管理员","country":"国家/地区","de":"德国","delete_user":"删除该用户","edit":"编辑权限","en":"美国","es":"西班牙","fr":"法国","not_country":"国家/地区 不能为空","not_user_input":"用户不能为空！","not_user_room":"房间不能为空！","pt":"葡萄牙","ru":"俄国","user":"用户","zh":"中国"},"wifi":{"add_desp":"正在添加控制器，请稍后","add_error":"添加控制器失败，请点击重试","add_success":"控制器添加成功","add_title":"无线设置成功.","add_tuya_wifi":"请将手机连接到配网设备 Wi-Fi 热点后点击确定","ipc_setting_desp":"正在设置IP Camera WIFI，请等待","ipc_setting_error":"无法自动连接，请您进入手机网络设置手工连接newway的无线热点，然后返回这里点击重试。","next_wifi_desp":"在下一步的操作中你的手机可能会提示你是否需要切换到蜂窝数据或者继续使用无线网络，请选择继续使用无线网络","reload_desp":"控制器正在重启，需要最多30秒来完成重启。","retry":"重试","setting_desp":"正在设置控制器WIFI，请等待","setting_error":"无法自动连接，请您进入手机网络设置手工连接PRESEN_CONTROLLER的无线热点，然后返回这里点击重试。","wifi_link_error":"设置WiFi失败，请重启你的控制器，然后再次进入网络设置模式进行重试","wired_net_add":"选择控制器","wired_net_btn":"手工添加控制器","wired_net_desp":"首先请插入网线，然后再插入电源线来启动你的控制器，请保证你的手机和控制器连接在同一个局域网中。"}}'));
I18n.translations.zh_tw = I18n.extend((I18n.translations.zh_tw || {}), JSON.parse('{"timezones":{"Abu Dhabi":"阿布達比","Adelaide":"阿得雷德","Alaska":"阿拉斯加","Almaty":"阿拉木圖","American Samoa":"美屬薩摩亞群島","Amsterdam":"阿姆斯特丹","Arizona":"亞利桑那州","Astana":"阿斯塔納","Athens":"雅典","Atlantic Time (Canada)":"大西洋時間(加拿大)","Auckland":"奧克蘭","Azores":"亞速群島","Baghdad":"巴格達","Baku":"巴庫","Bangkok":"曼谷","Beijing":"北京","Belgrade":"貝爾格勒","Berlin":"柏林","Bern":"伯恩","Bogota":"波哥大","Brasilia":"巴西利亞","Bratislava":"布拉提斯拉瓦","Brisbane":"布里斯本","Brussels":"布魯塞爾","Bucharest":"布加勒斯特","Budapest":"布達佩斯","Buenos Aires":"布宜諾斯艾利斯","Cairo":"開羅","Canberra":"坎培拉","Cape Verde Is.":"維德角島","Caracas":"卡拉卡斯","Casablanca":"達爾貝達","Central America":"中美洲","Central Time (US & Canada)":"中部時區(美國與加拿大)","Chatham Is.":"查塔姆","Chennai":"清奈","Chihuahua":"契瓦瓦","Chongqing":"重慶","Copenhagen":"哥本哈根","Darwin":"達爾文","Dhaka":"達卡","Dublin":"都布林","Eastern Time (US & Canada)":"東部時區(美國與加拿大)","Edinburgh":"愛丁堡","Ekaterinburg":"葉卡捷琳堡","Fiji":"斐濟","Georgetown":"喬治城","Greenland":"格陵蘭","Guadalajara":"瓜達拉哈拉","Guam":"關島","Hanoi":"河內","Harare":"哈拉雷","Hawaii":"夏威夷","Helsinki":"赫爾辛基","Hobart":"荷巴特","Hong Kong":"香港","Indiana (East)":"印第安那州(東部)","International Date Line West":"國際換日線(西)","Irkutsk":"伊爾庫茨克","Islamabad":"伊斯蘭瑪巴德","Istanbul":"伊斯坦堡","Jakarta":"雅加達","Jerusalem":"耶路撒冷","Kabul":"喀布爾","Kaliningrad":"加里寧格勒","Kamchatka":"堪察加","Karachi":"喀拉蚩","Kathmandu":"加德滿都","Kolkata":"加爾各答","Krasnoyarsk":"克拉斯諾亞爾斯克","Kuala Lumpur":"吉隆坡","Kuwait":"科威特","Kyiv":"基輔","La Paz":"拉巴斯","Lima":"利馬","Lisbon":"里斯本","Ljubljana":"盧比安納","London":"倫敦","Madrid":"馬德里","Magadan":"馬加丹","Marshall Is.":"馬紹爾群島","Mazatlan":"馬薩特蘭","Melbourne":"墨爾本","Mexico City":"墨西哥市","Mid-Atlantic":"大西洋中部","Midway Island":"中途島","Minsk":"明斯克","Monrovia":"蒙羅維亞","Monterrey":"蒙特雷","Montevideo":"蒙得維的亞","Moscow":"莫斯科","Mountain Time (US & Canada)":"山區時區(美國與加拿大)","Mumbai":"孟買","Muscat":"馬斯喀特","Nairobi":"奈洛比","New Caledonia":"新喀里多尼亞","New Delhi":"新德里","Newfoundland":"紐芬蘭","Novosibirsk":"新西伯利亞","Nuku\'alofa":"努瓜婁發","Osaka":"大阪","Pacific Time (US & Canada)":"太平洋時區(美國與加拿大)","Paris":"巴黎","Perth":"珀斯","Port Moresby":"莫士比港","Prague":"布拉格","Pretoria":"普勒托利亞","Quito":"基多","Rangoon":"仰光","Riga":"里加","Riyadh":"利雅德","Rome":"羅馬","Samara":"薩馬拉","Samoa":"薩摩亞","Santiago":"聖地亞哥","Sapporo":"札幌","Sarajevo":"塞拉耶佛","Saskatchewan":"薩斯喀徹溫","Seoul":"首爾","Singapore":"新加坡","Skopje":"史高比耶","Sofia":"索菲亞","Solomon Is.":"索羅門群島","Srednekolymsk":"中科雷姆斯克","Sri Jayawardenepura":"科泰","St. Petersburg":"聖彼得堡","Stockholm":"斯德哥爾摩","Sydney":"雪梨","Taipei":"台北","Tallinn":"塔林","Tashkent":"塔什干","Tbilisi":"提比里斯","Tehran":"德黑蘭","Tijuana":"提華納","Tokelau Is.":"托克勞群島","Tokyo":"東京","UTC":"世界標準時間","Ulaan Bataar":"烏蘭巴托","Ulaanbaatar":"烏蘭巴托","Urumqi":"烏魯木齊","Vienna":"維也納","Vilnius":"維爾紐斯","Vladivostok":"海參崴","Volgograd":"伏爾加格勒","Warsaw":"華沙","Wellington":"威靈頓","West Central Africa":"中西非時區","Yakutsk":"雅庫茨克","Yerevan":"葉里溫","Zagreb":"札格瑞布","Zurich":"蘇黎世"}}'));
export default I18n;
