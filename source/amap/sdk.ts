import {NativeModules, Platform} from 'react-native';

const {AMapSdk} = NativeModules;
console.log('AMapSdk', AMapSdk);
if (!AMapSdk.addListener) {
  console.warn('AMapSdk 缺少 addListener 方法');
}

export function init(apiKey?: string) {
  if (Platform.OS === 'android') {
    AMapSdk.init(apiKey);
  } else {
    AMapSdk.setApiKey(apiKey);
  }
}

export function getVersion(): Promise<string> {
  return AMapSdk.getVersion();
}
