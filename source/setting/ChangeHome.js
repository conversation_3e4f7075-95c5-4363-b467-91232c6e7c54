/* eslint-disable react/no-unstable-nested-components */
import React, { useRef, useState } from 'react';
import { View, Text, TouchableOpacity, FlatList, Image } from 'react-native';
import { Helper, HelperMemo } from '../Helper';
import I18n from '../I18n';
import { Tme, Colors } from '../ThemeStyle';
import NavBarView from '../share/NavBarView';
import EmptyView from '../share/EmptyView';
import ShadowView from '../share/ShadowView';
import { mainRadius } from '../Tools';
import { useFocusEffect } from '@react-navigation/native';
import { hideLoading, showLoading } from '../../ILoading';

export default function ChangeHome(props) {
  const [dataSource, setDataSource] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  useFocusEffect(
    React.useCallback(() => {
      doFetchData();
      return () => { };
    }, []),
  );

  const _flatList = useRef(null);

  const addHome = () => {
    props.navigation.push('AddHome', {
      title: I18n.t('home.add_home'),
    });
  };

  const touchRow = rowData => {
    props.navigation.push('changeController', {
      home_id: rowData.home_id,
      name: rowData.name,
      title: I18n.t('setting.home_setting'),
    });
  };

  const doFetchData = () => {
    showLoading();
    Helper.httpGET(Helper.urlWithQuery('/home/<USER>'), {
      cloud: true,
      success: data => {
        setDataSource(data);
        setRefreshing(false);
      },
      ensure: () => {
        hideLoading();
      },
    });
  };
  const doRefreshData = () => {
    setRefreshing(true);
    doFetchData();
  };

  const _renderRow = ({ item }) => {
    const checked = item.home_id === HelperMemo.user_data.home_id;
    return (
      <View activeOpacity={0.8} style={{ marginTop: 8 }}>
        <View style={{ marginHorizontal: 8, marginBottom: 12 }}>
          <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => touchRow(item)}
              style={[
                {
                  height: 100,
                  borderRadius: mainRadius(),
                  paddingHorizontal: 16,
                  backgroundColor: checked
                    ? Colors.MainColor
                    : Tme('cardColor'),
                  flexDirection: 'row',
                },
              ]}>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'flex-start',
                }}>
                <Text
                  style={{
                    fontSize: 16,
                    color: checked ? '#fff' : Tme('cardTextColor'),
                  }}>
                  {item.name}
                </Text>
              </View>
            </TouchableOpacity>
          </ShadowView>
        </View>
      </View>
    );
  };
  return (
    <NavBarView>
      <View style={{ flex: 1 }}>
        <FlatList
          style={{ paddingHorizontal: 20, flex: 1 }}
          ref={_flatList}
          data={dataSource}
          renderItem={_renderRow}
          ListEmptyComponent={() => <EmptyView />}
          onEndReachedThreshold={0.1}
          refreshing={refreshing}
          onRefresh={doRefreshData}
          ListFooterComponent={() => {
            return <View style={{ height: 20, flex: 1 }} />;
          }}
          keyExtractor={(item, index) => index.toString()}
        />
        <TouchableOpacity
          onPress={addHome}
          activeOpacity={1.0}
          style={{
            paddingVertical: 20,
            flexDirection: 'row',
            justifyContent: 'center',
            backgroundColor: Tme('cardColor'),
            alignItems: 'center',
          }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Image
              style={{ width: 12, height: 12, marginRight: 8 }}
              source={require('../../img/add-black.png')}
            />
            <Text
              style={{
                fontSize: 16,
                fontWeight: '600',
                color: Tme('cardTextColor'),
              }}>
              {I18n.t('home.add_home')}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </NavBarView>
  );
}
