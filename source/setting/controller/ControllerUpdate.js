import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import I18n from '../../I18n';
import { Helper } from '../../Helper';
import * as Progress from 'react-native-progress';
import PahoManager from '../../PahoManager';
import { Tme, Colors } from '../../ThemeStyle';
import IdleTimerManager from 'react-native-idle-timer';
import NavBarView from '../../share/NavBarView';
import CardView from '../../share/CardView';
import _ from 'lodash';
import AlertModal from '../../share/AlertModal';
import { hideLoading, showLoading } from '../../../ILoading';
import ScreenSizeContext from '../../../WindowResizeContext';
import PubSub from 'pubsub-js';
import { EVENT_GRPC_CONTROLLER_CHANGE } from '../../types/PubSubEvent';

export default class ControllerUpdate extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      progress: 0,
      indeterminate: true,
      uploading_text: false,
      current_size: 0,
      total_size: 0,
    };
    this.pahoManager = null;
  }

  static contextType = ScreenSizeContext;

  componentDidMount() {
    var that = this;
    this.doFetchData();

    PubSub.subscribe(
      EVENT_GRPC_CONTROLLER_CHANGE,
      (msg, payload) => {
        if (
          payload.queue == 'controller' &&
          payload.data.op_state == 'update_state'
        ) {
          const data = payload.data.data;
          switch (data.step) {
            case 'in_progress':
              // that.setState({
              //   uploading_text: true,
              //   indeterminate: false,
              // }, () => {
              //   AlertModal.alert(I18n.t("setting.update_failed_in_progress"));
              // });
              break;
            case 'started':
              that.setState({
                uploading_text: true,
                indeterminate: false,
              });
              break;
            case 'up_to_date':
              AlertModal.alert(I18n.t('setting.update_failed_up_to_date'), '', [
                {
                  text: I18n.t('home.cancel'),
                  onPress: () => {
                    this.props.navigation.goBack();
                  },
                },
              ]);
              break;
            case 'downloading':
              that.setState({
                uploading_text: true,
                current_size: (data.current_size / 1024 / 1024).toFixed(1),
                total_size: (data.total_size / 1024 / 1024).toFixed(1),
                progress: data.current_size / data.total_size,
              });
              break;
            case 'downloaded':
              that.setState({
                indeterminate: false,
                current_size: this.state.total_size,
                progress: 1,
              });
              break;
            case 'reboot':
              that.setState(
                {
                  indeterminate: false,
                  current_size: this.state.total_size,
                  progress: 1,
                },
                () => {
                  AlertModal.alert(I18n.t('setting.upgrade_desp'), '', [
                    {
                      text: I18n.t('home.confirm'),
                      onPress: () => {
                        this.props.navigation.goBack();
                      },
                    },
                  ]);
                },
              );
              break;
            case 'update_failed':
              AlertModal.alert(data.error, '', [
                {
                  text: I18n.t('home.confirm'),
                  onPress: () => {
                    this.props.navigation.goBack();
                  },
                },
              ]);
              break;
          }
        }
      },
    );

    IdleTimerManager.setIdleTimerDisabled(true);
  }

  componentWillUnmount() {
    if (this.pahoManager) {
      this.pahoManager.unmount();
      this.pahoManager = null;
    }
    PubSub.unsubscribe(EVENT_GRPC_CONTROLLER_CHANGE);
    IdleTimerManager.setIdleTimerDisabled(false);
  }

  onConnect() {
    Helper.httpPOST(
      '/partner/settings/upgrade',
      {
        success: () => { },
        error: () => {
          AlertModal.alert(I18n.t('setting.updated'), '', [
            {
              text: I18n.t('home.cancel'),
              onPress: () => {
                this.props.navigation.goBack();
              },
            },
          ]);
        },
      },
      { updated: 'no', sn_id: this.props.route.params.sn_id },
    );
  }

  doFetchData() {
    showLoading();
    var that = this;

    Helper.httpGET(
      Helper.urlWithQuery('/partner/controllers/conn_info', {
        sn_id: this.props.route.params.sn_id,
      }),
      {
        success: data => {
          if (_.isEmpty(data.mqtt)) {
            AlertModal.alert(
              I18n.t('home.add_device_fail'),
              '',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    this.props.navigation.goBack();
                  },
                },
              ],
              { cancelable: false },
            );
          } else {
            this.setState(
              {
                showView: true,
              },
              () => {
                this.pahoManager = new PahoManager({
                  mqtt: data.mqtt,
                  onConnect: this.onConnect.bind(this),
                  navigation: that.props.navigation,
                });
                this.pahoManager.mount();
              },
            );
          }
        },
        ensure: () => {
          hideLoading();
        },
      },
    );
  }

  render() {
    return (
      <NavBarView>
        <ScrollView style={{ flex: 1, backgroundColor: Tme('bgColor') }}>
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              marginTop: 20,
              padding: 20,
            }}>
            <CardView
              styles={{
                width: this.context.winWidth - 40,
                height: 500,
                padding: 20,
                alignItems: 'center',
                borderRadius: 8,
              }}>
              <View style={{ marginTop: 79, marginBottom: 20 }}>
                <Text
                  style={{
                    fontSize: 22,
                    fontWeight: '600',
                    textAlign: 'center',
                    color: Tme('cardTextColor'),
                  }}>
                  {I18n.t('setting.update_online')}
                </Text>
                <View style={{ padding: 16 }}>
                  <Text
                    style={{
                      fontSize: 14,
                      fontWeight: '600',
                      textAlign: 'center',
                      color: Tme('cardTextColor'),
                    }}>
                    {this.state.uploading_text &&
                      `${I18n.t('setting.new_update_version')} ${this.props.route.params.version
                      }, ${this.state.current_size}MB/${this.state.total_size
                      }MB`}
                  </Text>
                </View>
              </View>
              <Progress.Circle
                size={140}
                progress={this.state.progress}
                indeterminate={this.state.indeterminate}
                showsText={true}
                color={Colors.MainColor}
                formatText={() => {
                  return `${parseInt(this.state.progress * 100, 10)}%`;
                }}
              />
            </CardView>
          </View>
        </ScrollView>
      </NavBarView>
    );
  }
}
