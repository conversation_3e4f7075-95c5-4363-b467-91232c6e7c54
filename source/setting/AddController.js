/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import I18n from '../I18n';
import { Tme, Colors } from '../ThemeStyle';
import Permissions from '../Permissions';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { Helper } from '../Helper';
import { hideLoading, showLoading } from '../../ILoading';
import PubSub from 'pubsub-js';
import { EVENT_ADD_CONTROLLER_SCAN } from '../types/PubSubEvent';

export default class AddController extends Component {
  constructor(props) {
    super(props);

    this.state = {
      sn: '',
    };

    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.next')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this._save();
  }

  componentDidMount() {
    setTimeout(() => {
      this.textInput.focus();
    }, 600);

    PubSub.subscribe(EVENT_ADD_CONTROLLER_SCAN, (msg, sn) => {
      this.setState({ sn });
    });

  }

  componentWillUnmount() {
    PubSub.unsubscribe(EVENT_ADD_CONTROLLER_SCAN);
  }

  camera() {
    Permissions.CameraPermission(() => {
      this.props.navigation.push('ScanCodeScreen', {
        title: I18n.t('home.scan'),
      });
    });
  }

  render() {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: Tme('bgColor') }}>
        <ScrollView
          keyboardShouldPersistTaps="never"
          keyboardDismissMode="on-drag"
          showsVerticalScrollIndicator={false}>
          <View style={{ paddingHorizontal: 20 }}>
            <View style={{ height: 40, backgroundColor: Tme('bgColor') }}>
              <Text
                style={{
                  lineHeight: 40,
                  fontSize: 14,
                  color: Tme('cardTextColor'),
                }}>
                {this.props.route.params.type === 'nb-iot'
                  ? I18n.t('home.nb_sn_enter')
                  : I18n.t('home.controller_sn_enter')}
              </Text>
            </View>
          </View>
          <View
            style={{
              padding: 20,
              backgroundColor: Tme('cardColor'),
            }}>
            <View
              style={[
                styles.account_view,
                { borderColor: Tme('inputBorderColor') },
              ]}>
              <TextInput
                clearButtonMode="always"
                ref={ref => {
                  this.textInput = ref;
                }}
                returnKeyType="go"
                autoCapitalize="characters"
                placeholder={I18n.t('home.sn')}
                underlineColorAndroid="transparent"
                placeholderTextColor={Tme('placeholder')}
                autoCorrect={false}
                value={this.state.sn}
                onChangeText={this.changeSn.bind(this)}
                onSubmitEditing={this._save.bind(this)}
                style={Colors.TextInputStyle()}
              />
            </View>
            <TouchableOpacity
              onPress={this.camera.bind(this)}
              activeOpacity={0.8}
              style={{
                marginTop: 20,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text
                style={{
                  color: Colors.MainColor,
                }}>
                {I18n.t('home.scan')}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }
  changeSn(value) {
    this.setState({
      sn: value.toUpperCase(),
    });
  }

  _save() {
    var that = this;
    if (this.state.sn !== '') {
      that.pushSendSn(that.state.sn);
    } else {
      if (this.props.route.params.type === 'nb-iot') {
        AlertModal.alert(I18n.t('home.nb_sn_enter'));
      } else {
        AlertModal.alert(I18n.t('home.controller_sn_enter'));
      }
    }
  }

  pushSendSn(sn) {
    if (this.props.route.params.type === 'nb-iot') {
      showLoading();
      Helper.httpPOST(
        '/nb_nodes/add',
        {
          ensure: () => {
            hideLoading();
          },
          success: data => {
            // AlertModal.alert(
            //   I18n.t('home.add_success'),
            //   I18n.t('nb.add_device_success'),
            //   [
            //     {
            //       text: I18n.t('home.confirm'),
            //       onPress: () => {
            //         this.props.navigation.pop();
            //       },
            //     },
            //   ],
            // );
            this.props.navigation.push('AddSuccess', {
              uuid: data.sn.sn,
              sn_id: '',
              type: 'new',
            });
          },
        },
        {
          sn: sn,
        },
      );
    } else {
      this.props.navigation.replace('SendSn', {
        sn: sn,
        type: 'wifi',
      });
    }
  }
}
const styles = StyleSheet.create({
  account_view: {
    padding: 3,
    marginTop: 10,
    borderWidth: 1,
    borderRadius: 3,
  },
});
