import React, {Component} from 'react';
import WebViewWithProgress from '../WebViewWithProgress';
import I18n from '../I18n';
import {NotificationCenter, ALEXA_CALLBACK_EVENT} from '../NotificationCenter';
import NavBarView from '../share/NavBarView';
import AlertModal from '../share/AlertModal';

export default class AlexaLoginWithAmazonScreen extends Component {
  constructor(props) {
    super(props);
  }

  render() {
    return (
      <NavBarView>
        <WebViewWithProgress
          withoutDefaultHeaders={false}
          source={{uri: this.props.route.params.url}}
          onMessage={this._message.bind(this)}
        />
      </NavBarView>
    );
  }

  _message(event) {
    var data = event.nativeEvent.data;
    if (data !== '[object Object]') {
      var temp = data.split(',');
      if (temp[0] == 'error') {
        this.props.navigation.goBack();
        AlertModal.alert(I18n.t('global.alexa_link_error'));
      } else {
        setTimeout(() => {
          this.props.navigation.goBack();
          NotificationCenter.dispatchEvent(ALEXA_CALLBACK_EVENT, {
            state: temp[1],
            code: temp[2],
          });
        }, 500);
      }
    }
  }
}
