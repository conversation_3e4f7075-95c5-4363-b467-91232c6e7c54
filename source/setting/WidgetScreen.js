import React from 'react';
import {
  Image,
  SafeAreaView,
  SectionList,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { Tme } from '../ThemeStyle';
import { getImageFromKey, getSceneName, mainRadius } from '../Tools';
import { Helper } from '../Helper';
import I18n from '../I18n';
import _ from 'lodash';
import { Toast } from '../Toast';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { hideLoading, showLoading } from '../../ILoading';
import { SELECT_WIDGET_DATA } from '../types/PubSubEvent';
import PubSub from 'pubsub-js';
export default class WidgetScreen extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      widgets: [],
      refreshing: false,
      data: [],
    };
    this.props.navigation.setOptions({
      // eslint-disable-next-line react/no-unstable-nested-components
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this._save();
  }

  componentDidMount() {
    this.doFetchData();
  }

  doRefreshData() {
    this.doFetchData('refresh');
  }

  doFetchData(type) {
    var that = this;
    if (type === 'refresh') {
      that.setState({
        refreshing: true,
      });
    } else {
      showLoading();
    }
    Helper.httpGET('/partner/settings/actions_scenes', {
      success: data => {
        const temp = [
          { title: 'scene', data: data.scenes },
          { title: 'action', data: data.actions },
        ];
        that.setState({
          data: temp,
          widgets: data.widget_data,
        });
      },
      ensure: () => {
        that.setState({
          showBtn: true,
        });
        if (type == 'refresh') {
          that.setState({
            refreshing: false,
          });
        } else {
          hideLoading();
        }
      },
    });
  }

  render() {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: Tme('bgColor') }}>
        <View style={{ paddingHorizontal: 20, paddingTop: 20 }}>
          <Text style={{ color: Tme('cardTextColor'), fontSize: 12 }}>
            {I18n.t('setting.widget_desp')}
          </Text>
        </View>
        <SectionList
          style={{ flex: 1 }}
          sections={this.state.data}
          showsVerticalScrollIndicator={false}
          onEndReachedThreshold={0.1}
          refreshing={this.state.refreshing}
          onRefresh={this.doRefreshData.bind(this)}
          keyExtractor={(item, index) => item + index}
          renderItem={({ item }) => (
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.clickBox.bind(this, item)}
              style={{
                backgroundColor: Tme('cardColor'),
                padding: 20,
                marginBottom: 10,
                borderRadius: mainRadius(),
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Image
                  source={getImageFromKey(item.icon).value}
                  style={{ width: 25, height: 25, marginRight: 10 }}
                />
                <Text style={{ color: Tme('cardTextColor') }}>
                  {item.widget_type === 'scene'
                    ? getSceneName(item.name)
                    : item.name}
                </Text>
              </View>
              <View>
                {_.findIndex(this.state.widgets, d => d.uuid === item.uuid) ===
                  -1 ? (
                  <Image
                    source={require('../../img/Checkbox.png')}
                    style={{ width: 20, height: 20 }}
                  />
                ) : (
                  <Image
                    source={require('../../img/checkbox-checked.png')}
                    style={{ width: 20, height: 20 }}
                  />
                )}
              </View>
            </TouchableOpacity>
          )}
          renderSectionHeader={({ section: { title } }) => (
            <View
              style={{
                backgroundColor: Tme('bgColor'),
                paddingTop: 20,
                paddingBottom: 10,
              }}>
              <Text
                style={{
                  fontSize: 12,
                  color: Tme('smallTextColor'),
                  paddingHorizontal: 20,
                }}>
                {I18n.t('global.' + title)}
              </Text>
            </View>
          )}
        />
      </SafeAreaView>
    );
  }

  clickBox(item) {
    const index = _.findIndex(this.state.widgets, d => d.uuid === item.uuid);
    const temp = _.cloneDeep(this.state.widgets);
    if (index < 0) {
      if (temp.length < 4) {
        temp.push({
          uuid: item.uuid,
          type: item.widget_type,
        });
        this.setState({
          widgets: temp,
        });
      }
    } else {
      _.remove(temp, t => t.uuid === item.uuid);
      this.setState({
        widgets: temp,
      });
    }
  }

  _save() {
    showLoading();

    Helper.httpPOST(
      '/partner/settings/widget_data',
      {
        ensure: () => {
          hideLoading();
        },
        success: data => {
          PubSub.publish(SELECT_WIDGET_DATA);
          this.props.navigation.goBack();
          Toast.show();
        },
      },
      { widget_data: this.state.widgets },
    );
  }

  handleSuccess() {
    Toast.show();
  }
}
