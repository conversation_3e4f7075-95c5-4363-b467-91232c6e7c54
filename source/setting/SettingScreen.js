import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Platform,
  Switch,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import BackgroundGeolocation from 'react-native-background-geolocation';
import { Helper, HelperMemo } from '../Helper';
import I18n from '../I18n';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Tme, Colors, IsDark } from '../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Icon from 'react-native-vector-icons/Ionicons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import ActionSheet from '@alessiocancian/react-native-actionsheet';
import { isOwner } from '../Router';
import { mainRadius } from '../Tools';
import SharedGroupPreferences from 'react-native-shared-group-preferences';
import AlertModal from '../share/AlertModal';
import RNWidgetCenter from 'react-native-widget-center';
import PubSub from 'pubsub-js';
import { EVENT_GUARD, PubSubEvent } from '../types/PubSubEvent';
const appGroupIdentifier = 'group.com.presen.WidgetPresen';

class SettingScreen extends Component {
  constructor(props) {
    super(props);

    this.state = {
      isDataLoaded: false,
      showBtn: false,
      rooms: '',
      devices: '',
      switchState: null,
      isRefreshing: false,
      is_guard_mode: false,
      loading: false,
      theme: '',
    };
    this.fatch = true;
    this.actionSheet;

    this.focusListener = this.props.navigation.addListener('focus', () => {
      this.doFetchData();
    });
  }

  componentDidMount() {
    var that = this;

    PubSub.subscribe(EVENT_GUARD, (msg, data) => {
      that.doFetchData();
    });

    AsyncStorage.getItem('theme', (error, result) => {
      if (error) {
        console.error(error);
      } else {
        if (result !== null) {
          this.setState({
            theme: JSON.parse(result),
          });
        } else {
          this.setState({
            theme: 'system_with',
          });
        }
      }
    });
  }

  componentWillUnmount() {
    if (this.focusListener) {
      this.focusListener();
    }
    PubSub.unsubscribe(EVENT_GUARD);
  }

  doFetchData() {
    var that = this;
    Helper.httpGET('/home/<USER>', {
      success: data => {
        that.setState({
          is_guard_mode: data.is_guard_mode,
        });
      },
      ensure: () => {
      },
    });
  }

  order() {
    this.props.navigation.push('OrderScreen', {
      title: I18n.t('pay.order'),
    });
  }

  settingCard() {
    this.props.navigation.push('SettingCard', {
      title: I18n.t('pay.pay_info'),
    });
  }

  render() {
    return (
      <SafeAreaView
        style={[
          {
            flex: 1,
            backgroundColor: Tme('bgColor'),
          },
        ]}>
        {Platform.OS === 'android' && (
          <View style={{ height: HelperMemo.STATUS_BAR_HEIGHT }} />
        )}
        <View
          style={{
            height: HelperMemo.NAV_BAR_HEIGHT,
            paddingHorizontal: 16,
            backgroundColor: Tme('bgColor'),
            justifyContent: 'center',
          }}>
          <View
            style={{
              justifyContent: 'center',
            }}>
            <Text
              style={{
                color: Tme('cardTextColor'),
                fontWeight: 'bold',
                fontSize: 20,
              }}>
              {I18n.t('home.setting').toUpperCase()}
            </Text>
          </View>
        </View>
        <View
          style={{
            flex: 1,
          }}>
          <ScrollView showsVerticalScrollIndicator={false} style={{ flex: 1 }}>
            {this.state.loading &&
              <ActivityIndicator
                size="large"
                color="#eeeeee"
                animating={this.state.loading}
              />
            }
            <View
              style={{
                marginTop: 20,
                borderTopRightRadius: mainRadius(),
                borderTopLeftRadius: mainRadius(),
                backgroundColor: Tme('cardColor'),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <MCIcons
                    name="shield-home"
                    size={20}
                    color={Tme('textColor')}
                  />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('guard.guard_mode')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'flex-end',
                }}>
                <Switch
                  style={{ transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }] }}
                  thumbColor="#fff"
                  trackColor={{ true: Colors.MainColor }}
                  value={this.state.is_guard_mode}
                  onValueChange={this.sheetShow.bind(this)}
                />
              </View>
            </View>
            <View style={{ height: 2 }} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.dashboard.bind(this)}
              style={{
                backgroundColor: Tme('cardColor'),
                borderBottomLeftRadius: mainRadius(),
                borderBottomRightRadius: mainRadius(),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <MaterialIcons
                    name="dashboard"
                    size={20}
                    color={Tme('textColor')}
                  />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('global.dashboard')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>
            <View style={{ height: 20 }} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => {
                this.props.navigation.push('ProductScreen', {
                  title: I18n.t('pay.charge'),
                });
              }}
              style={{
                borderTopRightRadius: mainRadius(),
                borderTopLeftRadius: mainRadius(),
                backgroundColor: Tme('cardColor'),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <MaterialIcons
                    name="cloud-upload"
                    size={20}
                    color={Tme('textColor')}
                  />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('pay.charge')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>

            {/* <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.settingCard.bind(this)}
              style={{
                backgroundColor: Tme('cardColor'),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <Icon name="card" size={20} color={Tme('textColor')} />
                </IconView>
                <Text style={{marginLeft: 10, color: Tme('cardTextColor')}}>
                  {I18n.t('pay.pay_info')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity> */}
            <View style={{ height: 2 }} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.order.bind(this)}
              style={{
                backgroundColor: Tme('cardColor'),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
                borderBottomRightRadius: mainRadius(),
                borderBottomLeftRadius: mainRadius(),
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <MaterialIcons
                    name="format-list-bulleted"
                    size={20}
                    color={Tme('textColor')}
                  />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('pay.order')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>
            <View style={{ height: 20 }} />
            {Helper.isUsingLocalNetwork() ? null : (
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={this.click.bind(this, 'notify')}
                style={{
                  backgroundColor: Tme('cardColor'),
                  borderTopRightRadius: mainRadius(),
                  borderTopLeftRadius: mainRadius(),
                  paddingHorizontal: 20,
                  height: 54,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    flex: 1,
                  }}>
                  <IconView>
                    <MaterialIcons
                      name="notifications"
                      size={20}
                      color={Tme('textColor')}
                    />
                  </IconView>
                  <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                    {I18n.t('setting.inform')}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={20}
                    color={Tme('textColor')}
                  />
                </View>
              </TouchableOpacity>
            )}
            <View style={{ height: 2 }} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.click.bind(this, 'event')}
              style={{
                backgroundColor: Tme('cardColor'),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <MaterialIcons
                    name="event"
                    size={20}
                    color={Tme('textColor')}
                  />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('home.event')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>
            <View style={{ height: 2 }} />
            {isOwner() ? (
              <View>
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={this.click.bind(this, 'voice')}
                  style={{
                    backgroundColor: Tme('cardColor'),
                    paddingHorizontal: 20,
                    height: 54,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      flex: 1,
                    }}>
                    <IconView>
                      <FontAwesome
                        name="microphone"
                        size={20}
                        color={Tme('textColor')}
                      />
                    </IconView>
                    <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                      {I18n.t('global.service')}
                    </Text>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <MaterialIcons
                      name="keyboard-arrow-right"
                      size={20}
                      color={Tme('textColor')}
                    />
                  </View>
                </TouchableOpacity>
                <View style={{ height: 2 }} />
              </View>
            ) : null}
            {Helper.isUsingLocalNetwork() ? null : (
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={this.click.bind(this, 'change_home')}
                style={{
                  backgroundColor: Tme('cardColor'),
                  borderBottomRightRadius: mainRadius(),
                  borderBottomLeftRadius: mainRadius(),
                  paddingHorizontal: 20,
                  height: 54,
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    flex: 1,
                  }}>
                  <IconView>
                    <MaterialIcons
                      name="router"
                      size={20}
                      color={Tme('textColor')}
                    />
                  </IconView>
                  <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                    {I18n.t('setting.home_setting')}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={20}
                    color={Tme('textColor')}
                  />
                </View>
              </TouchableOpacity>
            )}

            <View style={{ height: 20 }} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.click.bind(this, 'profile')}
              style={{
                backgroundColor: Tme('cardColor'),
                borderTopRightRadius: mainRadius(),
                borderTopLeftRadius: mainRadius(),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <FontAwesome name="edit" size={20} color={Tme('textColor')} />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('home.profile')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>
            <View style={{ height: 2 }} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.click.bind(this, 'language')}
              style={{
                backgroundColor: Tme('cardColor'),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <FontAwesome
                    name="language"
                    size={20}
                    color={Tme('textColor')}
                  />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('home.language_for_mobile')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>
            <View style={{ height: 2 }} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.click.bind(this, 'image')}
              style={{
                backgroundColor: Tme('cardColor'),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <MCIcons name="image" size={20} color={Tme('textColor')} />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('setting.background_image')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>
            <View style={{ height: 2 }} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.clickTheme.bind(this)}
              style={{
                backgroundColor: Tme('cardColor'),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <Icon
                    name="color-palette-outline"
                    size={20}
                    color={Tme('textColor')}
                  />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('global.theme')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text style={{ color: Tme('cardTextColor') }}>
                  {I18n.t('global.' + this.state.theme)}
                </Text>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>
            <View style={{ height: 2 }} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.click.bind(this, 'widget')}
              style={{
                backgroundColor: Tme('cardColor'),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <MaterialIcons
                    name="widgets"
                    size={20}
                    color={Tme('textColor')}
                  />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('setting.widget_setting')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>
            <View style={{ height: 2 }} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.click.bind(this, 'about')}
              style={{
                backgroundColor: Tme('cardColor'),
                borderBottomRightRadius: mainRadius(),
                borderBottomLeftRadius: mainRadius(),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <MaterialIcons
                    name="info-outline"
                    size={20}
                    color={Tme('textColor')}
                  />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('index.about_about')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>

            <View style={{ height: 20 }} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.logout.bind(this)}
              style={{
                backgroundColor: Tme('cardColor'),
                borderRadius: mainRadius(),
                paddingHorizontal: 20,
                height: 54,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Text style={{ color: Colors.MainColor }}>
                {I18n.t('home.sign_out')}
              </Text>
            </TouchableOpacity>
            <View style={{ height: 20 }} />
          </ScrollView>
        </View>
        <ActionSheet
          ref={o => (this.actionSheet = o)}
          userInterfaceStyle={IsDark() ? 'dark' : 'light'}
          options={[
            I18n.t('home.cancel'),
            I18n.t('home.edit'),
            I18n.t('guard.exit_guard_mode'),
          ]}
          cancelButtonIndex={0}
          destructiveButtonIndex={2}
          theme="ios"
          styles={{
            cancelButtonBox: {
              height: 50,
              marginTop: 6,
              alignItems: 'center',
              justifyContent: 'center',
            },
            buttonBox: {
              height: 50,
              alignItems: 'center',
              justifyContent: 'center',
            },
          }}
          onPress={index => {
            this.sheetClick(index - 1);
          }}
        />
      </SafeAreaView>
    );
  }

  sheetShow() {
    if (this.state.is_guard_mode) {
      this.actionSheet.show();
    } else {
      this.props.navigation.push('GuardModeScreen');
    }
  }

  sheetClick(index) {
    var that = this;
    if (index === 0) {
      this._setting();
    }
    if (index == 1) {
      AlertModal.alert(
        I18n.t('guard.guard_mode'),
        I18n.t('guard.exit_guard_mode'),
        [
          { text: 'Cancel', onPress: () => { } },
          {
            text: 'OK',
            onPress: () => {
              Helper.httpPOST('/guards/delete_guard', {
                success: data => {
                  that.setState({
                    is_guard_mode: data.is_guard_mode,
                  });
                },
                ensure: () => { },
              });
            },
          },
        ],
        { cancelable: false },
      );
    }
  }

  _setting() {
    this.props.navigation.push('GuardModeView', {
      type: 'edit',
      title: I18n.t('guard.guard_mode'),
    });
  }

  async saveUserDataToSharedStorage(data) {
    try {
      await SharedGroupPreferences.setItem(
        'SceneData',
        data,
        appGroupIdentifier,
      );

      RNWidgetCenter.reloadTimelines('WidgetPresen');
    } catch (errorCode) {
      // errorCode 0 = There is no suite with that name
      console.log(errorCode);
    }
  }

  logout() {
    AlertModal.alert(I18n.t('home.sign_out'), '', [
      {
        text: I18n.t('home.cancel'),
        onPress: () => { },
      },
      {
        text: I18n.t('home.confirm'),
        onPress: () => {
          this.setState({ loading: true }, () => {
            Helper.httpPOST(
              '/users/signout',
              {
                ensure: () => {
                  this.setState({ loading: false });
                },
                success: data => {
                  BackgroundGeolocation.stop();
                  BackgroundGeolocation.removeGeofences().then(success => {
                    console.log(
                      '[removeGeofences] all geofences have been destroyed',
                    );
                  });
                  PubSub.publish(PubSubEvent.LOGOUT_WIDGET);
                  this.saveUserDataToSharedStorage({ data: [] });
                  AsyncStorage.removeItem('user_data', () => {
                    HelperMemo.user_data = null;
                    PubSub.publish(PubSubEvent.RESTART_APP);
                  });
                },
              },
              {
                user_id: HelperMemo.user_data.user.id,
                device_type: Platform.OS,
                device_token_id: HelperMemo.token_id,
              },
            );
          });
        },
      },
    ]);
  }

  addUser() {
    this.props.navigation.push('userScreen', {
      title: I18n.t('home.user'),
    });
  }

  clickTheme() {
    this.props.navigation.push('ThemeScreen', {
      theme: this.state.theme,
      title: I18n.t('global.theme'),
    });
  }

  click(name) {
    var title, screen;

    if (Helper.isUsingLocalNetwork()) {
      if (name == 'notify') {
        AlertModal.alert(I18n.t('global.cloud_required'));
        return;
      }
    }

    switch (name) {
      case 'notify':
        title = I18n.t('setting.inform');
        screen = 'notifyScreen';
        break;
      case 'event':
        title = I18n.t('home.event');
        screen = 'eventScreen';
        break;
      case 'voice':
        title = I18n.t('global.service');
        screen = 'VoiceScreen';
        // screen = "AlexaScreen"
        break;
      case 'change_home':
        title = I18n.t('setting.home_setting');
        screen = 'ChangeHome';
        break;
      case 'profile':
        title = I18n.t('home.profile');
        screen = 'ProfileList';
        break;
      case 'language':
        title = I18n.t('home.language_for_mobile');
        screen = 'langScreen';
        break;
      case 'image':
        title = I18n.t('setting.background_image');
        screen = 'BackgroundImage';
        break;
      case 'about':
        title = I18n.t('index.about_about');
        screen = 'AboutScreen';
        break;
      case 'widget':
        title = I18n.t('setting.widget_setting');
        screen = 'WidgetScreen';
        break;
    }
    if (screen == 'ProfileList') {
      if (HelperMemo.user_data.is_demo) {
        return;
      }
    }
    this.props.navigation.push(screen, {
      title: title,
    });
  }

  dashboard() {
    this.props.navigation.push('ControllerList');
  }
}
export default SettingScreen;

export class IconView extends React.Component {
  constructor(props) {
    super(props);
  }

  render() {
    return (
      <View
        style={{
          height: 24,
          width: 24,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        {this.props.children}
      </View>
    );
  }
}
