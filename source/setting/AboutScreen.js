import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import I18n from '../I18n';
import DeviceInfo from 'react-native-device-info';
import { Tme } from '../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import NavBarView from '../share/NavBarView';

export default function AboutScreen({ route, navigation }) {

  const click = name => {
    var title = '';
    var screen = '';
    switch (name) {
      case 'open':
        title = I18n.t('global.open_license');
        screen = 'OpenLicense';
        break;
      case 'feedback':
        title = I18n.t('home.feedback');
        screen = 'FeedbackScreen';
        break;
      default:
        break;
    }
    navigation.push(screen, {
      title: title,
    });
  };

  return (
    <NavBarView>
      <View
        style={{
          marginTop: 20,
          paddingBottom: 20,
        }}>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={() => click('open')}
          style={{ backgroundColor: Tme('cardColor') }}>
          <View
            style={{
              marginHorizontal: 20,
              flexDirection: 'row',
              paddingVertical: 16,
              justifyContent: 'space-between',
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text style={{ color: Tme('cardTextColor') }}>
                {I18n.t('global.open_license')}
              </Text>
            </View>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <MaterialIcons
                name="keyboard-arrow-right"
                size={20}
                color={Tme('textColor')}
              />
            </View>
          </View>
        </TouchableOpacity>
        <View style={{ height: 20 }} />
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={() => click('feedback')}
          style={{ backgroundColor: Tme('cardColor') }}>
          <View
            style={{
              marginHorizontal: 20,
              flexDirection: 'row',
              paddingVertical: 16,
              justifyContent: 'space-between',
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text style={{ color: Tme('cardTextColor') }}>
                {I18n.t('home.feedback')}
              </Text>
            </View>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <MaterialIcons
                name="keyboard-arrow-right"
                size={20}
                color={Tme('textColor')}
              />
            </View>
          </View>
        </TouchableOpacity>
        <View
          style={{
            marginVertical: 20,
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
          }}>
          <Text style={{ color: '#65758c', fontSize: 12 }}>
            v{DeviceInfo.getVersion()}
          </Text>
          <Text style={{ color: '#65758c', fontSize: 12, marginLeft: 10 }} />
        </View>
      </View>
    </NavBarView>
  );
}
