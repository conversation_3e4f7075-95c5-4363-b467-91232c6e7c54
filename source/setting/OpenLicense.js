import React, {Component} from 'react';
import {View, FlatList, Text, StyleSheet} from 'react-native';
import {Tme} from '../ThemeStyle';
import Data from '../../licenses.json';
import EmptyView from '../share/EmptyView';
import NavBarView from '../share/NavBarView';

function extractNameFromGithubUrl(url) {
  if (!url) {
    return null;
  }

  const reg =
    /((https?:\/\/)?(www\.)?github\.com\/)?(@|#!\/)?([A-Za-z0-9_]{1,15})(\/([-a-z]{1,20}))?/i;
  const components = reg.exec(url);

  if (components && components.length > 5) {
    return components[5];
  }
  return null;
}

function sortDataByKey(data, key) {
  data.sort(function (a, b) {
    return a[key] > b[key] ? 1 : b[key] > a[key] ? -1 : 0;
  });
  return data;
}

function capitalizeFirstLetter(string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

let licenses = Object.keys(Data).map(key => {
  // eslint-disable-next-line no-shadow
  let {licenses, ...license} = Data[key];
  let [name, version] = key.split('@');

  let username =
    extractNameFromGithubUrl(license.repository) ||
    extractNameFromGithubUrl(license.licenseUrl);

  let userUrl;
  let image;
  if (username) {
    username = capitalizeFirstLetter(username);
    image = `http://github.com/${username}.png`;
    userUrl = `http://github.com/${username}`;
  }

  return {
    key,
    name,
    image,
    userUrl,
    username,
    licenses: licenses.slice(0, 405),
    version,
    ...license,
  };
});

sortDataByKey(licenses, 'username');

export default class OpenLicense extends Component {
  renderItem = ({item}) => <LicensesListItem {...item} />;

  constructor(props) {
    super(props);
  }
  render() {
    return (
      <NavBarView>
        <FlatList
          showsVerticalScrollIndicator={false}
          style={{flex: 1, backgroundColor: Tme('bgColor')}}
          keyExtractor={(item, index) => index.toString()}
          data={licenses}
          ListEmptyComponent={() => <EmptyView />}
          renderItem={this.renderItem}
        />
      </NavBarView>
    );
  }
}
class LicensesListItem extends Component {
  render() {
    const {
      username,
      name,
      version,
      // eslint-disable-next-line no-shadow
      licenses,
      licenseUrl,
    } = this.props;

    let title = name;
    if (username) {
      if (title.toLowerCase() != username.toLowerCase()) {
        title += ` by ${username}`;
      }
    }

    return (
      <View
        style={{
          marginTop: 2,
        }}>
        <View style={{backgroundColor: Tme('cardColor')}}>
          <View style={styles.item}>
            <View>
              <Text
                style={{
                  color: Tme('cardTextColor'),
                  fontWeight: 'bold',
                  fontSize: 16,
                }}>
                {title}
              </Text>
            </View>
            <View
              style={{
                flexDirection: 'row',
              }}>
              <Link
                style={{
                  color: Tme('cardTextColor'),
                  marginTop: 3,
                  marginRight: 8,
                }}
                url={licenseUrl}>
                {licenses}
              </Link>
              <Link
                style={{
                  color: Tme('cardTextColor'),
                  marginTop: 3,
                }}>
                {version}
              </Link>
            </View>
          </View>
        </View>
      </View>
    );
  }
}

const Link = ({url, style, children}) => (
  <Text style={style} numberOfLines={1}>
    {children}
  </Text>
);

const styles = StyleSheet.create({
  item: {
    paddingVertical: 16,
    paddingHorizontal: 12,
    flex: 1,
    backgroundColor: 'transparent',
    maxWidth: '100%',
    flexWrap: 'wrap',
  },
});
