import React, {Component} from 'react';
import I18n from '../I18n';
import {View, Text, TouchableOpacity, ScrollView} from 'react-native';
import {Tme, Colors} from '../ThemeStyle';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import NavBarView from '../share/NavBarView';
export default class GuardModeScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      routine: {},
      room: [],
      devices: [],
    };
  }

  render() {
    return (
      <NavBarView>
        <ScrollView>
          <View style={{justifyContent: 'center', alignItems: 'center'}}>
            <MCIcons size={200} color={Tme('textColor')} name="shield-home" />
          </View>
          <View
            style={{
              justifyContent: 'space-between',
              flex: 1,
            }}>
            <View style={{padding: 16}}>
              <Text
                style={{
                  color: Tme('textColor'),
                  fontSize: 22,
                  fontWeight: '500',
                  marginBottom: 16,
                }}>
                {I18n.t('guard.guard_h1')}
              </Text>
              <Text
                style={{
                  color: Tme('cardTextColor'),
                  fontSize: 14,
                  marginBottom: 8,
                  fontWeight: '500',
                }}>
                {I18n.t('guard.guard_h2')}
              </Text>
              <Text
                style={{
                  color: Tme('textColor'),
                  fontSize: 14,
                  marginBottom: 16,
                }}>
                {I18n.t('guard.guard_h2_deps')}
              </Text>
              <Text
                style={{
                  color: Tme('cardTextColor'),
                  fontSize: 14,
                  marginBottom: 8,
                  fontWeight: '500',
                }}>
                {I18n.t('guard.guard_h3')}
              </Text>
              <Text
                style={{
                  color: Tme('textColor'),
                  fontSize: 14,
                  marginBottom: 16,
                }}>
                {I18n.t('guard.guard_h3_deps')}
              </Text>
            </View>
            <View style={{marginBottom: 32, padding: 16}}>
              <TouchableOpacity
                onPress={this.click.bind(this)}
                activeOpacity={0.8}
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 40,
                  backgroundColor: Colors.MainColor,
                  borderRadius: 4,
                }}>
                <Text style={{color: '#ffffff', fontSize: 14}}>
                  {I18n.t('guard.enter_guard_mode')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </NavBarView>
    );
  }

  click() {
    this.props.navigation.push('GuardModeView', {
      title: I18n.t('guard.guard_mode'),
    });
  }
}
