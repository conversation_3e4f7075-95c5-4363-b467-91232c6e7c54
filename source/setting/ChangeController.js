/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import { View, Text, TouchableOpacity, FlatList, Image } from 'react-native';
import I18n from '../I18n';
import { Tme, Colors } from '../ThemeStyle';
import { Helper<PERSON>emo, Helper } from '../Helper';
import NavBarView from '../share/NavBarView';
import EmptyView from '../share/EmptyView';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import {
  NotificationCenter,
  CLOSE_USER,
} from '../NotificationCenter';
import PubSub from 'pubsub-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import _ from 'lodash';
import { mainRadius } from '../Tools';
import AlertModal from '../share/AlertModal';
import { hideLoading, showLoading } from '../../ILoading';
import { PubSubEvent } from '../types/PubSubEvent';

export default class ChangeController extends Component {
  constructor(props) {
    super(props);

    this.state = {
      clickRow: '',
      sns: null,
      name: this.props.route.params.name,
      refreshing: false,
      deleteConfirmation: '',
      showHeader: false,
    };
    this.actionSheet = null;
  }

  componentDidMount() {
    this.doFetchData();
    NotificationCenter.addObserver(this, CLOSE_USER, data => {
      this.onSave(data);
    });
  }

  componentWillUnmount() {
    NotificationCenter.removeObserver(this, CLOSE_USER);
  }

  doFetchData() {
    var that = this;
    showLoading();
    Helper.httpGET(
      Helper.urlWithQuery('/home/<USER>', {
        home_id: this.props.route.params.home_id,
      }),
      {
        cloud: true,
        success: data => {
          that.setState({
            sns: data.sn,
            name: data.home_name,
            refreshing: false,
            showHeader: true,
          });
        },
        ensure: () => {
          hideLoading();
        },
      },
    );
  }

  doRefreshData() {
    this.setState(
      {
        refreshing: true,
      },
      () => {
        this.doFetchData();
      },
    );
  }

  selectHome() {
    Helper.httpPOST(
      '/home/<USER>',
      {
        cloud: true,
        success: data => {
          var sn_data = {
            home_id: data.home_id,
            role: data.user.role,
            user: data.user.user,
            sn: data.sn,
          };
          AsyncStorage.mergeItem('user_data', JSON.stringify(sn_data), () => {
            HelperMemo.user_data.home_id = data.user.home_id;
            HelperMemo.user_data.role = data.user.role;
            HelperMemo.user_data.user = data.user.user;
            HelperMemo.user_data.sn = data.sn;
          });
          PubSub.publish(PubSubEvent.RESTART_APP);
        },
      },
      { home_id: this.props.route.params.home_id },
    );
  }

  addUser() {
    this.props.navigation.push('userScreen', {
      title: I18n.t('home.user'),
    });
  }

  reset() {
    AlertModal.alert(
      I18n.t('setting.reset_home'),
      I18n.t('setting.reset_home_desp'),
      [
        {
          text: I18n.t('home.cancel'),
          onPress: () => { },
        },
        {
          text: I18n.t('home.confirm'),
          onPress: () => {
            this.deleteUser();
          },
        },
      ],
    );
  }

  deleteUser() {
    this.props.navigation.push('InputModal', {
      title: I18n.t('global.delete_desp'),
      from: 'default',
      inputValue: '',
    });
  }

  onSave(value) {
    if (value.toLocaleUpperCase() === 'YES') {
      showLoading();
      const that = this;
      Helper.httpPOST(
        '/partner/settings/set_factory_default',
        {
          ensure: () => {
            hideLoading();
          },
          success: data => {
            AlertModal.alert(I18n.t('home.successful'), data, [
              {
                text: I18n.t('home.confirm'),
                onPress: () => {
                  that.onClose();
                  setTimeout(() => {
                    PubSub.publish(PubSubEvent.RESTART_APP);
                  }, 500);
                },
              },
            ]);
          },
        },
        { home_id: this.props.home_id },
      );
    } else {
      AlertModal.alert(I18n.t('global.delete_desp'));
    }
  }

  click(name) {
    var title, screen;
    switch (name) {
      case 'name':
        title = I18n.t('home.edit_home');
        screen = 'AddHome';
        break;
    }

    this.props.navigation.push(screen, {
      name: this.state.name,
      home_id: this.props.route.params.home_id,
      title: title,
    });
  }

  header() {
    if (this.state.showHeader) {
      return (
        <>
          <View
            style={{
              backgroundColor: Tme('cardColor'),
              borderTopRightRadius: mainRadius(),
              borderTopLeftRadius: mainRadius(),
            }}>
            <View
              style={{
                paddingHorizontal: 20,
                paddingVertical: 16,
              }}>
              <Text
                style={{
                  color: Tme('smallTextColor'),
                }}>
                {this.state.name}
              </Text>
            </View>
          </View>
          <View style={{ height: 2 }} />
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.click.bind(this, 'name')}
            style={{
              backgroundColor: Tme('cardColor'),
            }}>
            <View
              style={{
                paddingHorizontal: 20,
                paddingVertical: 16,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <View
                  style={{
                    width: 30,
                    height: 25,
                    justifyContent: 'center',
                  }}>
                  <FontAwesome name="edit" size={20} color={Tme('textColor')} />
                </View>
                <Text style={{ color: Tme('cardTextColor') }}>
                  {I18n.t('home.edit_home')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </View>
          </TouchableOpacity>
          <View style={{ height: 2 }} />
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.reset.bind(this)}
            style={{
              backgroundColor: Tme('cardColor'),
            }}>
            <View
              style={{
                paddingHorizontal: 20,
                flexDirection: 'row',
                paddingVertical: 16,
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <View
                  style={{
                    width: 30,
                    height: 25,
                    justifyContent: 'center',
                  }}>
                  <MaterialIcons
                    name="settings-backup-restore"
                    size={20}
                    color={Tme('textColor')}
                  />
                </View>
                <Text style={{ color: Tme('cardTextColor') }}>
                  {I18n.t('setting.reset_home')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </View>
          </TouchableOpacity>
          <View style={{ height: 2 }} />
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.addUser.bind(this)}
            style={{
              backgroundColor: Tme('cardColor'),
              borderBottomRightRadius: mainRadius(),
              borderBottomLeftRadius: mainRadius(),
            }}>
            <View
              style={{
                paddingHorizontal: 20,
                flexDirection: 'row',
                paddingVertical: 16,
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <View
                  style={{
                    width: 30,
                    height: 25,
                    justifyContent: 'center',
                  }}>
                  <FontAwesome name="user" size={20} color={Tme('textColor')} />
                </View>
                <Text style={{ color: Tme('cardTextColor') }}>
                  {I18n.t('home.user')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </View>
          </TouchableOpacity>
          {HelperMemo.user_data.home_id ===
            this.props.route.params.home_id ? null : (
            <TouchableOpacity
              style={{
                marginTop: 20,
                borderRadius: mainRadius(),
                backgroundColor: Tme('cardColor'),
              }}
              activeOpacity={0.8}
              onPress={this.selectHome.bind(this)}>
              <View
                style={{
                  paddingVertical: 16,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Text style={{ color: Colors.MainColor }}>
                  {I18n.t('global.select_this_home')}
                </Text>
              </View>
            </TouchableOpacity>
          )}
          {this.state.sns && this.state.sns.length > 0 && (
            <View
              style={{
                marginTop: 20,
                backgroundColor: Tme('cardColor'),
                borderTopRightRadius: mainRadius(),
                borderTopLeftRadius: mainRadius(),
              }}>
              <View
                style={{
                  paddingHorizontal: 20,
                  paddingVertical: 16,
                }}>
                <Text
                  style={{
                    color: Tme('textColor'),
                  }}>
                  {I18n.t('global.sn_setting')}
                </Text>
              </View>
            </View>
          )}
        </>
      );
    }
    return null;
  }

  showAddController() {
    if (this.state.sns) {
      if (this.state.sns.length === 0) {
        return (
          <TouchableOpacity
            onPress={this.addController.bind(this)}
            activeOpacity={1.0}
            style={{
              paddingVertical: 20,
              borderRadius: mainRadius(),
              flexDirection: 'row',
              justifyContent: 'center',
              backgroundColor: Tme('cardColor'),
              alignItems: 'center',
            }}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Image
                style={{ width: 12, height: 12, marginRight: 8 }}
                source={require('../../img/add-black.png')}
              />
              <Text
                style={{
                  fontSize: 16,
                  fontWeight: '600',
                  color: Tme('cardTextColor'),
                }}>
                {I18n.t('home.add_controller')}
              </Text>
            </View>
          </TouchableOpacity>
        );
      }
    }
    return null;
  }

  render() {
    return (
      <NavBarView>
        <View style={{ flex: 1 }}>
          <FlatList
            ListHeaderComponent={this.header.bind(this)}
            refreshing={this.state.refreshing}
            onRefresh={this.doRefreshData.bind(this)}
            style={{ flex: 1 }}
            ref={flatList => (this._flatList = flatList)}
            data={this.state.sns}
            renderItem={this._renderRow.bind(this)}
            ListEmptyComponent={() => <EmptyView />}
            onEndReachedThreshold={0.1}
            ListFooterComponent={() => {
              return <View style={{ height: 20, flex: 1 }} />;
            }}
            keyExtractor={(item, index) => index.toString()}
          />

          {this.showAddController()}
        </View>
      </NavBarView>
    );
  }

  _renderRow({ item }) {
    return (
      <>
        <View style={{ height: 2 }} />
        <View style={{ marginBottom: 12 }}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.touchRow.bind(this, item)}
            style={[
              {
                height: 100,
                paddingHorizontal: 20,
                backgroundColor: Tme('cardColor'),
                borderBottomLeftRadius: mainRadius(),
                borderBottomRightRadius: mainRadius(),
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
              },
            ]}>
            <View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'flex-start',
              }}>
              <Text
                style={{
                  fontSize: 16,
                  color: Tme('cardTextColor'),
                }}>
                {_.isEmpty(item.controller_name)
                  ? item.name
                  : item.controller_name}
              </Text>
            </View>
            <View
              style={{
                alignItems: 'flex-end',
                justifyContent: 'center',
                flex: 1,
              }}>
              <View
                style={{
                  alignItems: 'flex-end',
                  justifyContent: 'center',
                  flex: 1,
                }}>
                <Text
                  style={{
                    fontSize: 17,
                    color: Tme('cardTextColor'),
                  }}>
                  {item.state
                    ? I18n.t('home.on_line')
                    : I18n.t('home.off_line')}
                </Text>
                <Text
                  style={{
                    fontSize: 12,
                    marginTop: 4,
                    color: Tme('cardTextColor'),
                  }}>
                  {item.version ? 'v' + item.version : ''}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </>
    );
  }

  touchRow(rowData) {
    this.props.navigation.push('AdvancedSettings', {
      sn: rowData.sn,
      sn_name: rowData.controller_name,
      sn_id: rowData.id,
      title: I18n.t('global.advanced_setting'),
    });
  }

  addController() {
    HelperMemo.select_home = this.props.route.params.home_id;
    this.props.navigation.push('WifiScreen', {
      title: I18n.t('home.add_controller'),
    });
  }
}
