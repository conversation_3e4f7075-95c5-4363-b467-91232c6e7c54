import React, {Component} from 'react';
import {View, Text} from 'react-native';
import AppConfig from '../../app_config';
import {Helper} from '../Helper';
import WebViewWithProgress from '../WebViewWithProgress';
import I18n from '../I18n';
import NavBarView from '../share/NavBarView';
import Scode from '../Scode';
import AlertModal from '../share/AlertModal';

export default class MoreScreen extends Component {
  constructor(props) {
    super(props);

    this.state = {
      uri: '',
      testValue: '',
    };
  }

  componentDidMount() {
    this.getUri();
  }

  async getUri() {
    const time = new Date().getTime();
    let uri = await Helper.hostByNetwork(
      `/voices/${this.props.route.params.type}?lang=${I18n.locale}&scopes=${AppConfig.scopes}&timestamp=${time}&apikey=${AppConfig.api_key}`,
    );

    const tempDate = `apikey${AppConfig.api_key}methodgetpath/iot/voices/${this.props.route.params.type}timestamp${time}`;

    Scode.code(tempDate.toLocaleUpperCase(), scode => {
      uri = uri + `&scode=${scode.toLocaleUpperCase()}`;
      this.setState({uri});
    });
  }

  render() {
    return (
      <NavBarView>
        <View style={{height: 0}}>
          <Text testID="webView">{this.state.testValue}</Text>
        </View>
        <WebViewWithProgress
          source={{uri: this.state.uri}}
          withoutDefaultHeaders={false}
          onMessage={this._message.bind(this)}
        />
      </NavBarView>
    );
  }

  _message(event) {
    var data = event.nativeEvent.data;
    if (data !== '[object Object]') {
      var temp = data.split(',');
      if (temp[0] == 'error') {
        this.props.navigation.goBack();
        AlertModal.alert(I18n.t('global.alexa_link_error'));
      } else {
        if (temp[1] == 'google_assistant') {
          this.setState({
            testValue: temp[1],
          });
        }
      }
    }
  }
}
