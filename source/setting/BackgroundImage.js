/* eslint-disable react/no-unstable-nested-components */
import React from 'react';
import {
  View,
  FlatList,
  TouchableOpacity,
  Image,
  Switch,
  Text,
  SafeAreaView,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import {Tme, Colors} from '../ThemeStyle';
import I18n from '../I18n';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  NotificationCenter,
  BACKGROUND_IMAGE,
} from '../NotificationCenter';
import {Toast} from '../Toast';
import HeaderRightBtn from '../share/HeaderRightBtn';
import ScreenSizeContext from '../../WindowResizeContext';
import { PubSub } from 'pubsub-js';
import { CHANGE_BACKGROUND_IMAGE } from '../types/PubSubEvent';
const imgs = [
  {key: 'bg_01', value: require('../../img/bg_0.png')},
  {key: 'bg_02', value: require('../../img/bg_2.png')},
  {key: 'bg_03', value: require('../../img/bg_3.png')},
  {key: 'bg_04', value: require('../../img/bg_6.png')},
  {key: 'bg_null', value: null},
];

class BackgroundImage extends React.PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      images: imgs,
      check: 'bg_02',
    };

    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  static contextType = ScreenSizeContext;

  componentDidMount() {
    var that = this;
    AsyncStorage.getItem('background_image').then(data => {
      var temp = JSON.parse(data);
      if (temp && temp.check) {
        that.setState({
          check: JSON.parse(data).check,
        });
      }
    });

    NotificationCenter.addObserver(this, BACKGROUND_IMAGE, data => {
      that.setState({
        images: imgs.concat({key: 'bg_05', value: data}),
        check: 'bg_05',
      });
    });
  }

  componentWillUnmount() {
    NotificationCenter.removeObserver(this, BACKGROUND_IMAGE);
  }

  rightClick() {
    var that = this;

    AsyncStorage.setItem(
      'background_image',
      JSON.stringify({check: that.state.check}),
      () => {
        PubSub.publish(CHANGE_BACKGROUND_IMAGE);
        this.props.navigation.goBack();
        Toast.show();
      },
    );
  }

  handleSuccess() {
    Toast.show();
  }

  _extractKey = item => {
    return item.key;
  };

  _renderHeader = item => {
    return (
      <View
        style={{
          backgroundColor: Tme('cardColor'),
          paddingHorizontal: 20,
        }}>
        <View
          style={{
            flexDirection: 'row',
            paddingVertical: 16,
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <Text style={{color: Tme('cardTextColor')}}>
            {I18n.t('setting.use_background_image')}
          </Text>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'flex-end',
            }}>
            <Switch
              style={{transform: [{scaleX: 0.8}, {scaleY: 0.8}]}}
              thumbColor="#fff"
              trackColor={{true: Colors.MainColor}}
              value={this.state.check !== 'bg_null'}
              onValueChange={this.sheetShow.bind(this)}
            />
          </View>
        </View>
      </View>
    );
  };

  sheetShow() {
    this.setState({
      check: 'bg_null',
    });
  }

  render() {
    return (
      <SafeAreaView style={{flex: 1, backgroundColor: Tme('bgColor')}}>
        <FlatList
          columnWrapperStyle={[
            {flex: 1, marginBottom: 20, justifyContent: 'center'},
          ]}
          style={{flex: 1, paddingTop: 20}}
          data={this.state.images}
          renderItem={this._renderItem}
          // ListHeaderComponent={this._renderHeader}
          ListHeaderComponentStyle={{marginBottom: 20}}
          numColumns={2}
          keyExtractor={this._extractKey}
        />
        {/* <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.photoBtn.bind(this)}>
            <View
              style={{
                backgroundColor: Tme('cardColor'),
                paddingVertical: 16,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Text style={{ color: Colors.MainColor }}>
                {I18n.t('setting.photo_btn')}
              </Text>
            </View>
          </TouchableOpacity>
          <View style={{ height: 34 }}></View> */}
      </SafeAreaView>
    );
  }

  _renderItem = item => {
    return (
      <TouchableOpacity
        style={{marginHorizontal: 16}}
        onPress={this.onClick.bind(this, item.item.key)}
        activeOpacity={1.0}>
        {item.item.key == 'bg_05' ? (
          <Image
            style={{
              width: (this.context.winWidth - 60) / 2,
              height: 300,
            }}
            source={{
              uri: item.item.value,
            }}
            resizeMode="cover"
          />
        ) : (
          <FastImage
            style={{
              width: (this.context.winWidth - 60) / 2,
              height: 300,
            }}
            source={item.item.value}
            resizeMode={FastImage.resizeMode.cover}
          />
        )}
        {item.item.key == this.state.check && (
          <View
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: Tme('cardColor'),
              position: 'absolute',
              bottom: 145,
              left: (this.context.winWidth - 60) / 2 / 2 - 20,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <MCIcons name="check" size={24} color={Colors.MainColor} />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  photoBtn() {
    this.props.navigation.push('PhotoList', {
      type: 'setting',
      sn: this.props.route.params.sn,
    });
  }

  onClick(item) {
    this.setState({
      check: item,
    });
  }
}
export default BackgroundImage;
