import React, {useCallback, useEffect, useState} from 'react';
import {View, TextInput, StyleSheet, Keyboard} from 'react-native';
import I18n from '../I18n';
import {Tme, Colors} from '../ThemeStyle';
import NavBarView from '../share/NavBarView';
import {Helper} from '../Helper';

import {Toast} from '../Toast';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {hideLoading, showLoading} from '../../ILoading';

export default function FeedbackScreen({route, navigation}) {
  const [email, setEmail] = useState('');
  const [content, setContent] = useState('');

  const rightClick = useCallback(() => {
    if (content !== '') {
      showLoading();
      Keyboard.dismiss();
      Helper.httpPOST(
        '/feedbacks',
        {
          ensure: () => {
            hideLoading();
          },
          success: data => {
            navigation.goBack();
            Toast.show();
          },
        },
        {email: email, content: content},
      );
    }
  }, [email, content, navigation]);

  useEffect(() => {
    navigation.setOptions({
      // eslint-disable-next-line react/no-unstable-nested-components
      headerRight: () => (
        <HeaderRightBtn text={I18n.t('home.save')} rightClick={rightClick} />
      ),
    });
  }, [navigation, rightClick]);

  return (
    <NavBarView>
      <View
        style={{
          paddingHorizontal: 16,
          paddingTop: 20,
          backgroundColor: Tme('cardColor'),
        }}>
        <View
          style={[styles.account_view, {borderColor: Tme('inputBorderColor')}]}>
          <TextInput
            autoFocus={true}
            autoCapitalize="none"
            clearButtonMode="always"
            underlineColorAndroid="transparent"
            placeholder={I18n.t('home.feedback')}
            placeholderTextColor={Tme('placeholder')}
            autoCorrect={false}
            value={content}
            multiline={true}
            numberOfLines={6}
            onChangeText={c => setContent(c)}
            style={[
              {
                height: 120,
                marginLeft: 6,
                marginRight: 6,
                textAlignVertical: 'top',
                color: Tme('cardTextColor'),
              },
            ]}
          />
        </View>
        <View
          style={{
            borderColor: Tme('inputBorderColor'),
            paddingVertical: 3,
            borderWidth: 1,
            borderRadius: 3,
            marginBottom: 20,
            marginTop: 16,
          }}>
          <TextInput
            placeholderTextColor={Tme('placeholder')}
            style={Colors.TextInputStyle()}
            keyboardType="email-address"
            autoCapitalize="none"
            underlineColorAndroid="transparent"
            placeholder={I18n.t('home.email')}
            value={email}
            returnKeyType="go"
            onSubmitEditing={rightClick}
            onChangeText={e => {
              setEmail(e);
            }}
          />
        </View>
      </View>
    </NavBarView>
  );
}

const styles = StyleSheet.create({
  account_view: {
    borderWidth: 1,
    borderRadius: 3,
    height: 120,
  },
});
