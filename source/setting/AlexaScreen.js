import React, {Component} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Platform,
  Image,
  Linking,
  NativeModules,
  ScrollView,
} from 'react-native';
import {Helper, HelperMemo, DEVICE_HEIGHT} from '../Helper';
import {Tme, Colors, IsDark} from '../ThemeStyle';
import {NotificationCenter, ALEXA_CALLBACK_EVENT} from '../NotificationCenter';
import I18n from '../I18n';
import NavBarView from '../share/NavBarView';
import AlertModal from '../share/AlertModal';
import {hideLoading, showLoading} from '../../ILoading';

export default class AlexaScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      alexaUrl: '',
      lwaURL: '',
    };
  }

  componentDidMount() {
    this.fetch = false;
    this.linkingListener = Linking.addEventListener(
      'url',
      this._handleOpenURL.bind(this),
    );

    var that = this;
    showLoading();

    Helper.httpGET('/oauth/service_urls?state=mobile-web', {
      apiPrefix: '',
      ensure: () => {
        hideLoading();
      },
      success: data => {
        that.setState({
          alexaUrl: data.alexa_url,
          lwaURL: data.lwa_url,
        });
      },
    });

    NotificationCenter.addObserver(this, ALEXA_CALLBACK_EVENT, data => {
      that.enableAlexa(data.state, data.code);
    });
  }

  componentWillUnmount() {
    if (this.linkingListener) {
      this.linkingListener.remove();
    }

    NotificationCenter.removeObserver(this, ALEXA_CALLBACK_EVENT);
  }

  _handleOpenURL(event) {
    var state = this.getQueryString(event.url, 'state');
    var code = this.getQueryString(event.url, 'code');
    if (state && code) {
      this.enableAlexa(state, code);
    } else {
      AlertModal.alert(I18n.t('global.alexa_link_error'));
    }
  }

  getQueryString(url, key) {
    var theRequest = {};
    if (url.split('?')[1]) {
      var strs = url.split('?')[1].split('&');
      for (var i = 0; i < strs.length; i++) {
        theRequest[strs[i].split('=')[0]] = unescape(strs[i].split('=')[1]);
      }
    }
    return theRequest[key];
  }

  enableAlexa(state, code) {
    if (!this.fetch) {
      this.fetch = true;
      showLoading();
      Helper.httpPOST(
        '/oauth/enable_alexa',
        {
          apiPrefix: '',
          ensure: () => {
            hideLoading();
          },
          success: data => {
            AlertModal.alert(I18n.t('global.alexa_link_success'));
          },
          error: data => {
            AlertModal.alert(I18n.t('global.alexa_link_error'));
          },
        },
        {user_uuid: HelperMemo.user_data.uuid, code: code, state: state},
      );
    }
  }

  link() {
    // var alexaUrl = "https://alexa.amazon.com/spa/skill-account-linking-consent?fragment=skill-account-linking-consent&client_id=amzn1.application-oa2-client.2d75e61f6b2f462499e70870bde94655&scope=alexa::skills:account_linking&skill_stage=development&response_type=code&redirect_uri=https://www.presensmarthome.com/oauth/app_link_success&state=oauth";
    if (Platform.OS == 'ios') {
      NativeModules.HelpersModule.openUniversalLink(
        this.state.alexaUrl,
        (err, res) => {
          if (err) {
          } else {
            if (res.status == 'no') {
              this.openWebview();
            }
          }
        },
      );
    } else {
      this.openWebview();
    }
  }

  openWebview() {
    // var lwaURL = "https://www.amazon.com/ap/oa?client_id=amzn1.application-oa2-client.2d75e61f6b2f462499e70870bde94655&scope=alexa::skills:account_linking&response_type=code&redirect_uri=https://www.presensmarthome.com/oauth/app_link_success&state=oauth";
    this.props.navigation.push('AlexaLoginWithAmazonScreen', {
      url: this.state.lwaURL,
      title: 'Link with Alexa',
    });
  }

  render() {
    return (
      <NavBarView>
        <ScrollView style={{flex: 1}}>
          <View style={{padding: 16}}>
            <View style={{justifyContent: 'space-between', flex: 1}}>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: DEVICE_HEIGHT / 2,
                }}>
                <Image
                  style={{
                    width: 250,
                    height: 60,
                    resizeMode: 'contain',
                    marginBottom: 32,
                  }}
                  source={
                    IsDark()
                      ? require('../../img/services/amazon-alexa-dark.png')
                      : require('../../img/services/amazon-alexa-light.png')
                  }
                />
                <Text style={{fontSize: 16, color: Tme('cardTextColor')}}>
                  {I18n.t('services.alexa')} {I18n.t('services.alexa_desp')}
                </Text>
              </View>

              <View style={{marginBottom: 50}}>
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={this.link.bind(this)}
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: 40,
                    backgroundColor: Colors.MainColor,
                    borderRadius: 4,
                  }}>
                  <Text style={{color: '#ffffff', fontSize: 14}}>
                    {I18n.t('global.link_with_alexa')}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </NavBarView>
    );
  }
}
