/* eslint-disable react/no-unstable-nested-components */
import React, {Component} from 'react';
import {View, ScrollView, SafeAreaView} from 'react-native';
import {HelperMemo} from '../Helper';
import I18n from '../I18n';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {Tme} from '../ThemeStyle';

import RadioButtons from '../RadioButtons';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {showLoading} from '../../ILoading';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';
export default class ThemeScreen extends Component {
  constructor(props) {
    super(props);

    this.state = {
      dataSource: [
        {
          key: 'system_with',
          value: I18n.t('global.system_with'),
        },
        {
          key: 'dark',
          value: I18n.t('global.dark'),
        },
        {
          key: 'light',
          value: I18n.t('global.light'),
        },
      ],
      checked: props.route.params.theme,
    };

    this.props.navigation.setOptions({
      headerRight: () => {
        if (
          this.props.route.params.from &&
          this.props.route.params.from === 'signup'
        ) {
          return null;
        } else {
          return (
            <HeaderRightBtn
              text={I18n.t('home.save')}
              rightClick={this.rightClick.bind(this)}
            />
          );
        }
      },
    });
  }

  rightClick() {
    this._save();
  }

  render() {
    return (
      <SafeAreaView style={{flex: 1, backgroundColor: Tme('bgColor')}}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{
            flex: 1,
          }}>
          <View style={{marginTop: 20, backgroundColor: Tme('cardColor')}}>
            <RadioButtons
              data={this.state.dataSource}
              defaultKey={this.state.checked}
              onChange={this.onChange.bind(this)}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }

  _save() {
    var that = this;
    showLoading();
    AsyncStorage.setItem('theme', JSON.stringify(that.state.checked), () => {
      HelperMemo.theme = that.state.checked;
      PubSub.publish(PubSubEvent.RESTART_APP);
    });
  }

  onChange(data) {
    this.setState({
      checked: data,
    });
  }
}
