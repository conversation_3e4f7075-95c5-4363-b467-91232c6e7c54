import React from 'react';
import { View, Text, ScrollView, Image, TouchableOpacity } from 'react-native';
import I18n from '../I18n';
import { Tme } from '../ThemeStyle';
import { Helper } from '../Helper';
import SwitchBtn from '../share/SwitchBtn';
import NavBarView from '../share/NavBarView';
import { mainRadius } from '../Tools';
import { hideLoading, showLoading } from '../../ILoading';
import ScreenSizeContext from '../../WindowResizeContext';

export default class VoiceScreen extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      using_alexa: false,
      using_ali: false,
      using_assistant: false,
      using_ifttt: false,
    };
  }
  componentDidMount() {
    this.focuListener = this.props.navigation.addListener('focus', () => {
      this.doFetchData();
    });
  }

  componentWillUnmount() {
    if (this.focuListener) {
      this.focuListener();
    }
  }

  static contextType = ScreenSizeContext;

  doFetchData() {
    var that = this;
    showLoading();
    Helper.httpGET(Helper.urlWithQuery('/voices'), {
      cloud: true,
      success: data => {
        that.setState({
          using_alexa: data.using_alexa,
          using_ali: data.using_ali,
          using_assistant: data.using_assistant,
          using_ifttt: data.using_ifttt,
        });
      },
      ensure: () => {
        hideLoading();
      },
    });
  }

  render() {
    return (
      <NavBarView>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{
            flex: 1,
          }}>
          <View
            style={{
              marginTop: 20,
              paddingBottom: 20,
            }}>
            <View
              style={{
                backgroundColor: Tme('cardColor'),
                borderTopRightRadius: mainRadius(),
                borderTopLeftRadius: mainRadius(),
              }}>
              <View
                style={{
                  marginHorizontal: 16,
                  flexDirection: 'row',
                  paddingVertical: 16,
                  justifyContent: 'space-between',
                }}>
                <View
                  style={{
                    flex: 1,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: 8,
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <View
                        style={{
                          width: 40,
                          height: 40,
                          borderRadius: 20,
                          justifyContent: 'center',
                          marginRight: 8,
                          alignItems: 'center',
                          backgroundColor: Tme('inputBorderColor'),
                        }}>
                        <Image
                          style={[{ width: 20, height: 20 }]}
                          source={require('../../img/services/alexa.png')}
                        />
                      </View>
                      <Text
                        style={{
                          color: Tme('cardTextColor'),
                          fontSize: 17,
                          fontWeight: '500',
                        }}>
                        Amazon Alexa
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <SwitchBtn
                        disabled={true}
                        value={this.state.using_alexa}
                      />
                    </View>
                  </View>
                  <Text
                    style={{ color: Tme('cardTextColor'), paddingVertical: 20 }}>
                    {I18n.t('services.alexa')}
                  </Text>
                </View>
              </View>
            </View>
            <View style={{ height: 2 }} />
            <View
              style={{
                backgroundColor: Tme('cardColor'),
                borderBottomRightRadius: mainRadius(),
                borderBottomLeftRadius: mainRadius(),
              }}>
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={this.linkAlexa.bind(this)}
                style={{
                  marginHorizontal: 20,
                  flexDirection: 'row',
                  paddingVertical: 16,
                }}>
                <Text style={{ color: Tme('cardTextColor'), fontWeight: '500' }}>
                  {I18n.t('global.link_with_alexa')}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={{ height: 20 }} />
            <View
              style={{
                backgroundColor: Tme('cardColor'),
                borderTopRightRadius: mainRadius(),
                borderTopLeftRadius: mainRadius(),
              }}>
              <View
                style={{
                  marginHorizontal: 16,
                  flexDirection: 'row',
                  paddingVertical: 16,
                  justifyContent: 'space-between',
                }}>
                <View
                  style={{
                    flex: 1,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: 8,
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <View
                        style={{
                          width: 40,
                          height: 40,
                          borderRadius: 20,
                          justifyContent: 'center',
                          marginRight: 8,
                          alignItems: 'center',
                          backgroundColor: Tme('inputBorderColor'),
                        }}>
                        <Image
                          style={[
                            { resizeMode: 'contain', width: 24, height: 24 },
                          ]}
                          source={require('../../img/services/google-assistant.png')}
                        />
                      </View>
                      <Text
                        style={{
                          color: Tme('cardTextColor'),
                          fontSize: 17,
                          fontWeight: '500',
                        }}>
                        Google Assistant
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <SwitchBtn
                        disabled={true}
                        value={this.state.using_assistant}
                      />
                    </View>
                  </View>
                  <Text
                    style={{ color: Tme('cardTextColor'), paddingVertical: 20 }}>
                    {I18n.t('services.google')}
                  </Text>
                </View>
              </View>
            </View>
            <View style={{ height: 2 }} />
            <View
              style={{
                backgroundColor: Tme('cardColor'),
                borderBottomRightRadius: mainRadius(),
                borderBottomLeftRadius: mainRadius(),
              }}>
              <TouchableOpacity
                testID="google_assistant"
                activeOpacity={0.8}
                onPress={this.more.bind(this, 'google_assistant')}
                style={{
                  marginHorizontal: 20,
                  flexDirection: 'row',
                  paddingVertical: 16,
                }}>
                <Text style={{ color: Tme('cardTextColor'), fontWeight: '500' }}>
                  {I18n.t('global.click_to_find_more')}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={{ height: 20 }} />
            <View
              style={{
                backgroundColor: Tme('cardColor'),
                borderTopRightRadius: mainRadius(),
                borderTopLeftRadius: mainRadius(),
              }}>
              <View
                style={{
                  marginHorizontal: 16,
                  flexDirection: 'row',
                  paddingVertical: 16,
                  justifyContent: 'space-between',
                }}>
                <View
                  style={{
                    flex: 1,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: 8,
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <View
                        style={{
                          width: 40,
                          height: 40,
                          borderRadius: 20,
                          justifyContent: 'center',
                          marginRight: 8,
                          alignItems: 'center',
                          backgroundColor: Tme('inputBorderColor'),
                        }}>
                        <Image
                          style={[
                            { resizeMode: 'contain', width: 20, height: 20 },
                          ]}
                          source={require('../../img/services/ifttt.png')}
                        />
                      </View>
                      <Text
                        style={{
                          color: Tme('cardTextColor'),
                          fontSize: 17,
                          fontWeight: '500',
                        }}>
                        IFTTT
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <SwitchBtn
                        disabled={true}
                        value={this.state.using_ifttt}
                      />
                    </View>
                  </View>
                  <Text
                    style={{
                      color: Tme('cardTextColor'),
                      paddingVertical: 20,
                    }}>
                    {I18n.t('services.ifttt')}
                  </Text>
                </View>
              </View>
            </View>
            <View style={{ height: 2 }} />
            <View
              style={{
                backgroundColor: Tme('cardColor'),
                borderBottomRightRadius: mainRadius(),
                borderBottomLeftRadius: mainRadius(),
              }}>
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={this.more.bind(this, 'ifttt')}
                style={{
                  marginHorizontal: 20,
                  flexDirection: 'row',
                  paddingVertical: 16,
                }}>
                <Text style={{ color: Tme('cardTextColor'), fontWeight: '500' }}>
                  {I18n.t('global.click_to_find_more')}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={{ height: 20 }} />
            <View
              style={{
                backgroundColor: Tme('cardColor'),
                borderTopRightRadius: mainRadius(),
                borderTopLeftRadius: mainRadius(),
              }}>
              <View
                style={{
                  marginHorizontal: 16,
                  flexDirection: 'row',
                  paddingVertical: 16,
                  justifyContent: 'space-between',
                }}>
                <View
                  style={{
                    flex: 1,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: 8,
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <View
                        style={{
                          width: 40,
                          height: 40,
                          borderRadius: 20,
                          justifyContent: 'center',
                          marginRight: 8,
                          alignItems: 'center',
                          backgroundColor: '#0084FF',
                        }}>
                        <Image
                          style={[
                            { resizeMode: 'contain', width: 20, height: 20 },
                          ]}
                          source={require('../../img/services/tmall.png')}
                        />
                      </View>
                      <Text
                        style={{
                          color: Tme('cardTextColor'),
                          fontSize: 17,
                          fontWeight: '500',
                        }}>
                        TmallGenie
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <SwitchBtn disabled={true} value={this.state.using_ali} />
                    </View>
                  </View>
                  <Text
                    style={{ color: Tme('cardTextColor'), paddingVertical: 20 }}>
                    {I18n.t('services.ali')}
                  </Text>
                </View>
              </View>
            </View>
            <View style={{ height: 2 }} />
            <View
              style={{
                backgroundColor: Tme('cardColor'),
                borderBottomRightRadius: mainRadius(),
                borderBottomLeftRadius: mainRadius(),
              }}>
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={this.more.bind(this, 'ali')}
                style={{
                  marginHorizontal: 20,
                  flexDirection: 'row',
                  paddingVertical: 16,
                }}>
                <Text style={{ color: Tme('cardTextColor'), fontWeight: '500' }}>
                  {I18n.t('global.click_to_find_more')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </NavBarView>
    );
  }

  more(type) {
    var title = '';
    switch (type) {
      case 'ali':
        title = 'TmallGenie';
        break;
      case 'ifttt':
        title = 'IFTTT';
        break;
      case 'google_assistant':
        title = 'Google Assistant';
        break;
    }
    this.props.navigation.push('MoreScreen', {
      type: type,
      title: title,
    });
  }

  linkAlexa() {
    this.props.navigation.push('AlexaScreen');
  }
}
