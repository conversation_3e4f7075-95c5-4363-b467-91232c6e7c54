import React, { Component } from 'react';
import { Colors, Tme } from '../ThemeStyle';
import { Camera } from 'react-native-camera-kit';
import IdleTimerManager from 'react-native-idle-timer';
import { Helper } from '../Helper';
import ScreenSizeContext from '../../WindowResizeContext';
import { View } from 'react-native';
import { hideLoading, showLoading } from '../../ILoading';
import { EVENT_ADD_CONTROLLER_SCAN, SCAN_CODE } from '../types/PubSubEvent';
import PubSub from 'pubsub-js';

class ScanCodeScreen extends Component {
  constructor(props) {
    super(props);

    this.state = {
      showRq: true,
    };

    this.focusListener = this.props.navigation.addListener('focus', () => {
      this.setState({
        showRq: true,
      });
    });
  }

  static contextType = ScreenSizeContext;

  componentDidMount() {
    IdleTimerManager.setIdleTimerDisabled(true);
  }
  componentWillUnmount() {
    IdleTimerManager.setIdleTimerDisabled(false);
    if (this.focusListener) {
      this.focusListener();
    }
  }

  render() {
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: Tme('bgColor'),
        }}>
        {this.state.showRq && (
          <Camera
            style={{ height: '100%', width: '100%' }}
            scanBarcode={true}
            showFrame={true}
            laserColor={'blue'}
            frameColor={Colors.MainColor}
            onReadCode={event => this.readCode(event)}
            hideControls={false}
            colorForScannerFrame={'red'} //(default white) optional, change colot of the scanner frame
          />
        )}
      </View>
    );
  }

  readCode(event) {
    var that = this;
    const code = event.nativeEvent.codeStringValue;
    console.log('scan', code);
    if (this.state.showRq) {
      that.setState(
        {
          showRq: false,
        },
        () => {
          if (this.props.route.params.type) {
            if (this.props.route.params.type === 'matter_wifi') {
              this.props.navigation.push('WifiSetting', {
                type: this.props.route.params.type,
                sn: this.props.route.params.sn,
                code: code,
                title: 'Matter Wifi',
              });
            } else {
              if (this.props.route.params.type === 'zigbee') {
                this.verifcationCode(code);
              } else {
                this.props.navigation.push('addDevice', {
                  type: this.props.route.params.type,
                  sn: this.props.route.params.sn,
                  code: code,
                });
              }
            }
          } else {
            PubSub.publish(EVENT_ADD_CONTROLLER_SCAN, code);
            this.props.navigation.goBack();
          }
        },
      );
    }
  }

  verifcationCode(code) {
    showLoading();
    Helper.httpPOST(
      '/partner/controllers/parse_zb_qr_code?code=' + code,
      {
        ensure: () => {
          hideLoading();
        },
        success: data => {
          PubSub.publish(SCAN_CODE, {
            installKey: data.install_code,
            addressKey: data.eui64,
          });
          this.props.navigation.goBack();
        },
        error: error => {
          console.log(error);
        },
      },
      {},
    );
  }
}

export default ScanCodeScreen;
