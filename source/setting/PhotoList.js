/* eslint-disable react/no-unstable-nested-components */
import React from 'react';
import {
  View,
  FlatList,
  TouchableOpacity,
  Image,
  SafeAreaView,
} from 'react-native';
import { DEVICE_WIDTH } from '../Helper';
import { Tme, Colors } from '../ThemeStyle';
import I18n from '../I18n';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import { NotificationCenter, BACKGROUND_IMAGE } from '../NotificationCenter';
import RNFS from 'react-native-fs';
import HeaderRightBtn from '../share/HeaderRightBtn';

export default class ProfileList extends React.PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      photos: [],
      check: '',
    };

    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  componentDidMount() {

    CameraRoll.getPhotos({
      first: 60,
      assetType: 'Photos',
    })
      .then(r => {
        if (r.edges.length > 0) {
          this.setState({ photos: r.edges }, () => {

          });
        }
      })
      .catch(err => {
        console.log(err);
      });
  }

  rightClick() {
    var path = RNFS.DocumentDirectoryPath + '/05.png';
    RNFS.copyAssetsFileIOS(this.state.check, path, 0, 0)
      .then(res => {
        NotificationCenter.dispatchEvent(BACKGROUND_IMAGE, 'file://' + path);
        this.props.navigation.goBack();
      })
      .catch(err => {
        console.log('ERROR: image file write failed!!!');
        console.log(err.message, err.code);
      });
  }

  _extractKey = item => {
    return item.node.image.uri;
  };

  render() {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: Tme('bgColor') }}>
        <FlatList
          columnWrapperStyle={[{ flex: 1, marginBottom: 20 }]}
          style={{ flex: 1, marginTop: 20 }}
          data={this.state.photos}
          renderItem={this._renderItem}
          numColumns={3}
          keyExtractor={this._extractKey}
        />
      </SafeAreaView>
    );
  }

  _renderItem = item => {
    return (
      <TouchableOpacity
        style={[
          item.index % 3 == 0
            ? { marginRight: 20, marginLeft: 20 }
            : { marginRight: 20 },
        ]}
        onPress={this.onClick.bind(this, item.item.node.image.uri)}
        activeOpacity={0.8}>
        <Image
          style={{ width: (DEVICE_WIDTH - 80) / 3, height: 200 }}
          source={{
            uri: item.item.node.image.uri,
          }}
          resizeMode="cover"
        />
        {item.item.node.image.uri == this.state.check && (
          <View
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: Tme('cardColor'),
              position: 'absolute',
              bottom: 80,
              left: (DEVICE_WIDTH / 3 - 55) / 2,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <MCIcons name="check" size={24} color={Colors.MainColor} />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  onClick(uri) {
    this.setState({
      check: uri,
    });
  }
}
