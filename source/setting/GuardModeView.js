/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import { View, Text, TouchableOpacity, FlatList, Platform } from 'react-native';
import { Helper, HelperMemo } from '../Helper';
import I18n from '../I18n';
import { Tme, Colors } from '../ThemeStyle';
import _ from 'lodash';
import { observer } from 'mobx-react/native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {
  NotificationCenter,
  SUN_SELECT_ADD,
  EVENT_MAP,
} from '../NotificationCenter';
import NavBarView from '../share/NavBarView';
import EmptyView from '../share/EmptyView';
import Icons from 'react-native-vector-icons/Ionicons';
import DeviceControl from '../DeviceControl';
import { Toast } from '../Toast';
import DeviceInfo from 'react-native-device-info';
import Permissions from '../Permissions';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { hideLoading, showLoading } from '../../ILoading';
import PubSub from 'pubsub-js';
import { EVENT_GUARD } from '../types/PubSubEvent';

@observer
class GuardModeView extends Component {
  constructor(props) {
    super(props);

    this.state = {
      key: '',
      options: [],
      current_room_id: '',
      room_name: '',
      devices: [],
      data_devices: [],
      value: [],
      address: '',
      lat: '',
      lng: '',
      data: {},
      targets: [],
      home_address: {},
    };

    this.flatList;
    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this._save();
  }

  componentDidMount() {
    var that = this;
    NotificationCenter.addObserver(this, SUN_SELECT_ADD, data => {
      that.setState({
        address: data.address,
        lat: data.lat,
        lng: data.lng,
      });
    });
    NotificationCenter.addObserver(this, EVENT_MAP, data => {
      that.setState({
        address: data.address,
        lat: data.lat,
        lng: data.lng,
      });
    });

    this.doFetchData();
  }

  doFetchData() {
    var that = this;
    showLoading();
    Helper.httpGET(Helper.urlWithQuery('/guards'), {
      success: data => {
        var devices = [];
        var rooms = [];
        _.forEach(data.devices, function (v, k) {
          if (v.index != 1) {
            if (
              _.find(v.cc_specs, function (o) {
                return o.name == 'Switch';
              })
            ) {
              devices.push(v);
            }
          }
        });
        _.each(data.rooms, (cc, index) => {
          rooms.push({ label: cc.name, value: cc.id });
        });
        if (data.routines) {
          const location = Helper.locationFormat(data.routines.location);
          that.setState({
            data: data.routines,
            address: data.routines.location_info,
            lat: location[1],
            lng: location[0],
            targets: data.routines.targets || [],
          });
        } else {
          if (data.home_address.location) {
            if (data.home_address.location.length > 1) {
              const location = Helper.locationFormat(
                data.home_address.location,
              );
              that.setState({
                address: data.home_address.city,
                lat: location[1],
                lng: location[0],
              });
            }
          }
        }

        that.setState({
          devices: devices,
          data_devices: devices,
          options: rooms,
          rooms: data.rooms,
          home_address: data.home_address,
        });
      },
      ensure: () => {
        hideLoading();
      },
    });
  }

  componentWillUnmount() {
    NotificationCenter.removeObserver(this, SUN_SELECT_ADD);
    NotificationCenter.removeObserver(this, EVENT_MAP);
  }

  render() {
    return (
      <NavBarView>
        <Text style={{ display: 'none' }}>{this.state.key}</Text>
        <View
          style={{
            flex: 1,
          }}>
          <View style={{ width: 1, height: 16 }} />
          <View style={{ backgroundColor: Tme('cardColor') }}>
            <TouchableOpacity
              onPress={this.click.bind(this)}
              activeOpacity={1.0}
              style={{
                paddingTop: 20,
                paddingBottom: 15,
                paddingHorizontal: 20,
                backgroundColor: Tme('cardColor'),
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <Text style={{ color: Tme('cardTextColor'), fontSize: 17 }}>
                {this.state.address
                  ? this.state.address
                  : I18n.t('automation.current_located_city')}
              </Text>
              <View styl={{ width: 25 }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>
          </View>
          <View
            style={{ paddingHorizontal: 20, marginTop: 8, marginBottom: 20 }}>
            <Text
              style={{
                fontSize: 12,
                fontWeight: '600',
                color: Tme('cardTextColor'),
              }}>
              {I18n.t('guard.guard_gps_desp')}
            </Text>
          </View>
          <View style={{ paddingHorizontal: 20, marginBottom: 8 }}>
            <Text
              style={{
                fontSize: 12,
                fontWeight: '600',
                color: Tme('cardTextColor'),
              }}>
              {I18n.t('guard.guard_device_desp')}
            </Text>
          </View>
          <FlatList
            ref={flatList => (this._flatList = flatList)}
            data={this.state.devices}
            renderItem={this._renderRow.bind(this)}
            ItemSeparatorComponent={this._line.bind(this)}
            numColumns={1}
            ListEmptyComponent={() => <EmptyView />}
            onEndReachedThreshold={0.1}
            keyExtractor={(item, index) => index.toString()}
          />
        </View>
      </NavBarView>
    );
  }

  _line() {
    return <View style={{ height: 2, backgroundColor: Tme('bgColor') }} />;
  }

  _renderRow({ item, index }) {
    var check = false;
    var targets = [];

    _.each(this.state.targets, (v, key) => {
      targets.push('spec_' + v.value_id);
    });

    _.each(item.cc_specs, cc => {
      if (cc.spec_type !== 1) {
        if (_.includes(targets, 'spec_' + cc.value_id)) {
          check = true;
          return;
        }
      }
    });
    return (
      <TouchableOpacity
        activeOpacity={1.0}
        style={{
          backgroundColor: Tme('cardColor'),
          flex: 1,
          flexDirection: 'row',
        }}
        onPress={this.deviceShow.bind(this, item)}>
        <View
          style={{
            flex: 1,
            paddingLeft: 20,
          }}>
          <View
            style={{
              alignItems: 'center',
              flexDirection: 'row',
              paddingVertical: 16,
              paddingRight: 16,
            }}>
            <Text style={{ fontSize: 17, color: Tme('cardTextColor') }}>
              {item.display_name}
            </Text>
            {check ? (
              <Icons
                name="checkmark-circle-outline"
                size={18}
                color={Colors.MainColor}
                style={{ marginLeft: 8 }}
              />
            ) : null}
          </View>
        </View>
      </TouchableOpacity>
    );
  }

  deviceShow(item) {
    var that = this;
    var targets = this.state.targets;
    _.each(item.cc_specs, (cc, index) => {
      if (cc.name == 'Switch') {
        var target = {};
        target.delay = 0;
        target.node_id = cc.device_id;
        target.value_id = cc.value_id;
        target.instance_id = cc.instance_id;
        target.spec_cc = cc.cc;
        target.spec_name = cc.name;
        target.spec_value = 'On';
        target.logic = null;
        target.params = '255';
        target.desp = 'On';
        var deviceControl = new DeviceControl({ spec: cc, param: 255 });
        deviceControl.switch(cmd => {
          target.commands = cmd;
        });

        target.app_url = null;
        target.target_type = '';

        var target_off = JSON.parse(JSON.stringify(target));
        target_off.spec_value = 'Off';
        target_off.params = '0';
        target_off.desp = 'Off';

        var deviceControl = new DeviceControl({ spec: cc, param: 0 });
        deviceControl.switch(cmd => {
          target_off.commands = cmd;
        });

        target_off.delay = 3600 * 4.2;

        if (
          _.find(targets, function (s) {
            return s.value_id == cc.value_id;
          })
        ) {
          _.remove(targets, function (n) {
            return n.value_id == cc.value_id;
          });
        } else {
          targets.push(target);
          targets.push(target_off);
        }

        that.setState({
          key: Math.random(),
          targets: targets,
        });
      }
    });
  }

  dv_type(data) {
    var type = null;
    switch (data.dv_type) {
      case 'zwave':
        type = 'Z-WAVE';
        break;
      case 'camera':
        type = 'Camera';
        break;
      case 'zigbee':
        type = 'Zigbee';
        break;
      default:
        type = '433M';
    }
    return type;
  }

  _createEmptyView() {
    return (
      <View
        style={{
          height: 100,
          alignItems: 'center',
          justifyContent: 'center',
        }}
      />
    );
  }
  click() {
    var that = this;
    if (HelperMemo.user_data.user.zone == 'cn') {
      that.pushCity();
    } else {
      if (Platform.OS == 'android') {
        DeviceInfo.hasGms().then(hasGms => {
          if (!hasGms) {
            that.pushCity();
          } else {
            that.pushMap();
          }
        });
      } else {
        that.pushMap();
      }
    }
  }

  pushCity() {
    Permissions.LocationPermission(() => {
      this.props.navigation.push('CityList', {
        data: this.state.data,
        routine: this.state.routine,
        title: I18n.t('automation.select_location'),
      });
    });
  }

  pushMap() {
    this.props.navigation.push('MapViewShow', {
      lat: this.state.lat,
      lng: this.state.lng,
      address: this.state.address,
      title: I18n.t('automation.select_location'),
    });
  }

  _save() {
    if (this.state.lat) {
      showLoading();
      var body = {
        address: this.state.address,
        lat: this.state.lat,
        lng: this.state.lng,
        type: 'guard',
        target_type: 'device',
        targets: this.state.targets,
      };
      if (this.state.data.uuid) {
        Object.assign(
          body,
          {},
          {
            uuid: this.state.data.uuid,
          },
        );
      }
      Helper.httpPOST(
        '/guards',
        {
          ensure: () => {
            hideLoading();
          },
          success: data => {
            PubSub.publish(EVENT_GUARD);
            this.props.navigation.popToTop();
            Toast.show();
          },
        },
        body,
      );
    } else {
      AlertModal.alert(I18n.t('automation.select_location'));
    }
  }

  handleSuccess() {
    Toast.show();
  }

  onChange(value) {
    this.setState({ value });
    if (value == undefined) {
      this.setState({ devices: this.state.data_devices, room_name: '' });
    } else {
      var current_room_id = value[0];
      this.setState(
        {
          current_room_id: current_room_id,
          room_name: _.find(this.state.options, v => {
            return v.value == current_room_id;
          }).label,
        },
        () => {
          if (current_room_id && current_room_id.length > 0) {
            var room = _.find(this.state.rooms, v => {
              return v.id == current_room_id;
            });
            this.setState({
              devices: _.filter(this.state.data_devices, v => {
                return room.device_ids.indexOf(v.uuid) != -1;
              }),
            });
          } else {
            this.setState({ devices: this.state.data_devices, room_name: '' });
          }
        },
      );
    }
  }
}
export default GuardModeView;
