/**
 * Based on http://stackoverflow.com/a/33585501/1783214
 *
 * Handle resizing enclosed View and scrolling to input
 * Usage:
 *    <KeyboardHandler ref='kh' offset={DEVICE_HEIGHT / 3} keyboardDismissMode='on-drag'>
 *      <View>
 *        ...
 *        <TextInput ref='username'
 *          onFocus={()=>this.refs.kh.inputFocused(this,'username')}/>
 *        ...
 *      </View>
 *    </KeyboardHandler>
 *
 *  offset is optional and defaults to 34
 *  Any other specified props will be passed on to ScrollView
 */
import React, {Component} from 'react';
import PropTypes from 'prop-types';

import {ScrollView, View, Keyboard, findNodeHandle} from 'react-native';

class KeyboardHandler extends Component {
  constructor(props) {
    super(props);
    this.state = {keyboardSpace: 0};
    this.focused = null;
    this._didShowListener = null;
    this._willHideListener = null;

    this.scrollRef = React.createRef();
  }

  onKeyboarDidShow(frames) {
    if (!frames.endCoordinates || !this.focused) {
      return;
    }

    this.setState({keyboardSpace: frames.endCoordinates.height});
    let scrollResponder =
      this.scrollRef.current.scrollView.getScrollResponder();
    scrollResponder.scrollResponderScrollNativeHandleToKeyboard(
      // findNodeHandle(TextInput.State.currentlyFocusedField()),
      findNodeHandle(this.focused),
      this.props.offset, //additionalOffset
      true,
    );
  }

  onKeyboardWillHide() {
    this.setState({keyboardSpace: 0});
  }
  componentDidMount() {
    this._didShowListener = Keyboard.addListener(
      'keyboardDidShow',
      this.onKeyboarDidShow.bind(this),
    );
    this._willHideListener = Keyboard.addListener(
      'keyboardWillHide',
      this.onKeyboardWillHide.bind(this),
    );

    this.scrollviewProps = {
      automaticallyAdjustContentInsets: true,
      keyboardShouldPersistTaps: 'never',
      scrollEventThrottle: 200,
    };
    // pass on any props we don't own to ScrollView
    Object.keys(this.props)
      .filter(n => {
        return n != 'children';
      })
      .forEach(e => {
        this.scrollviewProps[e] = this.props[e];
      });
  }

  componentWillUnmount() {
    this._didShowListener.remove();
    this._willHideListener.remove();
  }

  render() {
    return (
      <ScrollView ref={this.scrollRef} {...this.scrollviewProps}>
        {this.props.children}
        <View style={{height: this.state.keyboardSpace}} />
      </ScrollView>
    );
  }

  inputFocused(_this, refName) {
    this.focused = findNodeHandle(_this.refs[refName]);
  }

  static propTypes = {
    offset: PropTypes.number,
  };

  static defaultProps = {
    offset: 0,
  };
}

export default KeyboardHandler;
