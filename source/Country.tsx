/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ListRenderItemInfo } from 'react-native';
import { RouteProp } from '@react-navigation/native';
import { NavigationProp } from '@react-navigation/native';

import I18n from './I18n';
import EmptyView from './share/EmptyView';
import PubSub from 'pubsub-js';
import { PubSubEvent } from './types/PubSubEvent';

// 定义国家/地区数据接口
interface CountryItem {
  id: string;
  name: string;
  type?: string;
}

// 定义组件状态接口
interface CountryState {
  dataSource: CountryItem[];
}

// 定义路由参数接口
interface CountryRouteParams {
  type: string;
}

// 定义导航属性接口
interface CountryProps {
  route: RouteProp<{ Country: CountryRouteParams }, 'Country'>;
  navigation: NavigationProp<any>;
}

export default class Country extends Component<CountryProps, CountryState> {

  constructor(props: CountryProps) {
    super(props);
    const UNIT: CountryItem[] = [
      {
        id: '+86',
        name: I18n.t('user.zh'),
      },
      {
        id: '+1',
        name: I18n.t('user.en'),
      },
      {
        id: '+49',
        name: I18n.t('user.de'),
      },
      {
        id: '+33',
        name: I18n.t('user.fr'),
      },
      {
        id: '+7',
        name: I18n.t('user.ru'),
      },
      {
        id: '+34',
        name: I18n.t('user.es'),
      },
      {
        id: '+351',
        name: I18n.t('user.pt'),
      },
    ];
    this.state = {
      dataSource: UNIT,
    };
  }

  render() {
    return (
      <FlatList<CountryItem>
        style={styles.list_view}
        data={this.state.dataSource}
        renderItem={this._renderRow.bind(this)}
        numColumns={1}
        onEndReachedThreshold={0.1}
        ListEmptyComponent={() => <EmptyView />}
        keyExtractor={(item) => item.id}
      />
    );
  }

  private _renderRow({ item }: ListRenderItemInfo<CountryItem>) {
    const rowData = item;
    const content = (
      <View style={styles.row}>
        <Text>{rowData.name}</Text>
      </View>
    );
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => this._rowPressEvent(rowData)}>
        {content}
      </TouchableOpacity>
    );
  }

  private _rowPressEvent(rowData: CountryItem) {
    const dataWithType = {
      ...rowData,
      type: this.props.route.params.type,
    };
    PubSub.publish(
      PubSubEvent.EVENT_MEMBER_CATE_SUCCESS,
      dataWithType,
    );
    this.props.navigation.goBack();
  }
}

const styles = StyleSheet.create({
  list_view: {
    flex: 1,
    backgroundColor: 'white',
  },
  row: {
    flex: 1,
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderColor: '#eeeeee',
  },
  row_second: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  red: {
    color: 'red',
  },
  icon: {
    fontSize: 14,
    flex: 1,
    marginRight: 20,
  },
});
