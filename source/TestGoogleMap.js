import React, { Component } from 'react';
import { View, Text, TextInput } from 'react-native';
import MapView from 'react-native-maps';

export default class TestGoogleMap extends Component {
  constructor(props) {
    super(props);
    this.state = {
      region: {
        latitude: 37.78825,
        longitude: -122.4324,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      },
    };
  }
  onRegionChange(region) {
    this.setState({ region });
  }
  render() {
    return (
      <View style={{ flex: 1, backgroundColor: 'red' }}>
        <Text>Test Map</Text>
        <TextInput testID="test input" />
        <MapView
          testID="Maps"
          style={{ flex: 1 }}
          provider="google"
          maxZoomLevel={14}
          minZoomLevel={10}
          pitchEnabled={false}
          rotateEnabled={false}
          region={this.state.region}
          onRegionChange={this.onRegionChange.bind(this)}
        />
      </View>
    );
  }
}
