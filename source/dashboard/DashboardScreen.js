/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import { Dimensions, Platform, View } from 'react-native';
import I18n from '../I18n';
import { Tme } from '../ThemeStyle';
import { DashboardHelper, HelperMemo } from '../Helper';
import ScrollTabView from '../share/ScrollTabView';
import ActionsView from './ActionsView';
import ScenesView from './ScenesView';
import NodesView from './NodesView';
import RoutinesView from './RoutinesView';
import { NotificationCenter, DASHBOARD_REFRESH } from '../NotificationCenter';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { hideLoading, showLoading } from '../../ILoading';

export default class DashboardScreen extends Component {
  constructor(props) {
    super(props);

    this.state = {
      viewShow: false,
      page: 0,
      finished: false,
      isDataLoaded: false,
      styleFlex: 1,
      actions: [],
      scenes: [],
      nodes: [],
      current_scene_uuid: '',
      routes: [
        { key: 'device', title: I18n.t('home.device').toUpperCase() },
        { key: 'scene', title: I18n.t('home.scene').toUpperCase() },
        { key: 'action', title: I18n.t('action.action').toUpperCase() },
        { key: 'auto', title: I18n.t('global.auto').toUpperCase() },
      ],
      winWidth: this.initWidth(Dimensions.get('window')),
      winHeight: Dimensions.get('window').height,
    };
    this.backgroundImageRef = React.createRef();
    this.tabView = null;

    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          icon={{ icon: 'Ionicons', name: 'information-circle-outline' }}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  _onResize({ window }) {
    this.setState({
      winWidth: this.initWidth(window),
      winHeight: window.height,
    });
  }

  initWidth(window) {
    if (Platform.OS == 'android') {
      return window.width;
    } else {
      if (window.width > window.height) {
        return (
          window.width -
          HelperMemo.STATUS_BAR_HEIGHT -
          HelperMemo.BOTTOM_BAR_HEIGHT -
          22
        );
      } else {
        return window.width;
      }
    }
  }

  componentDidMount() {
    this.DimensionsEvent = Dimensions.addEventListener(
      'change',
      this._onResize.bind(this),
    );
    this.doFetchData();
    var that = this;
    NotificationCenter.addObserver(this, DASHBOARD_REFRESH, () => {
      setTimeout(() => {
        that.doFetchData(false);
      }, 1000);
    });
  }

  componentWillUnmount() {
    if (this.DimensionsEvent) {
      this.DimensionsEvent.remove();
    }
    NotificationCenter.removeObserver(this, DASHBOARD_REFRESH);
  }

  rightClick() {
    this.props.navigation.push('DashboardInfo', {
      title: I18n.t('home.controller_information'),
    });
  }

  showLoad() {
    showLoading();
  }

  hideLoad() {
    hideLoading();
  }

  componentDidAppear() {
    this.setState({
      styleFlex: 1,
    });
  }
  componentDidDisappear() {
    this.setState({
      styleFlex: 0,
    });
  }

  renderScene(navigation, { route }) {
    switch (route.key) {
      case 'device':
        return (
          <NodesView
            nodes={this.state.nodes}
            navigation={navigation}
            parent={this}
          />
        );
      case 'scene':
        return (
          <ScenesView
            scenes={this.state.scenes}
            current_scene_uuid={this.state.current_scene_uuid}
            navigation={navigation}
            parent={this}
          />
        );
      case 'action':
        return (
          <ActionsView
            actions={this.state.actions}
            navigation={navigation}
            parent={this}
          />
        );
      case 'auto':
        return (
          <RoutinesView
            routines={this.state.routines}
            navigation={navigation}
            parent={this}
          />
        );
    }
  }

  render() {
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: Tme('bgColor'),
        }}>
        <View style={{ height: 20 }} />
        <ScrollTabView
          parent={this}
          from="dashboard"
          renderScene={this.renderScene.bind(this, this.props.navigation)}
          routes={this.state.routes}
        />
      </View>
    );
  }

  doFetchData(show = true) {
    if (show) {
      this.showLoad();
    }
    DashboardHelper.httpPOST('/index', {
      ensure: () => {
        if (show) {
          this.hideLoad();
        }
      },
      success: data => {
        console.log(data.nodes);
        this.setState({
          actions: data.actions,
          scenes: data.scenes,
          nodes: data.nodes,
          routines: data.routines,
          current_scene_uuid: data.current_scene_uuid,
        });
      },
      error: data => {
        AlertModal.alert(data);
      },
    });
  }
}
