import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  TextInput,
  TouchableOpacity,
  PixelRatio,
  Platform,
  Text,
  SafeAreaView,
} from 'react-native';
import _ from 'lodash';
import {Tme, Colors} from '../ThemeStyle';
import ScreenSizeContext from '../../WindowResizeContext';

class VerificationCodeInput extends Component {
  constructor(props) {
    super(props);
    this.state = {
      verifyCode: '', // 验证码
      hiddenCode: false,
    };
    this.onTouchInput = this.onTouchInput.bind(this);
    this.textInput;
  }

  static contextType = ScreenSizeContext;

  componentDidMount() {
    setTimeout(() => {
      this.textInput.focus();
    }, 600);
  }

  onTouchInput() {
    const isFocused = this.textInput.isFocused();
    if (!isFocused) {
      this.textInput.focus();
    }
  }

  renderVerifyCode(value) {
    var that = this;
    const {verifyCodeLength} = this.props;
    const paddedValue = _.padEnd(value, verifyCodeLength, ' ');
    const valueArray = paddedValue.split('');
    return (
      <TouchableOpacity
        activeOpacity={1}
        onPress={this.onTouchInput}
        style={[
          styles.verifyTextContainer,
          {width: this.context.winWidth - 60},
        ]}>
        {valueArray.map((digit, index) => {
          if (that.state.hiddenCode) {
            return (
              <View
                key={index}
                style={
                  digit === ' '
                    ? [
                        styles.textInputItem,
                        {borderBottomColor: Tme('cardTextColor')},
                      ]
                    : styles.textInputItemIn
                }>
                {digit === ' ' ? null : (
                  <View style={styles.textInputItemhidden} />
                )}
              </View>
            );
          } else {
            return (
              <View
                key={index}
                style={
                  digit === ' '
                    ? [
                        styles.textInputItem,
                        {borderBottomColor: Tme('cardTextColor')},
                      ]
                    : styles.textInputItemIn
                }>
                <Text style={styles.verifyText}>{digit}</Text>
              </View>
            );
          }
        })}
      </TouchableOpacity>
    );
  }

  render() {
    const {verifyCode} = this.state;
    const {onChangeText, verifyCodeLength} = this.props;
    return (
      <SafeAreaView
        style={[styles.verifyContainer, {width: this.context.winWidth - 60}]}>
        {this.renderVerifyCode(verifyCode)}
        <View style={{width: 0, height: 0}}>
          <TextInput
            ref={ref => {
              this.textInput = ref;
            }}
            underlineColorAndroid="transparent"
            caretHidden
            keyboardType={'numeric'}
            maxLength={verifyCodeLength}
            onChangeText={text => {
              const reg = /^[0-9]*$/;
              if (reg.test(text)) {
                this.setState({verifyCode: text});
                onChangeText(text);
              }
            }}
            value={verifyCode}
          />
        </View>
      </SafeAreaView>
    );
  }

  showCode() {
    this.setState({
      hiddenCode: false,
    });
  }
}

const isIos = Platform.OS === 'ios';

function getRealDP(designPx) {
  return PixelRatio.roundToNearestPixel(designPx / 3);
}
const styles = StyleSheet.create({
  textInput: {
    height: isIos ? 0 : getRealDP(1),
    position: 'absolute',
    bottom: 0,
    left: 0,
  },

  verifyContainer: {
    height: getRealDP(150),
  },

  textInputItem: {
    width: getRealDP(120),
    borderBottomWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textInputItemIn: {
    width: getRealDP(120),
    borderBottomWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomColor: Colors.MainColor,
  },
  textInputItemhidden: {
    width: 14,
    height: 14,
    borderRadius: 7,
    alignItems: 'center',
    justifyContent: 'center',
  },

  verifyText: {
    fontSize: getRealDP(72),
    color: Colors.MainColor,
  },

  verifyTextContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    height: getRealDP(150),
    paddingHorizontal: getRealDP(74),
    position: 'absolute',
    left: 0,
    top: 0,
  },
});
export default VerificationCodeInput;
