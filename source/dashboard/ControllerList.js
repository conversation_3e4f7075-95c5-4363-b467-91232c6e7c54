/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { HelperMemo, DashboardHelper } from '../Helper';
import I18n from '../I18n';
import { Tme, Colors } from '../ThemeStyle';
import ControllerDetect from '../ControllerDetect';
import EmptyView from '../share/EmptyView';
import NavBarView from '../share/NavBarView';
import ShadowView from '../share/ShadowView';
import { mainRadius } from '../Tools';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { NotificationCenter, CLOSE_USER } from '../NotificationCenter';
import { hideLoading, showLoading } from '../../ILoading';

export default class ControllerList extends Component {
  constructor(props) {
    super(props);

    this.state = {
      dataSource: [],
      clickRow: '',
      input_ip: '',
      ip: '',
    };

    this.props.navigation.setOptions({
      headerRight: () => (
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <HeaderRightBtn
            icon={{ name: 'help-outline', icon: 'MaterialIcons' }}
            rightClick={this.helpClick.bind(this)}
          />
          <HeaderRightBtn
            icon={{ name: 'add', icon: 'MaterialIcons' }}
            rightClick={this.addClick.bind(this)}
          />
        </View>
      ),
    });
  }

  componentDidMount() {
    NotificationCenter.addObserver(this, CLOSE_USER, data => {
      this.onSave(data);
    });
  }

  componentWillUnmount() {
    NotificationCenter.removeObserver(this, CLOSE_USER);
  }

  helpClick() {
    this.help();
  }

  addClick() {
    this.add();
  }

  help() {
    this.props.navigation.push('HintView', {
      urlTitle: 'dashboard-help',
      type: 'help',
    });
  }

  add() {
    this.props.navigation.push('InputModal', {
      placeholder: 'x.x.x.x',
      title: 'Local IP',
      inputValue: this.state.input_ip,
      from: 'default',
    });
  }

  onSave(value) {
    if (this.state.ip !== '') {
      this.setState(
        {
          input_ip: value,
        },
        () => {
          this.touchRow({ ip: this.state.input_ip });
        },
      );
    } else {
      this.setState({
        input_ip: '',
      });
    }
  }

  ipCallback(ipRows) {
    var that = this;
    if (ipRows.length > 0) {
      that.setState({
        dataSource: ipRows,
      });
    }
  }

  render() {
    return (
      <NavBarView>
        <ControllerDetect
          onFinished={this.ipCallback.bind(this)}
          navigation={this.props.navigation}
        />
        <View
          style={{
            flex: 1,
          }}>
          <View style={{ padding: 20, flexDirection: 'row' }}>
            <Text
              style={{
                marginTop: 3,
                fontSize: 12,
                fontWeight: '500',
                marginRight: 8,
                color: Tme('cardTextColor'),
              }}>
              {I18n.t('home.controller')}
            </Text>
            <ActivityIndicator size="small" color={Colors.MainColor} />
          </View>
          <FlatList
            style={{ paddingHorizontal: 16, marginBottom: 30, flex: 1 }}
            ref={flatList => (this._flatList = flatList)}
            data={this.state.dataSource}
            renderItem={this._renderRow.bind(this)}
            numColumns={1}
            ListEmptyComponent={() => <EmptyView />}
            ListFooterComponent={() => <View style={{ height: 20 }} />}
            onEndReachedThreshold={0.1}
            keyExtractor={(item, index) => index.toString()}
          />
        </View>
      </NavBarView>
    );
  }

  _renderRow({ item, index }) {
    var html;
    var rowData = item;
    html = (
      <View style={{ marginBottom: 12, marginHorizontal: 8, marginTop: 8 }}>
        <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.touchRow.bind(this, rowData)}
            style={[
              {
                height: 80,
                borderRadius: mainRadius(),
                paddingHorizontal: 20,
                backgroundColor: Tme('cardColor'),
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
              },
            ]}>
            <View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'flex-start',
              }}>
              <Text
                style={{
                  fontSize: 16,
                  color: Tme('cardTextColor'),
                }}>
                {this.plusXing(rowData.sn, 4, 4)}
              </Text>
            </View>
            <View
              style={{
                justifyContent: 'center',
                flex: 1,
              }}>
              <View
                style={{
                  justifyContent: 'center',
                  alignItems: 'flex-end',
                }}>
                <Text
                  style={{
                    fontSize: 14,
                    color: Tme('cardTextColor'),
                  }}>
                  {rowData.ip}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        </ShadowView>
      </View>
    );
    // }
    return <View>{html}</View>;
  }

  touchRow(rowData) {
    showLoading();
    const sn = HelperMemo.user_data.sn;
    var body = {
      sn: rowData.sn,
      salt: sn ? sn.salt : '',
    };
    HelperMemo.dashboard_ip = rowData.ip;
    DashboardHelper.httpPOST(
      '/auth',
      {
        ensure: () => {
          hideLoading();
        },
        success: data => {
          if (data.sessionid) {
            HelperMemo.dashboard_sessionid = data.sessionid;
            if (data.need_set_password) {
              this.props.navigation.push('DashboardSignUp', {
                type: 'set_password',
              });
            } else {
              this.props.navigation.goBack();
              this.props.navigation.push('DashboardScreen', {
                title: I18n.t('global.dashboard'),
              });
            }
          } else {
            AlertModal.alert(I18n.t('home.failed'), I18n.t('home.try_again'));
          }
        },
        error: data => {
          this.authError(data);
        },
      },
      body,
    );
  }

  authError(data) {
    switch (data) {
      case 'sn_auth_failed':
      case 'sn_salt_empty':
        AlertModal.alert(I18n.t('home.error'), data);
        break;
      case 'passwd_auth_failed':
        AlertModal.alert('dashboard.pin_error');
        break;
      case 'no_initial_password':
        AlertModal.alert('dashboard.no_initial_password');
        break;
    }
  }

  plusXing(str, frontLen, endLen) {
    var len = str.length - frontLen - endLen;
    var xing = '';
    for (var i = 0; i < len; i++) {
      xing += '*';
    }
    var res =
      str.substring(0, frontLen) + xing + str.substring(str.length - endLen);
    return res.toUpperCase();
  }

  closeModal() {
    this.setState({
      modalVisible: false,
    });
  }
}
