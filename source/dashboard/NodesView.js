/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import { View, FlatList, Text } from 'react-native';
import { Tme } from '../ThemeStyle';
import { HelperMemo, Helper } from '../Helper';
import _ from 'lodash';
import CardView from '../share/CardView';
import SwitchBtn from '../share/SwitchBtn';
import memoizeOne from 'memoize-one';
import DeviceControl from '../DeviceControl';
import EmptyView from '../share/EmptyView';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { getDeviceIcon, getOneSwitchSpec, mainRadius, mainTitle } from '../Tools';

export default class NodesView extends Component {
  constructor(props) {
    super(props);
    this._flatList = null;
  }

  render() {
    return (
      <FlatList
        style={{
          flex: 1,
          paddingHorizontal: 16,
          paddingTop: 20,
          borderRadius: mainRadius(),
          backgroundColor: Tme('cardColor2'),
        }}
        columnWrapperStyle={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
        ref={flatList => (this._flatList = flatList)}
        data={this.props.nodes}
        renderItem={this._renderRow.bind(this)}
        numColumns={2}
        ListEmptyComponent={() => <EmptyView />}
        ListFooterComponent={() => <View style={{ height: 20 }} />}
        onEndReachedThreshold={0.1}
        keyExtractor={(item, index) => index.toString()}
      />
    );
  }

  _renderRow({ item, index }) {
    return (
      <DeviceList
        item={item}
        navigation={this.props.navigation}
        winWidth={this.props.parent.state.winWidth}
      />
    );
  }
}

class DeviceList extends Component {
  constructor(props) {
    super(props);
  }

  hasSwitch = memoizeOne(propsDevice => {
    var spec = getOneSwitchSpec(propsDevice);
    if (spec) {
      return spec;
    } else {
      return null;
    }
  });

  render() {
    var isAlive = true;

    if (this.props.item.dv_type == '433') {
      isAlive = Helper.utc() - this.props.item.last_received_update_time < 7200;
    } else {
      isAlive =
        this.props.item.is_failed !== undefined
          ? !this.props.item.is_failed
          : true;
    }
    var switchSpec = false;
    return (
      <View style={{ marginHorizontal: 4, marginBottom: 20 }}>
        <CardView
          withWaveBg={true}
          onChange={this.onClick.bind(this, this.props.item)}
          styles={{
            width: this.props.winWidth / 2 - 30,
            padding: 12,
            borderRadius: mainRadius(),
            height: 100,
          }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: 12,
            }}>
            {getDeviceIcon(this.props.item)}
            {switchSpec ? (
              <SwitchBtn
                key={Math.random()}
                cusStyle={{ marginRight: -3 }}
                trackColor={{ true: '#7f8fa4' }}
                value={_.toInteger(switchSpec.real_value) > 0}
                change={this.change.bind(this, switchSpec)}
              />
            ) : null}
          </View>
          <Text
            numberOfLines={1}
            style={[
              {
                color: Tme('cardTextColor'),
                fontSize: mainTitle(),
                width: this.props.winWidth / 2 - 100,
                fontWeight: '500',
              },
            ]}>
            {this.props.item.display_name}
          </Text>
          <View style={{ position: 'absolute', right: 8, bottom: 8 }}>
            {isAlive ? null : (
              <Ionicons
                name="information-circle-outline"
                size={16}
                color="#fc577acc"
              />
            )}
          </View>
        </CardView>
      </View>
    );
  }

  change(switchSpec, e) {
    var param = e ? 255 : 0;
    var deviceControl = new DeviceControl({
      spec: switchSpec,
      param: param,
      successCb: false,
      runCMD: true,
      sn_id: HelperMemo.DASHBOARD_SN_ID,
    });
    deviceControl.switch();
  }

  onClick(item) {
    this.props.navigation.push('DashboardDevicesView', {
      index: item.index,
    });
  }
}
