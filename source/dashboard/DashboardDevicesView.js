import React, { Component } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import I18n from '../I18n';
import { Helper, HelperMemo, DEVICE_HEIGHT, DashboardHelper } from '../Helper';
import DeviceItemCCSpecs from '../device_spec/DeviceItemCCSpecs';
import _ from 'lodash';
import { Tme, Colors } from '../ThemeStyle';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { getDeviceIcon, mainRadius } from '../Tools';
import AlertModal from '../share/AlertModal';
import { hideLoading, showLoading } from '../../ILoading';

export default class DashboardDevicesView extends Component {
  static defaultProps = {
    draggableRange: {
      top: DEVICE_HEIGHT,
      bottom: 138,
    },
  };

  constructor(props) {
    super(props);

    this.state = {
      showView: false,
      device: {},
      sn: 'dashboard',
    };
  }

  componentDidMount() {
    this.doFetchData(true);
  }

  componentWillUnmount() {
    // clearInterval(this.timer)
  }

  doFetchData(loading) {
    if (loading) {
      showLoading();
    }
    var that = this;
    DashboardHelper.httpPOST('/nodes', {
      ensure: () => {
        if (loading) {
          hideLoading();
        }
      },
      success: data => {
        _.forEach(data.nodes, (v, k) => {
          if (v.index == that.props.route.params.index) {
            v.sn_id = HelperMemo.DASHBOARD_SN_ID;
            that.setState({
              device: v,
              showView: true,
            });
          }
        });
      },
      error: data => {
        AlertModal.alert(
          I18n.t('home.warning_message'),
          I18n.t('home.try_again'),
        );
      },
    });
  }

  render() {
    if (this.state.showView) {
      var icon = getDeviceIcon(this.state.device);
      var isAlive = true;

      if (this.state.device.dv_type == '433') {
        isAlive =
          Helper.utc() - this.state.device.last_received_update_time < 7200;
      } else {
        isAlive =
          this.state.device.is_failed !== undefined
            ? !this.props.route.params.device.is_failed
            : true;
      }

      return (
        <View
          style={{
            flex: 1,
            backgroundColor: Tme('bgColor'),
          }}>
          <View style={{ padding: 20 }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: 12,
              }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                {icon}
                <Text
                  style={[
                    { color: Tme('cardTextColor'), marginLeft: 12 },
                    Colors.RoomTitleFontStyle,
                  ]}>
                  {this.state.device.display_name}
                </Text>
              </View>
              {isAlive ? null : (
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={() => {
                    AlertModal.alert(
                      I18n.t('home.warning_message'),
                      I18n.t('global.device_failed'),
                    );
                  }}>
                  <Ionicons
                    name="information-circle-outline"
                    size={22}
                    color="#fc577acc"
                  />
                </TouchableOpacity>
              )}
            </View>
          </View>
          <View
            style={{
              backgroundColor: Tme('cardColor2'),
              paddingTop: 20,
              flex: 1,
              borderRadius: mainRadius(),
            }}>
            <ScrollView
              contentContainerStyle={{ flexGrow: 1 }}
              showsVerticalScrollIndicator={false}>
              <DeviceItemCCSpecs
                parent={this}
                viewType="dashboard"
                navigation={this.props.navigation}
                device={this.state.device}
                sn={this.state.sn}
                from="dashboard"
              />
            </ScrollView>
          </View>
        </View>
      );
    } else {
      return (
        <View
          style={{
            flex: 1,
            backgroundColor: Tme('bgColor'),
          }}
        />
      );
    }
  }
}
