/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  // Image
} from 'react-native';
import { DashboardHelper } from '../Helper';
import I18n from '../I18n';
import _ from 'lodash';
import { Tme, Colors, IsDark } from '../ThemeStyle';
import ActionSheet from '@alessiocancian/react-native-actionsheet';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import memoizeOne from 'memoize-one';
import EmptyView from '../share/EmptyView';
import moment from 'moment';
import { getAutomationIcon, mainRadius } from '../Tools';
import { Shadow } from 'react-native-shadow-2';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import AlertModal from '../share/AlertModal';
import GrayscaledImage from '../share/GrayscaledImage';

const weekdays = {
  1: 'monday',
  2: 'tuesday',
  3: 'wednesday',
  4: 'thursday',
  5: 'friday',
  6: 'saturday',
  0: 'sunday',
};

class RoutinesView extends Component {
  constructor(props) {
    super(props);

    this.state = {
      isFlash: false,
      is_foreign: false,
      clickRow: '',
    };
  }

  routineInit = memoizeOne(routines => {
    var temp = [];
    _.forEach(routines, (v, k) => {
      if (v.is_enabled) {
        temp.push(v);
      }
    });
    return temp;
  });

  render() {
    var routines = this.routineInit(this.props.routines);
    return (
      <View style={{ flex: 1 }}>
        <FlatList
          style={{
            flex: 1,
            paddingHorizontal: 20,
            paddingTop: 20,
            borderRadius: mainRadius(),
            backgroundColor: Tme('cardColor2'),
          }}
          ref={flatList => (this._flatList = flatList)}
          data={routines}
          contentContainerStyle={{ alignItems: 'center' }}
          renderItem={this._renderRow.bind(this)}
          numColumns={1}
          onEndReachedThreshold={0.1}
          ListEmptyComponent={() => <EmptyView />}
          ListFooterComponent={() => <View style={{ height: 20 }} />}
          keyExtractor={(item, index) => index.toString()}
        />
        <ActionSheet
          ref={o => (this.actionSheet = o)}
          options={[I18n.t('home.cancel'), I18n.t('home.remove_btn')]}
          cancelButtonIndex={0}
          userInterfaceStyle={IsDark() ? 'dark' : 'light'}
          destructiveButtonIndex={1}
          theme="ios"
          styles={{
            cancelButtonBox: {
              height: 50,
              marginTop: 6,
              alignItems: 'center',
              justifyContent: 'center',
            },
            buttonBox: {
              height: 50,
              alignItems: 'center',
              justifyContent: 'center',
            },
          }}
          onPress={index => {
            this.sheetClick(index - 1);
          }}
        />
      </View>
    );
  }

  _renderRow({ item, index }) {
    var image = getAutomationIcon(item);
    return (
      <Shadow
        distance={3}
        offset={[0, 0]}
        startColor={IsDark() ? '#00000007' : '#00000007'}>
        <View
          style={[
            {
              backgroundColor: Tme('cardColor2'),
              marginBottom: 1,
              width: this.props.parent.state.winWidth - 40,
              paddingBottom: 20,
            },
            index === 0 && {
              borderTopRightRadius: mainRadius(),
              borderTopLeftRadius: mainRadius(),
            },
            index + 1 === this.props.routines.length && {
              borderBottomLeftRadius: mainRadius(),
              borderBottomRightRadius: mainRadius(),
            },
          ]}>
          <View
            style={{
              paddingLeft: 20,
              paddingRight: 10,
              paddingTop: 20,
            }}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <GrayscaledImage
                  source={image}
                  style={{ width: 24, height: 24, marginRight: 8 }}
                />
                {/* <Image
                  source={image}
                  style={{width: 24, height: 24, marginRight: 8}}
                /> */}
                <Text
                  numberOfLines={2}
                  style={[
                    {
                      color: Tme('cardTextColor'),
                    },
                    Colors.CardFontStyle,
                  ]}>
                  {item.name}
                </Text>
              </View>
              <TouchableOpacity
                activeOpacity={0.8}
                hitSlop={{ top: 20, right: 20, bottom: 20, left: 20 }}
                onPress={this.sheetShow.bind(this, item)}>
                <MCIcons
                  name="dots-horizontal"
                  size={22}
                  color={Colors.MainColor}
                />
              </TouchableOpacity>
            </View>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginTop: 5,
              }}>
              <View style={{ flex: 5 }}>{this.textShow(item)}</View>
            </View>
          </View>
        </View>
      </Shadow>
    );
  }

  sheetShow(item) {
    this.setState(
      {
        clickRow: item,
      },
      () => {
        this.actionSheet.show();
      },
    );
  }

  sheetClick(index) {
    if (index === 0) {
      var that = this;
      AlertModal.alert(I18n.t('global.activate_sure'), '', [
        {
          text: I18n.t('home.cancel'),
          onPress: () => { },
        },
        {
          text: I18n.t('home.confirm'),
          onPress: () => {
            this.props.parent.showLoading();
            DashboardHelper.httpPOST(
              '/delete_routine',
              {
                ensure: () => {
                  this.props.parent.hideLoading();
                },
                success: data => {
                  that.props.parent.doFetchData();
                },
                error: data => {
                  AlertModal.alert(I18n.t('home.try_again'));
                },
              },
              { uuid: this.state.clickRow.uuid },
            );
          },
        },
      ]);
    }
  }

  formatTime(time) {
    return moment.utc(time).format('HH:mm');
  }

  textShow(data) {
    var week = [];

    if (data.week_day) {
      if (data.week_day.length == 7) {
        week.push(I18n.t('automation.everyday'));
      } else {
        if (data.week_day.sort().join(',') == '1,2,3,4,5') {
          week.push(I18n.t('automation.workday'));
        } else {
          data.week_day.sort().map((v, i) => {
            week.push(I18n.t('setting.' + weekdays[v]));
          });
        }
      }
    }

    var startTime = `${data.begin_at.toString().substring(2, 4)}:${data.begin_at
      .toString()
      .substring(4, 6)}`;

    var endTime = `${data.end_at.toString().substring(2, 4)}:${data.end_at
      .toString()
      .substring(4, 6)}`;

    return (
      <View style={{ marginTop: 10 }}>
        <View style={{ flexDirection: 'row' }}>
          <Text style={{ color: Tme('smallTextColor') }} numberOfLines={2}>
            {week.join(', ')}
          </Text>
        </View>
        <View style={{ flexDirection: 'row', marginTop: 4 }}>
          {data.type == 'sun' ? null : (
            <Text style={{ color: Tme('smallTextColor'), marginRight: 4 }}>
              {startTime}
            </Text>
          )}
          {data.type === 'sun' || data.type === 'time' ? null : (
            <MaterialIcons
              size={18}
              color={Tme('smallTextColor')}
              name="trending-flat"
              style={{ marginRight: 4 }}
            />
          )}
          {data.type === 'time' || data.type === 'sun' ? null : (
            <Text style={{ color: Tme('smallTextColor'), marginRight: 4 }}>
              {endTime}
            </Text>
          )}
          <Text style={{ color: Tme('smallTextColor') }}>
            {data.is_cycle
              ? I18n.t('routine.run_multiple')
              : I18n.t('routine.run_once')}
          </Text>
        </View>
        {data.target_type === 'device' && (
          <View
            style={{
              flexDirection: 'row',
              marginTop: 4,
              alignItems: 'center',
            }}>
            <Text
              style={{
                marginRight: 5,
                color: Tme('smallTextColor'),
                fontSize: 11,
              }}>
              {data.targets.length}
            </Text>
            <Text style={{ color: Tme('smallTextColor'), fontSize: 11 }}>
              {I18n.t('global.device_count')}
            </Text>
          </View>
        )}
      </View>
    );
  }
}

RoutinesView = RoutinesView;
export default RoutinesView;
