/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import {
  View,
  FlatList,
  Text,
  // Image
} from 'react-native';
import { Tme, Colors, IsDark } from '../ThemeStyle';
import { DashboardHelper } from '../Helper';
import I18n from '../I18n';
import CardView from '../share/CardView';
import EmptyView from '../share/EmptyView';
import { getImageFromKey, getSceneName, mainRadius } from '../Tools';
import AlertModal from '../share/AlertModal';
import GrayscaledImage from '../share/GrayscaledImage';

class ScenesView extends Component {
  constructor(props) {
    super(props);

    this._flatList;
  }

  render() {
    return (
      <FlatList
        style={{
          flex: 1,
          paddingHorizontal: 16,
          paddingTop: 20,
          borderRadius: mainRadius(),
          backgroundColor: Tme('cardColor2'),
        }}
        columnWrapperStyle={{
          justifyContent: 'space-between',
        }}
        ref={flatList => (this._flatList = flatList)}
        data={this.props.scenes}
        renderItem={this._renderRow.bind(this)}
        ListEmptyComponent={() => <EmptyView />}
        numColumns={2}
        ListFooterComponent={() => <View style={{ height: 20 }} />}
        onEndReachedThreshold={0.1}
        keyExtractor={(item, index) => index.toString()}
      />
    );
  }

  _renderRow({ item, index }) {
    return (
      <View style={{ marginHorizontal: 4, marginBottom: 20 }}>
        <CardView
          withWaveBg={true}
          withWaveBgOnlyOne={
            item.uuid === this.props.current_scene_uuid && !IsDark()
          }
          onChange={this.onClick.bind(this, item)}
          styles={[
            {
              width: this.props.parent.state.winWidth / 2 - 30,
              padding: 12,
              borderRadius: mainRadius(),
              flexDirection: 'row',
              alignItems: 'center',
            },
          ]}>
          {/* <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: 20,
            }}> */}
          <GrayscaledImage
            source={getImageFromKey(item.icon).value}
            style={{ width: 32, height: 32 }}
          />
          {/* </View> */}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginLeft: 8,
              justifyContent: 'space-between',
            }}>
            <Text
              numberOfLines={2}
              style={[
                {
                  color: Tme('cardTextColor'),
                  width: this.props.parent.state.winWidth / 2 - 100,
                },
                Colors.CardFontStyle,
              ]}>
              {getSceneName(item.name)}
            </Text>
            {item.uuid === this.props.current_scene_uuid && (
              <View
                style={{
                  height: 6,
                  backgroundColor: Colors.MainColor,
                  position: 'absolute',
                  // left: 10,
                  // bottom: 5,
                  top: 5,
                  left: -8,
                  width: 6,
                  borderRadius: 3,
                }}
              />
            )}
          </View>
        </CardView>
      </View>
    );
  }

  onClick(item) {
    var that = this;
    AlertModal.alert(
      getSceneName(item.name),
      I18n.t('global.activate_scene'),
      [
        {
          text: I18n.t('home.cancel'),
          onPress: () => { },
        },
        {
          text: I18n.t('home.confirm'),
          onPress: () => {
            this.props.parent.showLoading();
            DashboardHelper.httpPOST(
              '/control',
              {
                ensure: () => {
                  this.props.parent.hideLoading();
                },
                success: data => {
                  that.props.parent.doFetchData();
                },
                error: data => {
                  AlertModal.alert(I18n.t('home.try_again'));
                },
              },
              {
                type: 'scene',
                uuid: item.uuid,
                cmd: JSON.stringify(item.commands),
              },
            );
          },
        },
      ],
    );
  }
}
ScenesView = ScenesView;
export default ScenesView;
