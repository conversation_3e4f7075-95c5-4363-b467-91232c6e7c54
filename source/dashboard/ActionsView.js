/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import {
  FlatList,
  Text,
  View,
  //  Image
} from 'react-native';
import { Tme, Colors } from '../ThemeStyle';
import { DashboardHelper } from '../Helper';
import I18n from '../I18n';
import CardView from '../share/CardView';
import EmptyView from '../share/EmptyView';
import { getImageFromKey, mainRadius } from '../Tools';
import AlertModal from '../share/AlertModal';
import GrayscaledImage from '../share/GrayscaledImage';

export default class ActionView extends Component {
  constructor(props) {
    super(props);
    this._flatList = null;
  }

  render() {
    return (
      <FlatList
        style={{
          flex: 1,
          paddingHorizontal: 16,
          paddingTop: 20,
          borderRadius: mainRadius(),
          backgroundColor: Tme('cardColor2'),
        }}
        columnWrapperStyle={{ justifyContent: 'space-between' }}
        ref={flatList => (this._flatList = flatList)}
        data={this.props.actions}
        renderItem={this._renderRow.bind(this)}
        numColumns={2}
        onEndReachedThreshold={0.1}
        ListFooterComponent={() => <View style={{ height: 20 }} />}
        ListEmptyComponent={() => <EmptyView />}
        keyExtractor={(item, index) => index.toString()}
      />
    );
  }

  _renderRow({ item, index }) {
    return (
      <View style={{ marginHorizontal: 4, marginBottom: 20 }}>
        <CardView
          withWaveBg={true}
          onChange={this.onClick.bind(this, item)}
          styles={[
            {
              width: this.props.parent.state.winWidth / 2 - 30,
              padding: 12,
              borderRadius: mainRadius(),
              flexDirection: 'row',
            },
          ]}>

          <GrayscaledImage
            source={getImageFromKey(item.icon).value}
            style={{ width: 32, height: 32 }}
          />
          {/* </View> */}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginLeft: 8,
            }}>
            <Text
              numberOfLines={2}
              style={[
                {
                  width: this.props.parent.state.winWidth / 2 - 100,
                  color: Tme('cardTextColor'),
                },
                Colors.CardFontStyle,
              ]}>
              {item.name}
            </Text>
          </View>
        </CardView>
      </View>
    );
  }

  onClick(item) {
    var that = this;
    AlertModal.alert(item.name, I18n.t('global.activate_action'), [
      {
        text: I18n.t('home.cancel'),
        onPress: () => { },
      },
      {
        text: I18n.t('home.confirm'),
        onPress: () => {
          this.props.parent.showLoading();
          DashboardHelper.httpPOST(
            '/control',
            {
              ensure: () => {
                this.props.parent.hideLoading();
              },
              success: data => {
                that.props.parent.doFetchData();
              },
              error: data => {
                AlertModal.alert(I18n.t('home.try_again'));
              },
            },
            {
              type: 'action',
              uuid: item.uuid,
              cmd: JSON.stringify(item.commands),
            },
          );
        },
      },
    ]);
  }
}
