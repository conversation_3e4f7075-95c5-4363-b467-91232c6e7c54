import React, {Component} from 'react';
import {View, Text, TouchableOpacity, Keyboard, ScrollView} from 'react-native';
import I18n from '../I18n';
import {Tme, Colors} from '../ThemeStyle';
import {DashboardHelper} from '../Helper';
import VerificationCodeInput from './VerificationCodeInput';
import NavBarView from '../share/NavBarView';
import CardView from '../share/CardView';
import {Toast} from '../Toast';
import AlertModal from '../share/AlertModal';
import {hideLoading, showLoading} from '../../ILoading';

export default class DashboardSignUp extends Component {
  constructor(props) {
    super(props);

    this.state = {
      value: '',
    };
  }

  render() {
    return (
      <NavBarView>
        <ScrollView style={{flex: 1}}>
          <View
            style={{flex: 1, alignItems: 'center', marginTop: 20, padding: 20}}>
            <CardView
              withWaveBg={true}
              styles={{
                height: 300,
                padding: 20,
                borderRadius: 8,
                alignItems: 'center',
              }}>
              <View
                style={{marginTop: 40, alignItems: 'center', marginBottom: 20}}>
                <Text
                  style={{
                    fontSize: 24,
                    color: Tme('cardTextColor'),
                    marginBottom: 20,
                  }}>
                  {this.props.route.params.type == 'change'
                    ? I18n.t('dashboard.change_pin')
                    : I18n.t('dashboard.set_pin')}
                </Text>

                <VerificationCodeInput
                  verifyCodeLength={6}
                  onChangeText={value => {
                    this.setState({value});
                  }}
                />
              </View>
              <TouchableOpacity
                activeOpacity={0.8}
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 40,
                  width: 120,
                  backgroundColor: Colors.MainColor,
                  borderRadius: 20,
                  marginTop: 30,
                }}
                onPress={this._loginBtnEvent.bind(this)}>
                <Text style={{color: 'white'}}>{I18n.t('home.save')}</Text>
              </TouchableOpacity>
            </CardView>
          </View>
        </ScrollView>
      </NavBarView>
    );
  }

  _loginBtnEvent() {
    Keyboard.dismiss();
    showLoading();
    DashboardHelper.httpPOST(
      '/set_password',
      {
        ensure: () => {
          hideLoading();
        },
        success: data => {
          if (this.props.route.params.type == 'change') {
            this.props.navigation.goBack();
            Toast.show();
          } else {
            this.props.navigation.push('DashboardLogin', {
              title: I18n.t('global.dashboard'),
            });
          }
          this.handleSuccess();
        },
        error: data => {
          AlertModal.alert(I18n.t('home.try_again'));
        },
      },
      {password: this.state.value},
    );
  }

  handleSuccess() {
    Toast.show();
  }
}
