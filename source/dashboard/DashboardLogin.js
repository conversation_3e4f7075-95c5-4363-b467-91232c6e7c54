import React, {Component} from 'react';
import {View, Text, TouchableOpacity, Keyboard, ScrollView} from 'react-native';
import I18n from '../I18n';
import {Tme, Colors} from '../ThemeStyle';
import {HelperMemo, DashboardHelper} from '../Helper';
import VerificationCodeInput from './VerificationCodeInput';
import NavBarView from '../share/NavBarView';
import CardView from '../share/CardView';
import AlertModal from '../share/AlertModal';
import {hideLoading, showLoading} from '../../ILoading';

export default class DashboardLogin extends Component {
  constructor(props) {
    super(props);

    this.state = {
      value: '',
    };
  }

  render() {
    return (
      <NavBarView>
        <ScrollView style={{flex: 1}}>
          <View
            style={{flex: 1, alignItems: 'center', marginTop: 20, padding: 20}}>
            <CardView
              withWaveBg={true}
              styles={{
                height: 300,
                padding: 20,
                borderRadius: 8,
                alignItems: 'center',
              }}>
              <View
                style={{marginTop: 40, alignItems: 'center', marginBottom: 20}}>
                <Text
                  style={{
                    fontSize: 24,
                    color: Tme('cardTextColor'),
                    marginBottom: 20,
                  }}>
                  {I18n.t('dashboard.enter_pin')}
                </Text>
                <VerificationCodeInput
                  verifyCodeLength={6}
                  onChangeText={value => {
                    this.setState({value});
                  }}
                />
              </View>
              <TouchableOpacity
                activeOpacity={0.8}
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: 40,
                  width: 120,
                  backgroundColor: Colors.MainColor,
                  borderRadius: 20,
                  marginTop: 30,
                }}
                onPress={this._loginBtnEvent.bind(this)}>
                <Text style={{color: 'white'}}>{I18n.t('home.sign')}</Text>
              </TouchableOpacity>
            </CardView>
          </View>
        </ScrollView>
      </NavBarView>
    );
  }

  _loginBtnEvent() {
    Keyboard.dismiss();
    showLoading();
    DashboardHelper.httpPOST(
      '/auth',
      {
        ensure: () => {
          hideLoading();
        },
        success: data => {
          if (data.sessionid) {
            HelperMemo.dashboard_sessionid = data.sessionid;
            this.props.navigation.popToTop();
            this.props.navigation.push('DashboardScreen', {
              title: I18n.t('global.dashboard'),
            });
          } else {
            AlertModal.alert(I18n.t('home.try_again'));
          }
        },
        error: data => {
          if (data == 'no_initial_password') {
            AlertModal.alert(I18n.t('dashboard.no_initial_password'));
          } else {
            AlertModal.alert(I18n.t('dashboard.pin_error'));
          }
        },
      },
      {password: this.state.value},
    );
  }
}
