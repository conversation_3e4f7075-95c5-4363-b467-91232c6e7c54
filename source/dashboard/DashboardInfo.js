import React, {Component} from 'react';
import {View, Text, FlatList, TouchableOpacity, StyleSheet} from 'react-native';
import {Tme} from '../ThemeStyle';
import {DashboardHelper} from '../Helper';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import I18n from '../I18n';
import EmptyView from '../share/EmptyView';
import NavBarView from '../share/NavBarView';
import {hideLoading, showLoading} from '../../ILoading';
export default class DashboardInfo extends Component {
  constructor(props) {
    super(props);

    this.state = {
      data: [],
    };
    this._flatList = null;
  }

  componentDidMount() {
    this.doFetchData();
  }

  doFetchData() {
    var that = this;
    showLoading();
    DashboardHelper.httpPOST('/controller_info', {
      ensure: () => {
        hideLoading();
      },
      success: data => {
        that.setState({
          data: [
            {name: 'Timezone', value: data.timezone},
            {name: 'Serial Number', value: data.sn.toUpperCase()},
            {name: 'Time', value: data.time},
            {name: 'password', value: 'change_pin'},
          ],
        });
      },
    });
  }

  render() {
    return (
      <NavBarView>
        <FlatList
          style={{paddingTop: 20}}
          ref={flatList => (this._flatList = flatList)}
          data={this.state.data}
          renderItem={this._renderRow.bind(this)}
          ItemSeparatorComponent={this.line.bind(this)}
          numColumns={1}
          ListEmptyComponent={() => <EmptyView />}
          onEndReachedThreshold={0.1}
          keyExtractor={(item, index) => index.toString()}
        />
      </NavBarView>
    );
  }

  _renderRow({item, index}) {
    if (item.name == 'password') {
      return (
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={this.click.bind(this)}
          style={{marginTop: 20, backgroundColor: Tme('cardColor')}}>
          <View style={styles.row}>
            <View style={styles.rowView}>
              <Text style={{color: Tme('cardTextColor'), fontSize: 14}}>
                {I18n.t('dashboard.' + item.value)}
              </Text>
            </View>
            <View style={styles.rowView}>
              <MaterialIcons
                name="keyboard-arrow-right"
                size={20}
                color={Tme('textColor')}
              />
            </View>
          </View>
        </TouchableOpacity>
      );
    } else {
      return (
        <View style={{backgroundColor: Tme('cardColor')}}>
          <View style={styles.row}>
            <View style={styles.rowView}>
              <Text style={{color: Tme('cardTextColor'), fontSize: 14}}>
                {item.name}
              </Text>
            </View>
            <View style={styles.rowView}>
              <Text style={{color: Tme('textColor'), fontSize: 14}}>
                {item.value}
              </Text>
            </View>
          </View>
        </View>
      );
    }
  }

  line() {
    return <View style={{height: 2, backgroundColor: Tme('bgColor')}} />;
  }

  click() {
    this.props.navigation.push('DashboardSignUp', {
      type: 'change',
    });
  }
}

const styles = StyleSheet.create({
  row: {
    marginLeft: 18,
    marginRight: 20,
    flexDirection: 'row',
    paddingVertical: 16,
    justifyContent: 'space-between',
  },
  rowView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
