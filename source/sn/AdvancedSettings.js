import React, {Component} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from 'react-native';
import _ from 'lodash';
import {Helper} from '../Helper';
import I18n from '../I18n';
import {Tme} from '../ThemeStyle';
import Icon from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
const updated = 'yes';
import NavBarView from '../share/NavBarView';
import AlertModal from '../share/AlertModal';
import {hideLoading, showLoading} from '../../ILoading';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';

export default class AdvancedSettings extends Component {
  constructor(props) {
    super(props);
  }

  componentWillUnmount() {}
  componentDidMount() {}

  editController() {
    this.props.navigation.push('ChangeSnName', {
      sn: this.props.route.params.sn,
      name: this.props.route.params.sn_name,
      type: 'edit',
      title: I18n.t('home.edit_controller'),
    });
  }

  render() {
    return (
      <NavBarView>
        <ScrollView showsVerticalScrollIndicator={false} style={styles.tabView}>
          <View
            style={{
              marginTop: 20,
              paddingBottom: 20,
            }}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.controllerInfo.bind(this)}
              style={{backgroundColor: Tme('cardColor')}}>
              <View
                style={{
                  marginLeft: 18,
                  marginRight: 20,
                  flexDirection: 'row',
                  paddingVertical: 16,
                  justifyContent: 'space-between',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <MaterialIcons
                    name="router"
                    size={20}
                    color={Tme('textColor')}
                  />
                  <Text style={{marginLeft: 12, color: Tme('cardTextColor')}}>
                    {I18n.t('home.controller_information')}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={20}
                    color={Tme('textColor')}
                  />
                </View>
              </View>
            </TouchableOpacity>
            <View style={{height: 2}} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.editController.bind(this)}
              style={{backgroundColor: Tme('cardColor')}}>
              <View
                style={{
                  marginLeft: 18,
                  marginRight: 20,
                  flexDirection: 'row',
                  paddingVertical: 16,
                  justifyContent: 'space-between',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <FontAwesome name="edit" size={20} color={Tme('textColor')} />
                  <Text style={{marginLeft: 12, color: Tme('cardTextColor')}}>
                    {I18n.t('home.edit_controller')}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={20}
                    color={Tme('textColor')}
                  />
                </View>
              </View>
            </TouchableOpacity>
            <View style={{height: 20}} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.wifiSetting.bind(this)}
              style={{backgroundColor: Tme('cardColor')}}>
              <View
                style={{
                  marginLeft: 18,
                  marginRight: 20,
                  flexDirection: 'row',
                  paddingVertical: 16,
                  justifyContent: 'space-between',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Icon name="wifi" size={20} color={Tme('textColor')} />
                  <Text style={{marginLeft: 12, color: Tme('cardTextColor')}}>
                    {I18n.t('global.wifi_connection')}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={20}
                    color={Tme('textColor')}
                  />
                </View>
              </View>
            </TouchableOpacity>
            <View style={{height: 20}} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.click.bind(this, 'update')}
              style={{backgroundColor: Tme('cardColor')}}>
              <View
                style={{
                  marginLeft: 18,
                  marginRight: 20,
                  flexDirection: 'row',
                  paddingVertical: 16,
                  justifyContent: 'space-between',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <MaterialIcons
                    name="cloud-download"
                    size={20}
                    color={Tme('textColor')}
                  />
                  <Text style={{marginLeft: 11, color: Tme('cardTextColor')}}>
                    {I18n.t('setting.update_online')}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={20}
                    color={Tme('textColor')}
                  />
                </View>
              </View>
            </TouchableOpacity>
            <View style={{height: 2}} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.click.bind(this, 'restart')}
              style={{backgroundColor: Tme('cardColor')}}>
              <View
                style={{
                  marginLeft: 18,
                  marginRight: 20,
                  flexDirection: 'row',
                  paddingVertical: 16,
                  justifyContent: 'space-between',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Icon name="refresh" size={20} color={Tme('textColor')} />
                  <Text style={{marginLeft: 11, color: Tme('cardTextColor')}}>
                    {I18n.t('setting.restart')}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={20}
                    color={Tme('textColor')}
                  />
                </View>
              </View>
            </TouchableOpacity>
            {/* <View style={{height: 2}} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.click.bind(this, 'reset')}
              style={{backgroundColor: Tme('cardColor')}}>
              <View
                style={{
                  marginLeft: 18,
                  marginRight: 20,
                  flexDirection: 'row',
                  paddingVertical: 16,
                  justifyContent: 'space-between',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <MaterialIcons
                    name="settings-backup-restore"
                    size={20}
                    color={Tme('textColor')}
                  />
                  <Text style={{marginLeft: 11, color: Tme('cardTextColor')}}>
                    {I18n.t('setting.reset')}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={20}
                    color={Tme('textColor')}
                  />
                </View>
              </View>
            </TouchableOpacity> */}
            {/*  ? null :
              <TouchableOpacity
                style={{ marginTop: 20 }}
                activeOpacity={0.8}
                onPress={this.changeController.bind(this)}>
                <View style={{
                  backgroundColor: Tme("cardColor"),
                  paddingVertical: 16,
                  justifyContent: "center",
                  alignItems: "center",
                }}>
                  <Text style={{ color: Colors.MainColor }}>{I18n.t("global.select_this_controller")}</Text>
                </View>
              </TouchableOpacity>
            } */}
          </View>
        </ScrollView>
      </NavBarView>
    );
  }

  controllerInfo() {
    this.props.navigation.push('ControllerInfo', {
      sn_id: this.props.route.params.sn_id,
      title: I18n.t('home.controller_information'),
    });
  }

  wifiSetting() {
    this.props.navigation.push('WifiSetting', {
      type: 'setting',
      sn: this.props.sn,
    });
  }

  click(name) {
    var url = '';
    var that = this;
    switch (name) {
      case 'restart':
        url = '/partner/settings/restart';
        that.restart(url);
        break;
      case 'update':
        that.update();
        break;
      case 'reset':
        that.reset();
        break;
      default:
        break;
    }
  }

  reset(url) {
    AlertModal.alert(
      I18n.t('setting.reset_title'),
      I18n.t('setting.reset_desp'),
      [
        {
          text: I18n.t('home.cancel'),
          onPress: () => {},
        },
        {
          text: I18n.t('home.confirm'),
          onPress: () => {
            showLoading();
            Helper.httpPOST(
              '/partner/settings/set_factory_default',
              {
                ensure: () => {
                  hideLoading();
                },
                success: data => {
                  AlertModal.alert(
                    I18n.t('setting.reset_success'),
                    _.flatten(data).join('\n'),
                    [
                      {
                        text: I18n.t('home.confirm'),
                        onPress: () => {
                          setTimeout(() => {
                            PubSub.publish(PubSubEvent.RESTART_APP);
                          }, 500);
                        },
                      },
                    ],
                  );
                },
              },
              {sn_id: this.props.route.params.sn_id},
            );
          },
        },
      ],
    );
  }

  restart(url) {
    AlertModal.alert(
      I18n.t('setting.restart_title'),
      I18n.t('setting.restart_message'),
      [
        {
          text: I18n.t('home.cancel'),
          onPress: () => {},
        },
        {
          text: I18n.t('home.confirm'),
          onPress: () => {
            showLoading();
            Helper.httpPOST(
              url,
              {
                ensure: () => {
                  hideLoading();
                },
                success: data => {},
              },
              {sn_id: this.props.route.params.sn_id},
            );
          },
        },
      ],
    );
  }

  update() {
    const that = this;
    if (updated === 'yes') {
      showLoading();
      Helper.httpPOST(
        '/partner/settings/upgrade',
        {
          ensure: () => {
            hideLoading();
          },
          error: () => {
            AlertModal.alert(I18n.t('setting.updated'));
          },
          success: data => {
            if (data.code === 'controller_offline') {
              AlertModal.alert(
                I18n.t('home.warning_message'),
                _.flatten([data.msg]).join('\n'),
              );
            } else {
              AlertModal.alert(
                I18n.t('setting.update_desp_one') + data.v,
                I18n.t('setting.update_desp'),
                [
                  {
                    text: I18n.t('home.cancel'),
                    onPress: () => {},
                  },
                  {
                    text: I18n.t('home.confirm'),
                    onPress: () => {
                      this.props.Navigation.push('ControllerUpdate', {
                        version: data.v,
                        sn: that.props.route.params.sn,
                        sn_id: that.props.route.params.sn_id,
                      });
                    },
                  },
                ],
              );
            }
          },
        },
        {updated: 'yes', sn_id: this.props.route.params.sn_id},
      );
    }
  }
}

const styles = StyleSheet.create({
  tabView: {
    flex: 1,
  },
  icon: {
    width: 20,
    height: 20,
    marginRight: 8,
  },
  row: {
    backgroundColor: Tme('cardColor'),
  },
});
