/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  BackHandler,
  Dimensions,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
} from 'react-native';
import I18n from '../I18n';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { Tme, Colors } from '../ThemeStyle';
import { Helper, HelperMemo } from '../Helper';
import _ from 'lodash';
import NavBarView from '../share/NavBarView';
import CardView from '../share/CardView';
import * as Progress from 'react-native-progress';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Toast } from '../Toast';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { hideLoading, showLoading } from '../../ILoading';

export default class ChangeSnName extends Component {
  constructor(props) {
    super(props);

    this.state = {
      name:
        this.props.route.params.type == 'new'
          ? ''
          : this.props.route.params.name || '',
      indeterminate: true,
      winWidth: this.initWidth(Dimensions.get('window')),
    };

    this.checkNode = 0;
    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this.changeName();
  }

  componentDidMount() {
    var that = this;
    this.DimensionsEvent = Dimensions.addEventListener(
      'change',
      this._onResize.bind(this),
    );
    this.backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (that.props.route.params.type == 'new') {
        return true;
      } else {
        return false;
      }
    });
    if (this.props.route.params.type == 'edit') {
      setTimeout(() => {
        this.textInput.focus();
      }, 600);
    } else {
      this.snNewCheck();
    }
  }

  componentWillUnmount() {
    this.backHandler.remove();
    if (this.DimensionsEvent) {
      this.DimensionsEvent.remove();
    }
  }

  initWidth(window) {
    if (Platform.OS == 'android') {
      return window.width;
    } else {
      if (window.width > window.height) {
        return (
          window.width -
          HelperMemo.STATUS_BAR_HEIGHT -
          HelperMemo.BOTTOM_BAR_HEIGHT -
          22
        );
      } else {
        return window.width;
      }
    }
  }

  _onResize({ window }) {
    this.setState({
      winWidth: this.initWidth(window),
      winHeight: window.height,
      width: window.width,
    });
  }

  snNewCheck() {
    var that = this;
    that.setState({
      indeterminate: false,
    });
  }

  render() {
    var html = '';
    if (this.props.route.params.type === 'new') {
      if (this.state.indeterminate) {
        html = (
          <CardView
            withWaveBg={true}
            styles={{
              height: 180,
              padding: 20,
              borderRadius: 8,
              alignItems: 'center',
            }}>
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                width: this.state.winWidth - 100,
              }}>
              <Progress.Circle
                size={140}
                color={Colors.MainColor}
                progress={0}
                indeterminate={this.state.indeterminate}
                showsText={true}
              />
            </View>
          </CardView>
        );
      } else {
        html = (
          <CardView
            withWaveBg={true}
            styles={{
              height: 400,
              padding: 20,
              borderRadius: 8,
              alignItems: 'center',
            }}>
            <View
              style={{
                justifyContent: 'center',
                marginTop: 75,
                marginBottom: 50,
                alignItems: 'center',
                height: 100,
              }}>
              <Ionicons
                name="checkmark-circle"
                size={100}
                color={Colors.MainColor}
              />
              <Text
                style={{
                  fontSize: 17,
                  color: Tme('cardTextColor'),
                  marginTop: 16,
                }}>
                {I18n.t('home.add_success')}
              </Text>
            </View>
            <View
              style={[
                styles.account_view,
                { borderColor: Tme('inputBorderColor') },
              ]}>
              <TextInput
                ref={ref => {
                  this.textInput = ref;
                }}
                clearButtonMode="always"
                placeholderTextColor={Tme('placeholder')}
                style={[
                  Colors.TextInputStyle(),
                  { width: this.state.winWidth - 100 },
                ]}
                autoCapitalize="none"
                underlineColorAndroid="transparent"
                placeholder={I18n.t('home.controller_input')}
                value={this.state.name}
                returnKeyType="go"
                onSubmitEditing={this.changeName.bind(this)}
                onChangeText={name => {
                  this.setState({ name });
                }}
              />
            </View>
          </CardView>
        );
      }
    }
    return (
      <NavBarView>
        <ScrollView
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}>
          <KeyboardAvoidingView
            style={{ flex: 1, alignItems: 'center', padding: 20 }}>
            {this.props.route.params.type === 'edit' ? (
              <CardView
                withWaveBg={true}
                styles={{
                  height: 120,
                  padding: 20,
                  borderRadius: 8,
                  alignItems: 'center',
                }}>
                <View
                  style={[
                    styles.account_view,
                    { borderColor: Tme('inputBorderColor') },
                  ]}>
                  <TextInput
                    ref={ref => {
                      this.textInput = ref;
                    }}
                    clearButtonMode="always"
                    placeholderTextColor={Tme('placeholder')}
                    style={[
                      Colors.TextInputStyle(),
                      { width: this.state.winWidth - 100 },
                    ]}
                    autoCapitalize="none"
                    underlineColorAndroid="transparent"
                    placeholder={I18n.t('home.controller_input')}
                    value={this.state.name}
                    returnKeyType="go"
                    onSubmitEditing={this.changeName.bind(this)}
                    onChangeText={name => {
                      this.setState({ name });
                    }}
                  />
                </View>
              </CardView>
            ) : (
              html
            )}
          </KeyboardAvoidingView>
        </ScrollView>
      </NavBarView>
    );
  }

  changeName() {
    var that = this;
    AsyncStorage.getItem('user_data', (error, result) => {
      if (error) {
        AlertModal.alert(error);
        return;
      }
      if (this.state.name !== '') {
        showLoading();
        Helper.httpPOST(
          '/sns/change_controller_name',
          {
            ensure: () => {
              hideLoading();
            },
            success: data => {
              this.props.navigation.goBack();
              Toast.show();
            },
            error: data => {
              if (_.isArray(data)) {
                AlertModal.alert(_.uniq(data).join('\n'));
              } else {
                AlertModal.alert(data);
              }
            },
          },
          {
            name: that.state.name,
            sn: that.props.route.params.sn,
          },
        );
      } else {
        AlertModal.alert(
          I18n.t('home.warning_message'),
          I18n.t('home.no_controller_input'),
          [{ text: 'OK', onPress: () => this.textInput.focus() }],
        );
      }
    });
  }
}
const styles = StyleSheet.create({
  tabView: {
    flex: 1,
  },
  account_view: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 20,
    marginTop: 20,
  },
});
