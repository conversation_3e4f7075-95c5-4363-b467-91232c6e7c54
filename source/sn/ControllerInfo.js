import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  RefreshControl,
  SafeAreaView,
} from 'react-native';
import {Tme} from '../ThemeStyle';
import {Helper} from '../Helper';
import I18n from '../I18n';
import _ from 'lodash';
import AlertModal from '../share/AlertModal';
import {hideLoading, showLoading} from '../../ILoading';

export default class ControllerInfo extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      state: false,
      sn: '',
      frequency: '',
      zigbee: false,
      zwave: false,
      m433: false,
      storage: '',
      memory: '',
      ssid: '',
      wifi: '',
      version: '',
      ip: '',
    };
  }

  componentDidMount() {
    this.doFetchData();
  }

  componentWillUnmount() {}

  refresh = () => {
    this.doFetchData('refresh');
  };

  showWifi(level) {
    if (level[0] == 'e') {
      return level;
    } else {
      const temp = level.split('/');
      return temp[0].toString() + '%';
    }
  }

  doFetchData = type => {
    if (type === 'refresh') {
      this.setState({
        isDataLoaded: true,
      });
    } else {
      showLoading();
    }
    Helper.httpGET(
      '/partner/controllers/info?sn_id=' + this.props.route.params.sn_id,
      {
        success: data => {
          this.setState({
            sn: data.sn,
            version: data.ctl_version,
            state: data.controller_state ? 'YES' : 'NO',
            frequency: data.frequency_region,
            storage: data.system ? data.system.disk : '',
            memory: data.system ? data.system.memory : '',
            wifi: data.wifi ? this.showWifi(data.wifi.level) : '',
            ssid: data.wifi ? data.wifi.ssid : '',
            ip: data.wifi ? data.wifi.local_ip : '',
            zwave: data.zwave ? (data.zwave.state ? 'YES' : 'NO') : 'hide',
            zigbee: data.zigbee ? (data.zigbee.state ? 'YES' : 'NO') : 'hide',
            m433: data.ftt ? (data.ftt.state ? 'YES' : 'NO') : 'hide',
          });
        },
        ensure: () => {
          if (type === 'refresh') {
            this.setState({
              isDataLoaded: false,
            });
          } else {
            hideLoading();
          }
        },
        error: data => {
          AlertModal.alert(_.uniq(data).join('\n'), '', [
            {
              text: I18n.t('home.confirm'),
              onPress: () => this.props.navigation.goBack(),
              style: 'cancel',
            },
          ]);
        },
      },
    );
  };

  render() {
    return (
      <SafeAreaView style={{flex: 1, backgroundColor: Tme('bgColor')}}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{flex: 1}}
          refreshControl={
            <RefreshControl
              refreshing={this.state.isDataLoaded}
              onRefresh={this.refresh.bind(this)}
            />
          }>
          <View style={[styles.row]}>
            <Text style={{color: Tme('textColor')}}>
              {I18n.t('home.controller_information')}
            </Text>
          </View>
          <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
            <View style={[styles.row]}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('home.controller')}
              </Text>
              <Text style={{color: Tme('cardTextColor')}}>
                {this.state.state}
              </Text>
            </View>
          </View>
          <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
            <View style={[styles.row]}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('home.sn')}
              </Text>
              <Text style={{color: Tme('cardTextColor')}}>{this.state.sn}</Text>
            </View>
          </View>
          <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
            <View style={styles.row}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('home.controller_frequency')}
              </Text>
              <Text style={{color: Tme('cardTextColor')}}>
                {this.state.frequency}
              </Text>
            </View>
          </View>
          <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
            <View style={styles.row}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('home.version')}
              </Text>
              <Text style={{color: Tme('cardTextColor')}}>
                {this.state.version}
              </Text>
            </View>
          </View>
          {this.state.zigbee !== 'hide' && (
            <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
              <View style={styles.row}>
                <Text style={{color: Tme('cardTextColor')}}>Zigbee</Text>
                <Text style={{color: Tme('cardTextColor')}}>
                  {this.state.zigbee}
                </Text>
              </View>
            </View>
          )}
          {this.state.zwave !== 'hide' && (
            <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
              <View style={styles.row}>
                <Text style={{color: Tme('cardTextColor')}}>Z-WAVE</Text>
                <Text style={{color: Tme('cardTextColor')}}>
                  {this.state.zwave}
                </Text>
              </View>
            </View>
          )}
          {this.state.m433 !== 'hide' && (
            <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
              <View style={styles.row}>
                <Text style={{color: Tme('cardTextColor')}}>433M</Text>
                <Text style={{color: Tme('cardTextColor')}}>
                  {this.state.m433}
                </Text>
              </View>
            </View>
          )}
          <View style={[styles.row]}>
            <Text style={{color: Tme('textColor')}}>
              {I18n.t('home.system')}
            </Text>
          </View>
          <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
            <View style={styles.row}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('home.storage')}
              </Text>
              <Text style={{color: Tme('cardTextColor')}}>
                {this.state.storage}
              </Text>
            </View>
          </View>
          <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
            <View style={styles.row}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('home.memory')}
              </Text>
              <Text style={{color: Tme('cardTextColor')}}>
                {this.state.memory}
              </Text>
            </View>
          </View>
          <View style={[styles.row]}>
            <Text style={{color: Tme('textColor')}}>{I18n.t('home.wifi')}</Text>
          </View>
          <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
            <View style={styles.row}>
              <Text style={{color: Tme('cardTextColor')}}>SSID</Text>
              <Text style={{color: Tme('cardTextColor')}}>
                {this.state.ssid}
              </Text>
            </View>
          </View>
          <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
            <View style={styles.row}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('tuya.wifi_signal')}
              </Text>
              <Text style={{color: Tme('cardTextColor')}}>
                {this.state.wifi}
              </Text>
            </View>
          </View>
          <View style={{backgroundColor: Tme('cardColor'), marginBottom: 2}}>
            <View style={styles.row}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('home.lan_ip')}
              </Text>
              <Text style={{color: Tme('cardTextColor')}}>{this.state.ip}</Text>
            </View>
          </View>
          <View style={{height: 38}} />
        </ScrollView>
      </SafeAreaView>
    );
  }
}

const styles = StyleSheet.create({
  row: {
    marginHorizontal: 20,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
});
