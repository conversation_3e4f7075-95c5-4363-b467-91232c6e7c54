import { useRoute } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import { Tme } from '../ThemeStyle';
import { SafeAreaView } from 'react-native-safe-area-context';
import EmptyView from '../share/EmptyView';
import { mainRadius } from '../Tools';
import moment from 'moment';
import I18n from '../I18n';
import { showOrderType } from './OrderScreen';

export default function OrderList() {
  const route = useRoute();
  const [data, setData] = useState([]);

  useEffect(() => {
    doFetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const doFetchData = async () => {
    setData(route.params.item);
  };

  const renderItem = ({ item }) => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => { }}
        style={{
          backgroundColor: Tme('cardColor'),
          padding: 12,
          borderRadius: mainRadius(),
          marginBottom: 20,
        }}>
        <View
          style={{
            borderBottomColor: Tme('inputBorderColor'),
            borderBottomWidth: 1,
            paddingBottom: 8,
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <Text
            style={{ fontSize: 12, marginTop: 10, color: Tme('cardTextColor') }}>
            {moment(item.created_at, 'YYYY-MM-DD HH:mm:ss').format(
              'YYYY-MM-DD HH:mm:ss',
            )}
          </Text>
          <Text
            style={{ fontSize: 12, marginTop: 10, color: Tme('cardTextColor') }}>
            {item.status_text}
          </Text>
        </View>
        <View
          style={{
            marginTop: 12,
            marginBottom: 12,
            flexDirection: 'row',
          }}>
          <View
            style={{
              alignItems: 'flex-start',
              justifyContent: 'center',
              marginLeft: 20,
            }}>
            <Text style={{ color: Tme('cardTextColor') }}>
              {showOrderType(item.sub_type)}
            </Text>
            <View
              style={{
                marginTop: 12,
                display: 'flex',
                alignItems: 'center',
                flexDirection: 'row',
                flexWrap: 'wrap',
              }}>
              <Text
                style={{
                  fontSize: 14,
                  color: Tme('cardTextColor'),
                }}>
                {I18n.t('pay.paid_price')}: {(item.paid_money / 100).toFixed(2)}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView
      edges={['left', 'right']}
      style={{
        backgroundColor: Tme('bgColor'),
        flex: 1,
      }}>
      <FlatList
        style={{
          flex: 1,
          paddingTop: 20,
        }}
        data={data}
        keyExtractor={(item, index) => index.toString()}
        renderItem={renderItem}
        showsVerticalScrollIndicator={false}
        onEndReachedThreshold={0.1}
        ListEmptyComponent={<EmptyView />}
        ListFooterComponent={<View style={{ height: 30 }} />}
        contentContainerStyle={{
          paddingHorizontal: 20,
        }}
      />
    </SafeAreaView>
  );
}
