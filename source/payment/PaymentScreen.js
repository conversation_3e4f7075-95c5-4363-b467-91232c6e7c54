import {initStripe} from '@stripe/stripe-react-native';
import React, {useEffect, useState} from 'react';
import {ActivityIndicator, ScrollView, StyleSheet} from 'react-native';
import AppConfig from '../../app_config';
import {Tme} from '../ThemeStyle';

const PaymentScreen = ({paymentMethod, children, onInit}) => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function initialize() {
      await initStripe({
        publishableKey: AppConfig.stripe_key,
        urlScheme: 'presen', // required for 3D Secure and bank redirects
        merchantIdentifier: 'merchant.com.presen', // required for Apple Pay
        setReturnUrlSchemeOnAndroid: true,
      });
      setLoading(false);
      onInit?.();
    }
    initialize();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return loading ? (
    <ActivityIndicator size="large" style={StyleSheet.absoluteFill} />
  ) : (
    <ScrollView
      accessibilityLabel="payment-screen"
      style={[styles.container, {backgroundColor: Tme('bgColor')}]}
      contentContainerStyle={{padding: 20}}
      keyboardShouldPersistTaps="always">
      {children}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default PaymentScreen;
