import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {
  Alert,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  useWindowDimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Tme, Colors } from '../ThemeStyle';
import { mainRadius } from '../Tools';
import { Helper } from '../Helper';
import ShadowView from '../share/ShadowView';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  PaymentSheetError,
  StripeProvider,
  useStripe,
} from '@stripe/stripe-react-native';
import { hideLoading, showLoading } from '../../ILoading';
// import { useNavigation } from '@react-navigation/native';
import I18n from '../I18n';
import AppConfig from '../../app_config';
import { Toast } from '../Toast';
import _ from 'lodash';

import moment from 'moment';
import { showOrderType } from './OrderScreen';
import TextItem from '../share/TextItem';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import BottomSheetProduct from './BottomSheetProduct';
import { TextInput } from 'react-native';

export default function ProductScreen() {
  const { initPaymentSheet, presentPaymentSheet } = useStripe();
  const [list, setList] = useState([]);
  const [user, setUser] = useState(null);
  // const navigation = useNavigation();

  const [selectItem, setSelectItem] = useState(null);
  const upProductRef = useRef(null);
  const payRef = useRef(null);
  const customerRef = useRef(null);
  const [showUp, setShowUp] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    doFetchData();
  }, []);

  const doFetchData = () => {
    Helper.httpGET('/users/home_data', {
      cloud: true,
      success: data => {
        setUser(data.user);
      },
    });
    showLoading();
    Helper.httpGET('/orders/templates', {
      success: data => {
        setRefreshing(false);
        const temp = [];
        const trial = data.trial;
        temp.push({
          days: trial.days,
          price: trial.price,
          price_id: trial.price_id,
          prod_id: trial.prod_id,
          file_days: trial.file_days,
          title: I18n.t('pay.sub.trial.title'),
          desp: I18n.t('pay.sub.trial.desp', { file_days: trial.file_days }),
          og_price: 0,
          name: 'trial',
        });
        Object.keys(data).forEach(key => {
          if (key != 'trial') {
            const item = data[key];
            temp.push({
              days: item.days,
              price: item.price,
              price_id: item.price_id,
              prod_id: item.prod_id,
              file_days: trial.file_days,
              title: I18n.t(`pay.sub.${key}.title`),
              desp: I18n.t(`pay.sub.${key}.desp`, { file_days: item.file_days }),
              og_price: item.og_price,
              name: key,
            });
          }
        });
        setList(temp);

        hideLoading();
      },
      error: () => {
        hideLoading();
      },
    });
  };

  const upProductPayment = price_id => {
    showLoading();
    Helper.httpPOST(
      '/stripes/change_subscription',
      {
        success: data => {
          hideLoading();
          Alert.alert(
            I18n.t('home.warning_message'),
            I18n.t('pay.change_success'),
            [
              {
                text: I18n.t('home.confirm'),
                onPress: () => {
                  Toast.show();
                  doFetchData();
                  upProductRef.current.close();
                },
              },
            ],
          );
        },
        error: () => {
          hideLoading();
        },
      },
      {
        price_id: price_id,
      },
    );
  };

  const fetchPaymentSheetParams = name => {
    payRef.current.close();
    if (name === 'trial') {
      showLoading();
      Helper.httpPOST(
        '/orders',
        {
          success: data => {
            hideLoading();
            Toast.show();
          },
          ensure: () => {
            hideLoading();
          },
        },
        {
          type: name,
        },
      );
    } else {
      if (user.stripe_customer_id) {
        showLoading();
        Helper.httpPOST(
          '/orders',
          {
            success: data => {
              if (data.client_secret) {
                initializePaymentSheet(data);
              } else {
                Toast.show(I18n.t('pay.success'));
                doFetchData();
              }
            },
            ensure: () => {
              hideLoading();
            },
          },
          {
            type: name,
          },
        );
      } else {
        customerRef.current.open(name);
      }
    }
  };

  const [customerUp, setCustomerUp] = useState(null);
  useEffect(() => {
    if (customerUp) {
      customerRef.current.close();
      showLoading();
      Helper.httpPOST(
        '/orders',
        {
          ensure: () => {
            hideLoading();
          },
          success: data => {
            if (data.client_secret) {
              initializePaymentSheet(data);
            } else {
              Toast.show(I18n.t('pay.success'));
              doFetchData();
            }
            setCustomerUp(null);
          },
        },
        {
          type: customerUp,
        },
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [customerUp]);

  const initializePaymentSheet = async clientSecret => {
    showLoading();
    const { error } = await initPaymentSheet({
      paymentIntentClientSecret: clientSecret.client_secret,
      returnURL: 'presen://payment-sheet',
      merchantDisplayName: 'Presen, Inc.',
      allowsDelayedPaymentMethods: true,
    });

    if (!error) {
      hideLoading();
      pay();
      // Handle error
    } else {
      hideLoading();
      Alert.alert(I18n.t('home.warning_message'), error.localizedMessage);
    }
  };

  const pay = async () => {
    const { error } = await presentPaymentSheet();
    if (error) {
      if (error.code === PaymentSheetError.Failed) {
        Alert.alert(I18n.t('home.warning_message'), error.message);
      } else if (error.code === PaymentSheetError.Canceled) {
        // Handle canceled
      }
    } else {
      Toast.show(I18n.t('pay.success'));
      setTimeout(() => {
        doFetchData();
      }, 1000);
    }
  };

  const cancelSub = item => {
    Alert.alert(I18n.t('home.warning_message'), I18n.t('pay.cancel_sub_desp'), [
      {
        text: I18n.t('home.cancel'),
        onPress: () => { },
      },
      {
        text: I18n.t('home.confirm'),
        onPress: () => {
          showLoading();
          Helper.httpPOST(
            '/stripes/cancel_subscription',
            {
              ensure: () => {
                hideLoading();
              },
              success: data => {
                doFetchData();
                Toast.show(I18n.t('pay.setting_wait'));
              },
            },
            {},
          );
        },
      },
    ]);
  };

  const resumeSub = () => {
    showLoading();
    Helper.httpPOST(
      '/stripes/resume_subscription',
      {
        ensure: () => {
          hideLoading();
        },
        success: data => {
          doFetchData();
          Toast.show(I18n.t('pay.setting_wait'));
        },
      },
      {},
    );
  };

  const onRefresh = () => {
    setRefreshing(true);
    // 这里模拟数据加载过程
    doFetchData();
  };

  return (
    <BottomSheetModalProvider>
      <SafeAreaView
        edges={['left', 'right']}
        style={{ flex: 1, backgroundColor: Tme('bgColor') }}>
        <StripeProvider
          publishableKey={AppConfig.stripe_key}
          urlScheme="presen" // required for 3D Secure and bank redirects
          merchantIdentifier="merchant.com.presen" // required for Apple Pay
        >
          {user && (
            <View style={{ paddingHorizontal: 20, marginTop: 10, marginBottom: 20 }}>
              <View style={{ marginBottom: 20 }}>
                <Text>{I18n.t('pay.now_sub_status')}</Text>
              </View>
              <View
                style={{
                  backgroundColor: Tme('cardColor'),
                  borderRadius: mainRadius(),
                  padding: 20,
                  flexDirection: 'row',
                }}>
                <View style={{ flex: 1 }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginTop: 10 }}>
                    <Text
                      numberOfLines={1}
                      style={{
                        fontSize: 16,
                        fontWeight: '600',
                        color: Tme('cardTextColor'),
                      }}>
                      {user.customer_email}1111
                    </Text>
                    <View style={{flexDirection: 'column', alignItems: 'flex-end', justifyContent: 'center'}}>
                      {user.sub_status === 'active' && (
                        <TouchableOpacity
                          hitSlop={{ top: 20, right: 20, bottom: 20, left: 20 }}
                          onPress={() => {
                            setShowUp(true);
                            setTimeout(() => {
                              upProductRef.current.open();
                            }, 500);
                          }}
                          activeOpacity={0.8}
                          style={{
                            paddingHorizontal: 12,
                            paddingVertical: 4,
                            borderRadius: mainRadius(),
                            borderColor: Tme('inputBorderColor'),
                            borderWidth: 1,
                          }}>
                          <TextItem>{I18n.t('pay.up_sub')}</TextItem>
                        </TouchableOpacity>
                      )}

                      {user.sub_status === 'cancel_at_period_end' && (
                        <TouchableOpacity
                          hitSlop={{ top: 20, right: 20, bottom: 20, left: 20 }}
                          onPress={resumeSub}
                          activeOpacity={0.8}
                          style={{
                            paddingHorizontal: 12,
                            paddingVertical: 4,
                            borderRadius: mainRadius(),
                            borderColor: Tme('inputBorderColor'),
                            borderWidth: 1,
                          }}>
                          <TextItem>{I18n.t('pay.reset_sub')}</TextItem>
                        </TouchableOpacity>
                      )}

                      {user.sub_status === 'active' && (
                        <TouchableOpacity
                          hitSlop={{ top: 20, right: 20, bottom: 20, left: 20 }}
                          onPress={() => {
                            cancelSub(user);
                          }}
                          activeOpacity={0.8}
                          style={{
                            marginTop: 10,
                            paddingHorizontal: 12,
                            paddingVertical: 4,
                            borderRadius: mainRadius(),
                            borderColor: Tme('inputBorderColor'),
                            borderWidth: 1,
                          }}>
                          <TextItem>{I18n.t('pay.cancel_sub')}</TextItem>
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                  <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginTop: 10 }}>
                    <Text
                      style={{
                        fontSize: 12,
                        color: Tme('smallTextColor'),
                      }}>
                      {I18n.t('pay.sub_type')}
                      {': '}
                    </Text>
                    <View style={{ flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                      <Text style={{
                        fontSize: 12,
                        color: Tme('smallTextColor'),
                      }}>
                        {showOrderType(user.sub_type)}
                      </Text>
                    </View>
                  </View>
                  <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginTop: 10 }}>
                    <Text
                      style={{
                        fontSize: 12,
                        color: Tme('smallTextColor'),
                      }}>
                      {I18n.t('pay.sub_time')}
                      {': '}
                    </Text>
                    <Text style={{
                      fontSize: 12,
                      color: Tme('smallTextColor'),
                    }}>
                      {user.sub_expire_ts !== 0 &&
                        moment(
                          user.sub_expire_at,
                          'YYYY-MM-DD HH:mm:ss',
                        ).format('YYYY-MM-DD')}
                    </Text>
                  </View>

                  <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginTop: 10 }}>
                    <Text
                      style={{
                        fontSize: 12,
                        color: Tme('smallTextColor'),
                      }}>
                      {I18n.t('pay.sub_status')}
                      {': '}

                    </Text>
                    <View style={{ flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                      <Text style={{
                        fontSize: 12,
                        color: Tme('smallTextColor'),
                      }}>
                        {user.sub_status === 'active' && I18n.t('pay.activate')}
                        {user.sub_status === 'cancel_at_period_end' &&
                          I18n.t('pay.pause')}
                      </Text>

                    </View>
                  </View>
                </View>
              </View>
            </View>
          )}
          <ScrollView
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={() => {
                  onRefresh();
                }}
              />
            }
            style={{ flex: 1 }}
            contentContainerStyle={{ paddingHorizontal: 20 }}>
            <View style={{ marginBottom: 10 }}>
              <TextItem>{I18n.t('pay.sub_type')}</TextItem>
            </View>
            {list.map((item, index) => {
              return (
                <RenderItem
                  item={item}
                  key={index}
                  index={index}
                  setSelectItem={d => {
                    setSelectItem(d);
                    payRef.current.open();
                  }}
                  selectItem={selectItem}
                />
              );
            })}
          </ScrollView>
        </StripeProvider>
        {showUp && (
          <UpProductView
            ref={upProductRef}
            products={list}
            user={user}
            setShowUp={setShowUp}
            upProductPayment={upProductPayment}
          />
        )}

        <PayView
          ref={payRef}
          selectItem={selectItem}
          fetchPaymentSheetParams={fetchPaymentSheetParams}
        />
        <CustomerView
          user={user}
          ref={customerRef}
          setCustomerUp={setCustomerUp}
        />
      </SafeAreaView>
    </BottomSheetModalProvider>
  );
}

const RenderItem = ({ item, index, selectItem, setSelectItem }) => {
  const { width } = useWindowDimensions();
  return (
    <ShadowView key={index} viewStyle={{ width: width - 40, marginBottom: 20 }}>
      <TouchableOpacity
        onPress={() => {
          setSelectItem(item);
        }}
        activeOpacity={0.8}
        style={{
          flex: 1,
          backgroundColor: Tme('cardColor'),
          borderRadius: mainRadius(),
          padding: 20,
        }}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          {item.name == 'trial' ? (
            <View
              style={{
                backgroundColor: '#FFECE8',
                paddingHorizontal: 6,
                paddingVertical: 4,
                borderRadius: 2,
              }}>
              <Text
                style={{
                  fontSize: 10,
                  color: 'red',
                  lineHeight: 12,
                }}>
                {I18n.t('pay.trial')}
              </Text>
            </View>
          ) : (
            <View
              style={{
                backgroundColor: '#FFECE8',
                paddingHorizontal: 6,
                paddingVertical: 4,
                borderRadius: 2,
              }}>
              <Text
                style={{
                  fontSize: 10,
                  color: 'red',
                  lineHeight: 12,
                }}>
                {I18n.t('pay.auto_pay')}
              </Text>
            </View>
          )}
          {selectItem && selectItem.name === item.name && (
            <MaterialCommunityIcons
              name="check-circle-outline"
              color="red"
              size={16}
            />
          )}
        </View>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            position: 'relative',
          }}>
          <View style={{ flex: 1, alignItems: 'flex-start' }}>
            <Text
              style={{
                fontSize: 16,
                fontWeight: '600',
                marginTop: 10,
                color: Tme('cardTextColor'),
              }}>
              {item.title}
            </Text>
            <Text style={{ marginTop: 10, color: Tme('smallTextColor') }}>
              {item.desp}
            </Text>
            <Text style={{ marginTop: 10, color: Tme('smallTextColor') }}>
              {I18n.t('pay.days', { day: item.days })}
            </Text>
          </View>
          <View style={{ marginLeft: 20, alignItems: 'flex-end' }}>
            <Text style={{ fontSize: 18, color: 'red' }}>
              ${item.price.toFixed(2)}
            </Text>
            <Text
              style={{
                fontSize: 12,
                textDecorationLine: 'line-through',
              }}>
              {item.og_price.toFixed(2)}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    </ShadowView>
  );
};

const PayView = forwardRef(({ selectItem, fetchPaymentSheetParams }, ref) => {
  useImperativeHandle(ref, () => ({
    open: () => {
      bottomSheetRef.current.openBottomSheet();
    },
    close: () => {
      bottomSheetRef.current.dismissBottomSheet();
    },
  }));
  const bottomSheetRef = useRef(null);

  return (
    <BottomSheetProduct ref={bottomSheetRef} snap={['30%']}>
      {selectItem && (
        <View>
          <View style={{ flexDirection: 'row' }}>
            <Text
              style={{
                color: Tme('cardTextColor'),
                fontSize: 18,
                fontWeight: '600',
              }}>
              {showOrderType(selectItem.name)}
            </Text>
          </View>
          <View style={{ flexDirection: 'row', marginTop: 10 }}>
            <Text style={{ color: Tme('cardTextColor'), fontSize: 14 }}>
              {I18n.t('pay.amount')}
            </Text>
            <Text
              style={{
                color: Tme('cardTextColor'),
                fontSize: 14,
                fontWeight: '600',
                marginLeft: 6,
              }}>
              $
            </Text>

            <Text
              style={{
                marginLeft: 6,
                color: Tme('cardTextColor'),
                fontSize: 18,
                fontWeight: '600',
              }}>
              {selectItem && selectItem.price.toFixed(2)}
            </Text>
          </View>

          <TouchableOpacity
            onPress={() => {
              fetchPaymentSheetParams(selectItem.name);
              // onePay();
            }}
            activeOpacity={0.8}
            style={{
              backgroundColor: Colors.MainColor,
              paddingVertical: 10,
              borderRadius: mainRadius(),
              marginTop: 20,
              marginBottom: 20,
              alignItems: 'center',
            }}>
            <Text style={{ color: '#fff', fontSize: 16 }}>
              {I18n.t('pay.pay')}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </BottomSheetProduct>
  );
});

const UpProductView = forwardRef(
  ({ products, user, setShowUp, upProductPayment }, ref) => {
    useImperativeHandle(ref, () => ({
      open: () => {
        bottomSheetRef.current.openBottomSheet();
      },
      close: () => {
        bottomSheetRef.current.dismissBottomSheet();
      },
    }));
    const { width } = useWindowDimensions();
    const bottomSheetRef = useRef(null);
    const [data, setData] = useState([]);

    useEffect(() => {
      const temp = _.cloneDeep(products);
      _.remove(temp, function (n) {
        return n.name === 'trial' || user.sub_type === n.name;
      });
      setData(temp);
    }, [products, user]);

    const [select, setSelect] = useState(null);

    const doUpData = yearly => {
      showLoading();
      Helper.httpGET(
        Helper.urlWithQuery('/stripes/preview_proration', {
          price_id: yearly.price_id,
        }),
        {
          success: d => {
            hideLoading();
            setSelect({
              name: yearly.name,
              price_id: yearly.price_id,
              price: yearly.price,
              total: d.total / 100,
            });
          },
          error: () => {
            hideLoading();
          },
        },
      );
    };

    return (
      <BottomSheetProduct
        snap={['90%']}
        ref={bottomSheetRef}
        onDismiss={() => {
          setShowUp(false);
        }}>
        {data.map((item, index) => {
          return (
            <RenderItem
              item={item}
              key={index}
              index={index}
              setSelectItem={v => {
                doUpData(v);
              }}
              selectItem={select}
            />
          );
        })}
        {select && (
          <ShadowView viewStyle={{ width: width - 40, marginBottom: 20 }}>
            <View
              style={{
                flex: 1,
                backgroundColor: Tme('cardColor'),
                borderRadius: mainRadius(),
                padding: 20,
              }}>
              <View style={{ flexDirection: 'row' }}>
                <Text
                  style={{
                    color: Tme('cardTextColor'),
                    fontSize: 18,
                    fontWeight: '600',
                  }}>
                  {showOrderType(select.name)}
                </Text>
              </View>
              <View style={{ flexDirection: 'row', marginTop: 10 }}>
                <Text style={{ color: Tme('cardTextColor'), fontSize: 14 }}>
                  {I18n.t('pay.amount')}
                </Text>
                <Text
                  style={{
                    color: Tme('cardTextColor'),
                    fontSize: 14,
                    fontWeight: '600',
                    marginLeft: 6,
                  }}>
                  $
                </Text>

                <Text
                  style={{
                    marginLeft: 6,
                    color: Tme('cardTextColor'),
                    fontSize: 18,
                    fontWeight: '600',
                  }}>
                  {select && select.total.toFixed(2)}
                </Text>
              </View>

              <TouchableOpacity
                onPress={() => {
                  upProductPayment(select.price_id);
                }}
                activeOpacity={0.8}
                style={{
                  backgroundColor: Colors.MainColor,
                  paddingVertical: 10,
                  borderRadius: mainRadius(),
                  marginTop: 20,
                  marginBottom: 20,
                  alignItems: 'center',
                }}>
                <Text style={{ color: '#fff', fontSize: 16 }}>
                  {I18n.t('pay.pay')}
                </Text>
              </TouchableOpacity>
            </View>
          </ShadowView>
        )}
      </BottomSheetProduct>
    );
  },
);

const CustomerView = forwardRef(({ user, setCustomerUp }, ref) => {
  useImperativeHandle(ref, () => ({
    open: name => {
      setSubName(name);
      bottomSheetRef.current.openBottomSheet();
    },
    close: () => {
      bottomSheetRef.current.dismissBottomSheet();
    },
  }));

  const [email, setEmail] = React.useState('');
  const [fullname, setFullname] = React.useState('');
  const [subName, setSubName] = React.useState('');

  useEffect(() => {
    if (user) {
      setEmail(user.customer_email);
      setFullname(user.customer_name);
    }
  }, [user]);

  const { width } = useWindowDimensions();
  const bottomSheetRef = useRef(null);

  const send = () => {
    if (!email) {
      Alert.alert(
        I18n.t('home.warning_message'),
        I18n.t('session.email_input'),
      );
      return;
    }
    if (!fullname) {
      Alert.alert(
        I18n.t('home.warning_message'),
        I18n.t('pay.input_full_name'),
      );
      return;
    }
    showLoading();
    Helper.httpPOST(
      '/users/update_stripe_customer',
      {
        success: data => {
          setCustomerUp(subName);
        },
        ensure: () => {
          hideLoading();
        },
      },
      { email, name: fullname },
    );
  };

  return (
    <BottomSheetProduct
      ref={bottomSheetRef}
      onDismiss={() => { }}
      snap={['90%']}>
      <ShadowView viewStyle={{ width: width - 40, marginBottom: 20 }}>
        <View
          style={{
            flex: 1,
            backgroundColor: Tme('cardColor'),
            borderRadius: mainRadius(),
            padding: 20,
          }}>
          <TextItem style={{ fontSize: 18, fontWeight: '600' }}>
            {I18n.t('pay.customer_title')}
          </TextItem>
          <View
            style={[
              styles.account_view,
              { borderColor: Tme('inputBorderColor') },
            ]}>
            <TextInput
              returnKeyType="go"
              autoCapitalize="none"
              underlineColorAndroid="transparent"
              autoCorrect={false}
              value={fullname}
              onChangeText={setFullname}
              placeholderTextColor={Tme('placeholder')}
              placeholder={I18n.t('pay.full_name')}
              style={[Colors.TextInputStyle(), { width: width - 100 }]}
            />
          </View>
          <View
            style={[
              styles.account_view,
              { borderColor: Tme('inputBorderColor') },
            ]}>
            <TextInput
              returnKeyType="go"
              autoCapitalize="none"
              underlineColorAndroid="transparent"
              autoCorrect={false}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              placeholderTextColor={Tme('placeholder')}
              placeholder={I18n.t('home.email')}
              style={[Colors.TextInputStyle(), { width: width - 100 }]}
            />
          </View>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={send}
            style={{
              backgroundColor: Colors.MainColor,
              width: width - 80,
              paddingVertical: 10,
              borderRadius: mainRadius(),
              alignItems: 'center',
              marginTop: 20,
            }}>
            <Text style={{ color: '#fff' }}>{I18n.t('home.submit')}</Text>
          </TouchableOpacity>
          <View style={{ marginTop: 20, alignItems: 'flex-start' }}>
            <Text style={{ color: Tme('smallTextColor'), fontSize: 12 }}>
              {I18n.t('pay.pay_info_desp')}
            </Text>
          </View>
        </View>
      </ShadowView>
    </BottomSheetProduct>
  );
});

const styles = StyleSheet.create({
  account_view: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 3,
    marginTop: 20,
  },
});
