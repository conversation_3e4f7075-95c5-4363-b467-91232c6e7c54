import React from 'react';
import {
  Al<PERSON>,
  Image,
  StyleSheet,
  Switch,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {CustomerSheetBeta} from '@stripe/stripe-react-native';
import {PaymentSheet} from '@stripe/stripe-react-native';
import PaymentScreen from './PaymentScreen';
import {PresenCustomerAdapter} from './PresenCustomerAdapter';

export default function CustomerSheetScreen() {
  const [useComponent, setUseComponent] = React.useState(false);
  const [stripeInitialized, setStripeInitialized] = React.useState(false);
  const [selectedPaymentOption, setSelectedPaymentOption] =
    React.useState(null);
  const [setupIntent, setSetupIntent] = React.useState('');
  const [ephemeralKeySecret, setEphemeralKeySecret] = React.useState('');
  const [customer, setCustomer] = React.useState('');
  const [customerSheetVisible, setCustomerSheetVisible] = React.useState(false);
  const [customerAdapter, setCustomerAdapter] = React.useState(null);

  const setup = async () => {
    // const {
    //   customer: customerId,
    //   setupIntent: setupIntentClientSecret,
    //   ephemeralKeySecret: customerEphemeralKeySecret,
    // } = await fetchCustomerSheetParams();
    const address = {
      city: 'San Francisco',
      country: 'US',
      line1: '510 Townsend St.',
      line2: '123 Street',
      postalCode: '94102',
      state: 'California',
    };
    const billingDetails = {
      name: 'Jane Doe',
      email: '<EMAIL>',
      phone: '************',
      address: address,
    };

    const {error} = await CustomerSheetBeta.initialize({
      // setupIntentClientSecret,
      // customerEphemeralKeySecret,
      // customerId,
      returnURL: 'presen://stripe-redirect',
      removeSavedPaymentMethodMessage:
        'Are you sure you wanna remove this payment method? 😿',
      style: 'alwaysLight',
      merchantDisplayName: 'React Native Test Merchant',
      headerTextForSelectionScreen: 'Welcome to customer sheet!',
      defaultBillingDetails: billingDetails,
      billingDetailsCollectionConfiguration: {
        phone: PaymentSheet.CollectionMode.ALWAYS,
      },
    });
    if (error) {
      Alert.alert(error.code, error.localizedMessage);
    }

    const {
      error: retrievalError,
      paymentOption,
      paymentMethod,
    } = await CustomerSheetBeta.retrievePaymentOptionSelection();
    if (retrievalError) {
      Alert.alert(retrievalError.code, retrievalError.localizedMessage);
    }
    if (paymentOption) {
      setSelectedPaymentOption(paymentOption);
    }
    if (paymentMethod) {
      console.log(JSON.stringify(paymentMethod, null, 2));
    }

    setStripeInitialized(true);
  };

  const present = async () => {
    if (useComponent) {
      setCustomerSheetVisible(true);
    } else {
      const {error, paymentOption, paymentMethod} =
        await CustomerSheetBeta.present();
      if (error) {
        Alert.alert(error.code, error.localizedMessage);
      }
      if (paymentOption) {
        setSelectedPaymentOption(paymentOption);
        console.log(JSON.stringify(paymentOption, null, 2));
      }
      if (paymentMethod) {
        console.log(JSON.stringify(paymentMethod, null, 2));
      }
    }
  };

  return (
    <PaymentScreen onInit={setup}>
      <TouchableOpacity
        onPress={() => {
          present();
        }}>
        <Text>
          {selectedPaymentOption
            ? selectedPaymentOption.label
            : 'Edit payment methods'}
        </Text>
      </TouchableOpacity>

      {useComponent && customerAdapter && (
        <CustomerSheetBeta.CustomerSheet
          visible={customerSheetVisible}
          setupIntentClientSecret={setupIntent}
          customerEphemeralKeySecret={ephemeralKeySecret}
          customerId={customer}
          returnURL={'stripe-example://stripe-redirect'}
          onResult={({error, paymentOption, paymentMethod}) => {
            setCustomerSheetVisible(false);
            if (error) {
              Alert.alert(error.code, error.localizedMessage);
            }
            if (paymentOption) {
              setSelectedPaymentOption(paymentOption);
              console.log(JSON.stringify(paymentOption, null, 2));
            }
            if (paymentMethod) {
              console.log(JSON.stringify(paymentMethod, null, 2));
            }
          }}
          customerAdapter={customerAdapter}
        />
      )}
      {selectedPaymentOption?.image && (
        <Image
          style={styles.image}
          source={{
            uri: `data:image/png;base64,${selectedPaymentOption?.image}`,
          }}
        />
      )}
      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Use component: </Text>
        <Switch
          testID="customer_adapter_switch"
          onValueChange={v => {
            if (!v) {
              setup();
            } else {
              setCustomerAdapter(new PresenCustomerAdapter(customer));
            }
            setUseComponent(v);
          }}
          value={useComponent}
        />
      </View>
    </PaymentScreen>
  );
}

const styles = StyleSheet.create({
  switchRow: {
    marginTop: 350,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  switchLabel: {
    fontSize: 20,
  },
  image: {alignSelf: 'center', width: 150, height: 100},
});
