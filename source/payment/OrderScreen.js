import { useGetState } from 'ahooks';
import React, { useEffect, useState } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FlatList, View, Text, TouchableOpacity } from 'react-native';
import { Tme } from '../ThemeStyle';

import I18n from '../I18n';
import { Helper } from '../Helper';
import { mainRadius } from '../Tools';
import moment from 'moment';
import EmptyView from '../share/EmptyView';
import { useNavigation } from '@react-navigation/native';
// import TextItem from '../share/TextItem';

export default function OrderScreen() {
  const [list, setList] = useState([]);
  const [, setPage, getPage] = useGetState(1);
  const [hasMore, setHasMore] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const navigation = useNavigation();

  useEffect(() => {
    doFetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const doFetchData = async () => {
    if (getPage() > 0) {
      Helper.httpGET(Helper.urlWithQuery('/orders', { page: getPage() }), {
        success: data => {
          if (getPage() === 1) {
            setList(data.orders);
          } else {
            setList(list.concat(data.orders));
          }
          if (data.next_page > 0) {
            setHasMore(true);
          } else {
            setHasMore(false);
          }
        },
      });
    }
    setRefreshing(false);
  };

  const doRefreshData = () => {
    setRefreshing(true);
    setPage(1);
    setTimeout(() => {
      doFetchData();
    }, 500);
  };

  const renderItem = ({ item }) => {
    const itemData = item;

    return (
      <View
        style={{
          backgroundColor: Tme('cardColor'),
          padding: 12,
          borderRadius: mainRadius(),
          marginBottom: 20,
        }}>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={() => {
            if (itemData.status === 'done') {
              navigation.push('OrderList', {
                title: I18n.t('pay.payment_record'),
                item: itemData.payment_records,
              });
            }
          }}>
          <View
            style={{
              borderBottomColor: Tme('inputBorderColor'),
              borderBottomWidth: 1,
              paddingBottom: 8,
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <Text
              style={{
                fontSize: 12,
                marginTop: 10,
                color: Tme('cardTextColor'),
              }}>
              {moment(itemData.created_at, 'YYYY-MM-DD HH:mm:ss').format(
                'YYYY-MM-DD HH:mm:ss',
              )}
            </Text>
            <Text
              style={{
                fontSize: 12,
                marginTop: 10,
                color: Tme('cardTextColor'),
              }}>
              {itemData.status_text}
            </Text>
          </View>
          <View
            style={{
              marginTop: 12,
              marginBottom: 12,
              flexDirection: 'row',
            }}>
            <View
              style={{
                alignItems: 'flex-start',
                justifyContent: 'center',
                marginLeft: 20,
              }}>
              <Text style={{ color: Tme('cardTextColor') }}>
                {showOrderType(item.sub_type)}
              </Text>
              <View
                style={{
                  marginTop: 12,
                  display: 'flex',
                  alignItems: 'center',
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                }}>
                <Text
                  style={{
                    fontSize: 14,
                    color: Tme('cardTextColor'),
                  }}>
                  {I18n.t('pay.paid_price')} ${itemData.paid_money}
                </Text>
              </View>
              {Number(itemData.refund_money) > 0 && (
                <View
                  style={{
                    marginTop: 12,
                    display: 'flex',
                    alignItems: 'center',
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                  }}>
                  <Text
                    style={{
                      fontSize: 14,
                      color: Tme('cardTextColor'),
                    }}>
                    {I18n.t('pay.refund_money')} ${itemData.refund_money}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView
      edges={['left', 'right']}
      style={{
        backgroundColor: Tme('bgColor'),
        flex: 1,
      }}>
      <FlatList
        style={{
          flex: 1,
          paddingTop: 20,
        }}
        data={list}
        keyExtractor={(item, index) => index.toString()}
        renderItem={renderItem}
        onRefresh={doRefreshData}
        refreshing={refreshing}
        showsVerticalScrollIndicator={false}
        onEndReachedThreshold={0.1}
        onEndReached={() => {
          if (hasMore) {
            doFetchData();
          }
        }}
        ListEmptyComponent={<EmptyView />}
        ListFooterComponent={<View style={{ height: 30 }} />}
        contentContainerStyle={{
          paddingHorizontal: 20,
        }}
      />
    </SafeAreaView>
  );
}

export const showOrderType = type => {
  switch (type) {
    case 'monthly':
      return I18n.t('pay.monthly');
    case 'trial':
      return I18n.t('pay.trial');
    case 'yearly':
      return I18n.t('pay.yearly');
    case 'daily':
      return I18n.t('pay.daily');
    default:
      return I18n.t('pay.unknown');
  }
};
