import React, { useEffect } from 'react';
import {
  Alert,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  useWindowDimensions,
} from 'react-native';
import { Colors, Tme } from '../ThemeStyle';
import CardView from '../share/CardView';
import I18n from '../I18n';
import { mainRadius } from '../Tools';
import { hideLoading, showLoading } from '../../ILoading';
import { Helper } from '../Helper';
import { Toast } from '../Toast';
import { useNavigation } from '@react-navigation/native';

export default function SettingCard() {
  const [email, setEmail] = React.useState('');
  const [fullname, setFullname] = React.useState('');
  const { width } = useWindowDimensions();
  const navigation = useNavigation();

  useEffect(() => {
    doFetchData();
  });

  const doFetchData = () => {
    showLoading();
    Helper.httpGET(Helper.urlWithQuery('/users/home_data'), {
      cloud: true,
      success: data => {
        setEmail(data.user.customer_email);
        setFullname(data.user.customer_name);
      },
      ensure: () => {
        hideLoading();
      },
    });
  };

  const send = () => {
    if (!email) {
      Alert.alert(
        I18n.t('home.warning_message'),
        I18n.t('session.email_input'),
      );
      return;
    }
    if (!fullname) {
      Alert.alert(
        I18n.t('home.warning_message'),
        I18n.t('pay.input_full_name'),
      );
      return;
    }
    showLoading();
    Helper.httpPOST(
      '/users/update_stripe_customer',
      {
        success: data => {
          Toast.show();
          navigation.goBack();
        },
        ensure: () => {
          hideLoading();
        },
      },
      { email, name: fullname },
    );
  };

  return (
    <View
      style={{
        backgroundColor: Tme('bgColor'),
        flex: 1,
        marginTop: 20,
        alignItems: 'center',
      }}>
      <CardView
        withWaveBg={true}
        styles={{
          padding: 20,
          borderRadius: 8,
          width: width - 40,
        }}>
        <View
          style={[styles.account_view, { borderColor: Tme('inputBorderColor') }]}>
          <TextInput
            returnKeyType="go"
            autoCapitalize="none"
            underlineColorAndroid="transparent"
            autoCorrect={false}
            value={fullname}
            onChangeText={setFullname}
            placeholderTextColor={Tme('placeholder')}
            placeholder={I18n.t('pay.full_name')}
            style={[Colors.TextInputStyle(), { width: width - 100 }]}
          />
        </View>
        <View
          style={[styles.account_view, { borderColor: Tme('inputBorderColor') }]}>
          <TextInput
            returnKeyType="go"
            autoCapitalize="none"
            underlineColorAndroid="transparent"
            autoCorrect={false}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            placeholderTextColor={Tme('placeholder')}
            placeholder={I18n.t('home.email')}
            style={[Colors.TextInputStyle(), { width: width - 100 }]}
          />
        </View>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={send}
          style={{
            backgroundColor: Colors.MainColor,
            width: width - 80,
            paddingVertical: 10,
            borderRadius: mainRadius(),
            alignItems: 'center',
            marginTop: 20,
          }}>
          <Text style={{ color: '#fff' }}>{I18n.t('home.submit')}</Text>
        </TouchableOpacity>
        <View style={{ marginTop: 20, alignItems: 'flex-start' }}>
          <Text style={{ color: Tme('smallTextColor'), fontSize: 12 }}>
            {I18n.t('pay.pay_info_desp')}
          </Text>
        </View>
      </CardView>
    </View>
  );
}

const styles = StyleSheet.create({
  account_view: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 3,
    marginTop: 20,
  },
});
