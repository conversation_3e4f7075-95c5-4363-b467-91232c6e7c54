/* eslint-disable react/no-unstable-nested-components */
import React, {useRef, useImperativeHandle, forwardRef} from 'react';
import {
  BottomSheetBackdrop,
  BottomSheetModal,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import {Tme} from '../ThemeStyle';

interface BottomSheetProductProps {
  onDismiss?: () => void;
  snap?: string[];
  children?: React.ReactNode;
}

interface BottomSheetProductRef {
  openBottomSheet: () => void;
  dismissBottomSheet: () => void;
}

const BottomSheetProduct = forwardRef<BottomSheetProductRef, BottomSheetProductProps>(
  (props, ref) => {
    const bottomSheetRef = useRef<BottomSheetModal>(null);

    const openBottomSheet = () => {
      bottomSheetRef.current?.present();
    };

    useImperativeHandle(ref, () => ({
      openBottomSheet: openBottomSheet,
      dismissBottomSheet: () => {
        bottomSheetRef.current?.dismiss();
      },
    }));

    return (
      <BottomSheetModal
        onDismiss={() => {
          if (props.onDismiss) {
            props.onDismiss();
          }
        }}
        ref={bottomSheetRef}
        snapPoints={props.snap ? props.snap : ['50%', '90%']}
        index={0}
        backgroundStyle={{
          backgroundColor: Tme('cardColor'),
        }}
        backdropComponent={(backdropProps) => (
          <BottomSheetBackdrop
            disappearsOnIndex={-1}
            appearsOnIndex={0}
            {...backdropProps}
          />
        )}>
        <BottomSheetScrollView
          contentContainerStyle={{
            padding: 20,
          }}>
          {props.children}
        </BottomSheetScrollView>
      </BottomSheetModal>
    );
  }
);

export default BottomSheetProduct;
