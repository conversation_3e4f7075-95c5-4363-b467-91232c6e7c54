import React, {useEffect} from 'react';
import {
  Alert,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  useWindowDimensions,
} from 'react-native';
import I18n from '../I18n';
import {Colors, Tme} from '../ThemeStyle';
import CardView from '../share/CardView';
import {Helper} from '../Helper';
import { mainRadius } from '../Tools';
import {hideLoading, showLoading} from '../../ILoading';

export default function DownloadBilling() {
  const [email, setEmail] = React.useState('');
  const {width} = useWindowDimensions();

  useEffect(() => {
    doFetchData();
  });

  const doFetchData = () => {
    showLoading();
    Helper.httpGET(Helper.urlWithQuery('/users/home_data'), {
      cloud: true,
      success: data => {
        setEmail(data.user.customer_email);
      },
      ensure: () => {
        hideLoading();
      },
    });
  };

  const send = () => {
    if (!email) {
      Alert.alert(
        I18n.t('home.warning_message'),
        I18n.t('session.email_input'),
      );
      return;
    }
    showLoading();
    Helper.httpPOST(
      '',
      {
        success: data => {
          Alert.alert(I18n.t('pay.download_success'));
        },
        ensure: () => {
          hideLoading();
        },
      },
      {email},
    );
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: Tme('bgColor'),
        alignItems: 'center',
        marginTop: 20,
      }}>
      <CardView
        withWaveBg={true}
        styles={{
          padding: 20,
          borderRadius: 8,
          alignItems: 'center',
        }}>
        <View
          style={[styles.account_view, {borderColor: Tme('inputBorderColor')}]}>
          <TextInput
            returnKeyType="go"
            autoCapitalize="none"
            underlineColorAndroid="transparent"
            autoCorrect={false}
            value={email}
            keyboardType="email-address"
            onChangeText={setEmail}
            placeholderTextColor={Tme('placeholder')}
            placeholder={I18n.t('home.email')}
            style={[Colors.TextInputStyle(), {width: width - 100}]}
          />
        </View>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={send}
          style={{
            backgroundColor: Colors.MainColor,
            width: width - 80,
            paddingVertical: 10,
            borderRadius: mainRadius(),
            alignItems: 'center',
          }}>
          <Text style={{color: '#fff'}}>{I18n.t('session.send')}</Text>
        </TouchableOpacity>
      </CardView>
    </View>
  );
}

const styles = StyleSheet.create({
  account_view: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 3,
    marginBottom: 20,
    marginTop: 20,
  },
});
