import React, { useState } from 'react';
import {
  Alert,
  Keyboard,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Tme, Colors } from '../ThemeStyle';
import { mainRadius } from '../Tools';
import { Helper } from '../Helper';
import { Toast } from '../Toast';
import {
  AddressSheet,
  AddressSheetError,
  PaymentSheetError,
  useStripe,
} from '@stripe/stripe-react-native';
import { hideLoading, showLoading } from '../../ILoading';
import I18n from '../I18n';
import { useNavigation, useRoute } from '@react-navigation/native';

import PaymentScreen from './PaymentScreen';

export default function CreateStripeCustomer() {
  const [email, setEmail] = useState('');

  const [address, setAddress] = useState(null);
  const { initPaymentSheet, presentPaymentSheet } = useStripe();
  const [addressSheetVisible, setAddressSheetVisible] = useState(false);

  const route = useRoute();
  const navigation = useNavigation();

  const createCustomer = () => {
    if (!email) {
      Alert.alert(I18n.t('session.email_input'));
      return;
    }
    if (!address) {
      Alert.alert(I18n.t('session.address_empty'));
      return;
    }
    showLoading();
    Helper.httpPOST(
      '/users/create_stripe_customer',
      {
        success: () => {
          fetchPaymentSheetParams();
        },
      },
      {
        name: address.name,
        email: email,
        phone: address.phone,
        country: address.address.country,
        city: address.address.city,
        line1: address.address.line1,
        line2: address.address.line2,
        postal_code: address.address.postal_code,
      },
    );
  };

  const fetchPaymentSheetParams = () => {
    Helper.httpPOST(
      '/orders',
      {
        success: data => {
          initializePaymentSheet(data);
        },
        error: () => {
          hideLoading();
        },
      },
      {
        type: route.params.type,
      },
    );
  };

  const initializePaymentSheet = async clientSecret => {
    const { error } = await initPaymentSheet({
      paymentIntentClientSecret: clientSecret.client_secret,
      returnURL: 'presen://payment-sheet',
      merchantDisplayName: 'Presen, Inc.',
      // Set `allowsDelayedPaymentMethods` to true if your business handles
      // delayed notification payment methods like US bank accounts.
      allowsDelayedPaymentMethods: true,
    });
    if (!error) {
      hideLoading();
      pay();
      // Handle error
    } else {
      hideLoading();
      Alert.alert(I18n.t('home.warning_message'), error.localizedMessage);
      console.log(error);
    }
  };

  const pay = async () => {
    const { error } = await presentPaymentSheet();
    if (error) {
      if (error.code === PaymentSheetError.Failed) {
        Alert.alert(I18n.t('home.warning_message'), error.message);
      } else if (error.code === PaymentSheetError.Canceled) {
        // Handle canceled
      }
    } else {
      navigation.goBack();
      Toast.show(I18n.t('pay.success'));
    }
  };

  return (
    <SafeAreaView
      edges={['left', 'right']}
      style={{ flex: 1, backgroundColor: Tme('bgColor') }}>
      <PaymentScreen>
        <View
          style={{
            padding: 20,
            backgroundColor: Tme('cardColor'),
            borderRadius: mainRadius(),
            position: 'relative',
          }}>
          <View
            style={{
              borderColor: Tme('inputBorderColor'),
              paddingVertical: 3,
              borderWidth: 1,
              borderRadius: 3,
              marginBottom: 20,
              marginTop: 16,
            }}>
            <TextInput
              placeholderTextColor={Tme('placeholder')}
              style={[Colors.TextInputStyle()]}
              autoCapitalize="none"
              keyboardType="email-address"
              underlineColorAndroid="transparent"
              placeholder={I18n.t('home.email')}
              value={email}
              onBlur={() => {
                Keyboard.dismiss();
              }}
              onChangeText={value => {
                setEmail(value);
              }}
            />
          </View>
          <TouchableOpacity
            onPress={() => {
              setAddressSheetVisible(true);
            }}
            activeOpacity={0.8}
            style={{
              borderColor: Tme('inputBorderColor'),
              paddingVertical: 3,
              borderWidth: 1,
              borderRadius: 3,
            }}>
            <View
              style={[
                {
                  justifyContent: 'center',
                  marginLeft: 6,
                  marginRight: 6,
                  minHeight: 40,
                },
              ]}>
              {address ? (
                <>
                  <Text>{address.name}</Text>
                  <Text>
                    {address.address.country},{address.address.city},
                    {address.address.line1}
                  </Text>
                  <Text>{address.phone}</Text>
                </>
              ) : (
                <Text>{I18n.t('pay.title')}</Text>
              )}
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              createCustomer();
            }}
            activeOpacity={0.8}
            style={{
              backgroundColor: Colors.MainColor,
              paddingVertical: 10,
              borderRadius: mainRadius(),
              marginTop: 20,
              alignItems: 'center',
            }}>
            <Text style={{ color: '#fff', fontSize: 16 }}>
              {I18n.t('home.submit')}
            </Text>
          </TouchableOpacity>
        </View>

        <AddressSheet
          visible={addressSheetVisible}
          presentationStyle={'popover'}
          animationStyle={'flip'}
          appearance={{}}
          onSubmit={async result => {
            setAddressSheetVisible(false);
            setAddress(result);
          }}
          onError={err => {
            if (err.code === AddressSheetError.Failed) {
              console.log(err?.localizedMessage);
            }
            setAddressSheetVisible(false);
          }}
          additionalFields={{
            phoneNumber: 'required',
          }}
          primaryButtonTitle={I18n.t('home.submit')}
          sheetTitle={I18n.t('pay.title')}
          googlePlacesApiKey={''}
        />
      </PaymentScreen>
    </SafeAreaView>
  );
}
