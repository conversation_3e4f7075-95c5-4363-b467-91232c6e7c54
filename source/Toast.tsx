import React, {createContext, useContext, useState} from 'react';
import {View} from 'react-native';
import I18n from './I18n';
import {Snackbar} from 'react-native-paper';

// 创建一个全局引用
let toastRef: {show: (content?: string) => void} | null = null;

interface ToastContextType {
  show: (content?: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const ToastProvider: React.FC<{children: React.ReactNode}> = ({children}) => {
  const [visible, setVisible] = useState(false);
  const [content, setContent] = useState<string | null>(null);

  const show = (text?: string) => {
    setContent(text || null);
    setVisible(true);
    setTimeout(() => {
      setVisible(false);
    }, 3000);
  };

  // 保存引用以供静态方法使用
  React.useEffect(() => {
    toastRef = {show};
    return () => {
      toastRef = null;
    };
  }, []);

  return (
    <ToastContext.Provider value={{show}}>
      {children}
      <View style={{position: 'absolute', bottom: 60, left: 0, right: 0}}>
        <Snackbar
          visible={visible}
          onDismiss={() => setVisible(false)}
          action={{
            label: I18n.t('home.close_hint'),
            onPress: () => setVisible(false),
          }}>
          {content || I18n.t('home.success')}
        </Snackbar>
      </View>
    </ToastContext.Provider>
  );
};

// Hook 方式 - 用于函数组件
export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

// 静态方法 - 用于类组件
export class Toast {
  static show(content?: string) {
    if (toastRef) {
      toastRef.show(content);
    } else {
      console.warn('Toast is not initialized. Make sure ToastProvider is mounted.');
    }
  }
}
