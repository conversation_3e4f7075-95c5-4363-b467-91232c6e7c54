/* eslint-disable react/no-unstable-nested-components */
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Linking, SafeAreaView, StyleSheet } from 'react-native';
import { Helper } from '../Helper';
import { Tme, Colors } from '../ThemeStyle';
import <PERSON><PERSON>iewer from 'react-native-image-zoom-viewer';
import { IImageInfo } from 'react-native-image-zoom-viewer/built/image-viewer.type';
import I18n from '../I18n';
import { DownloadImage } from './DownLoadImage';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { hideLoading, showLoading } from '../../ILoading';
import AlertModal from '../share/AlertModal';
import { hasSavePermission } from '../Util';
import { useNavigation, useRoute } from '@react-navigation/native';
import { IpcNavigationProp, IpcRouteParamList } from '../device/ipc/types/navigation';
import { RouteProp } from '@react-navigation/native';

const LookImage: React.FC = () => {
  const [image, setImage] = useState<IImageInfo[]>([]);
  const navigation = useNavigation<IpcNavigationProp>();
  const route = useRoute<RouteProp<IpcRouteParamList, 'LookImage'>>();

  // 保存图片
  const saveImage = (uri: string): void => {
    hasSavePermission()
      .then((hasPermission: boolean) => {
        showLoading();
        if (hasPermission) {
          DownloadImage(uri, 'image')!
            .then((_res: any) => {
              hideLoading();
            })
            .catch((error: Error) => {
              hideLoading();
              console.log(error);
            });
        } else {
          hideLoading();
          AlertModal.alert(
            I18n.t('home.warning_message'),
            I18n.t('permissions.media_title'),
            [
              {
                text: I18n.t('home.cancel'),
                onPress: () => {},
                style: 'cancel',
              },
              {
                text: I18n.t('home.confirm'),
                onPress: () => {
                  Linking.openSettings();
                },
              },
            ],
          );
        }
      })
      .catch((error: any) => {
        AlertModal.alert(I18n.t('home.error'), error);
      });
  };

  // 右上角按钮点击事件
  const rightClick = (): void => {
    if (image.length > 0) {
      saveImage(image[0].url);
    }
  };

  // 获取图片数据
  const doFetchData = (): void => {
    showLoading();
    Helper.httpGET(
      Helper.urlWithQuery('/partner/ip_cameras/get_image', { file: route.params.file }),
      {
        success: (data: { url: string }) => {
          setImage([
            {
              url: data.url,
            },
          ]);
          hideLoading();
        },
      },
    );
  };

  // 设置导航栏按钮
  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          rightClick={rightClick}
          text={I18n.t('event.save_image')}
        />
      ),
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigation]);

  // 组件加载时获取数据
  useEffect(() => {
    doFetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      {image.length > 0 && (
        <ImageViewer
          loadingRender={() => (
            <ActivityIndicator size="small" color={Colors.MainColor} />
          )}
          imageUrls={image}
          backgroundColor={Tme('bgColor')}
          onSave={(uri: string) => saveImage(uri)}
          menuContext={{
            saveToLocal: I18n.t('event.save_image'),
            cancel: I18n.t('home.cancel'),
          }}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Tme('bgColor'),
  },
});

export default LookImage;
