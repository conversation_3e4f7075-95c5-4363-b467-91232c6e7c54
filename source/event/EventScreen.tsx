/* eslint-disable react/no-unstable-nested-components */
import React, {useEffect, useState, useRef} from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Linking,
  Modal,
  Platform,
  Alert,
  FlatList,
  ListRenderItemInfo,
  Dimensions,
} from 'react-native';
import {Helper, HelperMemo} from '../Helper';
import I18n from '../I18n';
import {Tme, Colors} from '../ThemeStyle';
import EmptyView from '../share/EmptyView';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import PubSub from 'pubsub-js';
import {mainRadius} from '../Tools';
import {hideLoading, showLoading} from '../../ILoading';
import AlertModal from '../share/AlertModal';
import {ActivityIndicator} from 'react-native';
import FastImage from 'react-native-fast-image';
import {hasSavePermission} from '../Util';
import {DownloadImage} from './DownLoadImage';
import ImageViewer from 'react-native-image-zoom-viewer';
import {PUBSUB_SELECT_DEVICE_EVENT} from '../types/PubSubEvent';
import ImagePreviewView from '../device/ImagePreviewViewProps';

const HIT_SLOP = {top: 16, left: 16, bottom: 16, right: 16};

// 定义类型接口
interface EventScreenProps {
  navigation: {
    push: (screen: string, params?: any) => void;
  };
  route: {
    params: {
      device_uuid?: string;
      device_name?: string;
      value_id?: string;
      sn_id?: string;
      _from?: string;
    };
  };
}

interface EventDataItem {
  id: string;
  time: string;
  date: string;
  event_type: string;
  event_name: string;
  event_sub_type?: string;
  spec_name: string;
  spec_value: string;
  spec_scale?: string;
  unit_index?: number;
  ipc_screenshot_url?: string;
  ipc_video_url?: string;
  ipc_video_success: boolean;
  rekognition_labels?: string[];
}

interface PubSubEventData {
  key: string;
  value?: string;
  name?: string;
}

interface ImageItem {
  url: string;
}

export default function EventScreen(
  props: EventScreenProps,
): React.ReactElement {
  const flatListRef = useRef<FlatList<EventDataItem>>(null);
  const nextPageRef = useRef<number>(1); // 使用 useRef 替代 useState 管理 nextPage
  const selectKeyRef = useRef<string>(
    props.route.params.device_name ? 'device' : '',
  ); // 使用 useRef 替代 useState 管理 selectKey
  const device_uuidRef = useRef<string>(
    props.route.params.device_uuid ? props.route.params.device_uuid : '',
  ); // 使用 useRef 替代 useState 管理 device_uuid

  const [dataSource, setDataSource] = useState<EventDataItem[]>([]);
  // device_uuid 和 selectKey 已经改为 useRef
  const [selectVale, setSelectVale] = useState<string>(
    props.route.params.device_name ? props.route.params.device_name : '',
  );

  const [devices, setDevices] = useState<string[]>([]);
  const [isRefresh, setIsRefresh] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [visible, setVisible] = useState<boolean>(false);
  const [showImages, setShowImages] = useState<ImageItem[]>([]);

  useEffect(() => {
    doFetchData();

    // 订阅设备选择事件
    const subscription = PubSub.subscribe(
      PUBSUB_SELECT_DEVICE_EVENT,
      (_msg: string, data: PubSubEventData) => {
        // 重置页码，确保从第一页开始获取
        nextPageRef.current = 1;
        setDataSource([]);
        // 处理"全部"筛选条件
        if (data.key === 'all') {
          // 清空所有筛选条件
          device_uuidRef.current = '';
          setSelectVale(I18n.t('global.all'));
          selectKeyRef.current = '';
          doFetchData();
          return;
        }

        // 处理设备筛选
        if (data.key === 'device') {
          if (!data.value) {
            // 当设备值为空时，视为选择"全部"
            device_uuidRef.current = '';
            setSelectVale(I18n.t('global.all'));
            selectKeyRef.current = '';
          } else {
            // 设置设备ID和名称
            device_uuidRef.current = data.value;
            setSelectVale(data.name || '');
            selectKeyRef.current = data.key;
          }
        } else {
          // 处理其他类型筛选(controller/scene/action等)
          device_uuidRef.current = '';
          setSelectVale(data.value || '');
          selectKeyRef.current = data.key;
        }

        // 执行数据获取
        doFetchData();
      },
    );

    // 清除订阅
    return () => {
      PubSub.unsubscribe(subscription);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const event = (): void => {
    props.navigation.push('EventDeviceList', {
      device_uuid: device_uuidRef.current,
      selectVale: selectVale,
      selectKey: selectKeyRef.current,
      devices: devices,
      title: I18n.t('event.filter_events'),
    });
  };

  const refresh = (): void => {
    nextPageRef.current = 1;
    setIsRefresh(true); // 设置刷新状态为true
    doFetchData();
  };

  const _reachEnd = (): void => {
    if (nextPageRef.current > 1 && isLoading === false) {
      doFetchData();
    }
  };

  const _renderRow = ({
    item,
    index,
  }: ListRenderItemInfo<EventDataItem>): React.ReactElement => {
    var rowData = item;

    return (
      <View key={index} style={{marginBottom: 10}}>
        <View style={{flexDirection: 'row'}}>
          <View style={{marginRight: 17, marginTop: 2}}>
            <Image
              style={{width: 14, height: 14}}
              source={require('../../img/time.png')}
            />
          </View>
          <View style={{flex: 1, flexDirection: 'row'}}>
            <Text
              style={{
                fontSize: 14,
                marginRight: 10,
                color: Tme('smallTextColor'),
              }}>
              {rowData.time}
            </Text>
            <Text style={{fontSize: 14, color: Tme('smallTextColor')}}>
              {rowData.date}
            </Text>
          </View>
        </View>
        <View style={{marginTop: 10, flexDirection: 'row'}}>
          <View
            style={{
              width: 2,
              alignItems: 'center',
              marginRight: 20,
              marginLeft: 5,
              backgroundColor: '#e0edfa',
            }}
          />

          <View
            style={{
              flexDirection: 'column',
              marginRight: 16,
              flex: 1,
            }}>
            <View
              style={{
                flexDirection: 'column',
                flex: 1,
              }}>
              <Text
                style={{
                  fontSize: 15,
                  color: Tme('cardTextColor'),
                  marginBottom: 8,
                }}>
                {valueScale(rowData)}
              </Text>
              {rowData.ipc_screenshot_url && (
                <ImagePreviewView
                  imageUrl={rowData.ipc_screenshot_url}
                  onImagePress={() => {
                    setVisible(true);
                    setShowImages([{url: rowData.ipc_screenshot_url!}]);
                  }}
                  onVideoPress={() => openApp(rowData)}
                  hasVideo={rowData.ipc_video_success}
                />
              )}
            </View>
          </View>
        </View>
      </View>
    );
  };

  const valueScale = (spec: EventDataItem): string => {
    var value = '';
    switch (spec.event_type) {
      case 'routine':
        value = I18n.t(spec.spec_value);
        break;
      case 'device_state':
        value =
          I18n.t('device.device') +
          ' ' +
          spec.event_name +
          ' ' +
          Helper.i('is') +
          ' ' +
          I18n.t('home.' + spec.spec_value);
        break;
      case 'controller_state':
        switch (spec.event_sub_type) {
          case 'cloud':
            value =
              I18n.t('home.controller') +
              ' ' +
              Helper.i('is') +
              ' ' +
              I18n.t('home.' + spec.spec_value);
            break;
          case 'comm_module':
            value =
              I18n.t('home.controller') +
              ' ' +
              I18n.t('global.' + spec.event_name) +
              ' ' +
              I18n.t('global.' + spec.spec_value);
            break;
        }
        break;
      case 'action_exec':
        value =
          I18n.t('global.action') +
          ' ' +
          spec.event_name +
          ' ' +
          I18n.t('trigger.execute');
        break;
      case 'routine_exec':
        value =
          I18n.t('index.automation') +
          ' ' +
          spec.event_name +
          ' ' +
          I18n.t('trigger.execute');
        break;
      case 'scene_exec':
        value =
          I18n.t('global.scene') +
          ' ' +
          Helper.i('switch_to') +
          ' ' +
          spec.event_name;
        break;
      case 'battery_low':
        value =
          I18n.t('device.device') +
          ' ' +
          spec.event_name +
          ' ' +
          Helper.i('is') +
          ' ' +
          Helper.i('going_to_low_battery');
        break;
      case 's3_image':
        value = I18n.t('device.device') + ' ' + spec.event_name;
        break;
      case 'ipc':
        let labels = '';
        if (spec.rekognition_labels!.includes('person')) {
          labels = I18n.t('spec.person_detected');
        } else if (spec.rekognition_labels!.includes('car')) {
          labels = I18n.t('spec.car_detected');
        } else if (spec.rekognition_labels!.includes('animal')) {
          labels = I18n.t('spec.animal_detected');
        } else if (spec.rekognition_labels!.includes('package')) {
          labels = I18n.t('spec.package_detected');
        }
        value = `${I18n.t('spec.camera')} ${spec.event_name} ${labels}`;
        break;
      default:
        if (
          spec.spec_name.toUpperCase() == 'TEMPERATURE' ||
          spec.spec_name.toUpperCase() == 'THERMOSTATSETPOINT'
        ) {
          value =
            I18n.t('device.device') +
            ':' +
            Helper.i(spec.spec_name) +
            ' ' +
            Helper.temperatureScale(
              Number(spec.spec_value),
              Number(spec.unit_index),
              false,
            ) +
            ' ' +
            (spec.spec_scale ? '°' + HelperMemo.user_data.user.scale : '');
        } else {
          value =
            I18n.t('device.device') +
            ' ' +
            spec.event_name +
            ' ' +
            Helper.i(spec.spec_name) +
            ' ' +
            Helper.i(spec.spec_value) +
            (spec.spec_scale ? spec.spec_scale : '');
        }
    }
    return value;
  };

  const openApp = (rowData: EventDataItem): void => {
    showLoading();
    Helper.httpGET(
      Helper.urlWithQuery('/events/ipc_video_url', {id: rowData.id}),
      {
        success: (data: {url: string}) => {
          if (data.url) {
            props.navigation.push('LookVideo', {
              file: data.url,
            });
          } else {
            Alert.alert(
              I18n.t('home.warning_message'),
              I18n.t('ipc.ipc_video_null'),
              [
                {
                  text: I18n.t('home.cancel'),
                  onPress: () => {},
                  style: 'cancel',
                },
              ],
            );
          }
        },
        ensure: () => {
          hideLoading();
        },
      },
    );
  };

  const doFetchData = (): void => {
    if (nextPageRef.current == -1) {
      return;
    }

    setIsLoading(true);
    // 只有在不是下拉刷新状态时才显示loading指示器
    if (!isRefresh) {
      showLoading();
    }

    var data: {
      page: number;
      device_uuid: string;
      event_type: string;
      value_id?: string;
      sn_id?: string;
      _from?: string;
    } = {
      page: nextPageRef.current,
      device_uuid: device_uuidRef.current ? device_uuidRef.current : '',
      event_type: selectKeyRef.current ? selectKeyRef.current : '',
      value_id: props.route.params.value_id, // 确保这个值被保留
    };

    if (props.route.params.sn_id) {
      data.sn_id = props.route.params.sn_id;
    }

    if (props.route.params._from == 'home') {
      data._from = 'home';
    }

    Helper.httpGET(Helper.urlWithQuery('/events', data), {
      ensure: () => {
        hideLoading();
        setIsRefresh(false); // 请求完成后始终重置刷新状态
      },
      success: (responseData: any) => {
        if (nextPageRef.current === 1) {
          setDevices(responseData.devices.sort());
        }
        // 检查是否有返回数据
        if (!responseData.event || responseData.event.length === 0) {
          // 没有更多数据
          nextPageRef.current = -1;
          return;
        }

        if (nextPageRef.current === 1) {
          // 首次加载或刷新，直接设置数据
          setDataSource(responseData.event);
        } else {
          // 加载更多时，确保添加的数据不会重复
          setDataSource(prevData => {
            const newData = [...prevData];
            const existingIds = new Set(newData.map(item => item.id));

            // 过滤掉已有的数据，只添加新的
            const uniqueNewEvents = responseData.event.filter(
              (item: EventDataItem) => !existingIds.has(item.id),
            );

            return [...newData, ...uniqueNewEvents];
          });
        }

        setIsLoading(false);
        nextPageRef.current = responseData.next_page;
      },
    });
  };

  return (
    <View style={{backgroundColor: Tme('bgColor'), flex: 1}}>
      <View
        style={{
          flex: 1,
          flexDirection: 'column',
          backgroundColor: Tme('bgColor'),
        }}>
        <View style={{marginTop: 10}}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={event}
            style={{
              backgroundColor: Tme('cardColor'),
              borderRadius: mainRadius(),
            }}>
            <View
              style={{
                marginHorizontal: 20,
                paddingVertical: 16,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text style={{color: Tme('cardTextColor'), fontSize: 16}}>
                  {I18n.t('event.filter_events')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text style={{color: Tme('cardTextColor')}}>{selectVale}</Text>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </View>
          </TouchableOpacity>
        </View>
        <ImageShow
          images={showImages}
          open={visible}
          onRequestClose={() => {
            setVisible(false);
          }}
        />

        <FlatList
          style={{
            flex: 1,
            marginTop: 20,
            paddingTop: 20,
            paddingHorizontal: 20,
            backgroundColor: Tme('cardColor'),
            borderTopRightRadius: mainRadius(),
            borderTopLeftRadius: mainRadius(),
          }}
          showsVerticalScrollIndicator={false}
          ref={flatListRef}
          data={dataSource}
          renderItem={_renderRow}
          numColumns={1}
          onEndReachedThreshold={0.1}
          ListEmptyComponent={() => <EmptyView />}
          onRefresh={refresh}
          refreshing={isRefresh}
          keyExtractor={(item, index) =>
            item.id ? `${item.id}-${index}` : `item-${index}`
          }
          onEndReached={_reachEnd}
          ListFooterComponent={<View style={{height: 80}} />}
        />
      </View>
    </View>
  );
}

interface ImageLoadingProps {
  height: number;
  uri: string;
}

export function ImageLoading({
  height,
  uri,
}: ImageLoadingProps): React.ReactElement {
  const [showActive, setShowActive] = React.useState<boolean>(false);

  const onLoadEnd = (): void => {
    setShowActive(false);
  };

  return (
    <View
      style={{
        position: 'relative',
        alignItems: 'center',
        justifyContent: 'center',
        height: height,
        width: '100%',
      }}>
      <FastImage
        source={{
          uri: uri,
        }}
        onLoadStart={() => {
          setShowActive(true);
        }}
        onLoadEnd={onLoadEnd}
        style={{height: '100%', width: '100%', borderRadius: 3}}
        resizeMode="cover"
      />
      {showActive && (
        <View
          style={{
            position: 'absolute',
            top: 0,
            bottom: 0,
            left: 0,
            right: 0,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <ActivityIndicator size="small" color={Colors.MainColor} />
        </View>
      )}
    </View>
  );
}

interface ImageShowProps {
  images: ImageItem[];
  open: boolean;
  onRequestClose: () => void;
}

export function ImageShow({
  images,
  open,
  onRequestClose,
}: ImageShowProps): React.ReactElement {
  const _saveImage = (uri: string): void => {
    // 使用 react-native-permissions 来请求权限
    // 保存图片到相册

    hasSavePermission()
      .then(hasPermission => {
        showLoading();
        if (hasPermission) {
          DownloadImage(uri, 'image')!
            .then(res => {
              console.log('res', res);
              hideLoading();
            })
            .catch(error => {
              hideLoading();
              console.log(error);
            });
        } else {
          hideLoading();
          AlertModal.alert(
            I18n.t('home.warning_message'),
            I18n.t('permissions.media_title'),
            [
              {
                text: I18n.t('home.cancel'),
                onPress: () => {},
                style: 'cancel',
              },
              {
                text: I18n.t('home.confirm'),
                onPress: () => {
                  Linking.openSettings();
                },
              },
            ],
          );
        }
      })
      .catch(error => {
        AlertModal.alert(I18n.t('home.error'), error);
      });
  };
  return (
    <Modal
      visible={open}
      transparent={true}
      animationType="fade"
      presentationStyle="overFullScreen"
      statusBarTranslucent
      navigationBarTranslucent
      onRequestClose={() => {
        onRequestClose();
      }}>
      <ImageViewer
        loadingRender={() => (
          <ActivityIndicator size="small" color={Colors.MainColor} />
        )}
        imageUrls={images}
        saveToLocalByLongPress={false}
        renderIndicator={() => <View />}
      />
      <View
        style={{position: 'absolute', width: Dimensions.get('window').width}}>
        <View
          style={{
            marginTop: Platform.OS === 'ios' ? 58 : 40,
            paddingHorizontal: 20,
            alignItems: 'center',
            justifyContent: 'space-between',
            flexDirection: 'row',
          }}>
          <TouchableOpacity
            activeOpacity={0.8}
            style={{
              width: 45,
              height: 45,
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onPress={() => {
              onRequestClose();
            }}
            hitSlop={HIT_SLOP}>
            <Text
              style={{
                lineHeight: 25,
                fontSize: 25,
                includeFontPadding: false,
                color: Colors.MainColor,
              }}>
              ✕
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            activeOpacity={0.8}
            style={{
              alignItems: 'center',
              justifyContent: 'center',
            }}
            onPress={() => {
              AlertModal.alert(I18n.t('event.save_image'), '', [
                {
                  text: I18n.t('home.cancel'),
                  onPress: () => {},
                  style: 'cancel',
                },
                {
                  text: I18n.t('home.confirm'),
                  onPress: () => {
                    _saveImage(images[0].url);
                  },
                },
              ]);
            }}
            hitSlop={HIT_SLOP}>
            <Text
              style={{
                lineHeight: 25,
                includeFontPadding: false,
                color: Colors.MainColor,
              }}>
              {I18n.t('event.save_image')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}
