import React, {useState, useEffect} from 'react';
import {StyleSheet, View} from 'react-native';
import Svg, {Path, Circle} from 'react-native-svg';
import {GestureDetector, Gesture} from 'react-native-gesture-handler';
import {runOnJS} from 'react-native-reanimated';

interface Corner {
  x: number;
  y: number;
}

interface Props {
  corners: Corner[];
  width: number;
  height: number;
  onRegionChange: (region: string) => void;
}

const CORNER_RADIUS = 40;
const MIN_SIZE = 50;
// const MIN_DISTANCE = 10;

const ResizableDraggableView: React.FC<Props> = ({
  corners: initialCorners,
  width,
  height,
  onRegionChange,
}) => {
  // 使用普通的 React 状态
  const [corners, setCorners] = useState<Corner[]>(initialCorners);

  // 当前激活的角点索引
  const [activeCorner, setActiveCorner] = useState<number | null>(null);

  // 当属性变化时更新角点
  useEffect(() => {
    setCorners(initialCorners);
  }, [initialCorners]);

  // 处理角点更新并验证有效性的函数
  const handleCornerUpdate = (
    activeIndex: number,
    newX: number,
    newY: number,
  ): void => {
    // 获取当前角点的副本
    const currentCorners = [...corners];

    // 验证新位置是否有效
    if (isValidCornerPosition(currentCorners, activeIndex, newX, newY)) {
      // 创建更新后的角点数组
      const newCorners = [...currentCorners];
      newCorners[activeIndex] = {x: newX, y: newY};

      // 更新角点状态
      updateCorners(newCorners);
    }
  };

  const isValidCornerPosition = (
    currentCorners: Corner[],
    activeIndex: number,
    newX: number,
    newY: number,
  ): boolean => {
    // 创建新的角点数组用于验证
    const newCorners = [...currentCorners];
    newCorners[activeIndex] = {x: newX, y: newY};

    // 基本边界检查 - 确保点在视图范围内
    if (newX < -10 || newX > width + 10 || newY < -10 || newY > height + 10) {
      return false;
    }

    // 确保角点遵循正确的几何位置
    // 角点顺序: 0-左上, 1-右上, 2-右下, 3-左下
    const [leftTop, rightTop, rightBottom, leftBottom] = newCorners;

    // 根据当前移动的角点检查约束条件
    switch (activeIndex) {
      case 0: // 左上角
        // 左上角必须在右上角的左边
        if (newX > rightTop.x) {
          return false;
        }
        // 左上角的 y 坐标不能大于左下角
        if (newY > leftBottom.y) {
          return false;
        }
        break;
      case 1: // 右上角
        // 右上角必须在左上角的右边
        if (newX < leftTop.x) {
          return false;
        }
        // 右上角的 y 坐标不能大于右下角
        if (newY > rightBottom.y) {
          return false;
        }
        break;
      case 2: // 右下角
        // 右下角必须在左下角的右边
        if (newX < leftBottom.x) {
          return false;
        }
        // 右下角的 y 坐标不能小于右上角
        if (newY < rightTop.y) {
          return false;
        }
        break;
      case 3: // 左下角
        // 左下角必须在右下角的左边
        if (newX > rightBottom.x) {
          return false;
        }
        // 左下角的 y 坐标不能小于左上角
        if (newY < leftTop.y) {
          return false;
        }
        break;
    }

    // 确保四边形有足够的大小
    const minSize = MIN_SIZE / 2;

    // 检查宽度
    const topWidth = Math.abs(rightTop.x - leftTop.x);
    const bottomWidth = Math.abs(rightBottom.x - leftBottom.x);

    // 检查高度
    const leftHeight = Math.abs(leftBottom.y - leftTop.y);
    const rightHeight = Math.abs(rightBottom.y - rightTop.y);

    if (
      topWidth < minSize ||
      bottomWidth < minSize ||
      leftHeight < minSize ||
      rightHeight < minSize
    ) {
      return false;
    }

    return true;
  };

  const updateCorners = (newCorners: Corner[]): void => {
    // 创建新的角点数组，避免引用问题
    const cornersCopy = newCorners.map(corner => ({...corner}));

    // 更新 React 状态
    setCorners(cornersCopy);

    // 通知父组件区域已更改
    const normalizedCorners = [
      [newCorners[0].x, newCorners[0].y], // 左上
      [newCorners[1].x, newCorners[1].y], // 右上
      [newCorners[2].x, newCorners[2].y], // 右下
      [newCorners[3].x, newCorners[3].y], // 左下
    ]
      .map(([px, py]) => [
        Math.round((px / width) * 1000),
        Math.round((py / height) * 1000),
      ])
      .flat();

    onRegionChange(normalizedCorners.join(','));
  };

  const panGesture = Gesture.Pan()
    .onStart(e => {
      'worklet';
      // 检查是否点击到角点 - 找到最近的角点
      let closestCornerIndex = -1;
      let minDistance = CORNER_RADIUS * 2; // 设置一个较大的初始距离

      for (let i = 0; i < corners.length; i++) {
        // 计算距离
        const dx = e.x - corners[i].x;
        const dy = e.y - corners[i].y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // 更新最近的角点
        if (distance < minDistance && distance < CORNER_RADIUS) {
          minDistance = distance;
          closestCornerIndex = i;
        }
      }

      // 如果找到了最近的角点，就激活它
      if (closestCornerIndex !== -1) {
        runOnJS(setActiveCorner)(closestCornerIndex);
      }
    })
    .onUpdate(e => {
      'worklet';
      if (activeCorner !== null) {
        const newX = Math.max(0, Math.min(e.x, width));
        const newY = Math.max(0, Math.min(e.y, height));

        // 将验证和更新逻辑合并到一个JS函数中处理
        runOnJS(handleCornerUpdate)(activeCorner, newX, newY);
      }
    })
    .onEnd(() => {
      'worklet';
      runOnJS(setActiveCorner)(null);
    });

  const svgPath = `M ${corners[0].x} ${corners[0].y} L ${corners[1].x} ${corners[1].y} L ${corners[2].x} ${corners[2].y} L ${corners[3].x} ${corners[3].y} Z`;

  return (
    <View style={styles.container}>
      <GestureDetector gesture={panGesture}>
        <Svg width={width} height={height}>
          {/* 半透明背景 */}
          <Path d={svgPath} fill="rgba(255,0,0,0.1)" stroke="none" />
          {/* 边框 */}
          <Path d={svgPath} stroke="red" strokeWidth="2" fill="none" />
          {/* 角点和点击区域 */}
          {corners.map((corner, index) => (
            <React.Fragment key={index}>
              <Circle
                cx={corner.x}
                cy={corner.y}
                r={CORNER_RADIUS}
                fill="rgba(255,0,0,0.1)"
              />
              <Circle cx={corner.x} cy={corner.y} r={8} fill="red" />
            </React.Fragment>
          ))}
        </Svg>
      </GestureDetector>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    top: 0,
    right: 0,
    bottom: 0,
  },
});

export default ResizableDraggableView;
