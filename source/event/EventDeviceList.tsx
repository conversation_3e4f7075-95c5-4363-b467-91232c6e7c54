import React, { useState, useMemo, ReactElement } from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  Text,
  LayoutAnimation,
} from 'react-native';
import { Tme } from '../ThemeStyle';
import NavBarView from '../share/NavBarView';
import PubSub from 'pubsub-js';
import _ from 'lodash';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import I18n from '../I18n';
import { RouteProp } from '@react-navigation/native';
import { NavigationProp } from '@react-navigation/native';
import { PUBSUB_SELECT_DEVICE_EVENT } from '../types/PubSubEvent';

const filter = ['controller', 'scene', 'action', 'automation', 'device'];

// 定义设备接口
interface Device {
  uuid: string;
  name: string;
}

// 定义路由参数接口
interface RouteParams {
  device_uuid: string;
  selectKey: string;
  devices: Device[];
}

// 定义组件属性接口
interface EventDeviceListProps {
  navigation: NavigationProp<any>;
  route: RouteProp<{ params: RouteParams }, 'params'>;
}

const EventDeviceList: React.FC<EventDeviceListProps> = ({ navigation, route }) => {
  // 修改初始状态为 true，使设备列表默认显示，改善用户体验
  const [showDevice, setShowDevice] = useState<boolean>(
    route.params.device_uuid ? true : false,
  );
  console.log('EventDeviceList', route.params);
  // 使用useMemo替代memoizeOne，简化为直接使用route参数
  const checked = useMemo(
    () => (uuid: string) => route.params.device_uuid === uuid,
    [route.params.device_uuid]
  );

  // 点击设备处理函数
  const clickDevice = (data: Device | 'all') => {
    if (data === 'all') {
      PubSub.publish(PUBSUB_SELECT_DEVICE_EVENT, {
        key: 'all', // 修正为 all
        value: '',
      });
    } else {
      PubSub.publish(PUBSUB_SELECT_DEVICE_EVENT, {
        key: 'device',
        value: data.uuid,
        name: data.name,
      });
    }
    navigation.goBack();
  };

  // 点击类别处理函数
  const click = (row: string, title: string) => {
    if (row === 'device') {
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
      setShowDevice(!showDevice);
    } else {
      PubSub.publish(PUBSUB_SELECT_DEVICE_EVENT, {
        key: row,
        value: title,
      });
      navigation.goBack();
    }
  };

  // 渲染行组件
  const renderRow = () => {
    const html: ReactElement[] = [];
    const devices: ReactElement[] = [];

    _.forEach(filter, (row, index) => {
      let title = '';
      switch (row) {
        case 'controller':
          title = I18n.t('home.controller');
          break;
        case 'scene':
          title = I18n.t('global.scene');
          break;
        case 'action':
          title = I18n.t('global.action');
          break;
        case 'automation':
          title = I18n.t('index.routines');
          break;
        case 'device':
          title = I18n.t('device.device');
          break;
      }
      html.push(
        <TouchableOpacity
          key={index}
          activeOpacity={0.8}
          onPress={() => click(row, title)}
          style={[
            {
              height: 59,
              paddingHorizontal: 20,
              backgroundColor: Tme('cardColor'),
            },
            row === 'device'
              ? showDevice
                ? {
                    borderBottomWidth: 1,
                    borderBottomColor: Tme('inputBorderColor'),
                  }
                : { marginBottom: 8 }
              : { marginBottom: 8 },
          ]}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <Text
              style={{
                lineHeight: 59,
                fontSize: 17,
                color: Tme('cardTextColor'),
              }}>
              {title}
            </Text>
            {row === 'device' ? (
              showDevice ? (
                <MaterialIcons
                  name="keyboard-arrow-up"
                  size={20}
                  color={Tme('textColor')}
                />
              ) : (
                <MaterialIcons
                  name="keyboard-arrow-down"
                  size={20}
                  color={Tme('textColor')}
                />
              )
            ) : route.params.selectKey === row ? (
              <MaterialIcons name="check" size={20} color={Tme('textColor')} />
            ) : (
              <MaterialIcons
                name="keyboard-arrow-right"
                size={20}
                color={Tme('textColor')}
              />
            )}
          </View>
        </TouchableOpacity>,
      );
    });

    const length = route.params.devices.length - 1;
    _.forEach(route.params.devices, (v, k) => {
      devices.push(
        <TouchableOpacity
          key={v.uuid}
          activeOpacity={0.8}
          onPress={() => clickDevice(v)}
          style={[
            {
              height: 59,
              paddingHorizontal: 20,
              backgroundColor: Tme('cardColor'),
            },
            length === k
              ? {}
              : {
                  borderBottomWidth: 1,
                  borderBottomColor: Tme('inputBorderColor'),
                },
          ]}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <Text
              style={{
                lineHeight: 59,
                fontSize: 17,
                color: Tme('cardTextColor'),
              }}>
              {v.name}
            </Text>
            {checked(v.uuid) ? (
              <MaterialIcons name="check" size={24} color={Tme('cardTextColor')} />
            ) : (
              <MaterialIcons name="keyboard-arrow-right" size={20} color={Tme('textColor')} />
            )}
          </View>
        </TouchableOpacity>,
      );
    });

    return (
      <>
        {html}
        <View>{showDevice ? devices : null}</View>
      </>
    );
  };

  // 渲染主组件
  return (
    <NavBarView>
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={{
          flex: 1,
          backgroundColor: Tme('bgColor'),
        }}>
        <View style={{ marginTop: 20 }}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => clickDevice('all')}
            style={[
              {
                height: 59,
                paddingHorizontal: 20,
                backgroundColor: Tme('cardColor'),
                marginBottom: 8,
              },
            ]}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <Text
                style={{
                  lineHeight: 59,
                  fontSize: 17,
                  color: Tme('cardTextColor'),
                }}>
                {I18n.t('global.all')}
              </Text>
              {route.params.selectKey === '' || route.params.selectKey === 'all' ? (
                <MaterialIcons name="check" size={20} color={Tme('textColor')} />
              ) : (
                <MaterialIcons name="keyboard-arrow-right" size={20} color={Tme('textColor')} />
              )}
            </View>
          </TouchableOpacity>
          {renderRow()}
        </View>
      </ScrollView>
    </NavBarView>
  );
};

export default EventDeviceList;
