/* eslint-disable react/no-unstable-nested-components */
import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useLayoutEffect,
} from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Linking,
  View,
  BackHandler,
  StyleSheet,
  Animated,
} from 'react-native';
import {RouteProp} from '@react-navigation/native';
import {
  IpcNavigationProp,
  IpcRouteParamList,
} from '../device/ipc/types/navigation';
import {Colors} from '../ThemeStyle';
import I18n from '../I18n';
import HeaderRightBtn from '../share/HeaderRightBtn';
import AlertModal from '../share/AlertModal';
import {hasSavePermission} from '../Util';
import {DownloadImage} from './DownLoadImage';
import {hideLoading, showLoading} from '../../ILoading';
import Orientation from 'react-native-orientation-locker';
import type {OrientationType as RNOrientationType} from 'react-native-orientation-locker';
import {ReactNativeZoomableView} from '@openspacelabs/react-native-zoomable-view';
import Video from 'react-native-video';
import {ResizeMode} from 'react-native-video';

type LookVideoProps = {
  navigation: IpcNavigationProp;
  route: RouteProp<IpcRouteParamList, 'LookVideo'>;
};

type DimensionsType = {
  windowWidth: number;
  windowHeight: number;
  aspectRatio: number;
  initialVideoHeight?: number;
};

/**
 * 视频播放组件
 */
const LookVideo: React.FC<LookVideoProps> = props => {
  const {navigation, route} = props;
  const {file} = route.params;

  // 尺寸计算 - 使用 ref 记录初始维度避免重复计算
  const dimensions = useRef<DimensionsType>({
    windowWidth: Dimensions.get('window').width,
    windowHeight: Dimensions.get('window').height,
    aspectRatio: 16 / 9,
  }).current;

  dimensions.initialVideoHeight =
    dimensions.windowWidth / dimensions.aspectRatio;

  // 状态定义

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [videoWidth, setVideoWidth] = useState<number>(dimensions.windowWidth);
  const [videoHeight, setVideoHeight] = useState<number>(
    dimensions.initialVideoHeight || 0,
  );
  const [isFullScreen, setIsFullScreen] = useState<boolean>(false);
  const [showControls, setShowControls] = useState<boolean>(true);
  const fadeAnim = useRef(new Animated.Value(1)).current;

  // Refs
  const isMounted = useRef<boolean>(false);
  const zoomableViewRef = useRef<any>(null);
  const progressUpdateTimer = useRef<NodeJS.Timeout | null>(null);

  // 重置视频尺寸到默认状态
  const resetVideoDimensions = useCallback(() => {
    setVideoWidth(dimensions.windowWidth);
    setVideoHeight(dimensions.initialVideoHeight || 0);
  }, [dimensions]);

  // 下载视频
  const saveVideo = useCallback(() => {
    hasSavePermission()
      .then((hasPermission: boolean) => {
        showLoading();
        if (hasPermission) {
          DownloadImage(file, 'video')!
            .then(() => {
              hideLoading();
            })
            .catch(() => {
              hideLoading();
            });
        } else {
          hideLoading();
          AlertModal.alert(
            I18n.t('home.warning_message'),
            I18n.t('permissions.media_title'),
            [
              {
                text: I18n.t('home.cancel'),
                style: 'cancel',
              },
              {
                text: I18n.t('home.confirm'),
                onPress: () => Linking.openSettings(),
              },
            ],
          );
        }
      })
      .catch((error: any) => {
        AlertModal.alert(I18n.t('home.error'), error);
      });
  }, [file]);

  // 退出全屏
  const exitFullScreen = useCallback(() => {
    if (!isFullScreen) {
      return;
    } // 防止重复退出全屏

    // 先显示导航栏
    navigation.setOptions({headerShown: true, orientation: 'portrait'});

    // 更新状态
    setIsFullScreen(false);
    resetVideoDimensions();

    // 添加淡出动画
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();

    Orientation.lockToPortrait();
  }, [navigation, resetVideoDimensions, fadeAnim, isFullScreen]);

  // 重置缩放
  const resetZoom = useCallback(() => {
    if (zoomableViewRef.current) {
      zoomableViewRef.current.zoomTo(1);
    }
  }, []);

  // 处理返回按钮
  const onBackAndroid = useCallback(() => {
    if (isFullScreen) {
      exitFullScreen();
      return true;
    }
    return false;
  }, [isFullScreen, exitFullScreen]);

  // 设置导航栏选项
  useLayoutEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          rightClick={saveVideo}
          text={I18n.t('event.download_video')}
        />
      ),
      headerStyle: {
        backgroundColor: 'black',
      },
    });
  }, [navigation, saveVideo]);

  // 视频事件处理器
  const videoEventHandlers = {
    onLoad: useCallback(() => {
      setIsLoading(false);
    }, []),

    onError: useCallback(
      (err: any) => {
        const errorMessage =
          err.error?.message || err.error?.errorString || 'Video load error';
        AlertModal.alert(I18n.t('home.error'), errorMessage, [
          {
            text: I18n.t('home.confirm'),
            onPress: () => navigation.goBack(),
          },
        ]);
      },
      [navigation],
    ),

    onFullscreenPlayerWillPresent: useCallback(() => {
      // 先隐藏导航栏
      navigation.setOptions({headerShown: false, orientation: 'landscape'});
      // 锁定横屏
      Orientation.lockToLandscape();
    }, [navigation]),

    onFullscreenPlayerWillDismiss: useCallback(() => {
      // 先显示导航栏
      navigation.setOptions({headerShown: true, orientation: 'portrait'});
      // 锁定竖屏
      Orientation.lockToPortrait();
      // 重置视频尺寸
      resetVideoDimensions();
    }, [navigation, resetVideoDimensions]),
  };

  // 处理方向变化
  const handleOrientationChange = useCallback(
    (orientation: RNOrientationType) => {
      const isLandscape = orientation.startsWith('LANDSCAPE');

      // 如果是竖屏，重置视频尺寸
      if (!isLandscape) {
        resetVideoDimensions();
      }
    },
    [resetVideoDimensions],
  );

  // 组件挂载和卸载逻辑
  useEffect(() => {
    isMounted.current = true;
    // 确保初始尺寸正确
    resetVideoDimensions();

    // 添加返回键监听
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      onBackAndroid,
    );

    // 添加方向变化监听
    Orientation.addOrientationListener(handleOrientationChange);

    // 清理函数
    return () => {
      isMounted.current = false;

      // 移除监听器
      Orientation.removeOrientationListener(handleOrientationChange);
      backHandler.remove();

      // 清理定时器
      if (progressUpdateTimer.current) {
        clearTimeout(progressUpdateTimer.current);
        progressUpdateTimer.current = null;
      }
    };
  }, [resetVideoDimensions, onBackAndroid, handleOrientationChange]);

  // 在全屏切换时重置缩放
  useEffect(() => {
    resetZoom();
  }, [isFullScreen, resetZoom]);

  const controlsRef = useRef(showControls);

  return (
    <View style={styles.container}>
      <ReactNativeZoomableView
        ref={zoomableViewRef}
        maxZoom={3}
        minZoom={1}
        zoomStep={0.5}
        initialZoom={1}
        bindToBorders={true}
        disablePanOnInitialZoom={true}
        visualTouchFeedbackEnabled={false}
        onSingleTap={() => {
          const newControlsState = !controlsRef.current;
          controlsRef.current = newControlsState;
          setShowControls(newControlsState);
        }}
        style={{
          flex: 1,
          backgroundColor: 'black',
        }}>
        <Animated.View
          style={[
            styles.videoContainer,
            {width: videoWidth, height: videoHeight},
            {opacity: fadeAnim},
          ]}>
          <Video
            style={[
              {
                width: videoWidth,
                height: videoHeight,
                position: 'absolute',
              },
            ]}
            controls={true}
            source={{
              uri: file,
            }}
            resizeMode={ResizeMode.CONTAIN}
            fullscreenAutorotate={true}
            fullscreen={false}
            onLoad={videoEventHandlers.onLoad}
            onError={videoEventHandlers.onError}
            onFullscreenPlayerWillPresent={
              videoEventHandlers.onFullscreenPlayerWillPresent
            }
            onFullscreenPlayerWillDismiss={
              videoEventHandlers.onFullscreenPlayerWillDismiss
            }
          />
          {isLoading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator color={Colors.MainColor} size="large" />
            </View>
          )}
        </Animated.View>
      </ReactNativeZoomableView>
    </View>
  );
};

// 样式定义
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
  },
  videoContainer: {
    position: 'relative',
    backgroundColor: 'black',
    overflow: 'hidden',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
});

export default LookVideo;
