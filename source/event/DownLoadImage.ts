/**
 * 下载网络图片
 * @param uri  图片地址
 * @param type 文件类型 'image' 或 'mp4'
 * @returns Promise
 */
import RNFS from 'react-native-fs';
import {CameraRoll} from '@react-native-camera-roll/camera-roll';
import {Platform} from 'react-native';
import I18n from '../I18n';
import AlertModal from '../share/AlertModal';

type FileType = 'image' | 'mp4' | string;

export const DownloadImage = (
  uri: string,
  type: FileType,
): Promise<any> | null => {
  if (!uri) {
    return null;
  }

  return new Promise<any>((resolve, reject) => {
    let timestamp: number = new Date().getTime(); // 获取当前时间错
    // eslint-disable-next-line no-bitwise
    let random: string = String((Math.random() * 1000000) | 0); // 六位随机数
    let dirs: string =
      Platform.OS === 'ios'
        ? RNFS.LibraryDirectoryPath
        : RNFS.ExternalDirectoryPath; // 外部文件，共享目录的绝对路径（仅限android）

    const downloadDest: string = `${dirs}/${timestamp + random}.${
      type === 'image' ? 'jpg' : 'mp4'
    }`;

    const formUrl: string = uri;

    const options: RNFS.DownloadFileOptions = {
      fromUrl: formUrl,
      toFile: downloadDest,
      background: true,
      begin: (_res: RNFS.DownloadBeginCallbackResult) => {},
    };

    try {
      const ret = RNFS.downloadFile(options);
      ret.promise
        .then((res: RNFS.DownloadResult) => {
          var promise = CameraRoll.save(downloadDest, {type: 'auto'});
          promise
            .then(function (_result: string) {
              AlertModal.alert(I18n.t('home.success'), '', [
                {
                  text: 'OK',
                },
              ]);
            })
            .catch(function (error: Error) {
              console.log('error', error);
            });
          resolve(res);
        })
        .catch((err: any) => {
          reject(new Error(err));
        });
    } catch (e: any) {
      reject(new Error(e));
    }
  });
};
