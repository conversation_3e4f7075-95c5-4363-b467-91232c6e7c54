/* eslint-disable react/no-unstable-nested-components */
import React, {Component} from 'react';
import {View, Text, TouchableOpacity, ScrollView} from 'react-native';
import {Helper} from '../Helper';
import I18n from '../I18n';
import {
  NotificationCenter,
  EVENT_TIMEZONE,
} from '../NotificationCenter';
import PubSub from 'pubsub-js';
import {Tme} from '../ThemeStyle';
import NavBarView from '../share/NavBarView';
import {Toast} from '../Toast';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {hideLoading, showLoading} from '../../ILoading';
import { PubSubEvent } from '../types/PubSubEvent';

export default class TimezoneScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      time_zone: '',
      zone_id: '',
      time_zones: [],
    };
    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this._savezone();
  }

  componentDidMount() {
    var that = this;

    this.doFetchData();
    NotificationCenter.addObserver(this, EVENT_TIMEZONE, data => {
      that.setState({
        time_zone: data.s,
        zone_id: data.name,
      });
    });
  }

  componentWillUnmount() {
    NotificationCenter.removeObserver(EVENT_TIMEZONE);
  }

  render() {
    return (
      <NavBarView>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{
            backgroundColor: Tme('bgColor'),
            flex: 1,
          }}>
          <View
            style={{
              shadowOffset: {height: 4},
              shadowColor: 'rgba(0,0,0,0.02)',
              shadowOpacity: 0.2,
              shadowRadius: 1,
            }}>
            <View style={{backgroundColor: Tme('cardColor'), marginTop: 20}}>
              <View
                style={{
                  padding: 16,
                }}>
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={this.push_timezone_list.bind(this)}
                  style={{
                    height: 40,
                    flexDirection: 'row',
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                    borderColor: Tme('inputBorderColor'),
                    borderWidth: 1,
                    borderRadius: 3,
                  }}>
                  <Text
                    style={{
                      color: Tme('cardTextColor'),
                      marginLeft: 8,
                    }}>
                    {this.state.time_zone}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </NavBarView>
    );
  }

  doFetchData() {
    var that = this;
    showLoading();
    Helper.httpGET('/users/get_timezones', {
      ensure: () => {
        hideLoading();
      },
      success: data => {
        that.setState({
          time_zone: data.zone,
          time_zones: data.time_zones,
        });
      },
    });
  }
  push_timezone_list() {
    this.props.navigation.push('timezoneList', {
      time_zones: this.state.time_zones,
      time_zone: this.state.time_zone,
      title: I18n.t('home.time_zone'),
    });
  }
  _savezone() {
    if (this.state.zone_id !== '') {
      var body = {
        timezone: this.state.zone_id,
      };
      showLoading();
      Helper.httpPOST(
        '/users/save_user_timezone',
        {
          ensure: () => {
            hideLoading();
          },
          success: data => {
            PubSub.publish(PubSubEvent.RESTART_APP);
            Toast.show();
          },
        },
        body,
      );
    }
  }
}
