/* eslint-disable react/no-unstable-nested-components */
import React, {Component} from 'react';
import {View} from 'react-native';

import {Helper, HelperMemo} from '../Helper';
import I18n from '../I18n';
import RadioButtons from '../RadioButtons';
import NavBarView from '../share/NavBarView';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {hideLoading, showLoading} from '../../ILoading';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';
export default class ChangeScale extends Component {
  constructor(props) {
    super(props);

    this.state = {
      scales: [
        {key: 'C', value: '°C'},
        {key: 'F', value: '°F'},
      ],
      checked: HelperMemo.user_data.user.scale,
    };

    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }
  rightClick() {
    this._save();
  }

  render() {
    return (
      <NavBarView>
        <View
          style={{
            paddingTop: 20,
          }}>
          <RadioButtons
            data={this.state.scales}
            defaultKey={this.state.checked}
            onChange={this.onChange.bind(this)}
          />
        </View>
      </NavBarView>
    );
  }

  _save() {
    var body = {scale: this.state.checked};
    showLoading();
    Helper.httpPOST(
      '/profiles/set_scale',
      {
        ensure: () => {
          hideLoading();
        },
        success: data => {
          PubSub.publish(PubSubEvent.RESTART_APP);
        },
      },
      body,
    );
  }

  onChange(data) {
    this.setState({
      checked: data,
    });
  }
}
