/* eslint-disable react/no-unstable-nested-components */
import React, {Component} from 'react';
import {View, ScrollView} from 'react-native';

import {Helper, HelperMemo} from '../Helper';
import I18n from '../I18n';
import {Tme} from '../ThemeStyle';
import RadioButtons from '../RadioButtons';
import NavBarView from '../share/NavBarView';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {hideLoading, showLoading} from '../../ILoading';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';
export default class ChangeDatacenter extends Component {
  constructor(props) {
    super(props);

    this.state = {
      dc_names: [],
      checked: HelperMemo.user_data.user.dc_name,
    };

    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }
  rightClick() {
    this._save();
  }

  componentDidMount() {
    this.doFetchData();
  }

  doFetchData() {
    var that = this;
    showLoading();
    Helper.httpGET(Helper.urlWithQuery('/profiles/get_data_center'), {
      success: data => {
        that.setState({
          dc_names: data.dc_names,
        });
      },
      ensure: () => {
        hideLoading();
      },
    });
  }

  render() {
    return (
      <NavBarView>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{backgroundColor: Tme('bgColor'), flex: 1}}>
          <View style={{backgroundColor: Tme('cardColor'), marginTop: 20}}>
            <RadioButtons
              data={this.state.dc_names}
              defaultKey={this.state.checked}
              onChange={this.onChange.bind(this)}
            />
          </View>
        </ScrollView>
      </NavBarView>
    );
  }

  _save() {
    var body = {dc_name: this.state.checked};
    if (HelperMemo.user_data.role == 'owner') {
      showLoading();
      Helper.httpPOST(
        '/profiles/set_dc_name',
        {
          ensure: () => {
            hideLoading();
          },
          success: data => {
            PubSub.publish(PubSubEvent.RESTART_APP);
          },
        },
        body,
      );
    }
  }

  onChange(data) {
    this.setState({
      checked: data,
    });
  }
}
