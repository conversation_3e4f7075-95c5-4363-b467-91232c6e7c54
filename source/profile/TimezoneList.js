import React, {Component} from 'react';
import {Text, TouchableOpacity, FlatList, SafeAreaView} from 'react-native';
import {Helper} from '../Helper';
import {NotificationCenter, EVENT_TIMEZONE} from '../NotificationCenter';
import {Tme} from '../ThemeStyle';
import EmptyView from '../share/EmptyView';
import {hideLoading, showLoading} from '../../ILoading';

export default class TimezoneList extends Component {
  constructor(props) {
    super(props);

    this.state = {
      time_zone: this.props.route.params.time_zone,
      zone_id: '',
      dataSource: this.props.route.params.time_zones,
    };
  }

  componentDidMount() {
    if (this.props.route.params.from == 'signup') {
    } else {
      this.doFetchData();
    }
  }

  render() {
    return (
      <SafeAreaView style={{flex: 1, backgroundColor: Tme('cardColor')}}>
        <FlatList
          showsVerticalScrollIndicator={false}
          ref={flatList => (this._flatList = flatList)}
          data={this.state.dataSource}
          ListEmptyComponent={<EmptyView />}
          renderItem={this._renderRow.bind(this)}
          keyExtractor={(item, index) => index.toString()}
        />
      </SafeAreaView>
    );
  }

  _renderRow({item, index}) {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={this.touchRow.bind(this, item)}
        key={index}
        style={{
          flex: 1,
          padding: 16,
          borderBottomWidth: 1,
          borderBottomColor: Tme('inputBorderColor'),
        }}>
        <Text style={{color: Tme('cardTextColor')}}>{item.s}</Text>
      </TouchableOpacity>
    );
  }

  touchRow(data) {
    NotificationCenter.dispatchEvent(EVENT_TIMEZONE, data);
    this.props.navigation.goBack();
  }

  doFetchData() {
    var that = this;
    showLoading();
    Helper.httpGET('/users/get_timezones', {
      success: data => {
        that.setState({
          dataSource: data.list,
          time_zone: data.zone,
        });
      },
      ensure: () => {
        hideLoading();
      },
    });
  }
}
