/* eslint-disable react/no-unstable-nested-components */
import React, {useEffect, useCallback, useRef} from 'react';
import {View, StyleSheet, TextInput, ScrollView} from 'react-native';
import {DEVICE_HEIGHT} from '../Helper';
import I18n from '../I18n';
import KeyboardHandler from '../KeyboardHandler';
import {Tme, Colors} from '../ThemeStyle';
import NavBarView from '../share/NavBarView';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {ProfileScreenProps} from '../types/profile';
import {useProfileForm} from '../hooks/useProfileForm';
import {useKeyboardHandler} from '../hooks/useKeyboardHandler';

/**
 * 个人资料设置组件
 *
 * 功能：
 * - 修改密码
 * - 密码强度验证
 * - 确认密码匹配验证
 * - API 调用保存更改
 * - 自动键盘处理
 */
const ProfileScreen: React.FC<ProfileScreenProps> = ({navigation, route: _route}) => {
  // 使用自定义 Hook 管理表单状态和 API 调用
  const {
    formData,
    updateField,
    changePassword,
  } = useProfileForm({
    onSuccess: () => navigation.goBack(),
  });

  // 使用键盘处理 Hook
  const {
    keyboardHandlerRef,
    handleInputFocus,
    createNavigateToNext,
    createSubmitHandler,
  } = useKeyboardHandler();

  // 创建输入框引用
  const oldPasswordInputRef = useRef<TextInput>(null);
  const newPasswordInputRef = useRef<TextInput>(null);
  const confirmPasswordInputRef = useRef<TextInput>(null);

  // 保存操作
  const handleSave = useCallback(async () => {
    const success = await changePassword();
    if (success) {
      navigation.goBack();
    }
  }, [changePassword, navigation]);

  // 设置导航栏右侧按钮
  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={handleSave}
        />
      ),
    });
  }, [navigation, handleSave]);

  // 处理输入框聚焦
  const handleOldPasswordFocus = handleInputFocus(oldPasswordInputRef);
  const handleNewPasswordFocus = handleInputFocus(newPasswordInputRef);
  const handleConfirmPasswordFocus = handleInputFocus(confirmPasswordInputRef);

  // 处理导航到下一个输入框
  const navigateToNewPassword = createNavigateToNext(newPasswordInputRef);
  const navigateToConfirmPassword = createNavigateToNext(confirmPasswordInputRef);

  // 处理提交
  const handleSubmit = createSubmitHandler(handleSave);

  // 处理表单字段变化
  const updateFormField = useCallback((field: keyof typeof formData) => {
    return (value: string) => {
      updateField(field, value);
    };
  }, [updateField]);

  return (
    <NavBarView>
      <ScrollView
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="never"
        keyboardDismissMode="on-drag"
        style={styles.scrollView}>
        <View style={styles.container}>
          <KeyboardHandler
            ref={keyboardHandlerRef}
            offset={DEVICE_HEIGHT / 3}
            keyboardShouldPersistTaps="never"
            keyboardDismissMode="on-drag">
            <View style={[styles.content, {backgroundColor: Tme('cardColor')}]}>

              {/* 旧密码输入 */}
              <View style={styles.inputSection}>
                <View style={styles.inputContainer}>
                  <View style={[
                    styles.inputWrapper,
                    {borderColor: Tme('inputBorderColor')},
                  ]}>
                    <TextInput
                      ref={oldPasswordInputRef}
                      returnKeyType="next"
                      autoCapitalize="none"
                      placeholder={I18n.t('session.old_password')}
                      underlineColorAndroid="transparent"
                      autoCorrect={false}
                      secureTextEntry={true}
                      value={formData.old_password}
                      onChangeText={updateFormField('old_password')}
                      style={Colors.TextInputStyle()}
                      placeholderTextColor={Tme('placeholder')}
                      onFocus={handleOldPasswordFocus}
                      onSubmitEditing={navigateToNewPassword}
                    />
                  </View>
                </View>
              </View>

              {/* 新密码输入 */}
              <View style={styles.inputSection}>
                <View style={styles.inputContainer}>
                  <View style={[
                    styles.inputWrapper,
                    {borderColor: Tme('inputBorderColor')},
                  ]}>
                    <TextInput
                      ref={newPasswordInputRef}
                      returnKeyType="next"
                      autoCapitalize="none"
                      placeholder={I18n.t('session.new_password')}
                      underlineColorAndroid="transparent"
                      autoCorrect={false}
                      secureTextEntry={true}
                      value={formData.new_password}
                      placeholderTextColor={Tme('placeholder')}
                      onChangeText={updateFormField('new_password')}
                      style={Colors.TextInputStyle()}
                      onFocus={handleNewPasswordFocus}
                      onSubmitEditing={navigateToConfirmPassword}
                    />
                  </View>
                </View>
              </View>

              {/* 确认密码输入 */}
              <View style={styles.inputSection}>
                <View style={styles.inputContainerLast}>
                  <View style={[
                    styles.inputWrapper,
                    {borderColor: Tme('inputBorderColor')},
                  ]}>
                    <TextInput
                      ref={confirmPasswordInputRef}
                      returnKeyType="done"
                      autoCapitalize="none"
                      placeholder={I18n.t('session.password_confirmation')}
                      underlineColorAndroid="transparent"
                      placeholderTextColor={Tme('placeholder')}
                      autoCorrect={false}
                      secureTextEntry={true}
                      value={formData.password_confirmation}
                      onChangeText={updateFormField('password_confirmation')}
                      style={Colors.TextInputStyle()}
                      onFocus={handleConfirmPasswordFocus}
                      onSubmitEditing={handleSubmit}
                    />
                  </View>
                </View>
              </View>
            </View>
          </KeyboardHandler>
        </View>
      </ScrollView>
    </NavBarView>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    backgroundColor: Tme('bgColor'),
  },
  container: {
    flex: 1,
    shadowOffset: {width: 0, height: 4},
    shadowColor: 'rgba(0,0,0,0.02)',
    shadowOpacity: 0.2,
    shadowRadius: 1,
  },
  content: {
    backgroundColor: Tme('cardColor'),
    marginTop: 20,
  },
  inputSection: {
    backgroundColor: Tme('cardColor'),
    marginTop: 10,
  },
  inputContainer: {
    paddingHorizontal: 16,
  },
  inputContainerLast: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  inputWrapper: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 3,
  },
});

export default ProfileScreen;
