/* eslint-disable react/no-unstable-nested-components */
import React, {Component} from 'react';
import {View, Text, StyleSheet, TextInput, ScrollView} from 'react-native';
import {Helper, HelperMemo, DEVICE_HEIGHT} from '../Helper';
import I18n from '../I18n';
import KeyboardHandler from '../KeyboardHandler';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {Tme, Colors} from '../ThemeStyle';
import NavBarView from '../share/NavBarView';
import {Toast} from '../Toast';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {hideLoading, showLoading} from '../../ILoading';

export default class ChangeUserName extends Component {
  constructor(props) {
    super(props);

    this.state = {
      name:
        HelperMemo.user_data.user.name === undefined
          ? ''
          : HelperMemo.user_data.user.name,
    };

    this.focusRef = React.createRef();
    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }
  rightClick() {
    this._save();
  }

  render() {
    return (
      <NavBarView>
        <ScrollView
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="never"
          keyboardDismissMode="on-drag"
          style={{
            backgroundColor: Tme('bgColor'),
          }}>
          <View
            style={{
              flex: 1,
              shadowOffset: {height: 4},
              shadowColor: 'rgba(0,0,0,0.02)',
              shadowOpacity: 0.2,
              shadowRadius: 1,
            }}>
            <KeyboardHandler
              ref={this.focusRef}
              offset={DEVICE_HEIGHT / 3}
              keyboardShouldPersistTaps="never"
              keyboardDismissMode="on-drag">
              <View
                style={{
                  backgroundColor: Tme('cardColor'),
                }}>
                <View style={{backgroundColor: Tme('bgColor')}}>
                  <View style={{padding: 16}}>
                    <Text
                      style={{
                        fontSize: 18,
                        fontWeight: '600',
                        color: Tme('textColor'),
                      }}>
                      {HelperMemo.user_data.user.identity}
                    </Text>
                  </View>
                </View>
                <View
                  style={{
                    backgroundColor: Tme('cardColor'),
                    marginTop: 10,
                    marginBottom: 10,
                  }}>
                  <View style={{paddingHorizontal: 16}}>
                    <View
                      style={[
                        styles.account_view,
                        {borderColor: Tme('inputBorderColor')},
                      ]}>
                      <TextInput
                        returnKeyType="go"
                        autoCapitalize="none"
                        underlineColorAndroid="transparent"
                        autoCorrect={false}
                        defaultValue={this.state.name}
                        value={this.state.name}
                        onChangeText={name => this.setState({name})}
                        placeholderTextColor={Tme('placeholder')}
                        placeholder={I18n.t('session.user_name')}
                        style={Colors.TextInputStyle()}
                        onFocus={() =>
                          this.focusRef.current.inputFocused(this, 'name')
                        }
                      />
                    </View>
                  </View>
                </View>
              </View>
            </KeyboardHandler>
          </View>
        </ScrollView>
      </NavBarView>
    );
  }

  _save() {
    var errors = [];
    if (errors.length > 0) {
      AlertModal.alert(I18n.t('home.warning_message'), errors.join('\n'));
      return;
    }

    var body = {
      name: this.state.name,
    };

    showLoading();
    Helper.httpPOST(
      '/profiles/change_name',
      {
        ensure: () => {
          hideLoading();
        },
        success: data => {
          AsyncStorage.mergeItem(
            'user_data',
            JSON.stringify({user: data.user}),
            () => {
              HelperMemo.user_data.user = data.user;
            },
          );
          this.props.navigation.goBack();
          Toast.show();
        },
      },
      body,
    );
  }
}
const styles = StyleSheet.create({
  account_view: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 3,
  },
});
