/* eslint-disable react/no-unstable-nested-components */
import React, {Component} from 'react';
import {View, StyleSheet, TextInput, ScrollView} from 'react-native';
import {Helper, HelperMemo, DEVICE_HEIGHT} from '../Helper';
import I18n from '../I18n';
import _ from 'lodash';
import KeyboardHandler from '../KeyboardHandler';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {Tme, Colors} from '../ThemeStyle';
import NavBarView from '../share/NavBarView';
import {Toast} from '../Toast';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {hideLoading, showLoading} from '../../ILoading';

export default class ProfileScreen extends Component {
  constructor(props) {
    super(props);

    this.state = {
      name:
        HelperMemo.user_data.user.name === undefined
          ? ''
          : HelperMemo.user_data.user.name,
      old_password: '',
      new_password: '',
      password_confirmation: '',
      cloud: HelperMemo.user_data.network ? false : true,
    };

    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this._save();
  }

  render() {
    return (
      <NavBarView>
        <ScrollView
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="never"
          keyboardDismissMode="on-drag"
          style={{
            backgroundColor: Tme('bgColor'),
          }}>
          <View
            style={{
              flex: 1,
              shadowOffset: {height: 4},
              shadowColor: 'rgba(0,0,0,0.02)',
              shadowOpacity: 0.2,
              shadowRadius: 1,
            }}>
            <KeyboardHandler
              offset={DEVICE_HEIGHT / 3}
              keyboardShouldPersistTaps="never"
              keyboardDismissMode="on-drag">
              <View
                style={{
                  backgroundColor: Tme('cardColor'),
                  marginTop: 20,
                }}>
                <View
                  style={{backgroundColor: Tme('cardColor'), marginTop: 10}}>
                  <View
                    style={{
                      paddingHorizontal: 16,
                    }}>
                    <View
                      style={[
                        styles.account_view,
                        {borderColor: Tme('inputBorderColor')},
                      ]}>
                      <TextInput
                        returnKeyType="go"
                        autoCapitalize="none"
                        placeholder={I18n.t('session.old_password')}
                        underlineColorAndroid="transparent"
                        autoCorrect={false}
                        secureTextEntry={true}
                        value={this.state.old_password}
                        onChangeText={old_password =>
                          this.setState({old_password})
                        }
                        style={Colors.TextInputStyle()}
                        placeholderTextColor={Tme('placeholder')}
                      />
                    </View>
                  </View>
                </View>
                <View
                  style={{backgroundColor: Tme('cardColor'), marginTop: 10}}>
                  <View
                    style={{
                      paddingHorizontal: 16,
                    }}>
                    <View
                      style={[
                        styles.account_view,
                        {borderColor: Tme('inputBorderColor')},
                      ]}>
                      <TextInput
                        returnKeyType="go"
                        autoCapitalize="none"
                        placeholder={I18n.t('session.new_password')}
                        underlineColorAndroid="transparent"
                        autoCorrect={false}
                        secureTextEntry={true}
                        value={this.state.new_password}
                        placeholderTextColor={Tme('placeholder')}
                        onChangeText={new_password =>
                          this.setState({new_password})
                        }
                        style={Colors.TextInputStyle()}
                      />
                    </View>
                  </View>
                </View>
                <View
                  style={{backgroundColor: Tme('cardColor'), marginTop: 10}}>
                  <View
                    style={{
                      paddingHorizontal: 16,
                      paddingBottom: 16,
                    }}>
                    <View
                      style={[
                        styles.account_view,
                        {borderColor: Tme('inputBorderColor')},
                      ]}>
                      <TextInput
                        returnKeyType="go"
                        autoCapitalize="none"
                        placeholder={I18n.t('session.password_confirmation')}
                        underlineColorAndroid="transparent"
                        placeholderTextColor={Tme('placeholder')}
                        autoCorrect={false}
                        secureTextEntry={true}
                        value={this.state.password_confirmation}
                        onChangeText={password_confirmation =>
                          this.setState({password_confirmation})
                        }
                        style={Colors.TextInputStyle()}
                      />
                    </View>
                  </View>
                </View>
              </View>
            </KeyboardHandler>
          </View>
        </ScrollView>
      </NavBarView>
    );
  }

  _save() {
    var errors = [];
    var body = null;

    if (_.isEmpty(this.state.old_password)) {
      errors.push(I18n.t('session.please_enter_password'));
    }
    if (_.isEmpty(this.state.new_password)) {
      errors.push(I18n.t('session.new_input'));
    }

    var reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/;
    if (!reg.test(this.state.new_password)) {
      errors.push(I18n.t('session.password_length'));
    }
    if (this.state.new_password !== this.state.password_confirmation) {
      errors.push(I18n.t('session.two_password'));
    }
    if (errors.length > 0) {
      AlertModal.alert(I18n.t('home.warning_message'), errors.join('\n'));
      return;
    }

    if (this.state.cloud) {
      body = {
        old: this.state.old_password,
        new: this.state.new_password,
        conf: this.state.password_confirmation,
      };
    } else {
      body = {
        old: this.state.old_password,
        new: this.state.new_password,
        conf: this.state.password_confirmation,
      };
    }

    showLoading();
    Helper.httpPOST(
      '/profiles/change_password',
      {
        ensure: () => {
          hideLoading();
        },
        success: data => {
          AsyncStorage.mergeItem(
            'user_data',
            JSON.stringify({user: data.user}),
            () => {
              HelperMemo.user_data.user = data.user;
            },
          );
          this.props.navigation.goBack();
          Toast.show();
        },
      },
      body,
    );
  }
}
const styles = StyleSheet.create({
  account_view: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 3,
  },
});
