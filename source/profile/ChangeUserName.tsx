import React, {useEffect, useCallback, useRef} from 'react';
import {View, Text, StyleSheet, TextInput, ScrollView} from 'react-native';
import {DEVICE_HEIGHT, HelperMemo} from '../Helper';
import I18n from '../I18n';
import KeyboardHandler from '../KeyboardHandler';
import {Tme, Colors} from '../ThemeStyle';
import NavBarView from '../share/NavBarView';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {ChangeUserNameProps} from '../types/profile';
import {useProfileForm} from '../hooks/useProfileForm';
import {useKeyboardHandler} from '../hooks/useKeyboardHandler';

/**
 * 修改用户名组件
 *
 * 功能：
 * - 允许用户修改显示名称
 * - 表单验证
 * - API 调用保存更改
 * - 自动键盘处理
 */
const ChangeUserName: React.FC<ChangeUserNameProps> = ({navigation, route: _route}) => {
  // 使用自定义 Hook 管理表单状态和 API 调用
  const {
    formData,
    updateField,
    changeName,
  } = useProfileForm({
    onSuccess: () => navigation.goBack(),
  });

  // 使用键盘处理 Hook
  const {
    keyboardHandlerRef,
    handleInputFocus,
    createSubmitHandler,
  } = useKeyboardHandler();

  // 创建输入框引用
  const nameInputRef = useRef<TextInput>(null);

  // 保存操作
  const handleSave = useCallback(async () => {
    const success = await changeName();
    if (success) {
      navigation.goBack();
    }
  }, [changeName, navigation]);

  // 设置导航栏右侧按钮
  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={handleSave}
        />
      ),
    });
  }, [navigation, handleSave]);

  // 处理输入框聚焦
  const handleNameFocus = handleInputFocus(nameInputRef);

  // 处理文本变化
  const handleNameChange = useCallback((text: string) => {
    updateField('name', text);
  }, [updateField]);

  // 处理提交
  const handleSubmit = createSubmitHandler(handleSave);

  return (
    <NavBarView>
      <ScrollView
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="never"
        keyboardDismissMode="on-drag"
        style={styles.scrollView}>
        <View style={styles.container}>
          <KeyboardHandler
            ref={keyboardHandlerRef}
            offset={DEVICE_HEIGHT / 3}
            keyboardShouldPersistTaps="never"
            keyboardDismissMode="on-drag">
            <View style={[styles.content, {backgroundColor: Tme('cardColor')}]}>
              {/* 用户身份显示 */}
              <View style={[styles.identitySection, {backgroundColor: Tme('bgColor')}]}>
                <View style={styles.identityContainer}>
                  <Text style={[styles.identityText, {color: Tme('textColor')}]}>
                    {HelperMemo.user_data?.user?.identity || ''}
                  </Text>
                </View>
              </View>

              {/* 用户名输入区域 */}
              <View style={styles.inputSection}>
                <View style={styles.inputContainer}>
                  <View style={[
                    styles.inputWrapper,
                    {borderColor: Tme('inputBorderColor')},
                  ]}>
                    <TextInput
                      ref={nameInputRef}
                      returnKeyType="done"
                      autoCapitalize="none"
                      underlineColorAndroid="transparent"
                      autoCorrect={false}
                      value={formData.name}
                      onChangeText={handleNameChange}
                      placeholderTextColor={Tme('placeholder')}
                      placeholder={I18n.t('session.user_name')}
                      style={Colors.TextInputStyle()}
                      onFocus={handleNameFocus}
                      onSubmitEditing={handleSubmit}
                    />
                  </View>
                </View>
              </View>
            </View>
          </KeyboardHandler>
        </View>
      </ScrollView>
    </NavBarView>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    backgroundColor: Tme('bgColor'),
  },
  container: {
    flex: 1,
    shadowOffset: {width: 0, height: 4},
    shadowColor: 'rgba(0,0,0,0.02)',
    shadowOpacity: 0.2,
    shadowRadius: 1,
  },
  content: {
    backgroundColor: Tme('cardColor'),
  },
  identitySection: {
    backgroundColor: Tme('bgColor'),
  },
  identityContainer: {
    padding: 16,
  },
  identityText: {
    fontSize: 18,
    fontWeight: '600',
    color: Tme('textColor'),
  },
  inputSection: {
    backgroundColor: Tme('cardColor'),
    marginTop: 10,
    marginBottom: 10,
  },
  inputContainer: {
    paddingHorizontal: 16,
  },
  inputWrapper: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 3,
  },
});

export default ChangeUserName;
