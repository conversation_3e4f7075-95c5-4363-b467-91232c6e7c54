/* eslint-disable react/no-unstable-nested-components */
import React, {Component} from 'react';
import {View, ScrollView, SafeAreaView} from 'react-native';
import {Helper, HelperMemo} from '../Helper';
import I18n from '../I18n';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  NotificationCenter,
  SELECT_LONG,
} from '../NotificationCenter';
import {Tme} from '../ThemeStyle';
import _ from 'lodash';
import PubSub from 'pubsub-js';

var lang = [
  {
    key: 'en',
    value: 'English',
  },
  {
    key: 'zh_cn',
    value: '简体中文',
  },
  // {
  //   key: "de",
  //   value: "Deutsch"
  // },
  // {
  //   key: "fr",
  //   value: "français"
  // }, {
  //   key: "pt",
  //   value: "português"
  // }, {
  //   key: "es",
  //   value: "español"
  // }, {
  //   key: "ru",
  //   value: "русский"
  // }
];

import RadioButtons from '../RadioButtons';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {hideLoading, showLoading} from '../../ILoading';
import { PubSubEvent } from '../types/PubSubEvent';

export default class LangScreen extends Component {
  constructor(props) {
    super(props);

    this.state = {
      dataSource: lang,
      checked:
        this.props.route.params.from == 'signup'
          ? this.props.route.params.lang
          : HelperMemo.lang,
    };

    this.props.navigation.setOptions({
      headerRight: () => {
        if (this.props.route.params.from === 'signup') {
          return null;
        } else {
          return (
            <HeaderRightBtn
              text={I18n.t('home.save')}
              rightClick={this.rightClick.bind(this)}
            />
          );
        }
      },
    });
  }

  rightClick() {
    this._save();
  }

  render() {
    return (
      <SafeAreaView style={{flex: 1, backgroundColor: Tme('bgColor')}}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{
            flex: 1,
          }}>
          <View style={{marginTop: 20, backgroundColor: Tme('cardColor')}}>
            <RadioButtons
              data={lang}
              defaultKey={this.state.checked}
              onChange={this.onChange.bind(this)}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }

  _save() {
    var that = this;
    showLoading();
    AsyncStorage.setItem('lang', JSON.stringify(that.state.checked), () => {
      HelperMemo.lang = that.state.checked;

      var body = {locale: that.state.checked};
      Helper.httpPOST(
        '/users/save_locales',
        {
          ensure: () => {
            hideLoading();
          },
          success: data => {
            setTimeout(() => {
              PubSub.publish(PubSubEvent.RESTART_APP);
            }, 100);
          },
        },
        body,
      );
    });
  }

  onChange(data) {
    if (this.props.route.params.from == 'signup') {
      const checked = _.find(lang, l => l.key == data);
      NotificationCenter.dispatchEvent(SELECT_LONG, checked);
      this.props.navigation.goBack();
    } else {
      this.setState({
        checked: data,
      });
    }
  }
}
