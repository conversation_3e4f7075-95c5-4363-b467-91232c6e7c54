import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Platform,
} from 'react-native';
import { Helper, HelperMemo } from '../Helper';
import I18n from '../I18n';
import { Tme } from '../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Icon from 'react-native-vector-icons/Ionicons';
import NavBarView from '../share/NavBarView';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  NotificationCenter,
  CLOSE_USER,
} from '../NotificationCenter';
import { mainRadius } from '../Tools';
import AlertModal from '../share/AlertModal';
import { hideLoading, showLoading } from '../../ILoading';
import { IconView } from '../setting/SettingScreen';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';

export default class ProfileList extends Component {
  constructor(props) {
    super(props);

    this.state = {
      deleteConfirmation: '',
      theme: '',
    };
  }

  componentDidMount() {
    AsyncStorage.getItem('theme', (error, result) => {
      if (error) {
        console.error(error);
      } else {
        if (result !== null) {
          this.setState({
            theme: JSON.parse(result),
          });
        } else {
          this.setState({
            theme: 'system_with',
          });
        }
      }
    });

    NotificationCenter.addObserver(this, CLOSE_USER, data => {
      this.onSave(data);
    });
  }

  componentWillUnmount() {
    NotificationCenter.removeObserver(this, CLOSE_USER);
  }

  render() {
    return (
      <NavBarView>
        <ScrollView showsVerticalScrollIndicator={false} style={styles.tabView}>
          <View
            style={{
              marginTop: 20,
              paddingBottom: 20,
            }}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.click.bind(this, 'name')}
              style={{
                backgroundColor: Tme('cardColor'),
                borderTopLeftRadius: mainRadius(),
                borderTopRightRadius: mainRadius(),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <FontAwesome name="user" size={20} color={Tme('textColor')} />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('session.change_username')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>
            <View style={{ height: 2 }} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.click.bind(this, 'password')}
              style={{
                backgroundColor: Tme('cardColor'),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <MaterialIcons
                    name="lock"
                    size={20}
                    color={Tme('textColor')}
                  />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('session.change_password')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>
            <View style={{ height: 2 }} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.click.bind(this, 'time')}
              style={{
                backgroundColor: Tme('cardColor'),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <MaterialIcons
                    name="access-time"
                    size={20}
                    color={Tme('textColor')}
                  />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('global.timezone_setting')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>
            <View style={{ height: 2 }} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.click.bind(this, 'scale')}
              style={{
                backgroundColor: Tme('cardColor'),
                borderBottomLeftRadius: mainRadius(),
                borderBottomRightRadius: mainRadius(),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <FontAwesome
                    name="thermometer-empty"
                    size={20}
                    color={Tme('textColor')}
                  />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('global.temperature_setting')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>
            {/* <View style={{height: 2}} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.clickTheme.bind(this)}
              style={{backgroundColor: Tme('cardColor')}}>
              <View
                style={{
                  marginLeft: 21,
                  marginRight: 20,
                  flexDirection: 'row',
                  paddingVertical: 16,
                  justifyContent: 'space-between',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Icon
                    name="color-palette-outline"
                    size={19}
                    color={Tme('textColor')}
                  />
                  <Text style={{marginLeft: 13, color: Tme('cardTextColor')}}>
                    {I18n.t('global.theme')}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text style={{color: Tme('cardTextColor')}}>
                    {I18n.t('global.' + this.state.theme)}
                  </Text>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={20}
                    color={Tme('textColor')}
                  />
                </View>
              </View>
            </TouchableOpacity> */}

            <View style={{ height: 20 }} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.download.bind(this)}
              style={{
                backgroundColor: Tme('cardColor'),
                borderTopLeftRadius: mainRadius(),
                borderTopRightRadius: mainRadius(),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <Icon
                    name="cloud-download"
                    size={20}
                    color={Tme('textColor')}
                  />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('setting.download_data')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>
            <View style={{ height: 2 }} />
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.close.bind(this)}
              style={{
                backgroundColor: Tme('cardColor'),
                borderBottomLeftRadius: mainRadius(),
                borderBottomRightRadius: mainRadius(),
                paddingHorizontal: 20,
                height: 54,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                }}>
                <IconView>
                  <Icon name="close" size={20} color={Tme('textColor')} />
                </IconView>
                <Text style={{ marginLeft: 10, color: Tme('cardTextColor') }}>
                  {I18n.t('setting.close_account')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </NavBarView>
    );
  }

  clickTheme() {
    this.props.navigation.push('ThemeScreen', {
      theme: this.state.theme,
      title: I18n.t('global.theme'),
    });
  }

  click(name) {
    var title, screen;
    switch (name) {
      case 'password':
        title = I18n.t('session.change_password');
        screen = 'profileScreen';
        break;
      case 'name':
        title = I18n.t('session.change_username');
        screen = 'ChangeUserName';
        break;
      case 'time':
        title = I18n.t('global.timezone_setting');
        screen = 'TimezoneScreen';
        break;
      case 'grpc':
        title = I18n.t('global.datacenter_setting');
        screen = 'ChangeDatacenter';
        break;
      case 'scale':
        title = I18n.t('global.temperature_setting');
        screen = 'ChangeScale';
        break;
    }

    this.props.navigation.push(screen, {
      title: title,
    });
  }

  download() {
    AlertModal.alert(
      I18n.t('setting.download_data'),
      I18n.t('setting.download_desp'),
      [
        {
          text: I18n.t('home.cancel'),
          onPress: () => { },
        },
        {
          text: I18n.t('home.confirm'),
          onPress: () => {
            showLoading();
            Helper.httpPOST('/profiles/download_data', {
              ensure: () => {
                hideLoading();
              },
              success: data => {
                AlertModal.alert(I18n.t('global.download_alert'));
              },
            });
          },
        },
      ],
    );
  }

  close() {
    AlertModal.alert(
      I18n.t('setting.close_account'),
      I18n.t('setting.close_desp'),
      [
        {
          text: I18n.t('home.cancel'),
          onPress: () => { },
        },
        {
          text: I18n.t('home.confirm'),
          onPress: () => {
            this.deleteUser();
          },
        },
      ],
    );
  }

  deleteUser() {
    this.props.navigation.push('InputModal', {
      title: I18n.t('global.delete_desp'),
      from: 'default',
      inputValue: '',
    });
  }

  onSave(value) {
    if (value.toLocaleUpperCase() === 'YES') {
      showLoading();
      Helper.httpPOST('/profiles/close_account', {
        ensure: () => {
          hideLoading();
        },
        success: data => {
          Helper.httpPOST(
            '/users/signout',
            {
              ensure: () => {
                hideLoading();
              },
              success: () => {
                AsyncStorage.removeItem('user_data', () => {
                  HelperMemo.user_data = null;
                  PubSub.publish(PubSubEvent.RESTART_APP);
                });
              },
            },
            {
              user_id: HelperMemo.user_data.user.id,
              device_type: Platform.OS,
              token: HelperMemo.token,
            },
          );
        },
      });
    }
  }
}

const styles = StyleSheet.create({
  tabView: {
    flex: 1,
  },
  icon: {
    width: 20,
    height: 20,
    marginRight: 8,
  },
});
