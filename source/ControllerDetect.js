import {Component} from 'react';
import Zeroconf from 'react-native-zeroconf';
import _ from 'lodash';

export default class ControllerDetect extends Component {
  constructor(props) {
    super(props);

    this.rows = [];
    this.timer = null;
    this.socket = null;
    this.state = {
      isScanning: false,
    };

    this.focusListener = this.props.navigation.addListener('focus', () => {
      this.zeroconf.scan('presenctl', 'tcp', 'local.');
      if (!this.firstFetch) {
        this.zeroconf.addDeviceListeners();
      }
    });

    this.blurListener = this.props.navigation.addListener('blur', () => {
      this.firstFetch = false;
      this.zeroconf.stop();
      this.zeroconf.removeDeviceListeners();
    });
  }

  componentDidMount() {
    this.zeroconf = new Zeroconf();
    this.startListening();
    this.firstFetch = true;
  }

  componentWillUnmount() {
    if (this.focusListener) {
      this.focusListener();
    }
    if (this.blurListener) {
      this.blurListener();
    }
  }

  startListening() {
    const that = this;
    this.zeroconf.on('resolved', service => {
      const pattern =
        /((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}/g;
      let ip = '';
      _.forEach(service.addresses, (v, k) => {
        if (pattern.test(v)) {
          ip = v;
          return;
        }
      });
      const ip_data = {ip: ip, sn: service.txt.sn};
      if (
        _.find(that.rows, function (row) {
          return row.sn == service.txt.sn;
        })
      ) {
      } else {
        that.rows.push(ip_data);
      }

      that.props.onFinished(that.rows);
    });
  }

  render() {
    return null;
  }
}
