import {Platform, Linking} from 'react-native';
import I18n from './I18n';
import {check, PERMISSIONS, RESULTS, request} from 'react-native-permissions';
import Getui from './Getui';
import AlertModal from './share/AlertModal';

const PERMISSION_TYPES = {
  location: {
    ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
    android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
    title: 'permissions.location_title',
    blockMessage: 'permissions.location_block',
    errorMessage: 'permissions.location_error',
  },
  camera: {
    ios: PERMISSIONS.IOS.CAMERA,
    android: PERMISSIONS.ANDROID.CAMERA,
    title: 'permissions.camera_title',
    blockMessage: 'permissions.camera_block',
    errorMessage: 'permissions.camera_error',
  },
  media: {
    ios: PERMISSIONS.IOS.PHOTO_LIBRARY,
    android: PERMISSIONS.ANDROID.WRITE_EXTERNAL_STORAGE,
    title: 'permissions.media_title',
    blockMessage: 'permissions.media_block',
    errorMessage: 'permissions.media_error',
  },
};

async function getPermission(permissionType, callback) {
  const {ios, android, title, blockMessage, errorMessage} =
    PERMISSION_TYPES[permissionType];
  const permission = Platform.OS === 'ios' ? ios : android;
  const result = await check(permission);

  switch (result) {
    case RESULTS.UNAVAILABLE:
      alertError(errorMessage);
      return;
    case RESULTS.DENIED:
      const req = await request(permission);
      if (req === RESULTS.DENIED) {
        alertDenied(blockMessage);
        return;
      }
      break;
    case RESULTS.GRANTED:
      callback();
      break;
    case RESULTS.BLOCKED:
      openSetting(title);
      return;
  }
}

function openSetting(titleKey) {
  const title = I18n.t(titleKey);
  AlertModal.alert(title, '', [
    {
      text: I18n.t('home.cancel'),
      onDismiss: () => {},
    },
    {
      text: I18n.t('home.setting'),
      onPress: () => {
        if (Platform.OS === 'ios') {
          Linking.openURL('app-settings:').catch(err =>
            console.log('error', err),
          );
        } else {
          Getui.gotoNotificationSetting();
        }
      },
    },
  ]);
}

function alertDenied(messageKey) {
  AlertModal.alert(I18n.t(messageKey));
}

function alertError(messageKey) {
  AlertModal.alert(I18n.t(messageKey));
}

export default class Permissions {
  static async LocationPermission(callback) {
    return getPermission('location', callback);
  }

  static async CameraPermission(callback) {
    return getPermission('camera', callback);
  }

  static async MediaPermission(callback) {
    return getPermission('media', callback);
  }
}
