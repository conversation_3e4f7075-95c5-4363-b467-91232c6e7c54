import React, {Component} from 'react';
import {View, Text, TouchableOpacity, ScrollView} from 'react-native';
import {Helper<PERSON><PERSON><PERSON>, Helper} from '../Helper';
import I18n from '../I18n';
import {Tme} from '../ThemeStyle';
import {observer} from 'mobx-react/native';
import RadioButtons from '../RadioButtons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {
  NotificationCenter,
  SUN_SELECT_ADD,
  EVENT_MAP,
} from '../NotificationCenter';
import NavBarView from '../share/NavBarView';
import Permissions from '../Permissions';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';

@observer
class SunScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      is_sunrise: this.props.route.params.data
        ? this.props.route.params.data.is_sunrise
          ? 'sunrise'
          : 'sunset'
        : 'sunrise',
      is_gaode: this.props.route.params.is_gaode,
    };
    this.props.route.params.routine.is_sunrise = this.props.route.params.data
      ? this.props.route.params.data.is_sunrise
        ? 'sunrise'
        : 'sunset'
      : 'sunrise';
  }

  rightClick() {
    this._save();
  }

  componentDidMount() {
    NotificationCenter.addObserver(this, SUN_SELECT_ADD, data => {
      this.props.route.params.routine.address = data.address;
      this.props.route.params.routine.lat = data.lat;
      this.props.route.params.routine.lng = data.lng;
    });
    NotificationCenter.addObserver(this, EVENT_MAP, data => {
      this.props.route.params.routine.address = data.address;
      this.props.route.params.routine.lat = data.lat;
      this.props.route.params.routine.lng = data.lng;
    });
    var lat = '';
    var lng = '';
    var address = '';
    if (this.props.route.params.data && this.props.route.params.data.location) {
      const location = Helper.locationFormat(
        this.props.route.params.data.location,
      );
      if (location[1]) {
        lat = location[1];
      }
      if (location[0]) {
        lng = location[0];
      }
      if (this.props.route.params.data.location_info) {
        address = this.props.route.params.data.location_info;
      }
    } else {
      if (this.props.route.params.routine.home_address.location) {
        const location = Helper.locationFormat(
          this.props.route.params.routine.home_address.location,
        );
        if (location[0]) {
          lng = location[0];
        }
        if (location[1]) {
          lat = location[1];
        }
      }
      if (this.props.route.params.routine.home_address.city) {
        address = this.props.route.params.routine.home_address.city;
      }
    }

    if (lat && lng) {
      this.props.route.params.routine.address = address;
      this.props.route.params.routine.lat = lat;
      this.props.route.params.routine.lng = lng;
    }

    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.next')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  componentWillUnmount() {
    NotificationCenter.removeObserver(this, SUN_SELECT_ADD);
    NotificationCenter.removeObserver(this, EVENT_MAP);
  }

  render() {
    var types = [
      {key: 'sunrise', value: I18n.t('automation.sunrise')},
      {key: 'sunset', value: I18n.t('automation.sunset')},
    ];
    return (
      <NavBarView>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View
            style={{
              flex: 1,
            }}>
            <View style={{height: 20}} />
            <View style={{backgroundColor: Tme('cardColor')}}>
              <TouchableOpacity
                onPress={this.click.bind(this)}
                activeOpacity={1.0}
                style={{
                  padding: 16,
                  backgroundColor: Tme('cardColor'),
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <Text
                  style={{
                    marginLeft: 5,
                    fontSize: 17,
                    color: Tme('cardTextColor'),
                  }}>
                  {this.props.route.params.routine.address
                    ? this.props.route.params.routine.address
                    : I18n.t('automation.current_located_city')}
                </Text>
                <View styl={{width: 25}}>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={20}
                    color={Tme('textColor')}
                  />
                </View>
              </TouchableOpacity>
            </View>
          </View>
          <View style={{width: 1, height: 16}} />
          <View style={{backgroundColor: Tme('cardColor')}}>
            <RadioButtons
              data={types}
              defaultKey={this.state.is_sunrise}
              onChange={this.onChange.bind(this)}
            />
          </View>
        </ScrollView>
      </NavBarView>
    );
  }
  click() {
    var that = this;
    Permissions.LocationPermission(() => {
      if (HelperMemo.user_data.user.zone == 'cn') {
        this.props.navigation.push('CityList', {
          data: that.props.data,
          routine: that.props.routine,
          title: I18n.t('automation.select_location'),
        });
      } else {
        that.pushMap();
      }
    });
  }

  pushMap() {
    this.props.navigation.push('MapViewShow', {
      address: this.props.route.params.routine.address,
      lat: this.props.route.params.routine.lat,
      lng: this.props.route.params.routine.lng,
      title: I18n.t('automation.select_location'),
    });
  }

  _save() {
    if (
      this.props.route.params.routine.lat &&
      this.props.route.params.routine.address
    ) {
      this.props.navigation.push('SmartSelectCondition', {
        data: this.props.route.params.data,
        routine: this.props.route.params.routine,
        title: I18n.t('automation.select_conditions'),
      });
    } else {
      AlertModal.alert(I18n.t('automation.select_location'));
    }
  }

  onChange(e) {
    if (e == 'sunset') {
      this.props.route.params.routine.is_sunrise = false;
    } else {
      this.props.route.params.routine.is_sunrise = true;
    }
    this.setState({
      is_sunrise: e,
    });
  }
}
export default SunScreen;
