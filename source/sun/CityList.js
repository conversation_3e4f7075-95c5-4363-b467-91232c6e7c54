import React, {Component} from 'react';
import {
  TextInput,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Platform,
  NativeModules,
  NativeEventEmitter,
} from 'react-native';
const AMapGeolocation = NativeModules.AMapGeolocation;
const AMapEventEmitter = new NativeEventEmitter(AMapGeolocation);
import cityDatas from './CityData';
import GPSHelper from '../GPSHelper';
import I18n from '../I18n';
import {DEVICE_HEIGHT, DEVICE_WIDTH} from '../Helper';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import _ from 'lodash';
import {
  init,
  setNeedAddress,
  start,
  setLocatingWithReGeocode,
  stop,
} from 'react-native-amap-geolocation';
import AppConfig from '../../app_config';
import {Tme, Colors} from '../ThemeStyle';
const HEADER_HEIGHT = 24;
const ROW_HEIGHT = 48;

let totalHeight = [];
import {observer} from 'mobx-react/native';
import {NotificationCenter, SUN_SELECT_ADD} from '../NotificationCenter';
import NavBarView from '../share/NavBarView';
import axios from 'axios';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {hideLoading, showLoading} from '../../ILoading';

@observer
class CityList extends Component {
  constructor(props) {
    super(props);
    totalHeight = this._gotTotalHeightArray();

    this.state = {
      currentCity: I18n.t('automation.locating'),
      sectionListDatas: cityDatas,
      search: '',
      lat: '',
      lng: '',
      list: [],
    };

    init({
      ios: 'd22701c2277010686b0608373c038bf6',
      android: '82b00bf7dcae3645d454544f9c4457be',
    });

    this.scrollRef = React.createRef();
    this.props.navigation.setOptions({
      // eslint-disable-next-line react/no-unstable-nested-components
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.next')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    var that = this;
    that._save();
  }

  _save() {
    const location = GPSHelper.gcj02towgs84(this.state.lng, this.state.lat);
    NotificationCenter.dispatchEvent(SUN_SELECT_ADD, {
      address: this.state.currentCity,
      lat: location[1],
      lng: location[0],
    });
    this.props.navigation.goBack();
  }

  // 获取每个字母区域的高度
  _gotTotalHeightArray() {
    let totalArray = [];
    for (let i = 0; i < cityDatas.length; i++) {
      let eachHeight = HEADER_HEIGHT + ROW_HEIGHT * cityDatas[i].data.length;
      totalArray.push(eachHeight);
    }
    return totalArray;
  }

  // 获取字母列表头
  _gotLettersArray() {
    let LettersArray = [];
    for (let i = 0; i < cityDatas.length; i++) {
      let element = cityDatas[i];
      LettersArray.push(element.title);
    }
    return LettersArray;
  }

  componentDidMount() {
    this.location();
    showLoading();
    setTimeout(() => {
      this._renderCityList();
    }, 500);
  }

  componentWillUnmount() {
    if (this.AMapEvent) {
      this.AMapEvent.remove();
    }
    stop();
  }

  currentCityAction() {
    if (this.state.lat) {
      this._save();
    }
  }

  // 点击右侧字母滑动到相应位置
  scrollToList(item, index) {
    let position = 0;
    for (let i = 0; i < index; i++) {
      position += totalHeight[i];
    }
    this.scrollRef.current.scrollTo({y: position});
  }

  /*右侧索引*/
  _renderSideSectionView() {
    const sectionItem = cityDatas.map((item, index) => {
      return (
        <Text
          onPress={() => this.scrollToList(item, index)}
          key={index}
          style={{
            textAlign: 'center',
            alignItems: 'center',
            height: (DEVICE_HEIGHT * 0.7 - 180) / cityDatas.length,
            lineHeight: (DEVICE_HEIGHT * 0.7 - 180) / cityDatas.length,
            color: Colors.GoldenColor,
          }}>
          {item.sortLetters}
        </Text>
      );
    });

    return (
      <View
        style={{
          position: 'absolute',
          top: 190,
          right: 5,
          width: 20,
          height: DEVICE_HEIGHT * 0.7 - 180,
        }}>
        {sectionItem}
      </View>
    );
  }

  // 渲染城市列表
  _renderCityList() {
    let lists = [];
    for (let i = 0; i < this.state.sectionListDatas.length; i++) {
      let sections = this.state.sectionListDatas[i];
      let header = (
        <View
          key={sections.sortLetters}
          style={[
            styles.cityLetterBox,
            {backgroundColor: Tme('inputBorderColor')},
          ]}>
          <Text style={[styles.cityLetterText, {color: Tme('cardTextColor')}]}>
            {sections.sortLetters}
          </Text>
        </View>
      );
      lists.push(header);
      for (let j = 0; j < sections.data.length; j++) {
        let element = sections.data[j];
        let cityCell = (
          <TouchableOpacity
            activeOpacity={0.8}
            key={element.name + j}
            onPress={() => {
              this.selectCity(element);
            }}>
            <View
              style={[styles.cityTextBox, {backgroundColor: Tme('cardColor')}]}>
              <Text
                style={[styles.cityTextStyle, {color: Tme('cardTextColor')}]}>
                {element.name}
              </Text>
            </View>
          </TouchableOpacity>
        );
        lists.push(cityCell);
      }
    }
    this.setState(
      {
        list: lists,
      },
      () => {
        hideLoading();
      },
    );
  }

  selectCity(cityItem) {
    var that = this;
    showLoading();
    axios
      .get(
        AppConfig.gaode_api_url +
          `/v3/geocode/geo?key=${AppConfig.gapde_api_key}&address=${cityItem.name}`,
      )
      .then(res => {
        const response = res.data;
        if (response.status == '1' && response.geocodes.length > 0) {
          var temp = response.geocodes[0].location.split(',');
          that.setState(
            {
              currentCity: cityItem.name,
              lat: temp[1],
              lng: temp[0],
            },
            () => {
              hideLoading();
              that._save();
            },
          );
        }
      })
      .catch(e => console.log('Oops, error', e));
  }

  render() {
    return (
      <NavBarView>
        <View style={{backgroundColor: Tme('cardColor')}}>
          <View
            style={{
              paddingHorizontal: 16,
            }}>
            <View
              style={[
                styles.account_view,
                {
                  borderColor: Tme('inputBorderColor'),
                  borderRadius: 4,
                  flexDirection: 'row',
                },
              ]}>
              <TouchableOpacity
                onPress={this._search.bind(this)}
                style={{width: 18, marginHorizontal: 5}}
                activeOpacity={0.8}>
                <MaterialIcons
                  name="search"
                  size={20}
                  color={Tme('textColor')}
                />
              </TouchableOpacity>
              <TextInput
                placeholderTextColor={Tme('placeholder')}
                style={[Colors.TextInputStyle(), {width: DEVICE_WIDTH - 100}]}
                autoCapitalize="none"
                returnKeyType="go"
                clearButtonMode="always"
                underlineColorAndroid="transparent"
                value={this.state.search}
                onSubmitEditing={this._search.bind(this)}
                onChangeText={this.onChangeText.bind(this)}
              />
            </View>
          </View>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingHorizontal: 16,
              paddingBottom: 15,
            }}>
            <View>
              <Text
                style={{
                  color: Tme('textColor'),
                }}>
                {I18n.t('automation.current_located_city')}
              </Text>
              <View
                style={{marginTop: 12}}
                activeOpacity={0.8}
                onPress={() => {
                  this.currentCityAction();
                }}>
                <View
                  style={{
                    backgroundColor: Tme('cardColor'),
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <MaterialIcons
                    name="location-on"
                    size={20}
                    color={Colors.MainColor}
                  />
                  <Text style={{color: Tme('cardTextColor'), fontSize: 14}}>
                    {this.state.currentCity}
                  </Text>
                </View>
              </View>
            </View>
            <TouchableOpacity
              onPress={this.reset.bind(this)}
              activeOpacity={0.8}>
              <Text style={{color: Colors.MainColor}}>
                {I18n.t('automation.relocate')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        <View
          style={{
            backgroundColor: Tme('bgColor'),
            width: DEVICE_WIDTH,
            marginTop: 10,
            marginBottom: 10,
          }}>
          <Text style={{color: Tme('textColor'), paddingLeft: 16}}>
            {I18n.t('automation.city')}
          </Text>
        </View>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{backgroundColor: Tme('cardColor')}}
          ref={this.scrollRef}>
          {this.state.list}
        </ScrollView>
        {this._renderSideSectionView()}
      </NavBarView>
    );
  }

  location() {
    var that = this;
    if (Platform.OS == 'ios') {
      setLocatingWithReGeocode(true);
    } else {
      setNeedAddress(true);
    }
    this.AMapEvent = AMapEventEmitter.addListener(
      'AMapGeolocation',
      location => {
        if (location.city) {
          stop();
          that.setState({
            currentCity: location.city.replace('市', ''),
            lat: location.latitude,
            lng: location.longitude,
          });
        }
      },
    );
    // 开始连续定位
    start();
  }

  reset() {
    this.location();
  }

  _search() {
    var temp = [];
    let Chinese = new RegExp('^[\u4e00-\u9fa5]+$');
    let English = new RegExp('^[a-zA-Z]+$');
    if (this.state.search.length > 0) {
      var pinyin = true;
      _.each(cityDatas, (v, k) => {
        _.each(v.data, (city, key) => {
          if (Chinese.test(this.state.search)) {
            if (city.name.indexOf(this.state.search) != -1) {
              temp.push(city);
            }
          }
          if (English.test(this.state.search)) {
            if (_.startsWith(city.up, this.state.search.toUpperCase())) {
              temp.push(city);
              pinyin = false;
            }
          }
        });
      });
      if (pinyin) {
        _.each(cityDatas, (v, k) => {
          _.each(v.data, (city, key) => {
            if (English.test(this.state.search)) {
              if (_.startsWith(city.pinyin, this.state.search.toUpperCase())) {
                temp.push(city);
              }
            }
          });
        });
      }
      if (temp.length > 0) {
        this.setState(
          {
            sectionListDatas: [{sortLetters: '#', data: temp}],
          },
          () => {
            showLoading();
            this._renderCityList();
          },
        );
      }
    } else {
      this.setState(
        {
          sectionListDatas: cityDatas,
        },
        () => {
          showLoading();
          this._renderCityList();
        },
      );
    }
  }

  onChangeText(text) {
    if (text.length == 0) {
      this.setState(
        {
          sectionListDatas: cityDatas,
          search: text,
        },
        () => {
          showLoading();
          this._renderCityList();
        },
      );
    } else {
      this.setState(
        {
          search: text,
        },
        () => {
          this._search();
        },
      );
    }
  }
}
const styles = StyleSheet.create({
  textView: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 5,
    paddingRight: 10,
    marginRight: 10,
  },
  cityLetterBox: {
    height: HEADER_HEIGHT,
    justifyContent: 'center',
  },
  cityLetterText: {
    fontSize: 17,
    marginLeft: 16,
  },
  cityTextBox: {
    height: ROW_HEIGHT,
    justifyContent: 'center',
  },
  cityTextStyle: {
    fontSize: 16,
    marginLeft: 16,
  },
  account_view: {
    padding: 3,
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 3,
    marginBottom: 20,
    marginTop: 16,
  },
});
export default CityList;
