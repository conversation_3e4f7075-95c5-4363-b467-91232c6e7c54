import {DeviceEventEmitter, NativeModules, Platform} from 'react-native';

const GetuiModule = NativeModules.GetuiModule;

const listeners = {};
const ConnectEvent = 'onReceiveOnlineState'; //连接状态
const NotificationEvent = 'NotificationEvent'; //通知事件

export default class GetuiPush {
  static testGeTui() {
    GetuiModule.testGeTui();
  }

  static getWidgetData(callback) {
    GetuiModule.getWidgetData(callback);
  }

  /*
   * 初始化推送服务
   *
   * 请在componentDidMount()调用init，否则会影响通知点击事件的回调
   * */
  static init() {
    if (Platform.OS == 'android') {
      GetuiModule.init();
    }
  }

  static isNotificationEnabled(callback) {
    GetuiModule.isNotificationEnabled(callback);
  }

  static deleteNotify() {
    GetuiModule.deleteNotify();
  }

  /*
   * 设置调试模式，默认关闭状态
   *
   * 该接口需在 init 接口之后调用，避免出现部分日志没打印的情况
   * @param enable = boolean
   * */
  static setDebugMode(enable) {
    GetuiModule.setDebugMode(enable);
  }

  /*
   * 获取 RegistrationID
   *
   * 调用此 API 来取得应用程序对应的 RegistrationID。
   * 只有当应用程序成功注册到 Getui 的服务器时才返回对应的值，否则返回空字符串
   *
   * @param {Function} callback = (result) => {"registerID":String}
   * */
  static getRegistrationID(callback) {
    if (Platform.OS == 'android') {
      GetuiModule.getRegistrationID(callback);
    }
  }

  /*
   *   检查当前应用的通知开关是否开启
   * */
  static isPushTurnedOn(callback) {
    if (Platform.OS == 'android') {
      GetuiModule.isPushTurnedOn(callback);
    }
  }

  //***************************************接口回调***************************************

  //连接状态
  static addConnectEventListener(callback) {
    listeners[callback] = DeviceEventEmitter.addListener(
      ConnectEvent,
      result => {
        callback(result);
      },
    );
  }

  /*
   * 通知事件
   *
   * @param {Function} callback = (result) => {"messageID":String,"title":String，"content":String,"badge":String,"ring":String,"extras":{String:String},"notificationEventType":String}
   *
   * messageID:唯一标识通知消息的 ID
   *
   * title:对应 Portal 推送通知界面上的“通知标题”字段
   *
   * content:对应 Portal 推送通知界面上的“通知内容”字段
   *
   * badge:对应 Portal 推送通知界面上的可选设置里面的“badge”字段 (ios only)
   *
   * ring:对应 Portal 推送通知界面上的可选设置里面的“sound”字段 (ios only)
   *
   * extras:对应 Portal 推送消息界面上的“可选设置”里的附加字段
   *
   * notificationEventType：分为notificationArrived和notificationOpened两种
   *
   * 注意：应用在存活状态下点击通知不会有跳转行为，应用在被杀死状态下点击通知会启动应用
   *
   * */
  static addNotificationListener(callback) {
    listeners[callback] = DeviceEventEmitter.addListener(
      NotificationEvent,
      result => {
        callback(result);
      },
    );
  }

  //移除事件
  static removeListener(callback) {
    if (!listeners[callback]) {
      return;
    }
    listeners[callback].remove();
    listeners[callback] = null;
  }

  //***************************************Android Only***************************************

  /*
   * 停止推送服务
   * */
  static stopPush() {
    if (Platform.OS == 'android') {
      GetuiModule.stopPush();
    }
  }

  /*
   * 恢复推送服务
   * */
  static resumePush() {
    if (Platform.OS == 'android') {
      GetuiModule.resumePush();
    }
  }

  /*******
   * 跳转到通知页面
   * */
  static gotoNotificationSetting() {
    GetuiModule.gotoNotificationSetting();
  }

  static openNotification() {
    GetuiModule.openNotification();
  }

  // static setNavigationBarColor(color) {
  //   if (Platform.OS == 'android') {
  //     GetuiModule.setNavigationBarColor(color);
  //   }
  // }

  static isDark() {
    GetuiModule.isDark(data => {
      return data;
    });
  }

  static openAPP() {
    GetuiModule.openAPP();
  }

  static exitApp() {
    GetuiModule.exitApp();
  }
}
