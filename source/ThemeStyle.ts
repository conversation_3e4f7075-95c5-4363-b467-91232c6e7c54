import {Appearance, TextStyle, ViewStyle} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {mainTitle} from './Tools';

// 定义类型
interface ColorScheme {
  MainColor: string;
  GoldenColor: string;
  BlueColor: string;
  CardFontStyle: TextStyle;
  ShadowColor: () => string;
  RoomTitleFontStyle: TextStyle;
  HomeCardSize: number;
  TextInputStyle: () => ViewStyle;
}

interface DeviceIcon {
  icon: string;
  reg: RegExp;
}

interface DeviceIconType {
  icons: DeviceIcon[];
  defaultIcon: string;
}

interface ThemeStyleType {
  getColor: (key: string, theme?: string) => string;
  [key: string]: any; // 允许ThemeStyle有动态属性
}

// 变量声明
let ThemeMode: string | undefined;

// 函数定义
const ReloadThemeMode = (): string | undefined => {
  const t = Appearance.getColorScheme() || 'light';
  getTheme().then(theme => {
    if (theme) {
      if (theme === 'system_with') {
        ThemeMode = t;
        return t;
      } else {
        ThemeMode = theme;
        return theme;
      }
    } else {
      ThemeMode = t;
      return t;
    }
  });
  return ThemeMode;
};

const getTheme = async (): Promise<string | null> => {
  try {
    const jsonValue = await AsyncStorage.getItem('theme');
    return jsonValue != null ? JSON.parse(jsonValue) : null;
  } catch (e) {
    return null;
  }
};

const IsDark = (): boolean => {
  ReloadThemeMode();
  return ThemeMode == 'dark';
};

const Colors: ColorScheme = {
  MainColor: '#fc577a',
  GoldenColor: '#C49225', //"#e6ce1e"
  BlueColor: '#4da1ff',
  CardFontStyle: {
    fontSize: mainTitle(),
    fontWeight: '500',
  },
  ShadowColor: (): string => {
    return IsDark() ? '#000000' : 'rgba(0,0,0,0.2)';
  },
  RoomTitleFontStyle: {
    fontWeight: '500',
    fontSize: 26,
  },
  HomeCardSize: 100,
  TextInputStyle: (): TextStyle => {
    return {
      height: 40,
      marginLeft: 6,
      marginRight: 6,
      color: ThemeStyle.getColor('cardTextColor'),
    };
  },
};
const Tme = (key: string, theme?: string): string => {
  // 强制使用指定主题
  if (theme !== undefined) {
    return ThemeStyle[theme + key];
  }
  if (IsDark()) {
    return ThemeStyle['D' + key];
  } else {
    return ThemeStyle['L' + key];
  }
};

const ThemeStyle: ThemeStyleType = {
  getColor: (key: string, theme?: string): string => {
    return Tme(key, theme);
  },

  DAlexaPng: '../img/services/amazon-alexa-dark.png',
  LAlexaPng: '../img/services/amazon-alexa-light.png',
  DdeviceBg: '#2b323a',
  LdeviceBg: '#f6f9fa',

  DbgColor: 'rgb(12,12,12)',
  LbgColor: 'rgb(241,241,246)',
  DbgColor2: 'rgba(12,12,12, 0.4)',
  LbgColor2: 'rgba(241,241,246, 0.4)',
  DtextColor: 'rgba(255,255,255,0.6)',
  LtextColor: '#9497A0',

  DtransCardTextColor: 'rgba(255,255,255,0.6)',
  LtransCardTextColor: '#49515a',

  DcardColor2: 'rgba(29,29,29,0.6)',
  LcardColor2: 'rgba(255,255,255,0.9)',

  DcardColor: 'rgb(29,29,29)',
  LcardColor: 'rgb(255,255,255)',

  DcardTextColor: 'rgba(255,255,255,0.86)',
  LcardTextColor: '#323c47',

  DsmallTextColor: 'rgba(235, 235, 245, 0.6)',
  LsmallTextColor: '#969595',

  DmodeColor: 'rgba(20, 29, 36, 0.6)',
  LmodeColor: 'rgba(255,255,255,0.86)',
  DnavTextColor: 'rgba(255,255,255,0.86)',
  LnavTextColor: '#323c47',
  DinputBorderColor: '#2b2b2d',
  LinputBorderColor: '#EFEFEF',
  Dplaceholder: 'rgba(255,255,255,0.6)',
  Lplaceholder: '#949595',
  DtextTitleColor: '#7f8fa4',
  LtextTitleColor: '#7f8fa4',
  DbottomSheetBg: '#000',
  LbottomSheetBg: '#fff',

  DtabInactiveTextColor: '#868383',
  LtabInactiveTextColor: '#868383',
  DtabActiveTextColor: '#FFFFFF',
  LtabActiveTextColor: '#323c47',

  DgradientEndColor: 'transparent',
  LgradientEndColor: 'rgba(255,255,255,0)',

  DtopTextColor: '#fff',
  LtopTextColor: '#fff',

  DtabBarBackgroundColor: 'rgba(29,29,29,0.3)',
  LtabBarBackgroundColor: 'rgba(255,255,255,0.9)',
  DtabBarColor: '#FFFFFF',
  LtabBarColor: '#969595',

  DscrollTabActiveColor: '#EBEBEB',
  LscrollTabActiveColor: '#EBEBEB',

  DscrollTabColor: '#5B5B5B',
  LscrollTabColor: '#5B5B5B',
};

interface StyleType {
  [key: string]: ViewStyle | TextStyle;
}

const getStyle = (name: string): ViewStyle | TextStyle => {
  const styles: StyleType = {
    spec: {
      backgroundColor: ThemeStyle.getColor('cardColor2'),
      borderRadius: 16,
      padding: 12,
    },
    specNameText: {
      fontWeight: '500',
      fontSize: 17,
      color: ThemeStyle.getColor('cardTextColor'),
    },
  };
  return styles[name];
};

const DeviceIcons: DeviceIconType = {
  icons: [
    {icon: 'light-switch', reg: /switch/i},
    {icon: 'video-outline', reg: /camera/i},
    {icon: 'lamp', reg: /dimmer/i},
    {icon: 'thermostat', reg: /thermostat/i},
    {icon: 'thermometer', reg: /temperature/i},
    {icon: 'water-outline', reg: /water/i},
    {icon: 'door', reg: /door/i},
    {icon: 'window-maximize', reg: /window/i},
    {icon: 'water', reg: /water/i},
    {icon: 'water', reg: /flood/i},
    {icon: 'fire', reg: /fire/i},
    {icon: 'motion-sensor', reg: /motion/i},
    {icon: 'contactless-payment-circle-outline', reg: /contact/i},
    {icon: 'contactless-payment-circle-outline', reg: /sensor/i},
    {icon: 'bell-alert', reg: /alarm/i},
    {icon: 'vibrate', reg: /vibration/i},
    {icon: 'battery', reg: /battery/i},
    {icon: 'cctv', reg: /ipc/i},
  ],
  defaultIcon: 'album',
};

export {
  ThemeMode,
  ReloadThemeMode,
  IsDark,
  Tme,
  getStyle as GetStyle,
  Colors,
  DeviceIcons,
};
