/**
 * Based on http://stackoverflow.com/a/33585501/1783214
 *
 * Handle resizing enclosed View and scrolling to input
 *
 * 新的 TypeScript/Hooks 用法:
 *    const keyboardHandlerRef = useRef<KeyboardHandlerRef>(null);
 *    const inputRef = useRef<TextInput>(null);
 *
 *    <KeyboardHandler ref={keyboardHandlerRef} offset={DEVICE_HEIGHT / 3} keyboardDismissMode='on-drag'>
 *      <View>
 *        ...
 *        <TextInput ref={inputRef}
 *          onFocus={() => keyboardHandlerRef.current?.inputFocused(inputRef.current)}/>
 *        ...
 *      </View>
 *    </KeyboardHandler>
 *
 * 兼容旧的类组件用法:
 *    <KeyboardHandler ref='kh' offset={DEVICE_HEIGHT / 3} keyboardDismissMode='on-drag'>
 *      <View>
 *        ...
 *        <TextInput ref='username'
 *          onFocus={()=>this.refs.kh.inputFocused(this,'username')}/>
 *        ...
 *      </View>
 *    </KeyboardHandler>
 *
 *  offset is optional and defaults to 0
 *  Any other specified props will be passed on to ScrollView
 */
import React, {
  useRef,
  useState,
  useEffect,
  useCallback,
  useMemo,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {
  ScrollView,
  View,
  Keyboard,
  findNodeHandle,
  KeyboardEvent,
  ScrollViewProps,
} from 'react-native';

interface KeyboardHandlerProps extends Omit<ScrollViewProps, 'children'> {
  /**
   * 键盘显示时的额外偏移量
   */
  offset?: number;
  /**
   * 子组件
   */
  children: React.ReactNode;
}

export interface KeyboardHandlerRef {
  /**
   * 当输入框获得焦点时调用此方法
   *
   * 新用法: inputFocused(inputRef.current)
   * 旧用法: inputFocused(this, 'refName') - 为了向后兼容
   *
   * @param inputNodeOrComponent 输入框的节点引用或组件实例
   * @param refName 可选的引用名称（用于向后兼容）
   */
  inputFocused: (inputNodeOrComponent: any, refName?: string) => void;
}

const KeyboardHandler = forwardRef<KeyboardHandlerRef, KeyboardHandlerProps>(
  ({offset = 0, children, ...scrollViewProps}, ref) => {
    const [keyboardSpace, setKeyboardSpace] = useState<number>(0);
    const focusedNodeRef = useRef<any>(null);
    const scrollRef = useRef<ScrollView>(null);

    // 键盘显示事件处理
    const handleKeyboardDidShow = useCallback(
      (event: KeyboardEvent) => {
        if (!event.endCoordinates || !focusedNodeRef.current) {
          return;
        }

        setKeyboardSpace(event.endCoordinates.height);

        // 获取 ScrollView 的 scrollResponder 并滚动到聚焦的输入框
        const scrollResponder = scrollRef.current?.getScrollResponder?.();
        if (scrollResponder && scrollResponder.scrollResponderScrollNativeHandleToKeyboard) {
          const nodeHandle = findNodeHandle(focusedNodeRef.current);
          if (nodeHandle) {
            scrollResponder.scrollResponderScrollNativeHandleToKeyboard(
              nodeHandle,
              offset,
              true,
            );
          }
        }
      },
      [offset],
    );

    // 键盘隐藏事件处理
    const handleKeyboardWillHide = useCallback(() => {
      setKeyboardSpace(0);
    }, []);

    // 设置键盘事件监听器
    useEffect(() => {
      const didShowListener = Keyboard.addListener(
        'keyboardDidShow',
        handleKeyboardDidShow,
      );
      const willHideListener = Keyboard.addListener(
        'keyboardWillHide',
        handleKeyboardWillHide,
      );

      return () => {
        didShowListener.remove();
        willHideListener.remove();
      };
    }, [handleKeyboardDidShow, handleKeyboardWillHide]);

    // 输入框聚焦处理方法（支持新旧两种用法）
    const inputFocused = useCallback((inputNodeOrComponent: any, refName?: string) => {
      if (refName && inputNodeOrComponent?.refs) {
        // 旧的类组件用法：inputFocused(this, 'refName')
        const inputNode = inputNodeOrComponent.refs[refName];
        focusedNodeRef.current = inputNode;
      } else {
        // 新的 hooks 用法：inputFocused(inputRef.current)
        focusedNodeRef.current = inputNodeOrComponent;
      }
    }, []);

    // 暴露给父组件的方法
    useImperativeHandle(
      ref,
      () => ({
        inputFocused,
      }),
      [inputFocused],
    );

    // 合并 ScrollView 属性
    const mergedScrollViewProps = useMemo(
      () => ({
        automaticallyAdjustContentInsets: true,
        keyboardShouldPersistTaps: 'never' as const,
        scrollEventThrottle: 200,
        ...scrollViewProps,
      }),
      [scrollViewProps],
    );

    return (
      <ScrollView ref={scrollRef} {...mergedScrollViewProps}>
        {children}
        <View style={{height: keyboardSpace}} />
      </ScrollView>
    );
  },
);

KeyboardHandler.displayName = 'KeyboardHandler';

export default KeyboardHandler;
