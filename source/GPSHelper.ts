//坐标转换变量
const PI = 3.1415926535897932384626;
const aa = 6378245.0;
const ee = 0.00669342162296594323;

interface GPSHelperType {
  gcj02towgs84: (ng: number | string, at: number | string) => [number, number];
  wgs84togcj02: (ng: number | string, at: number | string) => [number, number];
  transformlat: (lng: number, lat: number) => number;
  transformlng: (lng: number, lat: number) => number;
  out_of_china: (lng: number, lat: number) => boolean;
}

const GPSHelper: GPSHelperType = {
  // GCJ02 转换为 WGS84
  gcj02towgs84: function (ng, at) {
    let lng = Number(ng);
    let lat = Number(at);
    if (this.out_of_china(lng, lat)) {
      return [lng, lat];
    }
    const dlat = this.transformlat(lng - 105.0, lat - 35.0);
    const dlng = this.transformlng(lng - 105.0, lat - 35.0);
    const radlat = (lat / 180.0) * PI;
    let magic = Math.sin(radlat);
    magic = 1 - ee * magic * magic;
    const sqrtmagic = Math.sqrt(magic);
    const dlat_calc = (dlat * 180.0) / (((aa * (1 - ee)) / (magic * sqrtmagic)) * PI);
    const dlng_calc = (dlng * 180.0) / ((aa / sqrtmagic) * Math.cos(radlat) * PI);
    const mglat = lat + dlat_calc;
    const mglng = lng + dlng_calc;
    return [lng * 2 - mglng, lat * 2 - mglat];
  },

  // WGS84 转换为 GCJ02
  wgs84togcj02: function (ng, at) {
    let lng = Number(ng);
    let lat = Number(at);
    if (this.out_of_china(lng, lat)) {
      return [lng, lat];
    }
    const dlat = this.transformlat(lng - 105.0, lat - 35.0);
    const dlng = this.transformlng(lng - 105.0, lat - 35.0);
    const radlat = (lat / 180.0) * PI;
    let magic = Math.sin(radlat);
    magic = 1 - ee * magic * magic;
    const sqrtmagic = Math.sqrt(magic);
    const dlat_calc = (dlat * 180.0) / (((aa * (1 - ee)) / (magic * sqrtmagic)) * PI);
    const dlng_calc = (dlng * 180.0) / ((aa / sqrtmagic) * Math.cos(radlat) * PI);
    const mglat = lat + dlat_calc;
    const mglng = lng + dlng_calc;
    return [mglng, mglat];
  },

  transformlat: function (lng, lat) {
    let ret =
      -100.0 +
      2.0 * lng +
      3.0 * lat +
      0.2 * lat * lat +
      0.1 * lng * lat +
      0.2 * Math.sqrt(Math.abs(lng));
    ret +=
      ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) *
        2.0) /
      3.0;
    ret +=
      ((20.0 * Math.sin(lat * PI) + 40.0 * Math.sin((lat / 3.0) * PI)) * 2.0) /
      3.0;
    ret +=
      ((160.0 * Math.sin((lat / 12.0) * PI) +
        320 * Math.sin((lat * PI) / 30.0)) *
        2.0) /
      3.0;
    return ret;
  },

  transformlng: function (lng, lat) {
    let ret =
      300.0 +
      lng +
      2.0 * lat +
      0.1 * lng * lng +
      0.1 * lng * lat +
      0.1 * Math.sqrt(Math.abs(lng));
    ret +=
      ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) *
        2.0) /
      3.0;
    ret +=
      ((20.0 * Math.sin(lng * PI) + 40.0 * Math.sin((lng / 3.0) * PI)) * 2.0) /
      3.0;
    ret +=
      ((150.0 * Math.sin((lng / 12.0) * PI) +
        300.0 * Math.sin((lng / 30.0) * PI)) *
        2.0) /
      3.0;
    return ret;
  },

  /**
   * 判断是否在国内，不在国内则不做偏移
   * @param lng
   * @param lat
   * @returns {boolean}
   */
  out_of_china: function (lng, lat) {
    return (
      lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271 || false
    );
  },
};

export default GPSHelper;
