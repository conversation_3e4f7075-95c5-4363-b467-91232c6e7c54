import React, {useState, useRef} from 'react';
import {StyleSheet, Animated, ViewStyle, Dimensions} from 'react-native';
import {HelperMemo} from './Helper';
import AppConfig from '../app_config';
import _ from 'lodash';
import {ThemeMode} from './ThemeStyle';
import {WebView, WebViewProps} from 'react-native-webview';
import {hideLoading, showLoading} from '../ILoading';

interface WebViewWithProgressProps extends WebViewProps {
  withoutDefaultHeaders?: boolean;
  source: {
    uri: string;
    headers?: Record<string, string>;
  };
  style?: ViewStyle;
}

const WebViewWithProgress: React.FC<WebViewWithProgressProps> = props => {
  const [barWidth] = useState<Animated.Value>(new Animated.Value(0));
  const [barOpacity] = useState<Animated.Value>(new Animated.Value(1));
  const webRef = useRef<WebView>(null);

  let isStartLoading = false;

  React.useEffect(() => {
    setTimeout(() => {
      showLoading();
    }, 1000);
  }, []);

  const onLoadStart = (): void => {
    if (isStartLoading) {
      return;
    }
    isStartLoading = true;
    setTimeout(() => {
      hideLoading();
    }, 150);
    Animated.timing(barWidth, {
      toValue: 0.7,
      duration: 800,
      useNativeDriver: false,
    }).start();
  };

  const onLoadEnd = (): void => {
    Animated.timing(barWidth, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: false,
    }).start(() => {
      Animated.timing(barOpacity, {
        toValue: 0,
        useNativeDriver: false,
      }).start(() => {
        hideLoading();
        barWidth.setValue(0);
        barOpacity.setValue(1);
        isStartLoading = false;
      });
    });
  };

  // 创建一个包含所有属性的webViewProps
  let webViewProps = _.cloneDeep(props);

  // 如果需要添加默认headers
  if (!props.withoutDefaultHeaders) {
    if (!webViewProps.source) {
      webViewProps.source = {uri: ''};
    } else {
      webViewProps.source = _.cloneDeep(props.source);
    }

    webViewProps.source = _.extend(webViewProps.source, {
      headers: {
        X_IOT_API_KEY: AppConfig.api_key,
        X_IOT_UUID: HelperMemo.user_data ? HelperMemo.user_data.uuid : '',
        X_IOT_HOME: HelperMemo.user_data ? HelperMemo.user_data.home_id : '',
        X_IOT_SOURCE: 'api_webview',
        X_IOT_THEME_MODE: ThemeMode,
      },
    });
  }

  return (
    <Animated.View style={{flex: 1}}>
      <WebView
        ref={webRef}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={false}
        onLoadStart={onLoadStart}
        onLoad={onLoadEnd}
        {...webViewProps}
      />
      <Animated.View
        style={[
          styles.bar,
          {
            width: barWidth.interpolate({
              inputRange: [0, 1],
              outputRange: [0, Dimensions.get('window').width],
            }),
            opacity: barOpacity.interpolate({
              inputRange: [0, 1],
              outputRange: [0, 1],
            }),
          },
        ]}
      />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  bar: {
    position: 'absolute',
    height: 3,
    top: 0,
    left: 0,
    backgroundColor: AppConfig.ui.backgroud_border,
  },
});

export default WebViewWithProgress;
