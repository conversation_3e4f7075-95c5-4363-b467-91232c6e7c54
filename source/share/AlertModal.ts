import {Alert} from 'react-native';
import {HelperMemo} from '../Helper';
import I18n from '../I18n';

type UserInterfaceStyle = 'unspecified' | 'light' | 'dark' | undefined;

export default class AlertModal {
  // 将 theme 转换为合法的 userInterfaceStyle 值
  private static getUIStyle(): UserInterfaceStyle {
    const theme = HelperMemo.theme;
    // 只接受 'light', 'dark', 'unspecified' 或 undefined
    if (
      theme === 'light' ||
      theme === 'dark' ||
      theme === 'unspecified' ||
      theme === undefined
    ) {
      return theme;
    }
    // 默认返回 undefined，让系统自动选择
    return undefined;
  }
  static alert(title: string, message?: string, btns?: any) {
    if (message) {
      Alert.alert(title, message, btns, {
        userInterfaceStyle: this.getUIStyle(),
      });
    } else {
      Alert.alert(I18n.t('home.warning_message'), title, btns, {
        userInterfaceStyle: this.getUIStyle(),
      });
    }
  }
}
