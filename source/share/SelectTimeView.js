import React from 'react';
import { Tme, IsDark } from '../ThemeStyle';
import DatePicker from 'react-native-date-picker';

const minTime = new Date();
minTime.setDate(minTime.getDate() - 7);
const maxTime = new Date();

export default class SelectTimeView extends React.PureComponent {
  constructor(props) {
    super(props);
  }

  render() {
    return (
      <DatePicker
        testID="dateTimePicker"
        date={this.props.value}
        mode="date"
        maximumDate={maxTime}
        minimumDate={minTime}
        dividerColor={Tme('cardTextColor')}
        theme={IsDark() ? 'dark' : 'light'}
        onDateChange={this.onChange.bind(this)}
      />
    );
  }

  onChange(selectedDate) {
    const currentDate = selectedDate || this.props.value;
    this.props.onChange(currentDate);
  }
}
