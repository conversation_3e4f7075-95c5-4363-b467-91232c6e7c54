import React, {useEffect, useState, useRef} from 'react';
import {
  View,
  useWindowDimensions,
  ScrollView,
  TouchableOpacity,
  Text,
  Platform,
} from 'react-native';
import {Tme, IsDark} from '../ThemeStyle';
import {TabView} from 'react-native-tab-view';

const tabsWidth = {};
let isClick = 0;
export default function ScrollTabView(props) {
  const layout = useWindowDimensions();

  const [index, setIndex] = useState(0);
  const [routes, setRoutes] = useState(props.routes);
  const [currentTabIndex, setCurrentTabIndex] = useState(index);
  const scrollViewRef = useRef();
  const [scrollingIndex, setScrollingIndex] = useState(0);

  useEffect(() => {
    setRoutes(props.routes);
  }, [props.routes]);

  const _renderTabBar = p => {
    return (
      <View
        style={{
          height: 30,
          justifyContent: 'center',
          marginBottom: 12,
          paddingLeft: 6,
        }}>
        <ScrollView
          style={{flex: 1, flexDirection: 'row'}}
          contentContainerStyle={{justifyContent: 'center'}}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          ref={scrollViewRef}>
          {p.navigationState.routes.map((route, i) => {
            let activeColor = IsDark() ? '#EBEBEB' : '#333';
            let tabColor = IsDark() ? '#5B5B5B' : '#C0C0C9';

            if (props.parent.backgroundImageRef) {
              if (
                props.parent.backgroundImageRef.current &&
                props.parent.backgroundImageRef.current.child
              ) {
                if (
                  props.parent.backgroundImageRef.current.child.current.state
                    .uri
                ) {
                  activeColor = Tme('scrollTabActiveColor', 'D');
                  tabColor = Tme('scrollTabColor', 'D');
                } else {
                  activeColor = Tme('scrollTabActiveColor');
                  tabColor = Tme('scrollTabColor');
                }
              }
            }
            return (
              <TouchableOpacity
                activeOpacity={0.8}
                style={{alignItems: 'center', justifyContent: 'center'}}
                key={i}
                onLayout={e => {
                  tabsWidth[i] = e.nativeEvent.layout.width;
                }}
                onPress={() => {
                  isClick = 1;
                  setIndex(i);
                }}>
                <Text
                  style={[
                    {color: currentTabIndex == i ? activeColor : tabColor},
                    {
                      fontSize: 22,
                      paddingHorizontal: 8,
                      fontWeight: 'bold',
                    },
                  ]}>
                  {route.title}
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>
    );
  };

  const _scrollEnd = () => {
    let x = 0;
    let scrollX = 0;
    if (currentTabIndex == index) {
      // 滑动
      for (let i = 0; i <= index; i++) {
        x += tabsWidth[i];
      }
      scrollX = x - layout.width / 2;
      if (x > layout.width / 2) {
        scrollViewRef.current.scrollTo({x: scrollX, y: 0, animated: true});
      } else {
        scrollViewRef.current.scrollTo({
          x: scrollX - tabsWidth[index],
          y: 0,
          animated: true,
        });
      }
    } else {
      // 点击
      for (let i = 0; i <= index; i++) {
        x += tabsWidth[i];
      }
      scrollX = x - layout.width / 2;
      if (scrollX <= 0 || x > layout.width / 2) {
        scrollViewRef.current.scrollTo({x: scrollX, y: 0, animated: true});
      }
    }

    setCurrentTabIndex(index);
  };

  useEffect(() => {
    setIndex(currentTabIndex);

    if (props.onIndexChange) {
      props.onIndexChange(currentTabIndex);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentTabIndex]);

  useEffect(() => {
    _scrollEnd();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [index]);

  return (
    <TabView
      lazy
      lazyPreloadDistance={2}
      style={{flex: 1}}
      navigationState={{index, routes}}
      renderScene={props.renderScene}
      onIndexChange={i => {
        setScrollingIndex(i);
        setCurrentTabIndex(i);
      }}
      renderTabBar={_renderTabBar}
      initialLayout={{width: layout.width}}
      onSwipeEnd={() => {
        if (Platform.OS == 'android') {
          if (isClick == 0) {
            setCurrentTabIndex(scrollingIndex);
          } else {
            isClick = 0;
          }
        } else {
          setCurrentTabIndex(scrollingIndex);
        }
      }}
    />
  );
}
