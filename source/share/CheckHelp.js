import React, {Component} from 'react';
import {TouchableOpacity, Text} from 'react-native';
import I18n from '../I18n';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {Tme} from '../ThemeStyle';
import _ from 'lodash';

export default class CheckHelp extends Component {
  constructor(props) {
    super(props);
  }

  render() {
    return (
      <TouchableOpacity
        style={{flexDirection: 'row', alignItems: 'center'}}
        activeOpacity={0.8}
        onPress={this.onClick.bind(this)}>
        <MaterialIcons
          name="help-outline"
          size={14}
          color={Tme('smallTextColor')}
        />
        <Text
          style={{fontSize: 12, color: Tme('smallTextColor'), marginLeft: 4}}>
          {this.props.title || I18n.t('global.click_help')}
        </Text>
      </TouchableOpacity>
    );
  }

  onClick() {
    var passProps = {};
    if (_.isEmpty(this.props.urlTitle)) {
      passProps = {
        type: 'user_role',
      };
    } else {
      passProps = {
        urlTitle: this.props.urlTitle,
      };
    }

    this.props.navigation.push('HintView', {
      ...passProps,
    });
  }
}
