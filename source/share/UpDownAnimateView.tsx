import React, {forwardRef, useImperativeHandle, ReactNode} from 'react';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
  interpolate,
} from 'react-native-reanimated';
import {Dimensions, ViewStyle} from 'react-native';

interface UpDownAnimateViewProps {
  children?: ReactNode;
  style?: ViewStyle;
}

export interface UpDownAnimateViewRef {
  start: () => void;
  close?: () => void;
}

const UpDownAnimateView = forwardRef<UpDownAnimateViewRef, UpDownAnimateViewProps>((props, ref) => {
  useImperativeHandle(ref, () => ({
    start,
    close,
  }));
  const poupMarginTop = useSharedValue(0);
  // 获取设备的实际高度
  const DEVICE_HEIGHT = Dimensions.get('window').height;

  const animStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          translateY: interpolate(
            poupMarginTop.value,
            [0, 1],
            [-DEVICE_HEIGHT, 0],
          ),
        },
      ],
      opacity: interpolate(poupMarginTop.value, [0, 1], [0, 1]),
    };
  });

  const start = () => {
    poupMarginTop.value = withTiming(1, {
      duration: 300,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  };

  const close = () => {
    poupMarginTop.value = withTiming(0, {
      duration: 200,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  };

  return (
    <Animated.View style={[props.style, animStyle]}>
      {props.children}
    </Animated.View>
  );
});

export default UpDownAnimateView;
