import React, {useState, useRef, useEffect} from 'react';
import {
  Text,
  TouchableOpacity,
  View,
  ScrollView,
} from 'react-native';
import {Tme, IsDark, Colors} from '../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import DatePicker from 'react-native-date-picker';
import moment from 'moment';
import PubSub from 'pubsub-js';
import I18n from '../I18n';
import {mainRadius} from '../Tools';
import {PubSubEvent} from '../types/PubSubEvent';
import CommonBottomDrawer from './CommonBottomDrawer';

interface TimePickerProps {
  value: Date;
  onChange: (date: Date) => void;
}

export function TimePicker({value, onChange}: TimePickerProps) {
  const handleDateChange = (selectedDate: Date) => {
    const currentDate = selectedDate || value;
    onChange(currentDate);
  };

  return (
    <DatePicker
      testID="dateTimePicker"
      date={value}
      mode="time"
      dividerColor={Tme('cardTextColor')}
      theme={IsDark() ? 'dark' : 'light'}
      onDateChange={handleDateChange}
    />
  );
}

interface DatePickerModalProps {
  visible: boolean;
  onClose: () => void;
  value: Date;
  from: string;
  onChange: (value: Date, from: string) => void;
}

function DatePickerModal({
  visible,
  onClose,
  value,
  from,
  onChange,
}: DatePickerModalProps) {
  const drawerRef = useRef<{close: () => void}>(null);
  const [selectedValue, setSelectedValue] = useState<Date>(value);

  useEffect(() => {
    setSelectedValue(value);
  }, [value]);

  const handleClose = () => {
    drawerRef.current?.close();
  };

  const handleConfirm = () => {
    onChange(selectedValue, from);
    handleClose();
  };

  const handleChange = (newValue: Date) => {
    setSelectedValue(newValue);
  };

  return (
    <CommonBottomDrawer
      ref={drawerRef}
      visible={visible}
      onClose={onClose}
      height={320}
      showCloseButton={false}>
      <ScrollView
        style={{flex: 1}}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="always"
        keyboardDismissMode="on-drag"
        contentContainerStyle={{paddingTop: 20, alignItems: 'center'}}>
        {from !== 'date' && (
          <TimePicker value={selectedValue} onChange={handleChange} />
        )}
        {from === 'date' && (
          <DatePicker
            date={selectedValue}
            mode="date"
            dividerColor={Tme('cardTextColor')}
            theme={IsDark() ? 'dark' : 'light'}
            onDateChange={handleChange}
          />
        )}
      </ScrollView>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: 20,
          paddingHorizontal: 20,
        }}>
        <TouchableOpacity
          hitSlop={{top: 20, right: 20, bottom: 20, left: 20}}
          style={{
            marginRight: 20,
            backgroundColor: Tme('bgColor'),
            flex: 1,
            paddingVertical: 12,
            alignItems: 'center',
            borderRadius: mainRadius(),
          }}
          onPress={handleClose}>
          <Text style={{color: Tme('cardTextColor')}}>
            {I18n.t('home.cancel')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          hitSlop={{top: 20, right: 20, bottom: 20, left: 20}}
          onPress={handleConfirm}
          style={{
            flex: 1,
            backgroundColor: Colors.MainColor,
            paddingVertical: 12,
            alignItems: 'center',
            borderRadius: mainRadius(),
          }}>
          <Text style={{color: '#fff'}}>{I18n.t('home.confirm')}</Text>
        </TouchableOpacity>
      </View>
    </CommonBottomDrawer>
  );
}

interface SelectDateProps {
  value: Date;
  title: string;
  from: string;
  onChange: (value: Date, from: string) => void;
}

export function SelectDate({value, title, from, onChange}: SelectDateProps) {
  const [dateValue, setDateValue] = useState<Date>(value);
  const [modalVisible, setModalVisible] = useState<boolean>(false);

  useEffect(() => {
    if (value !== dateValue) {
      setDateValue(value);
    }
  }, [value, dateValue]);

  useEffect(() => {
    const token = PubSub.subscribe(
      PubSubEvent.BOTTOM_DRAWER_CLOSE,
      (msg, data: {value: Date; from: string}) => {
        if (data.from === from) {
          handleChange(data.value, data.from);
        }
      },
    );

    return () => {
      PubSub.unsubscribe(token);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleChange = (newValue: Date, fromSource: string) => {
    setDateValue(newValue);
    onChange(newValue, fromSource);
  };

  const handleClick = () => {
    setModalVisible(true);
  };

  return (
    <View>
      <TouchableOpacity
        testID="dateBtn"
        activeOpacity={0.8}
        onPress={handleClick}
        style={{backgroundColor: Tme('cardColor')}}>
        <View
          style={{
            padding: 16,
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}>
          <View
            style={{
              width: 120,
              justifyContent: 'center',
              alignItems: 'flex-start',
            }}>
            <Text
              style={{
                color: Tme('cardTextColor'),
                textAlign: 'center',
                fontSize: 17,
              }}>
              {title}
            </Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'flex-end',
            }}>
            <View style={{flexDirection: 'column', alignItems: 'flex-end'}}>
              <Text
                testID="dateText"
                style={{color: Tme('textColor'), marginLeft: 8, fontSize: 16}}
                numberOfLines={2}>
                {from === 'date'
                  ? moment(dateValue).format('YYYY-MM-DD')
                  : moment(dateValue).format('HH:mm')}
              </Text>
            </View>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={20}
              color={Tme('textColor')}
            />
          </View>
        </View>
      </TouchableOpacity>

      <DatePickerModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        value={dateValue}
        from={from}
        onChange={handleChange}
      />
    </View>
  );
}
