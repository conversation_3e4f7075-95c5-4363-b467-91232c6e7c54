import React from 'react';
import {
  ScrollView,
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
} from 'react-native';
import {
  Fade,
  Loader,
  Placeholder,
  PlaceholderLine,
  PlaceholderMedia,
  Progressive,
  Shine,
  ShineOverlay,
} from 'rn-placeholder';
import AlertModal from './AlertModal';

export default function Test() {
  return (
    <ScrollView>
      <Card>
        <Title>Fade</Title>
        <Placeholder
          Left={PlaceholderMedia}
          Right={PlaceholderMedia}
          Animation={Fade}>
          <PlaceholderLine width={80} />
          <PlaceholderLine />
          <PlaceholderLine width={30} />
        </Placeholder>

        <Code
          code={`<Placeholder
  Left={PlaceholderMedia}
  Right={PlaceholderMedia}
  Animation={Fade}
>
  <PlaceholderLine width={80} />
  <PlaceholderLine />
  <PlaceholderLine width={30} />
</Placeholder>`}
        />
      </Card>

      <Card>
        <Title>Loader</Title>
        <Placeholder
          Left={PlaceholderMedia}
          Right={PlaceholderMedia}
          Animation={Loader}>
          <PlaceholderLine width={80} />
          <PlaceholderLine />
          <PlaceholderLine width={30} />
        </Placeholder>

        <Code
          code={`<Placeholder
  Left={PlaceholderMedia}
  Right={PlaceholderMedia}
  Animation={Loader}
>
  <PlaceholderLine width={80} />
  <PlaceholderLine />
  <PlaceholderLine width={30} />
</Placeholder>`}
        />
      </Card>

      <Card>
        <Title>Shine</Title>
        <Placeholder
          Left={PlaceholderMedia}
          Right={PlaceholderMedia}
          Animation={Shine}>
          <PlaceholderLine width={80} />
          <PlaceholderLine />
          <PlaceholderLine width={30} />
        </Placeholder>

        <Code
          code={`<Placeholder
  Left={PlaceholderMedia}
  Right={PlaceholderMedia}
  Animation={Shine}
>
  <PlaceholderLine width={80} />
  <PlaceholderLine />
  <PlaceholderLine width={30} />
</Placeholder>`}
        />
      </Card>

      <Card>
        <Title>Shine Reverse</Title>
        <Placeholder
          Left={PlaceholderMedia}
          Right={PlaceholderMedia}
          Animation={props => <Shine {...props} reverse={true} />}>
          <PlaceholderLine width={80} />
          <PlaceholderLine />
          <PlaceholderLine width={30} />
        </Placeholder>

        <Code
          code={`<Placeholder
  Left={PlaceholderMedia}
  Right={PlaceholderMedia}
  Animation={(props) => <Shine {...props} reverse={true} />}
>
  <PlaceholderLine width={80} />
  <PlaceholderLine />
  <PlaceholderLine width={30} />
</Placeholder>`}
        />
      </Card>

      <Card>
        <Title>ShineOverlay</Title>
        <Placeholder
          Left={PlaceholderMedia}
          Right={PlaceholderMedia}
          Animation={ShineOverlay}>
          <PlaceholderLine width={80} />
          <PlaceholderLine />
          <PlaceholderLine width={30} />
        </Placeholder>

        <Code
          code={`<Placeholder
  Left={PlaceholderMedia}
  Right={PlaceholderMedia}
  Animation={ShineOverlay}
>
  <PlaceholderLine width={80} />
  <PlaceholderLine />
  <PlaceholderLine width={30} />
</Placeholder>`}
        />
      </Card>

      <Card>
        <Title>ShineOverlay Reverse</Title>
        <Placeholder
          Left={PlaceholderMedia}
          Right={PlaceholderMedia}
          Animation={props => <ShineOverlay {...props} reverse={true} />}>
          <PlaceholderLine width={80} />
          <PlaceholderLine />
          <PlaceholderLine width={30} />
        </Placeholder>

        <Code
          code={`<Placeholder
  Left={PlaceholderMedia}
  Right={PlaceholderMedia}
  Animation={(props) => <ShineOverlay {...props} reverse={true} />}
>
  <PlaceholderLine width={80} />
  <PlaceholderLine />
  <PlaceholderLine width={30} />
</Placeholder>`}
        />
      </Card>

      <Card>
        <Title>Progressive</Title>
        <Placeholder
          Left={PlaceholderMedia}
          Right={PlaceholderMedia}
          Animation={Progressive}>
          <PlaceholderLine width={80} />
          <PlaceholderLine />
          <PlaceholderLine width={30} />
        </Placeholder>

        <Code
          code={`<Placeholder
  Left={PlaceholderMedia}
  Right={PlaceholderMedia}
  Animation={Progressive}
>
  <PlaceholderLine width={80} />
  <PlaceholderLine />
  <PlaceholderLine width={30} />
</Placeholder>`}
        />
      </Card>
    </ScrollView>
  );
}

const Card = ({children, style, dark, ...props}) => (
  <View style={[styles.card, style, dark && styles.dark]} {...props}>
    {children}
  </View>
);

const Code = ({code}) => (
  <View style={styles.container}>
    <Text style={styles.container}>{code}</Text>
    <TouchableOpacity onPress={() => handleClipBoard(code)} style={styles.copy}>
      <Text style={{color: 'white', textAlign: 'center', fontSize: 16}}>
        Copy code
      </Text>
    </TouchableOpacity>
  </View>
);

const handleClipBoard = code => {
  // Clipboard.setString(code);
  AlertModal.alert('Code saved in clipboard 🎉');
};

const Title = ({children, color = 'black'}) => (
  <Text
    style={{
      color,
      fontSize: 20,
      fontWeight: '500',
      marginBottom: 24,
      textAlign: 'center',
    }}>
    {children}
  </Text>
);

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    borderRadius: 3,
    elevation: 3,
    marginBottom: 12,
    padding: 12,
  },
  dark: {
    backgroundColor: '#111111',
  },
  container: {
    backgroundColor: 'white',
    borderRadius: 3,
    marginTop: 24,
  },
  copy: {
    backgroundColor: '#2196F3',
    borderRadius: 30,
    margin: 12,
    padding: 12,
  },
});
