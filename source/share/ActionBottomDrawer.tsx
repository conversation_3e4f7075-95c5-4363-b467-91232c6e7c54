import React, {useRef} from 'react';
import {TouchableOpacity, View, Text, ViewStyle} from 'react-native';
import {Tme, IsDark} from '../ThemeStyle';
import {mainRadius} from '../Tools';
import CommonBottomDrawer from './CommonBottomDrawer';
import I18n from '../I18n';

interface ActionBottomDrawerProps {
  visible?: boolean;
  title: string;
  desp: string;
  confirm?: string;
  cancel?: string;
  action: string;
  uuid: string;
  type?: 'home' | 'screen';
  viewHeight?: number;
  onClose: () => void;
  onConfirm?: (data: {action: string; uuid: string; type: string}) => void;
}

const ActionBottomDrawer: React.FC<ActionBottomDrawerProps> = ({
  visible = true,
  title,
  desp,
  confirm = I18n.t('home.confirm'),
  cancel = I18n.t('home.cancel'),
  action,
  uuid,
  type = 'home',
  viewHeight = 250,
  onClose,
  onConfirm,
}) => {
  const drawerRef = useRef<{close: () => void}>(null);

  const handleConfirm = () => {
    onConfirm?.({action, uuid, type});
    close();
  };

  const close = () => {
    drawerRef.current?.close();
  };

  const buttonStyle: ViewStyle = {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: mainRadius(),
  };

  return (
    <CommonBottomDrawer
      ref={drawerRef}
      visible={visible}
      onClose={onClose}
      height={viewHeight}>
      <View style={{flex: 1}}>
        <Text
          style={{
            color: Tme('cardTextColor'),
            fontSize: 18,
            marginBottom: 20,
            fontWeight: '600',
          }}>
          {title}
        </Text>
        <Text
          style={{
            color: Tme('cardTextColor'),
            fontSize: 14,
          }}>
          {desp}
        </Text>
        <View style={{marginTop: 30}}>
          <TouchableOpacity
            onPress={handleConfirm}
            activeOpacity={0.8}
            style={[
              buttonStyle,
              {
                backgroundColor: Tme('cardColor', IsDark() ? 'L' : 'D'),
              },
            ]}>
            <Text style={{color: Tme('cardTextColor', IsDark() ? 'L' : 'D')}}>
              {confirm}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={close}
            activeOpacity={0.8}
            style={[
              buttonStyle,
              {
                marginTop: 15,
                borderColor: Tme('cardColor', IsDark() ? 'L' : 'D'),
                borderWidth: 1,
                borderStyle: 'solid',
              },
            ]}>
            <Text style={{color: Tme('cardTextColor')}}>{cancel}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </CommonBottomDrawer>
  );
};

export default ActionBottomDrawer;
