import React from 'react';
import {<PERSON>} from 'react-native-shadow-2';
import {IsDark} from '../ThemeStyle';
import {ViewStyle, StyleProp} from 'react-native';

interface ShadowViewProps {
  viewStyle?: StyleProp<ViewStyle>;
  children: React.ReactNode;
}

const ShadowView: React.FC<ShadowViewProps> = ({viewStyle, children}) => {
  return (
    <Shadow
      style={[viewStyle]}
      distance={5}
      offset={[0, 0]}
      startColor={IsDark() ? '#00000007' : '#00000007'}>
      {children}
    </Shadow>
  );
};

export default ShadowView;
