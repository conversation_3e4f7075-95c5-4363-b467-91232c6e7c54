import React from 'react';
import {TouchableOpacity, ViewStyle, StyleProp} from 'react-native';
import {Tme, Colors} from '../ThemeStyle';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import ShadowView from './ShadowView';

const cardRadius = 8;

interface CardViewProps {
  styles?: StyleProp<ViewStyle>;
  onChange?: () => void;
  showMenu?: () => void;
  menuColor?: string;
  children: React.ReactNode;
}

const CardView: React.FC<CardViewProps> = ({
  styles,
  onChange,
  showMenu,
  menuColor,
  children,
}) => {
  // for automation card
  const hasOnTouch = !!onChange;

  return (
    <ShadowView viewStyle={{marginBottom: 10}}>
      <TouchableOpacity
        testID="cardView"
        activeOpacity={hasOnTouch ? 0.8 : 1}
        style={[
          {
            backgroundColor: Tme('cardColor2'),
            borderRadius: cardRadius,
          },
          styles,
        ]}
        onPress={hasOnTouch ? onChange : () => {}}>
        {children}
        {showMenu ? (
          <TouchableOpacity
            testID="cardMenu"
            activeOpacity={0.8}
            hitSlop={{top: 20, right: 20, bottom: 20, left: 20}}
            onPress={showMenu}>
            <MCIcons
              name="dots-horizontal"
              size={30}
              color={menuColor ? menuColor : Colors.MainColor}
            />
          </TouchableOpacity>
        ) : null}
      </TouchableOpacity>
    </ShadowView>
  );
};

export default CardView;
