import React, {Component} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Keyboard,
  StyleSheet,
} from 'react-native';
import {Colors, Tme} from '../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {
  NotificationCenter,
  DISMISS_OVERLAY,
  CLOSE_USER,
} from '../NotificationCenter';
import I18n from '../I18n';
import ScreenSizeContext from '../../WindowResizeContext';
import BgAnimateView from './BgAnimateView';
import UpDownAnimateView from './UpDownAnimateView';

const viewBorderRadius = 8;
const viewHeight = 130;

class InputModal extends Component {
  constructor(props) {
    super(props);

    this.state = {
      modalHeight: 0,
      inputValue: this.props.route.params.inputValue || '',
    };

    this.inputRef = React.createRef();
    this.bgAnimateRef = React.createRef();
    this.upAnimateRef = React.createRef();
    this.focusListener = this.props.navigation.addListener('focus', () => {
      if (this.inputRef.current) {
        setTimeout(() => {
          this.inputRef.current.focus();
        }, 500);
      }
    });
  }

  static contextType = ScreenSizeContext;

  _getInnerViewWidth() {
    return this.context.winWidth - 40;
  }

  componentDidMount() {
    this.upAnimateRef.current.start();
    this.bgAnimateRef.current.start();
    this.keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      this._keyboardDidShow.bind(this),
    );
  }

  componentWillUnmount() {
    this.keyboardDidShowListener.remove();

    if (this.focusListener) {
      this.focusListener();
    }
  }

  _keyboardDidShow(e) {
    var keyH = e.endCoordinates.height;
    var vH = this.context.winHeight / 2 - viewHeight / 2;
    if (vH - keyH < 20) {
      this.setState({
        modalHeight: -20,
      });
    }
  }

  delayInput() {
    return (
      <TextInput
        ref={this.inputRef}
        placeholderTextColor={Tme('placeholder')}
        style={Colors.TextInputStyle()}
        autoCapitalize="none"
        underlineColorAndroid="transparent"
        keyboardType="number-pad"
        returnKeyType="go"
        defaultValue={this.state.inputValue.toString()}
        onChangeText={this.onChange.bind(this)}
      />
    );
  }

  defaultInput() {
    return (
      <TextInput
        ref={this.inputRef}
        autoCapitalize="none"
        underlineColorAndroid="transparent"
        autoCorrect={false}
        defaultValue={this.state.inputValue}
        returnKeyType="go"
        onChangeText={inputValue =>
          this.setState({
            inputValue,
          })
        }
        placeholder={this.props.route.params.placeholder || ''}
        placeholderTextColor={Tme('placeholder')}
        style={Colors.TextInputStyle()}
      />
    );
  }

  mainView() {
    return (
      <>
        <View style={{paddingHorizontal: 20}}>
          <View
            style={{
              padding: 3,
              borderWidth: 1,
              borderRadius: 3,
              marginVertical: 16,
              borderColor: Tme('inputBorderColor'),
            }}>
            {this.props.route.params.from === 'delay' && this.delayInput()}
            {this.props.route.params.from === 'default' && this.defaultInput()}
          </View>
        </View>
        <View style={{flexDirection: 'row'}}>
          <TouchableOpacity
            style={{
              flex: 1,
              borderTopWidth: 1,
              padding: 16,
              borderTopColor: Tme('inputBorderColor'),
              borderRightWidth: 1,
              borderRightColor: Tme('inputBorderColor'),
            }}
            onPress={this.onClose.bind(this)}>
            <Text style={{textAlign: 'center', color: Tme('cardTextColor')}}>
              {I18n.t('home.cancel')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{
              flex: 1,
              padding: 16,
              borderTopWidth: 1,
              borderTopColor: Tme('inputBorderColor'),
            }}
            onPress={this.onSave.bind(this)}>
            <Text style={{textAlign: 'center', color: Tme('cardTextColor')}}>
              {I18n.t('home.confirm')}
            </Text>
          </TouchableOpacity>
        </View>
      </>
    );
  }

  onClose() {
    this.setState({
      inputValue: this.props.route.params.inputValue || '',
    });
    this.props.navigation.goBack();
  }

  onSave() {
    let value = this.state.inputValue;
    if (this.props.route.params.from === 'delay') {
      value = this.state.inputValue == '' ? '0' : this.state.inputValue;
    }

    if (this.props.route.params.from === 'delay') {
      NotificationCenter.dispatchEvent(DISMISS_OVERLAY, value);
    } else {
      NotificationCenter.dispatchEvent(CLOSE_USER, value);
    }

    this.props.navigation.goBack();
  }

  onChange(e) {
    let value = e == '' ? '0' : parseInt(e, 10).toString();
    this.setState({
      inputValue: value,
    });
  }

  render() {
    return (
      <BgAnimateView
        style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        ref={this.bgAnimateRef}>
        <View
          style={{
            position: 'absolute',
            top: 0,
            bottom: 0,
            right: 0,
            left: 0,
          }}>
          <TouchableOpacity
            key="background_close"
            style={{flex: 1}}
            onPress={() => this.close()}
          />
        </View>
        <UpDownAnimateView
          style={{
            width: this._getInnerViewWidth(),
            backgroundColor: Tme('bgColor'),
            borderRadius: viewBorderRadius,
          }}
          ref={this.upAnimateRef}>
          <View
            style={{
              width: this._getInnerViewWidth(),
              height: 44,
              backgroundColor: Tme('cardColor'),
              alignItems: 'center',
              justifyContent: 'center',
              borderTopLeftRadius: viewBorderRadius,
              borderTopRightRadius: viewBorderRadius,
              flexDirection: 'row',
            }}>
            <Text style={{color: Tme('cardTextColor'), fontSize: 16}}>
              {this.props.route.params.title || I18n.t('device.delay')}
            </Text>
            <TouchableOpacity
              key="btn_close"
              hitSlop={{top: 20, right: 20, bottom: 20, left: 20}}
              style={{position: 'absolute', right: 16}}
              activeOpacity={0.8}
              onPress={() => this.close()}>
              <MaterialIcons
                name="close"
                size={22}
                color={Tme('cardTextColor')}
                style={{textAlign: 'right'}}
              />
            </TouchableOpacity>
          </View>
          <View
            key="body_1"
            style={{
              borderBottomLeftRadius: viewBorderRadius,
              borderBottomRightRadius: viewBorderRadius,
              height: viewHeight,
              backgroundColor: Tme('cardColor'),
            }}>
            {this.mainView()}
          </View>
        </UpDownAnimateView>
      </BgAnimateView>
    );
  }

  close() {
    this.bgAnimateRef.current.close();
    this.upAnimateRef.current.close();
    setTimeout(() => {
      this.props.navigation.goBack();
    }, 500);
  }
}

class DelayInputModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      delay: this.props.delay || '0',
      delay_value: this.props.delay || '0',
    };
  }

  componentDidMount() {
    NotificationCenter.addObserver(this, DISMISS_OVERLAY, data => {
      this.onSave(data);
    });
  }

  componentWillUnmount() {
    NotificationCenter.removeObserver(this, DISMISS_OVERLAY);
  }

  onSave(value) {
    this.setState({delay: parseInt(value, 10).toString()}, () => {
      this.props.changeDelay(this.state.delay);
    });
  }

  render() {
    if (this.props.type == 'condition') {
      return (
        <TouchableOpacity
          activeOpacity={0.8}
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: Tme('cardColor'),
          }}
          onPress={this.delay.bind(this)}>
          <Text style={{color: Tme('cardTextColor')}}>
            {I18n.t('device.value')}
          </Text>
          <View style={{flexDirection: 'row'}}>
            <Text style={{color: Tme('cardTextColor')}}>
              {this.props.delay}
            </Text>
            <Text
              style={{
                marginLeft: 8,
                marginRight: 8,
                color: Tme('cardTextColor'),
              }}>
              {this.props.desp || null}
            </Text>
          </View>
        </TouchableOpacity>
      );
    } else {
      return (
        <View style={{marginTop: 16}}>
          <Text
            style={{
              fontWeight: '300',
              fontSize: 14,
              color: Tme('cardTextColor'),
            }}>
            {I18n.t('device.delay')}(s)
          </Text>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.delay.bind(this)}
            style={[
              styles.account_view,
              {borderColor: Tme('inputBorderColor')},
            ]}>
            <Text
              style={{
                lineHeight: 40,
                height: 40,
                marginLeft: 6,
                marginRight: 6,
                color: Tme('placeholder'),
              }}>
              {this.state.delay}
            </Text>
          </TouchableOpacity>
        </View>
      );
    }
  }

  delay() {
    this.props.navigation.push('InputModal', {
      title: this.props.title,
      from: 'delay',
    });
  }
}
const styles = StyleSheet.create({
  account_view: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 3,
    marginTop: 16,
    marginBottom: 20,
  },
});

module.exports = {
  DelayInputModal: DelayInputModal,
  InputModal: InputModal,
};
