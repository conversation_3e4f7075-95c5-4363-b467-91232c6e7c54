import React from 'react';
import { TouchableOpacity, View, ScrollView, Dimensions } from 'react-native';
import { Tme } from '../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import PubSub from 'pubsub-js';
import { Colors as ColorData } from '../ColorData';
import Ionicons from 'react-native-vector-icons/Ionicons';
import _ from 'lodash';
import ScreenSizeContext from '../../WindowResizeContext';
import BgAnimateView from './BgAnimateView';
import BottomUpAnimateView from './BottomUpAnimateView';
import { SELECT_COLOR_DONE } from '../types/PubSubEvent';

const viewBorderRadius = 16;

class SelectColorDrawer extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      r: props.route.params.r ? props.route.params.r : '',
      g: props.route.params.g ? props.route.params.g : '',
      b: props.route.params.b ? props.route.params.b : '',
    };

    this.viewHeight = Dimensions.get('window').height * 0.7;

    this.bgAnimateRef = React.createRef();
    this.bottomUpAnimateRef = React.createRef();
  }

  static contextType = ScreenSizeContext;

  componentDidMount() {
    this.bottomUpAnimateRef.current.start();
    this.bgAnimateRef.current.start();
  }

  componentWillUnmount() { }

  _getInnerViewHeight() {
    return this.context.winHeight * 0.7;
  }

  render() {
    return (
      <View
        style={{
          flex: 1,
        }}>
        <BgAnimateView
          ref={this.bgAnimateRef}
          style={{
            width: this.context.width,
            height: this.context.winHeight,
          }}>
          <TouchableOpacity style={{ flex: 1 }} onPress={this.close.bind(this)} />
        </BgAnimateView>
        <BottomUpAnimateView
          viewHeight={this.viewHeight}
          ref={this.bottomUpAnimateRef}
          style={{
            position: 'absolute',
            width: this.context.width,
            height:
              this.props.route.params.viewHeight || this._getInnerViewHeight(),
            borderTopLeftRadius: viewBorderRadius,
            borderTopRightRadius: viewBorderRadius,
            backgroundColor: Tme('cardColor'),
          }}>
          <ScrollView
            style={{ flex: 1 }}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="always"
            keyboardDismissMode="on-drag"
            contentContainerStyle={{ paddingTop: 50 }}>
            {this.renderContent()}
          </ScrollView>
          <View
            style={{
              height: 30,
              width: this.context.width,
              borderTopLeftRadius: viewBorderRadius,
              borderTopRightRadius: viewBorderRadius,
              position: 'absolute',
              top: 0,
            }}>
            <TouchableOpacity
              hitSlop={{ top: 20, right: 20, bottom: 20, left: 20 }}
              onPress={this.close.bind(this)}>
              <MaterialIcons
                name="close"
                size={26}
                color={Tme('navTextColor')}
                style={{ textAlign: 'right', marginTop: 8, marginRight: 8 }}
              />
            </TouchableOpacity>
          </View>
        </BottomUpAnimateView>
      </View>
    );
  }

  saveColor(color) {
    const rgb = color.split(',');
    this.setState({
      r: rgb[0],
      g: rgb[1],
      b: rgb[2],
    });

    PubSub.publish(SELECT_COLOR_DONE, color);
    if (this.props.route.params.from === 'device') {
      this.close();
    }
  }

  renderContent() {
    var rgb = this.state.r + ',' + this.state.g + ',' + this.state.b;
    var html = [];
    _.forEach(_.chunk(ColorData, 3), (value, key) => {
      var temp = [];
      _.forEach(value, item => {
        temp.push(
          <TouchableOpacity
            key={item.color}
            activeOpacity={0.8}
            onPress={this.saveColor.bind(this, item.color)}
            style={[
              {
                width: 48,
                height: 48,
                borderRadius: 24,
                backgroundColor: 'rgb(' + item.color + ')',
                justifyContent: 'center',
                alignItems: 'center',
              },
              rgb == item.color
                ? {
                  borderWidth: 5,
                  borderColor: Tme('inputBorderColor'),
                }
                : { borderWidth: 1, borderColor: Tme('inputBorderColor') },
            ]}>
            {rgb == item.color ? (
              <Ionicons name="checkmark" size={40} color={Tme('textColor')} />
            ) : null}
          </TouchableOpacity>,
        );
      });
      html.push(
        <View
          key={key}
          style={{
            flex: 1,
            flexDirection: 'row',
            justifyContent: 'space-around',
            alignItems: 'center',
            marginBottom: 16,
          }}>
          {temp}
        </View>,
      );
    });
    return html;
  }

  close() {
    this.bgAnimateRef.current.close();
    this.bottomUpAnimateRef.current.close();
    setTimeout(() => {
      this.props.navigation.goBack();
    }, 500);
  }
}

export default SelectColorDrawer;
