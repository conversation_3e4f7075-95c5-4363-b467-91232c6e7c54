import React, {
  forwardRef,
  useImperativeHandle,
  useState,
  useCallback,
} from 'react';
import {
  Modal,
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import {Tme, IsDark} from '../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
  interpolateColor,
} from 'react-native-reanimated';

const viewBorderRadius = 16;

interface CommonBottomDrawerProps {
  visible: boolean;
  onClose: () => void;
  children: React.ReactNode;
  height?: number;
  showCloseButton?: boolean;
}

interface CommonBottomDrawerRef {
  close: () => void;
}

const CommonBottomDrawer = forwardRef<
  CommonBottomDrawerRef,
  CommonBottomDrawerProps
>(
  (
    {
      visible,
      onClose,
      children,
      height = Dimensions.get('window').height * 0.7,
      showCloseButton = true,
    },
    ref,
  ) => {
    const [isVisible, setIsVisible] = useState(visible);
    const bgOpacity = useSharedValue(0);
    const drawerPosition = useSharedValue(0);
    const isDark = IsDark();

    useImperativeHandle(ref, () => ({
      close: () => {
        closeDrawer();
      },
    }));

    const bgStyle = useAnimatedStyle(() => {
      const backgroundColor = interpolateColor(
        bgOpacity.value,
        [0, 1],
        [
          isDark ? 'rgba(0, 0, 0, 0)' : 'rgba(10, 10, 10, 0)',
          isDark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(10, 10, 10, 0.3)',
        ],
      );
      return {backgroundColor};
    });

    const drawerStyle = useAnimatedStyle(() => {
      return {
        transform: [
          {
            translateY: drawerPosition.value,
          },
        ],
      };
    });

    const openDrawer = useCallback(() => {
      bgOpacity.value = withTiming(1, {
        duration: 200,
        easing: Easing.inOut(Easing.ease),
      });
      drawerPosition.value = withTiming(0, {
        duration: 200,
        easing: Easing.inOut(Easing.ease),
      });
    }, [bgOpacity, drawerPosition]);

    const closeDrawer = useCallback(() => {
      bgOpacity.value = withTiming(0, {
        duration: 200,
        easing: Easing.inOut(Easing.ease),
      });
      drawerPosition.value = withTiming(height, {
        duration: 200,
        easing: Easing.inOut(Easing.ease),
      });
      setTimeout(() => {
        setIsVisible(false);
        onClose();
      }, 200);
    }, [bgOpacity, drawerPosition, height, onClose]);

    React.useEffect(() => {
      if (visible) {
        setIsVisible(true);
        openDrawer();
      } else {
        closeDrawer();
      }
    }, [visible, closeDrawer, openDrawer]);

    if (!isVisible) {
      return null;
    }

    return (
      <Modal
        transparent
        statusBarTranslucent
        visible={isVisible}
        animationType="none"
        onRequestClose={closeDrawer}>
        <Animated.View style={[styles.container, bgStyle]}>
          <TouchableOpacity
            style={styles.background}
            activeOpacity={1}
            onPress={closeDrawer}
          />
          <Animated.View
            style={[
              styles.drawer,
              drawerStyle,
              {
                height,
                backgroundColor: Tme('cardColor'),
              },
            ]}>
            {showCloseButton && (
              <View style={styles.closeButtonContainer}>
                <TouchableOpacity
                  hitSlop={{top: 20, right: 20, bottom: 20, left: 20}}
                  onPress={closeDrawer}>
                  <MaterialIcons
                    name="close"
                    size={26}
                    color={Tme('navTextColor')}
                    style={styles.closeIcon}
                  />
                </TouchableOpacity>
              </View>
            )}
            <View style={styles.content}>{children}</View>
          </Animated.View>
        </Animated.View>
      </Modal>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  background: {
    ...StyleSheet.absoluteFillObject,
  },
  drawer: {
    borderTopLeftRadius: viewBorderRadius,
    borderTopRightRadius: viewBorderRadius,
    overflow: 'hidden',
  },
  closeButtonContainer: {
    height: 30,
    width: '100%',
    position: 'absolute',
    top: 0,
    zIndex: 1,
  },
  closeIcon: {
    textAlign: 'right',
    marginTop: 8,
    marginRight: 8,
  },
  content: {
    flex: 1,
    padding: 20,
  },
});

export default CommonBottomDrawer;
