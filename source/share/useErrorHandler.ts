// useErrorHandler.ts
import { useState, useEffect } from 'react';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';

export function useErrorHandler() {
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('system');

  const showErrorView = (message: string = 'system') => {
    setErrorMessage(message);
    setShowError(true);
  };

  const hideErrorView = () => {
    setShowError(false);
    setErrorMessage('system');
  };

  useEffect(() => {
    // 订阅全局错误事件
    const showErrorToken = PubSub.subscribe(PubSubEvent.SHOW_ERROR, (_, message) => {
      showErrorView(message);
    });

    // 订阅隐藏错误事件
    const hideErrorToken = PubSub.subscribe(PubSubEvent.HIDE_ERROR, () => {
      hideErrorView();
    });

    return () => {
      PubSub.unsubscribe(showErrorToken);
      PubSub.unsubscribe(hideErrorToken);
    };
  }, []);

  return {
    showError,
    errorMessage,
    showErrorView,
    hideErrorView,
  };
}
