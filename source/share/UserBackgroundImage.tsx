import React, { useEffect, useState, useContext, forwardRef } from 'react';
import { ImageBackground, View } from 'react-native';
import PubSub from 'pubsub-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { IsDark } from '../ThemeStyle';
import ScreenSizeContext from '../../WindowResizeContext';
import { CHANGE_BACKGROUND_IMAGE } from '../types/PubSubEvent';

interface BackgroundImage {
  key: string;
  value: any;
}

const imgs: BackgroundImage[] = [
  { key: 'bg_01', value: require('../../img/bg_0.png') },
  { key: 'bg_02', value: require('../../img/bg_2.png') },
  { key: 'bg_03', value: require('../../img/bg_3.png') },
  { key: 'bg_04', value: require('../../img/bg_6.png') },
  { key: 'bg_null', value: null },
];

const UserBackgroundImage = forwardRef<any, {}>((props, ref) => {
  const [uri, setUri] = useState<any>(require('../../img/bg_2.png'));
  const screenSize = useContext(ScreenSizeContext);

  const getImage = async () => {
    try {
      const data = await AsyncStorage.getItem('background_image');
      const temp = data ? JSON.parse(data) : null;

      if (temp?.check) {
        const img = imgs.find(v => v.key === temp.check);
        if (img) {
          setUri(img.value);
        }
      } else {
        await AsyncStorage.setItem(
          'background_image',
          JSON.stringify({ check: 'bg_02' })
        );
      }
    } catch (error) {
      console.error('Error getting background image:', error);
    }
  };

  useEffect(() => {
    const token = PubSub.subscribe(CHANGE_BACKGROUND_IMAGE, () => {
      getImage();
    });

    getImage();

    return () => {
      PubSub.unsubscribe(token);
    };
  }, []);

  if (!uri) {
    return null;
  }

  return (
    <ImageBackground
      ref={ref}
      blurRadius={80}
      style={{
        position: 'absolute',
        width: screenSize.width,
        height: screenSize.winHeight,
      }}
      source={uri}
    >
      <View
        style={{
          backgroundColor: `rgba(0,0,0,${IsDark() ? '0.5' : '0'})`,
          position: 'absolute',
          width: screenSize.width,
          height: screenSize.winHeight,
        }}
      />
    </ImageBackground>
  );
});

export default UserBackgroundImage;
