import React, {Component} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Keyboard,
} from 'react-native';
import {Helper} from '../Helper';
import {Tme} from '../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Markdown from 'react-native-markdown-display';
import _ from 'lodash';
import AppConfig from '../../app_config';
import ScreenSizeContext from '../../WindowResizeContext';
import I18n from '../I18n';
import BgAnimateView from './BgAnimateView';
import UpDownAnimateView from './UpDownAnimateView';

const viewBorderRadius = 16;

class HintView extends Component {
  constructor(props) {
    super(props);

    this.state = {
      markdownData: '',
      isLoading: false,
    };
    this.upAnimateRef = React.createRef();
    this.bgAnimateRef = React.createRef();
  }

  static contextType = ScreenSizeContext;

  _getInnerViewWidth() {
    return this.context.winWidth - 40;
  }

  _getInnerViewHeight() {
    return this.context.winHeight * 0.66;
  }

  componentDidMount() {
    Keyboard.dismiss();
    this.upAnimateRef.current.start();
    this.bgAnimateRef.current.start();
    if (this.props.route.params.urlTitle) {
      this.doFetchData();
    }
  }

  render() {
    const html = [];
    if (this.props.route.params.type === 'user_role') {
      html.push(
        <View style={{padding: 16}} key="user_role">
          <Text style={{color: Tme('cardTextColor')}}>
            {I18n.t('global.user_role_desp')}
          </Text>
        </View>,
      );
    }

    return (
      <BgAnimateView
        ref={this.bgAnimateRef}
        style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <View
          style={{
            position: 'absolute',
            top: 0,
            bottom: 0,
            right: 0,
            left: 0,
          }}>
          <TouchableOpacity style={{flex: 1}} onPress={() => this.close()} />
        </View>
        <UpDownAnimateView
          ref={this.upAnimateRef}
          style={{
            width: this._getInnerViewWidth(),
            height: this._getInnerViewHeight(),
            backgroundColor: Tme('cardColor'),
            borderRadius: viewBorderRadius,
          }}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="always"
            keyboardDismissMode="on-drag"
            contentContainerStyle={{
              paddingHorizontal: 20,
              paddingBottom: 20,
              paddingTop: 30,
            }}>
            {this.state.isLoading ? (
              <View style={{marginTop: 20}}>
                <ActivityIndicator size="large" color={Tme('cardTextColor')} />
              </View>
            ) : null}
            {_.isEmpty(this.state.markdownData) ? (
              <View>{html}</View>
            ) : (
              this._renderMarkdown()
            )}
          </ScrollView>
          <View
            colors={[Tme('cardColor'), Tme('gradientEndColor')]}
            style={{
              height: 40,
              width: this._getInnerViewWidth(),
              borderTopLeftRadius: viewBorderRadius,
              borderTopRightRadius: viewBorderRadius,
              position: 'absolute',
              top: 0,
            }}>
            <TouchableOpacity
              hitSlop={{top: 20, right: 20, bottom: 20, left: 20}}
              onPress={() => this.close()}>
              <MaterialIcons
                name="close"
                size={26}
                color={Tme('navTextColor')}
                style={{textAlign: 'right', marginTop: 8, marginRight: 8}}
              />
            </TouchableOpacity>
          </View>
          <View
            colors={[Tme('gradientEndColor'), Tme('cardColor')]}
            style={{
              height: 40,
              width: this._getInnerViewWidth(),
              borderBottomLeftRadius: viewBorderRadius,
              borderBottomRightRadius: viewBorderRadius,
              position: 'absolute',
              bottom: -1,
            }}
          />
        </UpDownAnimateView>
      </BgAnimateView>
    );
  }

  _renderMarkdown() {
    const rules = {
      // eslint-disable-next-line react/no-unstable-nested-components
      link: (node, children, parent, styles) => {
        return (
          <Text
            key={node.key}
            style={{textDecorationLine: 'none', fontWeight: 'normal'}}
            onPress={() => {}}>
            {children}
          </Text>
        );
      },
    };

    return (
      <Markdown
        rules={rules}
        style={{
          body: {
            color: Tme('cardTextColor'),
          },
          bullet_list_content: {
            padding: 0,
            margin: 0,
          },
          bullet_list_icon: {
            padding: 0,
            margin: 0,
          },
          ordered_list_content: {
            padding: 0,
            margin: 0,
          },
          ordered_list_icon: {
            padding: 0,
            margin: 0,
          },
        }}>
        {this.state.markdownData}
      </Markdown>
    );
  }

  close() {
    // close with animation
    this.upAnimateRef.current.close();
    this.bgAnimateRef.current.close();
    setTimeout(() => {
      this.props.navigation.goBack();
    }, 500);
  }

  doFetchData() {
    var that = this;
    this.setState(
      {
        isLoading: true,
      },
      () => {
        var url = '/helps/';
        if (that.props.route.params.type == 'about') {
          url = '/abouts/';
        }
        let options = {};
        if (this.props.route.params.from == 'signup') {
          options = {
            cloud: true,
            form: 'session',
            apiUrl:
              this.props.route.params.country == 'cn'
                ? `${AppConfig.cn_api}/iot`
                : `${AppConfig.api}/iot`,
          };
        }
        Helper.httpGET(
          Helper.urlWithQuery(url + this.props.route.params.urlTitle),
          {
            ensure: () => {
              that.setState({
                isLoading: false,
              });
            },
            ...options,
            success: data => {
              that.setState({
                isLoading: false,
                markdownData: data.message,
              });
            },
          },
        );
      },
    );
  }
}

const ShowHint = content => {
  this.props.navigation.push('HintView', {
    content: content,
  });
};

module.exports = {
  ShowHint: ShowHint,
  HintView: HintView,
};
