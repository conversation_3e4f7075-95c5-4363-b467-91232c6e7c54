import React, { forwardRef, useImperativeHandle } from 'react';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
  interpolateColor,
  ReduceMotion,
} from 'react-native-reanimated';
import { ViewStyle } from 'react-native';
import { IsDark } from '../ThemeStyle';

interface BgAnimateViewProps {
  style?: ViewStyle;
  children?: React.ReactNode;
}

export interface BgAnimateViewRef {
  start: () => void;
  close?: () => void;
}

const BgAnimateView = forwardRef<BgAnimateViewRef, BgAnimateViewProps>((props, ref) => {
  useImperativeHandle(ref, () => ({
    start,
    close,
  }));

  const bgColor = useSharedValue(0);

  const dColorStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      bgColor.value,
      [0, 1],
      ['rgba(10, 10, 10, 0)', 'rgba(10, 10, 10, 0.8)'],
    );

    return { backgroundColor };
  });

  const LColorStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      bgColor.value,
      [0, 1],
      ['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0.5)'],
    );

    return { backgroundColor };
  });

  const start = () => {
    bgColor.value = withTiming(1, {
      duration: 200,
      easing: Easing.elastic(2),
      reduceMotion: ReduceMotion.System,
    });
  };

  const close = () => {
    bgColor.value = withTiming(0, {
      duration: 200,
      easing: Easing.inOut(Easing.ease),
    });
  };

  return (
    <Animated.View style={[IsDark() ? dColorStyle : LColorStyle, props.style]}>
      {props.children}
    </Animated.View>
  );
});

export default BgAnimateView;
