import React, { forwardRef, useImperativeHandle } from 'react';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
  interpolate,
} from 'react-native-reanimated';
import { Dimensions, ViewStyle } from 'react-native';

interface BottomUpAnimateViewProps {
  viewHeight?: number;
  style?: ViewStyle;
  children?: React.ReactNode;
}

interface BottomUpAnimateViewRef {
  start: () => void;
  close: () => void;
}

const BottomUpAnimateView = forwardRef<BottomUpAnimateViewRef, BottomUpAnimateViewProps>((props, ref) => {
  useImperativeHandle(ref, () => ({
    start,
    close,
  }));

  const poupMarginTop = useSharedValue(0);
  const viewHeight = props.viewHeight
    ? props.viewHeight
    : Dimensions.get('window').height * 0.7;

  const animStyle = useAnimatedStyle(() => {
    return {
      height: viewHeight,
      bottom: -viewHeight,
      transform: [
        {
          translateY: interpolate(
            poupMarginTop.value,
            [0, 1],
            [0, -viewHeight],
          ),
        },
      ],
    };
  });

  const start = () => {
    poupMarginTop.value = withTiming(1, {
      duration: 200,
      easing: Easing.inOut(Easing.ease),
    });
  };

  const close = () => {
    poupMarginTop.value = withTiming(0, {
      duration: 100,
      easing: Easing.inOut(Easing.ease),
    });
  };

  return (
    <Animated.View style={[props.style, animStyle]}>
      {props.children}
    </Animated.View>
  );
});

export default BottomUpAnimateView;
