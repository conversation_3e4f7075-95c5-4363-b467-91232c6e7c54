import React, {useCallback} from 'react';
import {Colors} from '../ThemeStyle';
import {
  HeaderButton,
  HeaderButtons,
  Item,
} from 'react-navigation-header-buttons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {View} from 'react-native';

type IconType = {
  icon: 'Ionicons' | 'MaterialIcons' | 'MCIcons';
  name: string;
};

type HeaderRightBtnProps = {
  rightClick: () => void;
  text?: string;
  icon?: IconType;
};

const useIconHeaderButton = (icon?: IconType) => {
  return useCallback(
    (props: any) => {
      let IconComponent = Ionicons;
      if (icon) {
        switch (icon.icon) {
          case 'Ionicons':
            IconComponent = Ionicons;
            break;
          case 'MaterialIcons':
            IconComponent = MaterialIcons;
            break;
          case 'MCIcons':
            IconComponent = MCIcons;
            break;
        }
      }

      return (
        <HeaderButton
          IconComponent={IconComponent}
          iconSize={23}
          {...props}
          color={Colors.MainColor}
        />
      );
    },
    [icon],
  );
};

const HeaderRightBtn: React.FC<HeaderRightBtnProps> = ({
  rightClick,
  text,
  icon,
}) => {
  const IconHeaderButton = useIconHeaderButton(icon);

  return (
    <View style={{marginRight: -10}}>
      <HeaderButtons HeaderButtonComponent={IconHeaderButton}>
        {icon && <Item iconName={icon.name} onPress={rightClick} title="" />}
        {text && (
          <Item
            title={text}
            onPress={rightClick}
            buttonStyle={{
              textTransform: 'none',
            }}
          />
        )}
      </HeaderButtons>
    </View>
  );
};

export default HeaderRightBtn;
