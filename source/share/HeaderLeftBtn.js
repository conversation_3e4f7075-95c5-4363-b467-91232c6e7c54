/* eslint-disable react/react-in-jsx-scope */
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {Colors} from '../ThemeStyle';
import {TouchableOpacity, View} from 'react-native';

export default function HeaderLeftBtn({navigation}) {
  return (
    <TouchableOpacity
      hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
      activeOpacity={0.8}
      style={{
        marginRight: 22,
        marginVertical: 12,
      }}
      onPress={navigation.goBack}>
      <MaterialIcons name="arrow-back-ios" size={23} color={Colors.MainColor} />
    </TouchableOpacity>
  );
}

export function HeaderLeftModalBtn({navigation}) {
  return (
    <View
      style={{flexDirection: 'row', marginVertical: 12, alignItems: 'center'}}>
      <TouchableOpacity
        hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
        activeOpacity={0.8}
        style={{
          marginRight: 22,
        }}
        onPress={navigation.goBack}>
        <MaterialIcons
          name="arrow-back-ios"
          size={23}
          color={Colors.MainColor}
        />
      </TouchableOpacity>
      <TouchableOpacity
        hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
        activeOpacity={0.8}
        style={{
          marginRight: 22,
        }}
        onPress={() => navigation.popToTop()}>
        <MaterialIcons name="close" size={23} color={Colors.MainColor} />
      </TouchableOpacity>
    </View>
  );
}
