import React from 'react';
import { View, Dimensions } from 'react-native';
import {
  Placeholder,
  PlaceholderMedia,
  PlaceholderLine,
  Progressive,
} from 'rn-placeholder';
import { Tme } from '../ThemeStyle';
import { mainRadius } from '../Tools';
const width = Dimensions.get('window').width - 40;
const cardWidth = width / 2 - 10;

export function SkeletionAuto() {
  return (
    <View style={[{ flex: 1, backgroundColor: 'transparent', padding: 20 }]}>
      <View style={{ marginBottom: 20 }}>
        <Placeholder Animation={Progressive}>
          <PlaceholderMedia
            style={{ width: width, height: 80, borderRadius: mainRadius() }}
            color={Tme('cardColor')}
          />
        </Placeholder>
      </View>
      <View style={{ marginBottom: 1 }}>
        <Placeholder Animation={Progressive}>
          <PlaceholderMedia
            style={{
              width: width,
              height: 140,
              borderRadius: mainRadius(),
            }}
            color={Tme('cardColor')}
          />
        </Placeholder>
      </View>
      <View style={{ marginBottom: 20 }}>
        <Placeholder Animation={Progressive}>
          <PlaceholderMedia
            style={{
              width: width,
              height: 140,
              borderRadius: mainRadius(),
            }}
            color={Tme('cardColor')}
          />
        </Placeholder>
      </View>
    </View>
  );
}

export function SkeletionDevice() {
  return (
    <View style={[{ flex: 1, backgroundColor: 'transparent', padding: 20 }]}>
      <View style={{ marginBottom: 20 }}>
        <Placeholder Animation={Progressive}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <PlaceholderMedia
              style={{
                width: cardWidth,
                height: 78,
                borderRadius: mainRadius(),
              }}
              color={Tme('cardColor')}
            />
            <PlaceholderMedia
              style={{
                width: cardWidth,
                height: 78,
                borderRadius: mainRadius(),
              }}
              color={Tme('cardColor')}
            />
          </View>
        </Placeholder>
      </View>
      <View style={{ marginBottom: 20 }}>
        <Placeholder Animation={Progressive}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <PlaceholderMedia
              style={{
                width: cardWidth,
                height: 78,
                borderRadius: mainRadius(),
              }}
              color={Tme('cardColor')}
            />
            <PlaceholderMedia
              style={{
                width: cardWidth,
                height: 78,
                borderRadius: mainRadius(),
              }}
              color={Tme('cardColor')}
            />
          </View>
        </Placeholder>
      </View>
      <View style={{ marginBottom: 20 }}>
        <Placeholder Animation={Progressive}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <PlaceholderMedia
              style={{
                width: cardWidth,
                height: 78,
                borderRadius: mainRadius(),
              }}
              color={Tme('cardColor')}
            />
            <PlaceholderMedia
              style={{
                width: cardWidth,
                height: 78,
                borderRadius: mainRadius(),
              }}
              color={Tme('cardColor')}
            />
          </View>
        </Placeholder>
      </View>
    </View>
  );
}

export default function Skeletion() {
  return (
    <View style={[{ flex: 1, backgroundColor: 'transparent', padding: 20 }]}>
      <Placeholder Animation={Progressive}>
        <PlaceholderLine
          width={60}
          style={{
            backgroundColor: Tme('cardColor'),
            borderRadius: mainRadius(),
          }}
        />
      </Placeholder>
      <View style={{ marginTop: 20, marginBottom: 20 }}>
        <Placeholder Animation={Progressive}>
          <PlaceholderMedia
            style={{ width: width, height: 50, borderRadius: mainRadius() }}
            color={Tme('cardColor')}
          />
        </Placeholder>
      </View>
      <View style={{ marginBottom: 20 }}>
        <Placeholder Animation={Progressive}>
          <PlaceholderLine
            width={40}
            style={{
              backgroundColor: Tme('cardColor'),
              borderRadius: mainRadius(),
            }}
          />
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <PlaceholderMedia
              style={{
                width: cardWidth,
                height: 60,
                borderRadius: mainRadius(),
              }}
              color={Tme('cardColor')}
            />
            <PlaceholderMedia
              style={{
                width: cardWidth,
                height: 60,
                borderRadius: mainRadius(),
              }}
              color={Tme('cardColor')}
            />
          </View>
        </Placeholder>
      </View>
      <View style={{ marginBottom: 20 }}>
        <Placeholder Animation={Progressive}>
          <PlaceholderLine
            width={40}
            style={{
              backgroundColor: Tme('cardColor'),
              borderRadius: mainRadius(),
            }}
          />
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <PlaceholderMedia
              style={{
                width: cardWidth,
                height: 60,
                borderRadius: mainRadius(),
              }}
              color={Tme('cardColor')}
            />
            <PlaceholderMedia
              style={{
                width: cardWidth,
                height: 60,
                borderRadius: mainRadius(),
              }}
              color={Tme('cardColor')}
            />
          </View>
        </Placeholder>
      </View>
      <View style={{ marginBottom: 20 }}>
        <Placeholder Animation={Progressive}>
          <PlaceholderLine
            width={40}
            style={{
              backgroundColor: Tme('cardColor'),
              borderRadius: mainRadius(),
            }}
          />
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <PlaceholderMedia
              style={{
                width: cardWidth,
                height: 100,
                borderRadius: mainRadius(),
              }}
              color={Tme('cardColor')}
            />
            <PlaceholderMedia
              style={{
                width: cardWidth,
                height: 100,
                borderRadius: mainRadius(),
              }}
              color={Tme('cardColor')}
            />
          </View>
        </Placeholder>
      </View>
    </View>
  );
}
