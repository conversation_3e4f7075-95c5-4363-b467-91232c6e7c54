import React, { Component } from 'react';
import {
  View,
  ActivityIndicator,
  Keyboard,
  Text,
  Dimensions,
} from 'react-native';
import { Tme } from '../ThemeStyle';
import I18n from '../I18n';
import { Toast } from '../Toast';
import { Helper } from '../Helper';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';

const viewBorderRadius = 16;

class LinkOpenAlert extends Component {
  constructor(props) {
    super(props);

    this.state = {
      isLoading: false,
      name: this.props.route.params.name,
      type: this.props.route.params.type,
      winWidth: Dimensions.get('window').width,
      winHeight: Dimensions.get('window').height,
    };
  }

  _getInnerViewWidth() {
    return this.state.winWidth - 40;
  }

  _getInnerViewHeight() {
    return this.state.winHeight * 0.66;
  }

  componentDidMount() {
    Keyboard.dismiss();
    setTimeout(() => {
      if (this.props.route.params.type === 'action') {
        this.runAction({
          uuid: this.props.route.params.uuid,
        });
      }
      if (this.props.route.params.type === 'scene') {
        this.runScene({
          uuid: this.props.route.params.uuid,
        });
      }
    }, 1000);
  }

  runAction(data) {
    Helper.httpPOST(
      '/partner/actions/run',
      {
        ensure: () => {
          this.close();
        },
        success: () => {
          setTimeout(() => {
            Toast.show();
          }, 500);
        },
      },
      { uuid: data.uuid },
    );
  }

  runScene(data) {
    Helper.httpPOST(
      '/partner/scenes/run',
      {
        ensure: () => {
          PubSub.publish(PubSubEvent.EVENT_SCENE);
          PubSub.publish(PubSubEvent.SELECT_WIDGET_DATA);
          this.close();
        },
        success: () => {
          setTimeout(() => {
            Toast.show();
          }, 500);
        },
      },
      { uuid: data.uuid, type: 'scene' },
    );
  }

  handleSuccess() {
    Toast.show();
  }

  render() {
    return (
      <View
        style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <View
          style={[
            {
              width: 200,
              height: 120,
              backgroundColor: 'rgba(0,0,0,0.8)',
              borderRadius: viewBorderRadius,
            },
          ]}>
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <ActivityIndicator size="large" />
            <View style={{ marginTop: 20 }}>
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: 'bold',
                  color: '#fff',
                }}>
                {`${this.state.type === 'action'
                    ? I18n.t('action.execute')
                    : I18n.t('scene.activate')
                  } ${this.state.name}`}
              </Text>
            </View>
          </View>
          <View
            colors={[Tme('gradientEndColor'), Tme('cardColor')]}
            style={{
              height: 40,
              width: this._getInnerViewWidth(),
              borderBottomLeftRadius: viewBorderRadius,
              borderBottomRightRadius: viewBorderRadius,
              position: 'absolute',
              bottom: -1,
            }}
          />
        </View>
      </View>
    );
  }

  close() {
    this.props.navigation.goBack();
  }
}

module.exports = {
  LinkOpenAlert: LinkOpenAlert,
};
