import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {Tme} from '../ThemeStyle';

// 定义组件 Props 类型接口
interface CheckBoxProps {
  isLast?: boolean;
  value: string;
  index: number;
  isChecked: boolean;
  onClick: (index: number) => void;
}

// 使用函数式组件和 TypeScript
const CheckBox: React.FC<CheckBoxProps> = ({isLast, value, index, isChecked, onClick}) => {
  // 处理点击事件
  const handleClick = () => {
    onClick(index);
  };

  return (
    <TouchableOpacity
      testID="checkbox"
      activeOpacity={0.8}
      onPress={handleClick}
      style={[
        {
          height: 59,
        },
        isLast
          ? {}
          : {
              borderBottomWidth: 1,
              borderBottomColor: Tme('inputBorderColor'),
            },
      ]}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
        <Text
          style={{
            lineHeight: 59,
            fontSize: 17,
            color: Tme('cardTextColor'),
          }}>
          {value}
        </Text>
        {isChecked ? (
          <MaterialIcons
            name="check"
            size={24}
            color={Tme('cardTextColor')}
          />
        ) : null}
      </View>
    </TouchableOpacity>
  );
};

export default CheckBox;
