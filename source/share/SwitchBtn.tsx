import React, {useState, useEffect} from 'react';
import {Switch, ViewStyle, StyleProp} from 'react-native';
import {Colors} from '../ThemeStyle';

interface SwitchBtnProps {
  value: boolean;
  change: (value: boolean) => void;
  disabled?: boolean;
  trackColor?: {
    false?: string;
    true?: string;
  };
  cusStyle?: StyleProp<ViewStyle>;
}

const SwitchBtn: React.FC<SwitchBtnProps> = props => {
  const [value, setValue] = useState < boolean >(props.value);

  const change = (e: boolean): void => {
    setValue(e);
    props.change(e);
  };

  useEffect(() => {
    setValue(props.value);
  }, [props.value]);

  return (
    <Switch
      thumbColor="#fff"
      disabled={props.disabled || false}
      trackColor={
        props.trackColor ? props.trackColor : {true: Colors.MainColor}
      }
      style={[
        {transform: [{scaleX: 0.8}, {scaleY: 0.8}]},
        props.cusStyle || {},
      ]}
      value={value}
      onValueChange={change}
    />
  );
};

export default SwitchBtn;
