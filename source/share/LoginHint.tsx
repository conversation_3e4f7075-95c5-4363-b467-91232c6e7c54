import React, {useEffect, useRef} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Keyboard,
  Dimensions,
  BackHandler,
  Platform,
  Modal,
  ScaledSize,
} from 'react-native';
import {Tme, Colors} from '../ThemeStyle';
import AsyncStorage from '@react-native-async-storage/async-storage';
import I18n from '../I18n';
import UpDownAnimateView, { UpDownAnimateViewRef } from './UpDownAnimateView';
import BgAnimateView, { BgAnimateViewRef } from './BgAnimateView';

const viewBorderRadius = 16;

interface LoginHintProps {
  visible: boolean;
  onClose: () => void;
  navigation: any; // 或根据您的导航类型定义更具体的类型
  onShowHint: () => void;
}

const LoginHint: React.FC<LoginHintProps> = ({
  visible,
  onClose,
  navigation,
  onShowHint,
}) => {
  const [winWidth, setWinWidth] = React.useState(
    Dimensions.get('window').width,
  );
  const [winHeight, setWinHeight] = React.useState(
    Dimensions.get('window').height,
  );

  const upAnimateRef = useRef<UpDownAnimateViewRef>(null);
  const bgAnimateRef = useRef<BgAnimateViewRef>(null);

  const _onResize = ({window}: {window: ScaledSize}) => {
    setWinWidth(window.width);
    setWinHeight(window.height);
  };

  const _getInnerViewWidth = () => {
    return winWidth - 40;
  };

  const _getInnerViewHeight = () => {
    return winHeight * 0.66;
  };

  useEffect(() => {
    if (Platform.OS == 'android') {
      const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        () => {
          // 阻止返回键关闭 modal
          return true;
        },
      );
      return () => backHandler.remove();
    }
  }, []);

  useEffect(() => {
    const dimensionsEvent = Dimensions.addEventListener('change', _onResize);
    Keyboard.dismiss();

    if (visible) {
      // 先启动背景动画，然后启动内容动画
      setTimeout(() => {
        if (bgAnimateRef.current) {
          bgAnimateRef.current.start();
        }
        setTimeout(() => {
          if (upAnimateRef.current) {
            upAnimateRef.current.start();
          }
        }, 100);
      }, 0);
    }

    return () => {
      dimensionsEvent.remove();
    };
  }, [visible]);

  const submit = () => {
    AsyncStorage.setItem('privacy', '1');
    onClose();
  };

  const clickUrl = (type: string) => {
    let url = '';
    let title = '';
    if (type == 'service') {
      title = I18n.t('global.terms_of_service');
      url = 'https://www.presensmarthome.com/about/terms-of-service?lang=zh_cn';
    } else {
      title = I18n.t('global.privacy_statement');
      url =
        'https://www.presensmarthome.com/about/privacy-statement?lang=zh_cn';
    }

    onClose(); // 先关闭 modal
    navigation.push('LoginPrivacy', {
      url: url,
      title: title,
      onBack: () => {
        // 返回时重新显示 modal
        onShowHint();
      },
    });
  };

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={() => {}} // 阻止返回键关闭 modal
      statusBarTranslucent={true}>
      <View style={{flex: 1, backgroundColor: 'rgba(0, 0, 0, 0.5)'}}>
        <BgAnimateView
          style={{
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
          }}
          ref={bgAnimateRef}>
          <View
            style={{
              position: 'absolute',
              top: 0,
              bottom: 0,
              right: 0,
              left: 0,
            }}
          />
          <UpDownAnimateView
            style={{
              width: _getInnerViewWidth(),
              height: _getInnerViewHeight(),
              backgroundColor: Tme('cardColor'),
              borderRadius: viewBorderRadius,
            }}
            ref={upAnimateRef}>
            <ScrollView
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="always"
              keyboardDismissMode="on-drag"
              contentContainerStyle={{
                paddingHorizontal: 20,
                paddingBottom: 20,
                paddingTop: 30,
              }}>
              <View style={{padding: 16}}>
                <Text
                  style={{
                    color: Tme('cardTextColor'),
                    textAlign: 'center',
                    marginBottom: 20,
                    fontSize: 18,
                    fontWeight: 'bold',
                  }}>
                  服务条款和用户隐私协议
                </Text>
                <Text style={{color: Tme('cardTextColor'), marginBottom: 8}}>
                  欢迎使用Presen App!
                </Text>
                <Text style={{color: Tme('cardTextColor'), lineHeight: 20}}>
                  1.为了为您提供用户注册、家庭管理、智能设备管理等服务，我们会根据您所使用的服务和具体功能，来收集必要的用户信息；
                </Text>
                <Text
                  style={{
                    color: Tme('cardTextColor'),
                    marginTop: 4,
                    lineHeight: 20,
                  }}>
                  2.未经您的授权，我 们不会向第三方共享或对外提供您的信息；
                </Text>
                <Text
                  style={{
                    color: Tme('cardTextColor'),
                    marginTop: 4,
                    lineHeight: 20,
                  }}>
                  3.您可以随时修改和删除您的个人信息，包括停用账号。
                </Text>
                <View
                  style={{
                    marginTop: 4,
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                  }}>
                  <Text style={{color: Tme('cardTextColor'), lineHeight: 20}}>
                    请您阅读完整版的
                  </Text>
                  <TouchableOpacity onPress={() => clickUrl('service')}>
                    <Text
                      style={{
                        color: Tme('cardTextColor'),
                        textDecorationLine: 'underline',
                        lineHeight: 20,
                      }}>
                      《服务条款》
                    </Text>
                  </TouchableOpacity>
                  <Text style={{color: Tme('cardTextColor'), lineHeight: 20}}>
                    和
                  </Text>
                  <TouchableOpacity onPress={() => clickUrl('privacy')}>
                    <Text
                      style={{
                        color: Tme('cardTextColor'),
                        textDecorationLine: 'underline',
                        lineHeight: 20,
                      }}>
                      《隐私声明》
                    </Text>
                  </TouchableOpacity>
                  <Text
                    style={{
                      color: Tme('cardTextColor'),
                      marginTop: 2,
                      lineHeight: 20,
                    }}>
                    ，点击同意即表示您已阅读并同意全部条款。
                  </Text>
                </View>

                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={submit}
                  style={{
                    backgroundColor: Colors.MainColor,
                    paddingVertical: 18,
                    borderRadius: 8,
                    alignItems: 'center',
                    marginTop: 40,
                  }}>
                  <Text style={{color: 'white'}}>同意</Text>
                </TouchableOpacity>
                <View style={{height: 20}} />
                <TouchableOpacity style={{alignItems: 'center'}}>
                  <Text style={{color: Tme('textColor')}}>不同意</Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </UpDownAnimateView>
        </BgAnimateView>
      </View>
    </Modal>
  );
};

export default LoginHint;
