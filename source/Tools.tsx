/* eslint-disable no-useless-escape */

import _ from 'lodash';
import React, {ReactElement} from 'react';
import {View} from 'react-native';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {DeviceIcons} from './ThemeStyle';
import I18n from './I18n';
import tinycolor from 'tinycolor2';
import Images from './share/Images';
import { CCSpec, Device } from './types/home';
import { AutomationItem } from './types/smart';

export interface DeviceIcon {
  reg: RegExp;
  icon: string;
}

export interface QrcodeResult {
  [key: string]: string;
}

/**
 * 获取场景名称
 * @param name 场景名称
 * @returns 本地化的场景名称
 */
export function getSceneName(name: string | null | undefined): string {
  if (!name) {
    return '';
  }

  const value = name.toLowerCase();
  if (
    value === 'home' ||
    value === 'sleep' ||
    value === 'away' ||
    value === 'vacation'
  ) {
    return I18n.t('global.' + value);
  } else {
    return name;
  }
}

/**
 * 格式化规格值
 * @param spec_value 规格值
 * @returns 格式化后的规格值
 */
export function showSpecValue(spec_value: any): any {
  let val;
  switch (typeof spec_value) {
    case 'number':
      val = spec_value;
      break;
    case 'undefined':
      val = 'N/A';
      break;
    case 'object': // null
      val = '';
      break;
    default:
      val = spec_value;
  }
  return val;
}

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式字符串
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date, format: string): string {
  const o: {[key: string]: number} = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
    'q+': Math.floor((date.getMonth() + 3) / 3),
    S: date.getMilliseconds(),
  };
  if (/(y+)/.test(format)) {
    format = format.replace(
      RegExp.$1,
      (date.getFullYear() + '').substr(4 - RegExp.$1.length),
    );
  }
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1
          ? String(o[k]) // 将数字显式转换为字符串
          : ('00' + o[k]).substr(('' + o[k]).length),
      );
    }
  }
  return format;
}

/**
 * 获取颜色开关命令参数
 * @param spec_hash 规格哈希
 * @param rgb RGB颜色值
 * @param type 设备类型
 * @returns 命令参数
 */
export function colorSwitchCmdParams(
  spec_hash: any,
  rgb: number[],
  type: string,
): any {
  if (type === 'tuya') {
    const hsv = tinycolor({r: rgb[0], g: rgb[1], b: rgb[2]}).toHsv();
    return {
      h: hsv.h,
      s: hsv.s * 1000,
      v: hsv.v * 1000,
    };
  } else {
    return tinycolor({r: rgb[0], g: rgb[1], b: rgb[2]}).toHsv();
  }
}

/**
 * 从规格哈希中获取RGB颜色值
 * @param spec_hash 规格哈希
 * @returns RGB颜色值数组或false
 */
export function colorSwitchGetRgb(spec_hash: {
  [key: string]: any;
}): number[] | false {
  const res: any[] = [];
  _.each(spec_hash, function (v, k) {
    switch (k.toString()) {
      case '2':
        res.push(v);
        break;
      case '3':
        res.push(v);
        break;
      case '4':
        res.push(v);
        break;
      default:
        break;
    }
  });
  if (res.length === 3) {
    return res;
  } else {
    return false;
  }
}

/**
 * 获取设备类型名称
 * @param dv_type 设备类型代码
 * @returns 设备类型名称
 */
export function getDeviceType(dv_type: string): string {
  switch (dv_type) {
    case '433':
      return '433M';
    case 'camera':
      return 'Camera';
    case 'zwave':
      return 'Z-WAVE';
    case 'zigbee':
      return 'Zigbee';
    case 'tuya':
      return 'Tuya';
    case 'matter_wifi':
    case 'matter_thread':
      return 'Matter';
    default:
      return dv_type;
  }
}

/**
 * 获取设备的开关规格
 * @param device 设备对象
 * @returns 规格对象或null
 */
export function getOneSwitchSpec(device: Device): CCSpec | null {
  const temp: CCSpec[] = [];
  _.forEach(device.cc_specs, (v: CCSpec) => {
    if (v.name === 'Switch' || v.name === 'Dimmer' || v.name === 'Motor') {
      temp.push(v);
    }
  });

  if (temp.length === 1) {
    return temp[0];
  } else {
    return null;
  }
}

/**
 * 获取设备图标
 * @param device 设备对象
 * @returns 图标React元素
 */
export function getDeviceIcon(device: Device): ReactElement {
  let icon: string = ''; // 初始化为空字符串
  const specs = _.cloneDeep(device.cc_specs);
  if (device.dv_type === 'ipc') {
    for (const v of DeviceIcons.icons) {
      if (v.reg.test('ipc')) {
        icon = v.icon;
        break;
      }
    }
  } else {
    if (device.is_tuya_camera) {
      for (const v of DeviceIcons.icons) {
        if (v.reg.test('camera')) {
          icon = v.icon;
          break;
        }
      }
    } else {
      const battery = _.remove(specs, (v: any) => {
        return v.name === 'Battery';
      });

      for (const spec of specs) {
        if (icon) {
          break;
        }
        for (const v of DeviceIcons.icons) {
          if (v.reg.test(spec.name)) {
            icon = v.icon;
            break;
          }
        }
      }

      if (!icon) {
        if (battery.length > 0) {
          icon = 'battery';
        } else {
          icon = DeviceIcons.defaultIcon;
        }
      }
    }
  }

  return (
    <View
      style={{
        backgroundColor: device.icon_bg_color,
        width: 30,
        height: 30,
        borderRadius: 19,
        alignItems: 'center',
        justifyContent: 'center',
      }}>
      <MCIcons size={20} color="#fff" name={icon} />
    </View>
  );
}

/**
 * 根据key获取图片
 * @param key 图片key
 * @returns 图片对象
 */
export function getImageFromKey(key: string): any {
  let icon = _.find(Images, i => i.key === key);
  if (!icon) {
    icon = _.find(Images, i => i.key === 'icons8-home-page-96.png');
  }
  return icon;
}

/**
 * 获取自动化图标
 * @param routine 自动化规则
 * @returns 图标对象
 */
export function getAutomationIcon(routine: AutomationItem): any {
  let icon: any;
  switch (routine.type) {
    case 'time':
      icon = require('../img/icons/auto/icons8-clock-96.png');
      break;
    case 'gps':
      icon = require('../img/icons/auto/icons8-map-marker-96.png');
      break;
    case 'sun':
      if (routine.is_sunrise) {
        icon = require('../img/icons/auto/icons8-sunrise-96.png');
      } else {
        icon = require('../img/icons/auto/icons8-sunset-96.png');
      }
      break;
    case 'device':
      icon = require('../img/icons/auto/icons8-module-96.png');
      break;
    case 'guard':
      icon = require('../img/icons/auto/icons8-guard-96.png');
      break;
    default:
      icon = require('../img/icons/auto/icons8-home-page-96.png');
      break;
  }
  return icon;
}

/**
 * 规格名称标准化
 * @param name 规格名称
 * @returns 标准化后的名称
 */
export function specNameNormalized(name: string): string {
  const re = /[\-_\s]/g;
  return name.toString().toLowerCase().replace(re, '');
}

/**
 * 比较规格名称是否相等
 * @param a 第一个名称
 * @param b 第二个名称
 * @returns 是否相等
 */
export function specNameEqual(a: string, b: string): boolean {
  return specNameNormalized(a) === specNameNormalized(b);
}

/**
 * 获取主要圆角值
 * @returns 圆角值
 */
export function mainRadius(): number {
  return 16;
}

/**
 * 获取主标题字体大小
 * @returns 字体大小
 */
export function mainTitle(): number {
  return 14;
}

/**
 * 转换日期为iOS UTC日期
 * @param date 日期字符串或日期对象
 * @returns 转换后的日期对象
 */
export function convertDateUTCForIos(date: string | Date): Date {
  if (typeof date === 'string') {
    const arr = date.split(/[- :]/);

    date = new Date(
      Date.UTC(
        Number(arr[0]),
        Number(arr[1]) - 1,
        Number(arr[2]),
        Number(arr[3]),
        Number(arr[4]),
        Number(arr[5]),
      ),
    );
  }

  return date;
}

/**
 * 字符串切分为数组
 * @param str 要切分的字符串
 * @param n 每段长度
 * @returns 字符串数组
 */
export function StrCut2Arr(str: string, n: number): string[] {
  const arr: string[] = [];
  const len = Math.ceil(str.length / n);
  for (let i = 0; i < len; i++) {
    if (str.length >= n) {
      const strCut = str.substring(0, n);
      arr.push(strCut);
      str = str.substring(n);
    } else {
      arr.push(str);
    }
  }
  return arr;
}

/**
 * 解析SS二维码
 * @param code 二维码字符串
 * @returns 解析结果
 */
export function ssQrcode(code: string): QrcodeResult {
  const res: {[key: string]: string} = {};
  const temp: {[key: string]: [number, number]} = {
    leadin: [0, 1],
    version: [2, 3],
    chk_sum: [4, 8],
    req_keys: [9, 11],
    dsk: [12, 51],
  };
  _.forEach(temp, (rg, n) => {
    if (n === 'dsk') {
      const dskCode = code.slice(rg[0], rg[1] + 1);
      const dskArr = StrCut2Arr(dskCode, 5);
      res[n] = dskArr.join('-');
    } else {
      res[n] = code.slice(rg[0], rg[1] + 1);
    }
  });

  return res;
}

// 为了保持向后兼容，将所有函数作为对象导出
export default {
  getSceneName,
  showSpecValue,
  formatDate,
  colorSwitchCmdParams,
  colorSwitchGetRgb,
  getDeviceType,
  getOneSwitchSpec,
  getDeviceIcon,
  getImageFromKey,
  getAutomationIcon,
  specNameEqual,
  specNameNormalized,
  mainRadius,
  mainTitle,
  convertDateUTCForIos,
  ssQrcode,
  StrCut2Arr,
};
