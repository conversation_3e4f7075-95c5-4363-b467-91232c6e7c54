/* eslint-disable react/no-unstable-nested-components */
import React, { useState, useEffect, useRef } from 'react';
import {View, Text, StyleSheet, TextInput, ScrollView} from 'react-native';
import {Helper} from '../Helper';
import I18n from '../I18n';
import {Tme, Colors} from '../ThemeStyle';
import CheckHelp from '../share/CheckHelp';
import NavBarView from '../share/NavBarView';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {hideLoading, showLoading} from '../../ILoading';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';
import { useNavigation, useRoute } from '@react-navigation/native';

const UserShowView = () => {
  const navigation = useNavigation();
  const route = useRoute();

  const [name] = useState(route.params.name);
  const [shareRooms] = useState(
    route.params.share_rooms !== undefined
      ? route.params.share_rooms.join(',').split(',')
      : []
  );

  const [inputName, setInputName] = useState('');

  const inputRef = useRef(null);

  const _save = () => {
    if (inputName.length === 0) {
      return false;
    }

    showLoading();
    // submit to server
    const body = {
      room_list: shareRooms,
      id: route.params.id,
      name: inputName,
    };
    Helper.httpPOST(
      '/users/add_user',
      {
        ensure: () => {
          hideLoading();
        },
        cloud: true,
        success: data => {
          PubSub.publish(PubSubEvent.EVENT_USER);
          navigation.goBack();
        },
      },
      body,
    );
  };

  const rightClick = () => {
    _save();
  };

  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={rightClick}
        />
      ),
    });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigation]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 600);

    return () => clearTimeout(timer);
  }, []);

  let input;
  if (name !== undefined) {
    input = (
      <View>
        <Text
          style={{
            color: Tme('cardTextColor'),
            fontSize: 18,
            fontWeight: '600',
          }}>
          {name}
        </Text>
      </View>
    );
  } else {
    input = (
      <View
        style={[styles.account_view, {borderColor: Tme('inputBorderColor')}]}>
        <TextInput
          ref={inputRef}
          returnKeyType="go"
          autoCapitalize="none"
          autoCorrect={false}
          clearButtonMode="always"
          keyboardType="email-address"
          placeholder={I18n.t('home.email') + '/' + I18n.t('home.cellphone')}
          underlineColorAndroid="transparent"
          placeholderTextColor={Tme('placeholder')}
          value={inputName}
          onChangeText={text => setInputName(text)}
          onSubmitEditing={_save}
          style={Colors.TextInputStyle()}
        />
      </View>
    );
  }

  return (
    <NavBarView>
      <ScrollView
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="always"
        keyboardDismissMode="on-drag"
        style={{
          backgroundColor: Tme('bgColor'),
        }}>
        <View
          style={{
            marginTop: 20,
            padding: 16,
            backgroundColor:
              name != undefined
                ? Tme('bgColor')
                : Tme('cardColor'),
          }}>
          {input}
        </View>
        <View
          style={{
            marginTop: 10,
            flexDirection: 'row',
            justifyContent: 'flex-end',
            marginRight: 16,
          }}>
          <CheckHelp navigation={navigation}>
            <View style={{padding: 16}}>
              <Text style={{color: Tme('cardTextColor')}}>
                {I18n.t('global.user_role_desp')}
              </Text>
            </View>
          </CheckHelp>
        </View>
      </ScrollView>
    </NavBarView>
  );
};

const styles = StyleSheet.create({
  account_view: {
    padding: 3,
    borderWidth: 1,
    borderRadius: 3,
  },
});

export default UserShowView;
