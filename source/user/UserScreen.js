/* eslint-disable react/no-unstable-nested-components */
import React, { useState, useEffect, useRef } from 'react';
import {View, Text, TouchableOpacity, FlatList} from 'react-native';
import {Helper, HelperMemo} from '../Helper';
import I18n from '../I18n';
import {Tme, IsDark} from '../ThemeStyle';
import ActionSheet from '@alessiocancian/react-native-actionsheet';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import NavBarView from '../share/NavBarView';
import EmptyView from '../share/EmptyView';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {isOwnerOrAdmin} from '../Router';
import {hideLoading, showLoading} from '../../ILoading';
import { PubSubEvent } from '../types/PubSubEvent';
import PubSub from 'pubsub-js';
import { useNavigation } from '@react-navigation/native';

const UserScreen = () => {
  const navigation = useNavigation();
  const [dataSource, setDataSource] = useState([]);
  const [rooms, setRooms] = useState([]);
  const [role, setRole] = useState('');
  const [clickRow, setClickRow] = useState('');
  const [reFresh, setReFresh] = useState(false);

  const actionSheetRef = useRef(null);
  const flatListRef = useRef(null);

  const rightClick = () => {
    addUser();
  };

  const sheetShow = (rowData) => {
    setClickRow(rowData);

    let type = true;

    if (role === 'rooms') {
      type = false;
    }
    if (role === 'owner') {
      if (rowData.role === 'owner' || rowData.role === 'admin') {
        type = false;
      }
    }
    if (HelperMemo.user_data.user.email) {
      if (rowData.email !== HelperMemo.user_data.user.email) {
        if (type) {
          actionSheetRef.current.show();
        }
      }
    } else if (HelperMemo.user_data.user.cellphone) {
      if (rowData.email !== HelperMemo.user_data.user.cellphone) {
        if (type) {
          actionSheetRef.current.show();
        }
      }
    }
  };

  const sheetClick = (index) => {
    if (index === 0) {
      _remove(clickRow);
    }
  };

  const _remove = (data) => {
    const body = {
      id: data.share_id,
    };

    setTimeout(() => {
      AlertModal.alert(I18n.t('global.activate_sure'), '', [
        {
          text: I18n.t('home.cancel'),
          onPress: () => {},
        },
        {
          text: I18n.t('home.confirm'),
          onPress: () => {
            showLoading();
            Helper.httpPOST(
              '/users/delete_user',
              {
                ensure: () => {
                  hideLoading();
                },
                cloud: true,
                success: () => {
                  doFetchData();
                },
              },
              body,
            );
          },
        },
      ]);
    }, 100);
  };

  const addUser = () => {
    navigation.push('userShowView', {
      rooms: rooms,
      title: I18n.t('user.add_user'),
    });
  };

  const doFetchData = () => {
    showLoading();
    Helper.httpGET(Helper.urlWithQuery('/users'), {
      cloud: true,
      success: data => {
        setDataSource(data.user);
        setRooms(data.room);
        setRole(data.role);
        setReFresh(false);
      },
      ensure: () => {
        hideLoading();
      },
    });
  };

  const _renderRow = ({item, index}) => {
    const rowData = item;
    const reg =
      HelperMemo.user_data.user.cellphone == rowData.email &&
      HelperMemo.user_data.user.email == rowData.email;
    return (
      <View
        key={index}
        style={{
          backgroundColor: Tme('cardColor'),
          flexDirection: 'row',
          padding: 20,
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: 2,
        }}>
        <Text style={{color: Tme('cardTextColor')}}>{rowData.email}</Text>
        {reg ? null : (
          <TouchableOpacity
            activeOpacity={0.8}
            hitSlop={{top: 5, right: 10, bottom: 5, left: 10}}
            style={{width: 15, justifyContent: 'center', alignItems: 'center'}}
            onPress={() => sheetShow(rowData)}>
            <MCIcons
              name="dots-horizontal"
              size={20}
              color={Tme('smallTextColor')}
            />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // 设置headerRight
  useEffect(() => {
    navigation.setOptions({
      headerRight: () => {
        if (isOwnerOrAdmin()) {
          return (
            <HeaderRightBtn
              rightClick={rightClick}
              icon={{name: 'add', icon: 'MaterialIcons'}}
            />
          );
        }
        return null;
      },
    });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigation]);

  // 相当于componentDidMount和componentWillUnmount
  useEffect(() => {
    doFetchData();

    const subscription = PubSub.subscribe(PubSubEvent.EVENT_USER, () => {
      doFetchData();
    });

    return () => {
      PubSub.unsubscribe(subscription);
    };
  }, []);

  return (
    <NavBarView>
      <FlatList
        style={{
          backgroundColor: Tme('bgColor'),
          borderWidth: 0,
          borderColor: '#dfe2e5',
          borderRadius: 4,
          borderBottomWidth: 0,
          paddingTop: 20,
        }}
        ref={flatListRef}
        data={dataSource}
        renderItem={_renderRow}
        numColumns={1}
        onEndReachedThreshold={0.1}
        ListEmptyComponent={() => <EmptyView />}
        keyExtractor={(item, index) => index.toString()}
        refreshing={reFresh}
        onRefresh={()=> {
          setReFresh(true);
          doFetchData();
        }}
      />
      <ActionSheet
        ref={actionSheetRef}
        userInterfaceStyle={IsDark() ? 'dark' : 'light'}
        options={[I18n.t('home.cancel'), I18n.t('home.remove_btn')]}
        cancelButtonIndex={0}
        destructiveButtonIndex={1}
        theme="ios"
        styles={{
          cancelButtonBox: {
            height: 50,
            marginTop: 6,
            alignItems: 'center',
            justifyContent: 'center',
          },
          buttonBox: {
            height: 50,
            alignItems: 'center',
            justifyContent: 'center',
          },
        }}
        onPress={index => {
          sheetClick(index - 1);
        }}
      />
    </NavBarView>
  );
};

export default UserScreen;
