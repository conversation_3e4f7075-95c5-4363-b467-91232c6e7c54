import React, { Component } from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import * as Progress from 'react-native-progress';
import I18n from '../I18n';
import WifiManager from 'react-native-wifi-reborn';
import { Tme, Colors } from '../ThemeStyle';
import IdleTimerManager from 'react-native-idle-timer';
import NavBarView from '../share/NavBarView';
import CardView from '../share/CardView';
import axios from 'axios';
import AlertModal from '../share/AlertModal';
import ScreenSizeContext from '../../WindowResizeContext';

export default class LinkPresen extends Component {
  constructor(props) {
    super(props);

    this.state = {
      progress: 0,
      indeterminate: true,
      sn: this.props.route.params.sn,
      showLinkError: false,
      showConnectError: false,
    };
  }

  static contextType = ScreenSizeContext;

  componentDidMount() {
    this.linkWifi();
    IdleTimerManager.setIdleTimerDisabled(true);
  }

  componentWillUnmount() {
    IdleTimerManager.setIdleTimerDisabled(false);
  }

  linkWifi() {
    var that = this;
    WifiManager.getCurrentWifiSSID().then(SSID => {
      if (SSID == 'PRESEN_CONTROLLER') {
        that.setState(
          {
            progress: 0.5,
            indeterminate: false,
          },
          () => {
            that.setLink();
          },
        );
      } else {
        that.connectWifi('PRESEN_CONTROLLER', connected => {
          if (connected) {
            that.setState(
              {
                progress: 0.5,
                indeterminate: false,
              },
              () => {
                that.setLink();
              },
            );
          } else {
            that.setState({
              progress: 0,
              indeterminate: true,
              showLinkError: true,
            });
          }
        });
      }
    });
  }

  connectWifi(ssid, callback) {
    WifiManager.connectToProtectedWifiSSID({
      ssid: ssid,
      password: null,
    }).then(
      () => {
        setTimeout(() => {
          WifiManager.getCurrentWifiSSID().then(ss => {
            callback(ss == ssid);
          });
        }, 5000);
      },
      () => {
        callback(false);
      },
    );
  }

  render() {
    var desp = I18n.t('wifi.setting_desp');
    if (this.state.showLinkError) {
      desp = I18n.t('wifi.setting_error');
    }
    if (this.state.showConnectError) {
      desp = I18n.t('wifi.wifi_link_error');
    }
    return (
      <NavBarView>
        <ScrollView style={{ flex: 1 }}>
          <View style={{ alignItems: 'center', marginTop: 20, padding: 20 }}>
            <CardView
              withWaveBg={true}
              styles={{
                width: this.context.winWidth - 40,
                height: 380,
                padding: 20,
                borderRadius: 8,
                alignItems: 'center',
              }}>
              <View style={{ marginTop: 60, marginBottom: 20 }}>
                <Text
                  style={{
                    fontSize: 14,
                    fontWeight: '600',
                    textAlign: 'center',
                    color: Tme('cardTextColor'),
                  }}>
                  {desp}
                </Text>
              </View>
              {this.state.showLinkError ||
                this.state.showConnectError ? null : (
                <Progress.Circle
                  size={140}
                  color={Colors.MainColor}
                  progress={this.state.progress}
                  indeterminate={this.state.indeterminate}
                  showsText={true}
                  formatText={() => {
                    return `${parseInt(this.state.progress * 100, 10)}%`;
                  }}
                />
              )}
              {this.state.showLinkError || this.state.showConnectError ? (
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={this.retry.bind(this)}
                  style={{
                    height: 40,
                    borderRadius: 20,
                    width: 120,
                    backgroundColor: Colors.MainColor,
                    borderColor: Colors.MainColor,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text
                    style={{
                      fontSize: 14,
                      color: '#fff',
                    }}>
                    {I18n.t('wifi.retry')}
                  </Text>
                </TouchableOpacity>
              ) : null}
            </CardView>
          </View>
        </ScrollView>
      </NavBarView>
    );
  }

  retry() {
    var that = this;
    this.setState(
      {
        showLinkError: false,
        showConnectError: false,
      },
      () => {
        WifiManager.getCurrentWifiSSID().then(SSID => {
          if (SSID == 'PRESEN_CONTROLLER') {
            that.setState(
              {
                progress: 0.5,
                indeterminate: false,
              },
              () => {
                that.setLink();
              },
            );
          } else {
            that.setState({
              progress: 0,
              indeterminate: true,
              showLinkError: true,
            });
          }
        });
      },
    );
  }

  pushSendSn(sn) {
    this.props.navigation.push('SendSn', {
      sn: sn,
      type: 'wifi',
    });
  }

  setLink() {
    var that = this;
    var formData = new FormData();
    formData.append('ssid', this.props.route.params.ssid);
    formData.append('password', this.props.route.params.password);
    axios({
      url: 'http://************:8880/connect',
      method: 'POST',
      data: formData,
      timeout: 20000,
      headers: { 'Content-Type': 'multipart/form-data' },
    })
      .then(response => {
        if (response.status == 200) {
          const data = response.data;
          if (data.status == 'ok') {
            that.setState(
              {
                progress: 1,
              },
              () => {
                if (that.props.route.params.type == 'setting') {
                  setTimeout(() => {
                    AlertModal.alert(
                      I18n.t('wifi.add_title'),
                      I18n.t('wifi.reload_desp'),
                      [
                        {
                          text: I18n.t('home.confirm'),
                          onPress: () => {
                            this.props.navigation.goBack();
                          },
                        },
                      ],
                    );
                  }, 1000);
                } else {
                  setTimeout(() => {
                    AlertModal.alert(
                      I18n.t('wifi.add_title'),
                      I18n.t('wifi.reload_desp'),
                      [
                        {
                          text: I18n.t('home.confirm'),
                          onPress: () => {
                            that.pushSendSn(data.data.sn);
                          },
                        },
                      ],
                    );
                  }, 1000);
                }
              },
            );
          } else {
            that.setState({
              progress: 0,
              indeterminate: true,
              showLinkerror: false,
              showConnectError: true,
            });
          }
        } else {
          that.setState({
            progress: 0,
            indeterminate: true,
            showLinkerror: false,
            showConnectError: true,
          });
        }
      })
      .catch(error => {
        that.setState({
          progress: 0,
          indeterminate: true,
          showLinkerror: false,
          showConnectError: true,
        });
      });
  }
}
