import React, {Component} from 'react';
import {View, Text, TouchableOpacity, ScrollView} from 'react-native';
import I18n from '../I18n';
import Icon from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import NavBarView from '../share/NavBarView';
import {NotificationCenter, EVENT_ADD_CONTROLLER} from '../NotificationCenter';
import {Tme} from '../ThemeStyle';
import AlertModal from '../share/AlertModal';

export default class WifiScreen extends Component {
  constructor(props) {
    super(props);
  }

  componentDidMount() {
    var that = this;
    NotificationCenter.addObserver(this, EVENT_ADD_CONTROLLER, () => {
      setTimeout(() => {
        that.link('AddController');
      }, 500);
    });
  }

  componentWillUnmount() {
    NotificationCenter.removeObserver(this, EVENT_ADD_CONTROLLER);
  }

  render() {
    return (
      <NavBarView>
        <ScrollView style={{flex: 1}}>
          <View
            style={{
              marginTop: 20,
              paddingBottom: 2,
            }}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={this.link.bind(this, 'WifiSetting')}
              style={{
                backgroundColor: Tme('cardColor'),
              }}>
              <View
                style={{
                  marginHorizontal: 20,
                  flexDirection: 'row',
                  paddingVertical: 16,
                  justifyContent: 'space-between',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Icon name="wifi" size={20} color={Tme('textColor')} />
                  <Text style={{marginLeft: 12, color: Tme('cardTextColor')}}>
                    {I18n.t('global.wifi_connection')}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <MaterialIcons
                    name="keyboard-arrow-right"
                    size={20}
                    color={Tme('textColor')}
                  />
                </View>
              </View>
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.click.bind(this)}
            style={{backgroundColor: Tme('cardColor')}}>
            <View
              style={{
                marginHorizontal: 20,
                flexDirection: 'row',
                paddingVertical: 16,
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="settings-ethernet"
                  size={20}
                  color={Tme('textColor')}
                />
                <Text style={{marginLeft: 10, color: Tme('cardTextColor')}}>
                  {I18n.t('global.wired_connection')}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <MaterialIcons
                  name="keyboard-arrow-right"
                  size={20}
                  color={Tme('textColor')}
                />
              </View>
            </View>
          </TouchableOpacity>
        </ScrollView>
      </NavBarView>
    );
  }

  link(type) {
    this.props.navigation.push(type, {
      type: 'add',
      sn: '',
      title: type == 'WifiSetting' ? '' : I18n.t('global.wired_connection'),
    });
  }

  click() {
    AlertModal.alert(
      I18n.t('home.warning_message'),
      I18n.t('wifi.wired_net_desp'),
      [
        {
          text: I18n.t('home.cancel'),
          onPress: () => {},
        },
        {
          text: I18n.t('home.confirm'),
          onPress: () => {
            this.props.navigation.push('DetectController', {
              title: I18n.t('wifi.wired_net_add'),
            });
          },
        },
      ],
    );
  }
}
