/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import {
  View,
  Text,
  TextInput,
  Platform,
  TouchableOpacity,
  Keyboard,
  AppState,
  Dimensions,
  ScrollView,
} from 'react-native';
import I18n from '../I18n';
import WifiManager from 'react-native-wifi-reborn';
import { HelperMemo } from '../Helper';
import { Tme, Colors } from '../ThemeStyle';
import Icon from 'react-native-vector-icons/Ionicons';
import NavBarView from '../share/NavBarView';
import CardView from '../share/CardView';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';

class WifiSetting extends Component {
  constructor(props) {
    super(props);

    this.state = {
      ssid: '',
      password: '',
      appState: AppState.currentState,
      passwordHidden: true,
      winWidth: this.initWidth(Dimensions.get('window')),
    };

    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.next')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this.save();
  }

  componentDidMount() {
    this.setWifi();
    this.appStateEvent = AppState.addEventListener(
      'change',
      this.handleAppStateChange.bind(this),
    );
    this.DimensionsEvent = Dimensions.addEventListener(
      'change',
      this._onResize.bind(this),
    );
  }

  componentWillUnmount() {
    if (this.DimensionsEvent) {
      this.DimensionsEvent.remove();
    }
    if (this.appStateEvent) {
      this.appStateEvent.remove();
    }
  }

  initWidth(window) {
    if (Platform.OS == 'android') {
      return window.width;
    } else {
      if (window.width > window.height) {
        return (
          window.width -
          HelperMemo.STATUS_BAR_HEIGHT -
          HelperMemo.BOTTOM_BAR_HEIGHT -
          22
        );
      } else {
        return window.width;
      }
    }
  }

  _onResize({ window }) {
    this.setState({
      winWidth: this.initWidth(window),
      winHeight: window.height,
    });
  }

  setWifi() {
    var that = this;

    WifiManager.getCurrentWifiSSID().then(
      SSID => {
        that.setState({ ssid: SSID });
      },
      () => {
        AlertModal.alert(I18n.t('global.open_wifi'));
      },
    );
  }

  handleAppStateChange(appState) {
    var that = this;
    //判断后台还是前台
    if (
      this.state.appState.match(/inactive|background/) &&
      appState === 'active'
    ) {
      that.setWifi();
    }

    this.setState({ appState });
  }

  render() {
    return (
      <NavBarView>
        <ScrollView>
          <View style={{ alignItems: 'center', padding: 20, marginTop: 20 }}>
            <CardView
              styles={{
                height: 380,
                padding: 20,
                borderRadius: 8,
                alignItems: 'center',
              }}>
              <View
                style={{
                  marginTop: 40,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    fontSize: 24,
                    textAlign: 'center',
                    color: Tme('textColor'),
                  }}>
                  {I18n.t('global.setting_wifi')}
                </Text>
                <View
                  style={{
                    flexDirection: 'row',
                    marginBottom: 8,
                    marginTop: 8,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text
                    style={{
                      color: Tme('textColor'),
                    }}>
                    {I18n.t('global.setting_wifi_desp')}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  padding: 3,
                  marginTop: 16,
                  borderWidth: 1,
                  borderRadius: 3,
                  borderColor: Tme('inputBorderColor'),
                }}>
                <TextInput
                  autoCapitalize="none"
                  placeholder={I18n.t('global.ssid')}
                  placeholderTextColor={Tme('placeholder')}
                  autoCorrect={false}
                  underlineColorAndroid="transparent"
                  value={this.state.ssid}
                  onChangeText={ssid => this.setState({ ssid })}
                  style={[
                    Colors.TextInputStyle(),
                    { width: this.state.winWidth - 100 },
                  ]}
                />
              </View>
              <View
                style={{
                  padding: 3,
                  marginTop: 16,
                  borderWidth: 1,
                  borderRadius: 3,
                  borderColor: Tme('inputBorderColor'),
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}>
                <TextInput
                  returnKeyType="go"
                  autoCapitalize="none"
                  placeholder={I18n.t('global.wifi_password')}
                  placeholderTextColor={Tme('placeholder')}
                  underlineColorAndroid="transparent"
                  autoCorrect={false}
                  value={this.state.password}
                  onChangeText={password => this.setState({ password })}
                  onSubmitEditing={this.save.bind(this)}
                  secureTextEntry={this.state.passwordHidden}
                  style={[
                    Colors.TextInputStyle(),
                    { width: this.state.winWidth - 125 },
                  ]}
                />
                <TouchableOpacity
                  activeOpacity={0.8}
                  style={{ width: 26 }}
                  onPress={() =>
                    this.setState({
                      passwordHidden: !this.state.passwordHidden,
                    })
                  }>
                  {this.state.passwordHidden ? (
                    <Icon name="eye" size={20} color={Tme('textColor')} />
                  ) : (
                    <Icon name="eye-off" size={20} color={Tme('textColor')} />
                  )}
                </TouchableOpacity>
              </View>
            </CardView>
          </View>
        </ScrollView>
      </NavBarView>
    );
  }

  save() {
    Keyboard.dismiss();
    if (!this.state.ssid) {
      AlertModal.alert(I18n.t('global.input_ssid'));
      return;
    }

    // eslint-disable-next-line no-useless-escape
    var reg = /\_(5G)$/;
    if (reg.test(this.state.ssid)) {
      AlertModal.alert(I18n.t('global.setting_wifi_desp'));
      return;
    }
    var passProps = {
      type: this.props.route.params.type,
      ssid: this.state.ssid,
      password: this.state.password,
    };
    var name = '';
    switch (this.props.route.params.type) {
      case 'tuya':
        name = 'AddTuyaCamera';
        break;
      case 'tuya_ap':
      case 'tuya_ez':
        name = 'AddTuyaDevice';
        break;
      case 'matter_wifi':
        passProps.sn = this.props.route.params.sn;
        passProps.code = this.props.route.params.code;
        name = 'addDevice';
        break;
      case 'ipc_ez':
        name = 'AddIPCDevice';
        break;
      case 'ipc_ap':
        name = 'AddIPCAPDevice';
        break;
      default:
        passProps.sn = this.props.route.params.sn;
        name = 'WifiLink';
    }
    this.props.navigation.push(name, {
      ...passProps,
    });
  }
}

export default WifiSetting;
