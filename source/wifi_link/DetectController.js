/* eslint-disable react/no-unstable-nested-components */
import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { Helper, HelperMemo } from '../Helper';
import I18n from '../I18n';
import { Tme, Colors } from '../ThemeStyle';
import EmptyView from '../share/EmptyView';
import NavBarView from '../share/NavBarView';
import {
  NotificationCenter,
  EVENT_ADD_CONTROLLER,
} from '../NotificationCenter';
import ShadowView from '../share/ShadowView';
import AlertModal from '../share/AlertModal';
import PubSub from 'pubsub-js';
import { PubSubEvent } from '../types/PubSubEvent';
export default class DetectController extends Component {
  constructor(props) {
    super(props);

    this.state = {
      dataSource: [],
    };
    this.timer = null;
    this.showAi = true;
  }

  componentDidMount() {
    this.timer = setInterval(() => {
      this.doFetchData();
    }, 2000);
  }

  componentWillUnmount() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }

  render() {
    return (

      <NavBarView>
        <View
          style={{
            flex: 1,
          }}>
          <View style={{ padding: 20, flexDirection: 'row' }}>
            <Text
              style={{
                marginTop: 3,
                fontSize: 12,
                fontWeight: '500',
                marginRight: 8,
                color: Tme('cardTextColor'),
              }}>
              {I18n.t('home.controller')}
            </Text>
            <ActivityIndicator size="small" color={Colors.MainColor} />
          </View>
          <View style={{ paddingBottom: 20, flex: 1 }}>
            <FlatList
              style={{ paddingHorizontal: 12, marginBottom: 30, flex: 1 }}
              ref={flatList => (this._flatList = flatList)}
              data={this.state.dataSource}
              renderItem={this._renderRow.bind(this)}
              numColumns={1}
              ListEmptyComponent={() => <EmptyView />}
              onEndReachedThreshold={0.1}
              keyExtractor={(item, index) => index.toString()}
            />
          </View>
        </View>
        <TouchableOpacity
          onPress={this.click.bind(this)}
          activeOpacity={1.0}
          style={{
            paddingVertical: 20,
            flexDirection: 'row',
            justifyContent: 'center',
            backgroundColor: Tme('cardColor'),
            alignItems: 'center',
          }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Text
              style={{
                fontSize: 16,
                fontWeight: '600',
                color: Tme('cardTextColor'),
              }}>
              {I18n.t('wifi.wired_net_btn')}
            </Text>
          </View>
        </TouchableOpacity>
      </NavBarView>

    );
  }

  _renderRow({ item, index }) {
    var html;
    var rowData = item;
    html = (
      <View style={{ marginHorizontal: 8, marginBottom: 12 }}>
        <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.touchRow.bind(this, rowData)}
            style={{
              height: 80,
              borderRadius: 8,
              paddingHorizontal: 20,
              backgroundColor: Tme('cardColor'),
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <View
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'flex-start',
              }}>
              <Text
                style={{
                  fontSize: 16,
                  color: Tme('cardTextColor'),
                }}>
                {rowData.name}
              </Text>
            </View>
            <View
              style={{
                alignItems: 'flex-end',
                justifyContent: 'center',
                flex: 1,
              }}>
              <View
                style={{
                  alignItems: 'flex-end',
                  justifyContent: 'center',
                  flex: 1,
                }}>
                <Text
                  style={{
                    fontSize: 17,
                    color: Tme('cardTextColor'),
                  }}>
                  {/* {rowData.state
                    ? I18n.t('home.on_line')
                    : I18n.t('home.off_line')} */}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        </ShadowView>
      </View>
    );
    return <View>{html}</View>;
  }

  touchRow(rowData) {
    var that = this;
    if (HelperMemo.select_home) {
      Helper.httpPOST(
        '/sns/add',
        {
          success: data => {
            AlertModal.alert(I18n.t('wifi.add_success'), '', [
              {
                text: I18n.t('home.confirm'),
                onPress: () => {
                  that.props.navigation.goBack();
                  setTimeout(() => {
                    PubSub.publish(PubSubEvent.RESTART_APP);
                  }, 1000);
                },
              },
            ]);
          },
          error: (errors, type) => { },
        },
        { sn: rowData.sn, home_id: HelperMemo.select_home },
      );
    } else {
      AlertModal.alert(I18n.t('global.please_add_home'));
    }
  }

  doFetchData() {
    var that = this;
    Helper.httpGET(Helper.urlWithQuery('/sns/check'), {
      cloud: true,
      success: data => {
        that.setState(
          {
            dataSource: data.controller,
          },
          () => {
            that.showAi = false;
          },
        );
      },
    });
  }

  click() {
    this.props.navigation.goBack();
    NotificationCenter.dispatchEvent(EVENT_ADD_CONTROLLER);
  }
}
