import React, { Component } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import * as Progress from 'react-native-progress';
import I18n from '../I18n';
import { Tme, Colors } from '../ThemeStyle';
import { Helper, HelperMemo } from '../Helper';
import IdleTimerManager from 'react-native-idle-timer';
import NetInfo from '@react-native-community/netinfo';
import NavBarView from '../share/NavBarView';
import CardView from '../share/CardView';
import ScreenSizeContext from '../../WindowResizeContext';
class SendSn extends Component {
  constructor(props) {
    super(props);

    this.state = {
      progress: 0,
      indeterminate: true,
      show_error: false,
      show_reload: false,
      error_text: I18n.t('wifi.add_desp'),
    };
    this.down;
    this.count = 10;
  }

  static contextType = ScreenSizeContext;

  componentDidMount() {
    if (!HelperMemo.select_home) {
      this.props.navigation.goBack();
    }
    setTimeout(() => {
      this.addController();
    }, 2000);

    IdleTimerManager.setIdleTimerDisabled(true);
  }

  componentWillUnmount() {
    IdleTimerManager.setIdleTimerDisabled(false);
  }

  onCancel() {
    this.props.navigation.goBack();
  }

  render() {
    return (
      <NavBarView>
        <View style={{ alignItems: 'center', marginTop: 40 }}>
          <CardView
            withWaveBg={true}
            styles={{
              width: this.context.winWidth - 40,
              height: 480,
              padding: 20,
              borderRadius: 8,
              alignItems: 'center',
            }}>
            <View style={{ marginTop: 60, marginBottom: 20 }}>
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: '600',
                  textAlign: 'center',
                  color: Tme('cardTextColor'),
                }}>
                {this.state.error_text}
              </Text>
            </View>
            {this.state.show_error ? null : (
              <Progress.Circle
                size={140}
                color={Colors.MainColor}
                progress={this.state.progress}
                indeterminate={this.state.indeterminate}
                showsText={true}
                formatText={() => {
                  return `${parseInt(this.state.progress * 100, 10)}%`;
                }}
              />
            )}
            {this.state.show_error ? (
              <>
                <TouchableOpacity
                  activeOpacity={0.8}
                  onPress={this.retry.bind(this)}
                  style={{
                    height: 40,
                    borderRadius: 20,
                    width: 120,
                    backgroundColor: Colors.MainColor,
                    borderColor: Colors.MainColor,
                  }}>
                  <Text
                    style={{
                      fontSize: 14,
                    }}>
                    {I18n.t('wifi.retry')}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={{ marginTop: 20 }}
                  onPress={this.onCancel.bind(this)}>
                  <Text style={{ color: Colors.MainColor }}>
                    {I18n.t('home.cancel')}
                  </Text>
                </TouchableOpacity>
              </>
            ) : null}
          </CardView>
        </View>
      </NavBarView>
    );
  }

  retry() {
    var that = this;
    this.setState(
      {
        show_error: false,
        error_text: I18n.t('wifi.add_desp'),
        progress: 0,
        indeterminate: true,
      },
      () => {
        that.addController();
      },
    );
  }

  addSuccess() {
    this.props.navigation.replace('ChangeSnName', {
      sn: this.props.route.params.sn,
      type: 'new',
    });
  }

  addController() {
    var that = this;

    NetInfo.fetch().then(state => {
      if (state.isConnected) {
        that.setState(
          {
            indeterminate: false,
          },
          () => {
            Helper.httpPOST(
              '/sns/add',
              {
                success: data => {
                  if (that.props.route.params.type == 'wifi') {
                    that.setState(
                      {
                        progress: 1,
                        show_reload: true,
                        show_error: false,
                      },
                      () => {
                        that.addSuccess();
                      },
                    );
                  } else {
                    that.setState(
                      {
                        progress: 1,
                        error_text: I18n.t('wifi.add_success'),
                      },
                      () => {
                        that.addSuccess();
                      },
                    );
                  }
                },
                error: (errors, type) => {
                  if (type == 'inner') {
                    if (that.count <= 0) {
                      that.setState({
                        show_error: true,
                        error_text: I18n.t('wifi.add_error'),
                        progress: 0,
                        show_reload: false,
                        indeterminate: true,
                      });
                    } else {
                      setTimeout(() => {
                        that.count--;
                        that.addController();
                      }, 3000);
                    }
                  } else {
                    that.setState({
                      progress: 0,
                      indeterminate: true,
                      show_error: true,
                      error_text: errors,
                    });
                  }
                },
              },
              { sn: that.props.route.params.sn, home_id: HelperMemo.select_home },
            );
          },
        );
      } else {
        that.setState({
          progress: 0,
          indeterminate: true,
          show_error: true,
          error_text: I18n.t('home.network_issue'),
        });
      }
    });
  }
}
export default SendSn;
