import React, {Component} from 'react';
import {View, Text, TouchableOpacity, Image, ScrollView} from 'react-native';
import I18n from '../I18n';
import CheckBox from '../check_box/index';
import {Tme} from '../ThemeStyle';
import NavBarView from '../share/NavBarView';
import CardView from '../share/CardView';
import AlertModal from '../share/AlertModal';
import HeaderRightBtn from '../share/HeaderRightBtn';

export default class WifiLink extends Component {
  constructor(props) {
    super(props);

    this.state = {
      isClicked: false,
    };
    this.key;
    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.next')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    this.save();
  }

  componentWillUnmount() {}

  render() {
    return (
      <NavBarView>
        <ScrollView
          contentContainerStyle={{alignItems: 'center'}}
          style={{flex: 1}}>
          <View style={{flex: 1, marginTop: 20, padding: 20}}>
            <CardView
              withWaveBg={true}
              styles={{
                height: 320,
                padding: 20,
                borderRadius: 8,
                alignItems: 'center',
              }}>
              <View>
                <Text
                  style={{
                    marginTop: 40,
                    lineHeight: 22,
                    fontWeight: '500',
                    color: Tme('cardTextColor'),
                  }}>
                  1. {I18n.t('global.wifi_link_desp')}
                </Text>
                <Text
                  style={{
                    marginTop: 8,
                    fontWeight: '500',
                    lineHeight: 22,
                    color: Tme('cardTextColor'),
                  }}>
                  2. {I18n.t('wifi.next_wifi_desp')}
                </Text>
              </View>
              <View
                style={{
                  marginTop: 16,
                  justifyContent: 'center',
                  alignItems: 'center',
                  flexDirection: 'row',
                }}>
                <CheckBox
                  // rightTextStyle={{ fontSize: 17, color: Tme("cardTextColor"), lineHeight: 49 }}
                  style={{marginRight: 8}}
                  onClick={() => this.onClick()}
                  // rightText={I18n.t("global.wifi_link_btn")}
                  isChecked={this.state.isClicked}
                  checkedImage={
                    <Image
                      source={require('../../img/checkbox-checked.png')}
                      style={{width: 16, height: 16}}
                    />
                  }
                  unCheckedImage={
                    <Image
                      source={require('../../img/Checkbox.png')}
                      style={{width: 16, height: 16}}
                    />
                  }
                />
                <TouchableOpacity
                  onPress={this.onClick.bind(this)}
                  activeOpacity={0.8}>
                  <Text
                    style={{
                      fontSize: 17,
                      color: Tme('cardTextColor'),
                      lineHeight: 49,
                    }}>
                    {I18n.t('global.wifi_link_btn')}
                  </Text>
                </TouchableOpacity>
              </View>
            </CardView>
          </View>
        </ScrollView>
      </NavBarView>
    );
  }

  onClick() {
    this.setState({
      isClicked: !this.state.isClicked,
    });
  }

  save() {
    if (this.state.isClicked) {
      this.props.navigation.push('LinkPresen', {
        ssid: this.props.route.params.ssid,
        password: this.props.route.params.password,
        sn: this.props.route.params.sn,
        type: this.props.route.params.type,
      });
    } else {
      AlertModal.alert('', I18n.t('global.wifi_link_desp'));
    }
  }
}
