import React from 'react';
import {View} from 'react-native';
import {Helper} from '../Helper';
import I18n from '../I18n';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import DeviceControl from '../DeviceControl';
import {Colors, Tme} from '../ThemeStyle';

export default class DeviceSummaryItemSwitch extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: this.props.spec.value == 'Off' ? 0 : 1,
      time: this.props.time,
    };
  }

  render() {
    return (
      <View>
        <SegmentedControl
          tintColor={Colors.MainColor}
          fontStyle={{color: Tme('cardTextColor'), fontSize: 16}}
          activeFontStyle={{color: '#ffffff', fontSize: 16}}
          style={{flex: 1, height: 42}}
          values={[I18n.t('spec.off_b'), I18n.t('spec.on_b')]}
          selectedIndex={this.state.value}
          onChange={this.handleRadioChange.bind(this)}
        />
      </View>
    );
  }

  handleRadioChange(e) {
    var status = e.nativeEvent.selectedSegmentIndex;
    this.setState({
      value: status,
      time: Helper.utc(),
    });
    var param = status == 1 ? 255 : 0;
    new DeviceControl({
      spec: this.props.spec,
      param: param,
      sn_id: this.props.device.sn_id,
      runCMD: true,
    }).switch();
  }
}
