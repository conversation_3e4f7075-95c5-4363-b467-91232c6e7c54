import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Helper, HelperMemo } from '../Helper';
import _ from 'lodash';
import { Tme, Colors, GetStyle } from '../ThemeStyle';
import Slider from '@react-native-community/slider';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import DeviceControl from '../DeviceControl';
import ShadowView from '../share/ShadowView';
import { showSpecValue } from '../Tools';

export default class DeviceSummaryItemOther extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      initialValue: this.props.spec.value,
      value: 0,
      values: [],
      keys: [],
    };
  }

  componentDidMount() {
    this.initValue();
  }
  initValue() {
    const { spec } = this.props;
    if (spec.options.length > 0) {
      var values = [];
      var keys = [];
      _.each(spec.options, (v, k) => {
        keys.push(Helper.i(v.name));
        values.push(v.val);
      });

      this.setState({
        value: _.findIndex(values, v => {
          return v == spec.value;
        }),
        values: values,
        keys: keys,
      });
    }
  }

  render() {
    var that = this;
    const { spec } = this.props;
    var con;
    if (spec.min >= 0 && spec.max > 0) {
      con = that.showSlider(spec);
    } else if (spec.options.length > 0) {
      con = this.showOptions(spec);
    }
    return (
      <View
        style={{ marginBottom: 20, marginHorizontal: 8 }}
        key={'spec_' + spec.value_id}>
        <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
          <View style={[GetStyle('spec')]}>
            <View
              style={{
                marginBottom: 16,
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <Text style={GetStyle('specNameText')}>
                {Helper.i(spec.name)}
              </Text>
            </View>
            <View>{con}</View>
          </View>
        </ShadowView>
      </View>
    );
  }
  showOptions(spec) {
    var html;
    const that = this;
    const { keys, values, value } = this.state;
    switch (spec.options.length) {
      case 2:
      case 3:
        html = (
          <SegmentedControl
            key={Math.random()}
            tintColor={Colors.MainColor}
            values={keys}
            selectedIndex={value}
            fontStyle={{ color: Tme('cardTextColor'), fontSize: 16 }}
            activeFontStyle={{ color: '#ffffff', fontSize: 16 }}
            style={{ flex: 1, height: 42 }}
            onChange={this.handleRadioChange.bind(this, values)}
          />
        );
        break;
      default:
        html = _.sortBy(spec.options, function (n) {
          return n.val;
        }).map(function (option, k) {
          if (that.state.value == option.val) {
            return (
              <TouchableOpacity
                key={'spec_' + k}
                activeOpacity={0.8}
                onPress={that.setMode.bind(that, option.val)}
                style={{
                  marginRight: 16,
                  paddingVertical: 10,
                  paddingHorizontal: 16,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: Colors.MainColor,
                  borderRadius: 8,
                  marginTop: 16,
                }}>
                <Text style={{ color: 'white', fontSize: 17 }}>
                  {Helper.i(option.name)}
                </Text>
              </TouchableOpacity>
            );
          } else {
            return (
              <TouchableOpacity
                key={'spec_' + k}
                activeOpacity={0.8}
                onPress={that.setMode.bind(that, option.val)}
                style={{
                  marginRight: 16,
                  paddingVertical: 10,
                  paddingHorizontal: 16,
                  borderWidth: 1,
                  borderColor: Colors.MainColor,
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderRadius: 8,
                  marginTop: 16,
                }}>
                <Text style={{ color: Tme('cardTextColor'), fontSize: 17 }}>
                  {Helper.i(option.name)}
                </Text>
              </TouchableOpacity>
            );
          }
        });
    }
    return html;
  }

  setMode(e) {
    this.setState({
      value: e,
    });
    new DeviceControl({
      spec: this.props.spec,
      param: e,
      sn_id: this.props.device.sn_id,
      runCMD: true,
    }).other();
  }

  handleRadioChange(values, e) {
    var index = e.nativeEvent.selectedSegmentIndex;
    new DeviceControl({
      spec: this.props.spec,
      param: values[index],
      sn_id: this.props.device.sn_id,
      runCMD: true,
    }).other();
  }
  showSlider(spec) {
    return (
      <View style={{ marginTop: 8 }}>
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 8,
            marginTop: 20,
          }}>
          <Text
            style={{
              fontSize: 22,
              fontWeight: '500',
              color: Tme('cardTextColor'),
            }}>
            {this.state.initialValue}
          </Text>
        </View>
        <Slider
          minimumValue={spec.min}
          maximumValue={spec.max}
          step={1}
          minimumTrackTintColor={Colors.MainColor}
          value={this.state.initialValue}
          onChange={this.handleSliderChange.bind(this)}
          onSlidingComplete={this.sliderDidChange.bind(this)}
        />
        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <Text style={{ color: Tme('cardTextColor') }}>{spec.min}</Text>
          <Text style={{ color: Tme('cardTextColor') }}>{spec.max}</Text>
        </View>
      </View>
    );
  }

  handleSliderChange(e) {
    this.setState({
      initialValue: e,
      time: Helper.utc(),
    });
  }

  sliderDidChange(e) {
    this.handleSliderChange(e);
    var deviceControl = new DeviceControl({
      spec: this.props.spec,
      param: e,
      sn_id: this.props.device.sn_id,
      runCMD: true,
    });
    deviceControl.other();
  }

  value_scale(spec) {
    var value = '';
    if (spec.name.toUpperCase() == 'TEMPERATURE') {
      var unit_index = spec.unit_index;
      if (spec.unit_index !== 0 || 1) {
        switch (spec.scale) {
          case '°C':
            unit_index = 0;
            break;
          case '°F':
            unit_index = 1;
            break;
          default:
            unit_index = '';
            break;
        }
      }
      value =
        Helper.temperatureScale(spec.value, unit_index, false) +
        ' °' +
        HelperMemo.user_data.user.scale;
    } else {
      value =
        Helper.i(showSpecValue(spec.value).toString()) +
        ' ' +
        (spec.scale || '');
    }
    return value;
  }
}
