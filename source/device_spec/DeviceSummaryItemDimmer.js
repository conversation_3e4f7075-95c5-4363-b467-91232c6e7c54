/* eslint-disable radix */
import React from 'react';
import {Text, View} from 'react-native';
import {Helper} from '../Helper';
import Slider from '@react-native-community/slider';
import {Tme, Colors} from '../ThemeStyle';
import DeviceControl from '../DeviceControl';

export default class DeviceSummaryItemDimmer extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      initialValue: this.parseInitValue(),
      value: this.parseValueByRealValue(this.props.spec.real_value),
      time: this.props.time,
      max: this.props.spec.max,
      min: this.props.spec.min,
    };
  }

  parseInitValue() {
    if (
      this.props.spec.dv_type == 'zigbee' ||
      this.props.spec.dv_type == 'matter_wifi' ||
      this.props.spec.dv_type == 'matter_thread'
    ) {
      return parseInt((100 / 255) * this.props.spec.value);
    } else {
      return parseInt(this.props.spec.real_value);
    }
  }
  parseValueByRealValue(real_value) {
    if (this.props.spec.dv_type == 'zwave') {
      var v = parseInt(real_value);
      if (isNaN(v) || v == 0) {
        return 0;
      } else if (v >= 99) {
        return 2;
      } else {
        return 1;
      }
    }
  }

  render() {
    return (
      <View>
        <View style={{marginTop: 8}}>
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 8,
              marginTop: 20,
            }}>
            <Text
              style={{
                fontSize: 22,
                fontWeight: '500',
                color: Tme('cardTextColor'),
              }}>
              {this.state.initialValue}
            </Text>
          </View>
          <Slider
            minimumValue={0}
            maximumValue={99}
            step={1}
            minimumTrackTintColor={Colors.MainColor}
            value={this.state.initialValue}
            onSlidingComplete={this.sliderDidChange.bind(this)}
          />
          <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
            <Text style={{color: Tme('cardTextColor')}}>{0}</Text>
            <Text style={{color: Tme('cardTextColor')}}>{99}</Text>
          </View>
        </View>
      </View>
    );
  }

  sliderDidChange(e) {
    this.setState(
      {
        initialValue: e,
        value: this.parseValueByRealValue(e),
        time: Helper.utc(),
      },
      () => {
        var param = this.parseParam(e);
        var deviceControl = new DeviceControl({
          spec: this.props.spec,
          param: param,
          sn_id: this.props.device.sn_id,
          runCMD: true,
        });
        deviceControl.dimmer();
      },
    );
  }

  parseParam(param) {
    if (
      this.props.spec.dv_type == 'zigbee' ||
      this.props.spec.dv_type == 'matter_wifi' ||
      this.props.spec.dv_type == 'matter_thread'
    ) {
      return parseInt((255 / 100) * param);
    } else {
      return param;
    }
  }

  // handleThreeBtnChange(e) {
  //   var status = e.nativeEvent.selectedSegmentIndex;
  //   this.setState({
  //     value: status,
  //     time: Helper.utc(),
  //   });
  //   var param = status == 0 ? 0 : 255;
  //   if (status == '2') {
  //     param = 99;
  //     this.setState({
  //       initialValue: 99,
  //     });
  //   } else if (status == '0') {
  //     this.setState({
  //       initialValue: 0,
  //     });
  //   }
  //   var deviceControl = new DeviceControl({
  //     spec: this.props.spec,
  //     param: param,
  //     sn_id: this.props.device.sn_id,
  //     runCMD: true,
  //   });
  //   deviceControl.dimmer();
  // }
}
