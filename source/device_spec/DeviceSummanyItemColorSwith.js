import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import I18n from '../I18n';
import { Tme } from '../ThemeStyle';
import tinycolor from 'tinycolor2';
import { observer } from 'mobx-react/native';
import { observable } from 'mobx';
import { Shadow } from 'react-native-shadow-2';
import { colorSwitchGetRgb } from '../Tools';
import DeviceControl from '../DeviceControl';
import PubSub from 'pubsub-js';
import { SELECT_COLOR_DONE } from '../types/PubSubEvent';
class Tool {
  @observable color = tinycolor('rgb(0,0,0)').toHsl();
}

@observer
class DeviceSummaryItemColorSwith extends React.Component {
  constructor(props) {
    super(props);
    this.tool = new Tool();
    this.state = {
      r: '0',
      g: '0',
      b: '0',
    };
  }

  componentDidMount() {
    let rgb = [];
    if (this.props.spec.dv_type == 'tuya') {
      const value_hash = this.props.spec.value_hash;
      const temp = tinycolor({
        h: value_hash[81],
        s: value_hash[82] / 10,
        v: value_hash[83] / 10,
      }).toRgb();
      rgb = [temp.r, temp.g, temp.b];
    } else {
      rgb = colorSwitchGetRgb(this.props.spec.value_hash);
    }

    this.setState(
      {
        r: rgb[0],
        g: rgb[1],
        b: rgb[2],
      },
      () => {
        this.tool.color = tinycolor(
          'rgb(' + rgb[0] + ',' + rgb[1] + ',' + rgb[2] + ')',
        ).toHsl();
      },
    );

    PubSub.subscribe(SELECT_COLOR_DONE, (msg, data) => {
      this.save(data);
    });
  }

  componentWillUnmount() {
    PubSub.unsubscribe(SELECT_COLOR_DONE);
  }

  render() {
    return (
      <View style={{ paddingTop: 20, paddingBottom: 20, paddingHorizontal: 20 }}>
        <Shadow
          style={{ alignSelf: 'stretch' }}
          startColor={tinycolor(
            'rgba(' +
            this.state.r +
            ',' +
            this.state.g +
            ',' +
            this.state.b +
            ', 0.1' +
            ')',
          ).toHex8String()}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.settingColor.bind(this)}
            style={[
              {
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                paddingVertical: 12,
                backgroundColor: Tme('cardColor'),
                borderRadius: 6,
              },
            ]}>
            <Text
              style={{
                color: Tme('textColor'),
                fontSize: 17,
                fontWeight: '500',
              }}>
              {I18n.t('device.change_color')}
            </Text>
          </TouchableOpacity>
        </Shadow>
      </View>
    );
  }

  settingColor() {
    this.tool.color = tinycolor(
      'rgb(' + this.state.r + ',' + this.state.g + ',' + this.state.b + ')',
    ).toHsl();
    this.props.navigation.push('SelectColorDrawer', {
      from: 'device',
      r: this.state.r,
      g: this.state.g,
      b: this.state.b,
    });
  }

  save(color) {
    var rgb = color.split(',');
    this.setState({
      r: rgb[0],
      g: rgb[1],
      b: rgb[2],
    });
    var deviceControl = new DeviceControl({
      spec: this.props.spec,
      color: rgb,
      sn_id: this.props.device.sn_id,
      runCMD: true,
    });
    deviceControl.colorSwitch();
  }
}
export default DeviceSummaryItemColorSwith;
