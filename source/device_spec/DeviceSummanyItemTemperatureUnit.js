/* eslint-disable radix */
import React from 'react';
import {View} from 'react-native';
import {Helper} from '../Helper';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import DeviceControl from '../DeviceControl';
import {Colors, Tme} from '../ThemeStyle';

export default class DeviceSummanyItemTemperatureUnit extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      value: parseInt(this.props.spec.value) || 0,
      time: this.props.time,
    };
  }

  render() {
    return (
      <View>
        <SegmentedControl
          tintColor={Colors.MainColor}
          values={['°C', '°F']}
          selectedIndex={this.state.value}
          fontStyle={{color: Tme('cardTextColor'), fontSize: 16}}
          activeFontStyle={{color: '#ffffff', fontSize: 16}}
          style={{flex: 1, height: 42}}
          onChange={this.handleRadioChange.bind(this)}
        />
      </View>
    );
  }

  handleRadioChange(e) {
    var status = e.nativeEvent.selectedSegmentIndex;
    this.setState({
      value: status,
      time: Helper.utc(),
    });
    var param = status;
    new DeviceControl({
      spec: this.props.spec,
      param: param,
      sn_id: this.props.device.sn_id,
      runCMD: true,
    }).temperatureunit();
  }
}
