/* eslint-disable radix */
import React from 'react';
import {Text, View, TouchableOpacity} from 'react-native';
import DeviceControl from '../DeviceControl';
import {Colors, Tme} from '../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Slider from '@react-native-community/slider';

export default class DeviceSummanyItemWindowCovering extends React.Component {
  constructor(props) {
    super(props);
    // spec.value === "" || "111" 如果 为空则为0 如果是0 则0 否者除以100
    const value =
      this.props.spec.value === '' ? 0 : parseInt(this.props.spec.value);
    console.log('value', value);
    this.state = {
      time: this.props.time,
      initialValue: value === 0 ? 0 : value / 100,

      value: value === 0 ? 0 : value / 100,
    };
  }

  render() {
    return (
      <View>
        <View
          style={{
            flex: 1,
            flexDirection: 'row',
            justifyContent: 'space-around',
            alignItems: 'center',
          }}>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.setMode.bind(this, 'up')}
            style={{
              marginRight: 16,
              paddingVertical: 10,
              paddingHorizontal: 16,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: Colors.MainColor,
              borderRadius: 8,
              marginTop: 16,
            }}>
            <MCIcons size={25} color="white" name="table-split-cell" />
          </TouchableOpacity>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.setMode.bind(this, 'stop')}
            style={{
              marginRight: 16,
              paddingVertical: 10,
              paddingHorizontal: 16,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: Colors.MainColor,
              borderRadius: 8,
              marginTop: 16,
            }}>
            <MaterialIcons size={25} color="white" name="pause" />
          </TouchableOpacity>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={this.setMode.bind(this, 'down')}
            style={{
              marginRight: 16,
              paddingVertical: 10,
              paddingHorizontal: 16,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: Colors.MainColor,
              borderRadius: 8,
              marginTop: 16,
            }}>
            <MCIcons size={25} color="white" name="table-merge-cells" />
          </TouchableOpacity>
        </View>
        <View style={{marginTop: 8}}>
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 8,
              marginTop: 20,
            }}>
            <Text
              style={{
                fontSize: 22,
                fontWeight: '500',
                color: Tme('cardTextColor'),
              }}>
              {this.state.initialValue}
            </Text>
          </View>
          <Slider
            minimumValue={0}
            maximumValue={100}
            step={1}
            minimumTrackTintColor={Colors.MainColor}
            value={this.state.initialValue}
            onSlidingComplete={this.sliderDidChange.bind(this)}
          />
          <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
            <Text style={{color: Tme('cardTextColor')}}>0</Text>
            <Text style={{color: Tme('cardTextColor')}}>100</Text>
          </View>
        </View>
      </View>
    );
  }

  setMode(type) {
    var deviceControl = new DeviceControl({
      spec: this.props.spec,
      param: type,
      param_type: 'mode',
      sn_id: this.props.device.sn_id,
      runCMD: true,
    });
    deviceControl.windowcovering();
  }

  sliderDidChange(e) {
    this.setState(
      {
        initialValue: e,
      },
      () => {
        let value = e * 100;
        var deviceControl = new DeviceControl({
          spec: this.props.spec,
          param_type: 'percentage',
          param: value,
          sn_id: this.props.device.sn_id,
          runCMD: true,
        });
        deviceControl.windowcovering();
      },
    );
  }
}
