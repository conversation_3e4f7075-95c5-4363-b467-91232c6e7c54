import React from 'react';
import {
  Text,
  // TouchableOpacity,
  View,
} from 'react-native';
import {Helper, HelperMemo} from '../Helper';
import Slider from '@react-native-community/slider';
import {Tme, Colors} from '../ThemeStyle';
import DeviceControl from '../DeviceControl';
// import Ionicons from 'react-native-vector-icons/Ionicons';

export default class DeviceSummaryItemThermostatSetpoint extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      initialValue: this.props.spec.value,
      time: this.props.time,
      max: this.props.spec.max == 0 ? 30 : this.props.spec.max,
      min: this.props.spec.min,
    };
  }

  componentDidMount() {
    var max = this.props.spec.max == 0 ? 30 : this.props.spec.max;
    this.setState({
      initialValue: Helper.temperatureScale(
        this.props.spec.value || 0,
        this.props.spec.unit_index,
        false,
      ),
      max: HelperMemo.user_data.user.scale == 'F' ? max * 1.8 + 32 : max,
      min: HelperMemo.user_data.user.scale == 'C' ? 0 : 32,
    });
  }

  // add() {
  //   if (this.state.initialValue >= this.state.max) {
  //     return;
  //   }

  //   this.setState(
  //     {
  //       initialValue: this.state.initialValue + 1,
  //     },
  //     () => {
  //       this.onAfterChange('add');
  //     },
  //   );
  // }

  // remove() {
  //   if (this.state.initialValue <= this.state.min) {
  //     return;
  //   }

  //   this.setState(
  //     {
  //       initialValue: this.state.initialValue - 1,
  //     },
  //     () => {
  //       this.onAfterChange('remove');
  //     },
  //   );
  // }

  render() {
    return (
      <View style={{marginTop: 8}}>
        {/* <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: 8,
            marginTop: 20,
          }}>
          <TouchableOpacity
            onPress={this.remove.bind(this)}
            activeOpacity={0.8}
            style={{
              marginRight: 20,
              borderWidth: 1,
              borderColor: Colors.MainColor,
              borderRadius: 5,
              padding: 5,
            }}>
            <Ionicons
              name="remove-outline"
              size={22}
              color={Colors.MainColor}
            />
          </TouchableOpacity>
          <Text
            style={{
              fontSize: 22,
              fontWeight: '500',
              color: Tme('cardTextColor'),
            }}>
            {this.state.initialValue} °{HelperMemo.user_data.user.scale}
          </Text>

          <TouchableOpacity
            onPress={this.add.bind(this)}
            activeOpacity={0.8}
            style={{
              marginLeft: 20,
              borderWidth: 1,
              borderColor: Colors.MainColor,
              borderRadius: 5,
              padding: 5,
            }}>
            <Ionicons name="add-outline" size={22} color={Colors.MainColor} />
          </TouchableOpacity>
        </View> */}

        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 8,
            marginTop: 20,
            flexDirection: 'row',
          }}>
          <Text
            style={{
              fontSize: 22,
              fontWeight: '500',
              color: Tme('cardTextColor'),
            }}>
            {this.state.initialValue}
          </Text>
          <Text
            style={{
              fontSize: 22,
              fontWeight: '500',
              color: Tme('cardTextColor'),
            }}>
            {' '}
            °{HelperMemo.user_data.user.scale}
          </Text>
        </View>

        <Slider
          minimumValue={this.state.min}
          maximumValue={this.state.max}
          step={1}
          minimumTrackTintColor={Colors.MainColor}
          // eslint-disable-next-line radix
          value={parseInt(this.state.initialValue)}
          onSlidingComplete={this.handleSliderChange.bind(this)}
        />
        <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
          <Text style={{color: Tme('cardTextColor')}}>
            {this.state.min} °{HelperMemo.user_data.user.scale}
          </Text>
          <Text style={{color: Tme('cardTextColor')}}>
            {this.state.max} °{HelperMemo.user_data.user.scale}
          </Text>
        </View>
      </View>
    );
  }

  handleSliderChange(e) {
    this.setState(
      {
        initialValue: e,
        time: Helper.utc(),
      },
      () => {
        this.onAfterChange(e);
      },
    );
  }

  onAfterChange(e) {
    var param = Helper.temperatureScale(e, this.props.spec.unit_index, true);
    let value = param * 100;

    new DeviceControl({
      spec: this.props.spec,
      param: value,
      sn_id: this.props.device.sn_id,
      setpoint_type: this.props.parent.state.setpoint_type,
      runCMD: true,
    }).thermostat_setpoint();
  }
}
