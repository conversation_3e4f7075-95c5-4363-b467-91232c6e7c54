import React from 'react';
import {View} from 'react-native';
import {Helper} from '../Helper';
import I18n from '../I18n';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import DeviceControl from '../DeviceControl';
import {Colors, Tme} from '../ThemeStyle';

export default class DeviceSummaryItemDoorLock extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      value: this.props.spec.value == 'Unlocked' ? 1 : 0,
      time: this.props.time,
      param: 0,
    };
  }

  render() {
    return (
      <View>
        <SegmentedControl
          tintColor={Colors.MainColor}
          values={[I18n.t('spec.lock'), I18n.t('spec.unlock')]}
          selectedIndex={this.state.value}
          fontStyle={{color: Tme('cardTextColor'), fontSize: 16}}
          activeFontStyle={{color: '#ffffff', fontSize: 16}}
          style={{flex: 1, height: 42}}
          onChange={this.handleRadioChange.bind(this)}
        />
      </View>
    );
  }

  handleRadioChange(e) {
    var status = e.nativeEvent.selectedSegmentIndex;

    this.setState({
      value: status,
      time: Helper.utc(),
    });
    // 0 = lock, 1 = unlock
    var param = status == 1 ? 0 : 255;
    new DeviceControl({
      spec: this.props.spec,
      param: param,
      sn_id: this.props.device.sn_id,
      runCMD: true,
    }).doorlock();
  }
}
