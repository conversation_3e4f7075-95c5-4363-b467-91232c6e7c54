import React from 'react';
import {Text, View} from 'react-native';
import {Helper} from '../Helper';
import {Colors, GetStyle} from '../ThemeStyle';
import ShadowView from '../share/ShadowView';

export default class DeviceSummanyItemBattery extends React.Component {
  showCss(spec) {
    let text_css = {
      color: '#00d18a',
    };
    let view_css = {
      backgroundColor: 'rgba(0, 209, 138, 0.2)',
    };
    // if (!spec) {
    //   return { text_css: text_css, view_css: view_css };
    // }
    // if (!spec.options) {
    //   return { text_css: text_css, view_css: view_css };
    // }

    var pattern3 = new RegExp('[0-9]+');
    if (pattern3.test(spec.value)) {
      if (spec.value >= 70) {
        text_css = {
          color: '#00d18a',
        };
        view_css = {
          backgroundColor: 'rgba(0, 209, 138, 0.2)',
        };
      } else if (spec.value < 50 && spec.value > 30) {
        text_css = {
          color: '#5eaaff',
        };
        view_css = {
          backgroundColor: 'rgba(94, 170, 255, 0.2)',
        };
      } else {
        text_css = {
          color: Colors.MainColor,
        };
        view_css = {
          backgroundColor: 'rgba(252, 87, 122, 0.2)',
        };
      }
    } else {
      if (
        spec.value.toLowerCase() == 'high' ||
        spec.value.toLowerCase() == 'normal'
      ) {
        text_css = {
          color: '#00d18a',
        };
        view_css = {
          backgroundColor: 'rgba(0, 209, 138, 0.2)',
        };
      } else if (spec.value.toLowerCase() == 'middle') {
        text_css = {
          color: '#5eaaff',
        };
        view_css = {
          backgroundColor: 'rgba(94, 170, 255, 0.2)',
        };
      } else {
        text_css = {
          color: Colors.MainColor,
        };
        view_css = {
          backgroundColor: 'rgba(252, 87, 122, 0.2)',
        };
      }
    }

    return {text_css: text_css, view_css: view_css};
  }

  render() {
    const {spec, device} = this.props;

    const css = this.showCss(spec);
    var valueText;
    if (device.dv_type == 'zigbee' || device.dv_type == 'tuya') {
      valueText = spec.value + (spec.scale ? spec.scale : '');
    } else if (device.dv_type == 'zwave') {
      valueText = spec.battery_charge_text;
    } else {
      valueText = Helper.i(spec.battery_charge_text);
    }
    return (
      <View
        style={{marginBottom: 20, marginHorizontal: 8}}
        // style={{
        //   marginBottom: 1,
        // }}
        key={'spec_' + spec.value_id}>
        <ShadowView viewStyle={{alignSelf: 'stretch'}}>
          <View style={GetStyle('spec')}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <Text style={GetStyle('specNameText')}>
                {Helper.i(spec.name)}
              </Text>
              <View
                style={[
                  {
                    borderRadius: 4,
                    padding: 8,
                  },
                  css.view_css,
                ]}>
                <Text
                  style={[
                    {fontWeight: '500', textAlign: 'center'},
                    css.text_css,
                  ]}>
                  {valueText}
                </Text>
              </View>
            </View>
          </View>
        </ShadowView>
      </View>
    );
  }
}
