import React from 'react';
import { View, Text } from 'react-native';
import { Helper, HelperMemo } from '../Helper';
import { showSpecValue } from '../Tools';
import { Colors, GetStyle } from '../ThemeStyle';
import ShadowView from '../share/ShadowView';

export default class DeviceSummaryItemDefault extends React.Component {
  constructor(props) {
    super(props);
  }

  render() {
    var that = this;
    const { spec } = this.props;
    var con;
    var text_css, view_css;
    if (showSpecValue(spec.value).toString() === 'No' || showSpecValue(spec.value).toString() === 'CLose') {
      text_css = {
        color: '#00d18a',
      };
      view_css = {
        backgroundColor: 'rgba(0, 209, 138, 0.2)',
      };
    } else if (showSpecValue(spec.value).toString() === 'Yes' || showSpecValue(spec.value).toString() === 'Open') {
      text_css = {
        color: Colors.MainColor,
      };
      view_css = {
        backgroundColor: 'rgba(252, 87, 122, 0.2)',
      };
    } else {
      text_css = {
        color: '#5eaaff',
      };
      view_css = {
        backgroundColor: 'rgba(94, 170, 255, 0.2)',
      };
    }
    con = (
      <View
        style={[
          {
            borderRadius: 4,
            padding: 8,
          },
          view_css,
        ]}>
        <Text style={[{ fontWeight: '500', textAlign: 'center' }, text_css]}>
          {that.value_scale(spec)}
        </Text>
      </View>
    );

    return (
      <View
        style={{ marginBottom: 20, marginHorizontal: 8 }}
        // style={{
        //   borderBottomColor: Tme('bgColor'),
        //   borderBottomWidth: 1,
        //   borderStyle: 'solid',
        // }}
        key={'spec_' + spec.value_id}>
        <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
          <View style={GetStyle('spec')}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <Text style={GetStyle('specNameText')}>
                {Helper.i(spec.name)}
              </Text>
              {con}
            </View>
          </View>
        </ShadowView>
      </View>
    );
  }

  value_scale(spec) {
    var value = '';
    if (spec.name.toUpperCase() == 'TEMPERATURE') {
      var unit_index = spec.unit_index;
      if (spec.unit_index !== 0 || 1) {
        switch (spec.scale) {
          case '°C':
            unit_index = 0;
            break;
          case '°F':
            unit_index = 1;
            break;
          default:
            unit_index = '';
            break;
        }
      }
      value =
        Helper.temperatureScale(spec.value, unit_index, false) +
        ' °' +
        HelperMemo.user_data.user.scale;
    } else {
      value =
        Helper.i(showSpecValue(spec.value).toString()) +
        ' ' +
        (spec.scale || '');
    }
    return value;
  }
}
