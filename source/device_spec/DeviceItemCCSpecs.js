import React from 'react';
import { Text, View, TouchableOpacity } from 'react-native';
import { Helper, HelperMemo } from '../Helper';
import _ from 'lodash';
import I18n from '../I18n';
import DeviceSummaryItemDimmer from './DeviceSummaryItemDimmer';
import DeviceSummaryItemSwitch from './DeviceSummaryItemSwitch';
import DeviceSummaryItemSwitchAll from './DeviceSummaryItemSwitchAll';
import DeviceSummaryItemThermostatMode from './DeviceSummaryItemThermostatMode';
import DeviceSummaryItemThermostatSetpoint from './DeviceSummaryItemThermostatSetpoint';
import DeviceSummaryItemDoorLock from './DeviceSummaryItemDoorLock';
import DeviceSummaryItemColorSwith from './DeviceSummanyItemColorSwith';
import DeviceSummanyItemColorTemperature from './DeviceSummanyItemColorTemperature';
import DeviceSummanyItemTemperatureUnit from './DeviceSummanyItemTemperatureUnit';
import DeviceSummanyItemWindowCovering from './DeviceSummanyItemWindowCovering';
import DeviceSummanyItemDefault from './DeviceSummanyItemDefault';
import DeviceSummanyItemOther from './DeviceSummanyItemOther';
import DeviceSummanyItemBattery from './DeviceSummanyItemBattery';

import { Tme, Colors, GetStyle, IsDark } from '../ThemeStyle';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import ActionSheet from '@alessiocancian/react-native-actionsheet';
import ShadowView from '../share/ShadowView';
// import Tools from '../Tools';
import AlertModal from '../share/AlertModal';
import IpcScanner from '../../IpcScanner';
import { Toast } from '../Toast';

export default class DeviceItemCCSpecs extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      actionSheet: [I18n.t('home.cancel'), I18n.t('home.add_qiock')],
      value_id: '',
      isFavor: false,
    };
    this.actionSheet = null;
  }

  onClick(spec) {
    this.props.navigation.push('eventScreen', {
      device_uuid: this.props.device.uuid,
      device_name: this.props.device.display_name,
      title: I18n.t('home.event'),
    });
  }

  render() {
    var content = '',
      that = this;
    if (!_.isEmpty(this.props.device)) {
      content = this.props.device.cc_specs.map(function (spec, key) {
        switch (spec.spec_type) {
          case 1:

            switch (spec.name) {
              case 'Battery':
                return (
                  <DeviceSummanyItemBattery
                    spec={spec}
                    navigation={that.props.navigation}
                    device={that.props.device}
                    key={'spec_' + spec.value_id}
                  />
                );
              default:
                return (
                  <DeviceSummanyItemDefault
                    navigation={that.props.navigation}
                    spec={spec}
                    key={'spec_' + spec.value_id}
                  />
                );
            }
          // }
          case 2:
            switch (spec.name) {
              case 'Switch':
                return (
                  <View
                    style={{
                      marginBottom: 20,
                      marginHorizontal: 8,
                    }}
                    key={'spec_' + spec.value_id}>
                    <ShadowView
                      viewStyle={{
                        alignSelf: 'stretch',
                      }}>
                      <View style={GetStyle('spec')}>
                        <View
                          style={{
                            marginBottom: 16,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}>
                          <Text style={GetStyle('specNameText')}>
                            {Helper.i('Switch')}
                          </Text>
                          {that.props.from == 'device' ? (
                            <TouchableOpacity
                              activeOpacity={0.8}
                              hitSlop={{
                                top: 20,
                                right: 20,
                                bottom: 20,
                                left: 20,
                              }}
                              onPress={that.clickMenu.bind(
                                that,
                                spec.value_id,
                              )}>
                              <MCIcons
                                name="dots-horizontal"
                                size={20}
                                color={Colors.MainColor}
                              />
                            </TouchableOpacity>
                          ) : null}
                        </View>

                        <DeviceSummaryItemSwitch
                          navigation={that.props.navigation}
                          key={that.props.device.last_received_update_time}
                          sn={that.props.sn}
                          spec={spec}
                          device={that.props.device}
                          time={that.props.device.last_received_update_time}
                        />
                      </View>
                    </ShadowView>
                  </View>
                );
              case 'SwitchAll':
                return (
                  <View
                    style={{ marginBottom: 20, marginHorizontal: 8 }}
                    key={'spec_' + spec.value_id}>
                    <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
                      <View style={GetStyle('spec')}>
                        <View
                          style={{
                            marginBottom: 16,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}>
                          <Text style={GetStyle('specNameText')}>
                            {Helper.i('SwitchAll')}
                          </Text>
                          {that.props.from == 'device' ? (
                            <TouchableOpacity
                              activeOpacity={0.8}
                              hitSlop={{
                                top: 20,
                                right: 20,
                                bottom: 20,
                                left: 20,
                              }}
                              onPress={that.clickMenu.bind(
                                that,
                                spec.value_id,
                              )}>
                              <MCIcons
                                name="dots-horizontal"
                                size={20}
                                color={Colors.MainColor}
                              />
                            </TouchableOpacity>
                          ) : null}
                        </View>
                        <View>
                          <DeviceSummaryItemSwitchAll
                            sn={that.props.sn}
                            navigation={that.props.navigation}
                            spec={spec}
                            device={that.props.device}
                            time={that.props.device.last_received_update_time}
                          />
                        </View>
                      </View>
                    </ShadowView>
                  </View>
                );
              case 'Dimmer':
              case 'Motor':
                return (
                  <View
                    style={{ marginBottom: 20, marginHorizontal: 8 }}
                    key={'spec_' + spec.value_id}>
                    <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
                      <View style={GetStyle('spec')}>
                        <View
                          style={{
                            marginBottom: 16,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}>
                          <Text style={GetStyle('specNameText')}>
                            {Helper.i(spec.name)}
                          </Text>
                          {that.props.from == 'device' ? (
                            <TouchableOpacity
                              activeOpacity={0.8}
                              hitSlop={{
                                top: 20,
                                right: 20,
                                bottom: 20,
                                left: 20,
                              }}
                              onPress={that.clickMenu.bind(
                                that,
                                spec.value_id,
                              )}>
                              <MCIcons
                                name="dots-horizontal"
                                size={20}
                                color={Colors.MainColor}
                              />
                            </TouchableOpacity>
                          ) : null}
                        </View>
                        <View>
                          <DeviceSummaryItemDimmer
                            sn={that.props.sn}
                            navigation={that.props.navigation}
                            spec={spec}
                            device={that.props.device}
                            time={that.props.device.last_received_update_time}
                          />
                        </View>
                      </View>
                    </ShadowView>
                  </View>
                );
              case 'ColorTemperature':
                return (
                  <View
                    style={{ marginBottom: 20, marginHorizontal: 8 }}
                    key={'spec_' + spec.value_id}>
                    <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
                      <View style={GetStyle('spec')}>
                        <View
                          style={{
                            marginBottom: 16,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}>
                          <Text style={GetStyle('specNameText')}>
                            {Helper.i(spec.name)}
                          </Text>
                          {that.props.from == 'device' ? (
                            <TouchableOpacity
                              activeOpacity={0.8}
                              hitSlop={{
                                top: 20,
                                right: 20,
                                bottom: 20,
                                left: 20,
                              }}
                              onPress={that.clickMenu.bind(
                                that,
                                spec.value_id,
                              )}>
                              <MCIcons
                                name="dots-horizontal"
                                size={20}
                                color={Colors.MainColor}
                              />
                            </TouchableOpacity>
                          ) : null}
                        </View>
                        <View>
                          <DeviceSummanyItemColorTemperature
                            sn={that.props.sn}
                            navigation={that.props.navigation}
                            spec={spec}
                            device={that.props.device}
                            time={that.props.device.last_received_update_time}
                          />
                        </View>
                      </View>
                    </ShadowView>
                  </View>
                );
              case 'ThermostatMode':
                return (
                  <View
                    style={{ marginBottom: 20, marginHorizontal: 8 }}
                    key={'spec_' + spec.value_id}>
                    <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
                      <View style={GetStyle('spec')}>
                        <View
                          style={{
                            marginBottom: 16,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}>
                          <Text style={GetStyle('specNameText')}>
                            {Helper.i(spec.name)}
                          </Text>
                          {that.props.from == 'device' ? (
                            <TouchableOpacity
                              activeOpacity={0.8}
                              hitSlop={{
                                top: 20,
                                right: 20,
                                bottom: 20,
                                left: 20,
                              }}
                              onPress={that.clickMenu.bind(
                                that,
                                spec.value_id,
                              )}>
                              <MCIcons
                                name="dots-horizontal"
                                size={20}
                                color={Colors.MainColor}
                              />
                            </TouchableOpacity>
                          ) : null}
                        </View>
                        <View>
                          <DeviceSummaryItemThermostatMode
                            parent={that.props.parent}
                            navigation={that.props.navigation}
                            sn={that.props.sn}
                            device={that.props.device}
                            spec={spec}
                            time={that.props.device.last_received_update_time}
                          />
                        </View>
                      </View>
                    </ShadowView>
                  </View>
                );
              case 'CoolingSetpoint':
              case 'HeatingSetpoint':
              case 'ThermostatSetpoint':
                let fromItem = 'setpoint';
                var mode = _.find(that.props.device.cc_specs, function (o) {
                  return o.name == 'ThermostatMode';
                });
                var show = false;
                if (mode) {
                  var modes = _.groupBy(mode.options, 'val');
                  if (spec.name == 'HeatingSetpoint') {
                    if (modes['4'] || modes.Heat) {
                      if (
                        that.props.parent.state.setpoint_type === 4 ||
                        that.props.parent.state.setpoint_type === 'Heat'
                      ) {
                        show = true;
                      }
                      fromItem = 'Heat';
                    }
                  }
                  if (spec.name == 'CoolingSetpoint') {
                    if (modes['3'] || modes.Cool) {
                      if (
                        that.props.parent.state.setpoint_type === 3 ||
                        that.props.parent.state.setpoint_type === 'Cool'
                      ) {
                        show = true;
                      }
                      fromItem = 'Cool';
                    }
                  }
                }
                if (spec.name == 'ThermostatSetpoint') {
                  show = true;
                }
                if (show) {
                  return (
                    <View
                      style={{ marginBottom: 20, marginHorizontal: 8 }}
                      key={'spec_' + spec.value_id}>
                      <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
                        <View style={GetStyle('spec')}>
                          <View
                            style={{
                              marginBottom: 16,
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                            }}>
                            <Text style={GetStyle('specNameText')}>
                              {Helper.i(spec.name)}
                            </Text>
                            {that.props.from == 'device' ? (
                              <TouchableOpacity
                                activeOpacity={0.8}
                                hitSlop={{
                                  top: 20,
                                  right: 20,
                                  bottom: 20,
                                  left: 20,
                                }}
                                onPress={that.clickMenu.bind(
                                  that,
                                  spec.value_id,
                                )}>
                                <MCIcons
                                  name="dots-horizontal"
                                  size={20}
                                  color={Colors.MainColor}
                                />
                              </TouchableOpacity>
                            ) : null}
                          </View>
                          <View>
                            <DeviceSummaryItemThermostatSetpoint
                              fromItem={fromItem}
                              parent={that.props.parent}
                              sn={that.props.sn}
                              device={that.props.device}
                              navigation={that.props.navigation}
                              spec={spec}
                              time={that.props.device.last_received_update_time}
                            />
                          </View>
                        </View>
                      </ShadowView>
                    </View>
                  );
                } else {
                  return null;
                }
              case 'TemperatureUnit':
                if (
                  spec.dv_type === 'matter_wifi' ||
                  spec.dv_type === 'matter_thread'
                ) {
                  return (
                    <View
                      style={{ marginBottom: 20, marginHorizontal: 8 }}
                      key={'spec_' + spec.value_id}>
                      <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
                        <View style={GetStyle('spec')}>
                          <View
                            style={{
                              marginBottom: 16,
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                            }}>
                            <Text style={GetStyle('specNameText')}>
                              {Helper.i(spec.name)}
                            </Text>
                            {that.props.from == 'device' ? (
                              <TouchableOpacity
                                activeOpacity={0.8}
                                hitSlop={{
                                  top: 20,
                                  right: 20,
                                  bottom: 20,
                                  left: 20,
                                }}
                                onPress={that.clickMenu.bind(
                                  that,
                                  spec.value_id,
                                )}>
                                <MCIcons
                                  name="dots-horizontal"
                                  size={20}
                                  color={Colors.MainColor}
                                />
                              </TouchableOpacity>
                            ) : null}
                          </View>
                          <View>
                            <DeviceSummanyItemTemperatureUnit
                              sn={that.props.sn}
                              navigation={that.props.navigation}
                              spec={spec}
                              device={that.props.device}
                              time={that.props.device.last_received_update_time}
                            />
                          </View>
                        </View>
                      </ShadowView>
                    </View>
                  );
                } else {
                  return null;
                }
              case 'DoorLock':
                return (
                  <View
                    style={{ marginBottom: 20, marginHorizontal: 8 }}
                    key={'spec_' + spec.value_id}>
                    <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
                      <View style={GetStyle('spec')}>
                        <View
                          style={{
                            marginBottom: 16,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}>
                          <Text style={GetStyle('specNameText')}>
                            {Helper.i(spec.name)}
                          </Text>
                          {that.props.from == 'device' ? (
                            <TouchableOpacity
                              activeOpacity={0.8}
                              hitSlop={{
                                top: 20,
                                right: 20,
                                bottom: 20,
                                left: 20,
                              }}
                              onPress={that.clickMenu.bind(
                                that,
                                spec.value_id,
                              )}>
                              <MCIcons
                                name="dots-horizontal"
                                size={20}
                                color={Colors.MainColor}
                              />
                            </TouchableOpacity>
                          ) : null}
                        </View>
                        <View>
                          <DeviceSummaryItemDoorLock
                            sn={that.props.sn}
                            spec={spec}
                            navigation={that.props.navigation}
                            device={that.props.device}
                            time={spec.u_t}
                          />
                        </View>
                      </View>
                    </ShadowView>
                  </View>
                );
              case 'ColorSwitch':
                if (spec.dv_type == 'zigbee' || spec.dv_type == 'tuya') {
                  return (
                    <View
                      style={{ marginBottom: 20, marginHorizontal: 8 }}
                      key={'spec_' + spec.value_id}>
                      <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
                        <View style={GetStyle('spec')}>
                          <View
                            style={{
                              marginBottom: 16,
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                            }}>
                            <Text style={GetStyle('specNameText')}>
                              {Helper.i(spec.name)}
                            </Text>
                            {that.props.from == 'device' ? (
                              <TouchableOpacity
                                activeOpacity={0.8}
                                hitSlop={{
                                  top: 20,
                                  right: 20,
                                  bottom: 20,
                                  left: 20,
                                }}
                                onPress={that.clickMenu.bind(
                                  that,
                                  spec.value_id,
                                )}>
                                <MCIcons
                                  name="dots-horizontal"
                                  size={20}
                                  color={Colors.MainColor}
                                />
                              </TouchableOpacity>
                            ) : null}
                          </View>
                          <DeviceSummaryItemColorSwith
                            key={'spec_' + Math.random().toString()}
                            parent={that.props.parent}
                            navigation={that.props.navigation}
                            sn={that.props.sn}
                            spec={spec}
                            device={that.props.device}
                            time={spec.u_t}
                          />
                        </View>
                      </ShadowView>
                    </View>
                  );
                } else {
                  return (
                    <View
                      style={{ marginBottom: 20, marginHorizontal: 8 }}
                      key={'spec_' + spec.value_id}>
                      <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
                        <View style={GetStyle('spec')}>
                          <View
                            style={{
                              marginBottom: 16,
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                            }}>
                            <Text style={GetStyle('specNameText')}>
                              {Helper.i(spec.name)}
                            </Text>
                            {that.props.from == 'device' ? (
                              <TouchableOpacity
                                activeOpacity={0.8}
                                hitSlop={{
                                  top: 20,
                                  right: 20,
                                  bottom: 20,
                                  left: 20,
                                }}
                                onPress={that.clickMenu.bind(
                                  that,
                                  spec.value_id,
                                )}>
                                <MCIcons
                                  name="dots-horizontal"
                                  size={20}
                                  color={Colors.MainColor}
                                />
                              </TouchableOpacity>
                            ) : null}
                          </View>
                          <DeviceSummaryItemColorSwith
                            key={'spec_' + Math.random().toString()}
                            parent={that.props.parent}
                            sn={that.props.sn}
                            navigation={that.props.navigation}
                            spec={spec}
                            device={that.props.device}
                            time={spec.u_t}
                          />
                        </View>
                      </ShadowView>
                    </View>
                  );
                }
              case 'WindowCovering':
                if (
                  spec.dv_type === 'zigbee' ||
                  spec.dv_type === 'matter_wifi' ||
                  spec.dv_type === 'matter_thread'
                ) {
                  return (
                    <View
                      style={{ marginBottom: 20, marginHorizontal: 8 }}
                      key={'spec_' + spec.value_id}>
                      <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
                        <View style={GetStyle('spec')}>
                          <View
                            style={{
                              marginBottom: 16,
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                            }}>
                            <Text style={GetStyle('specNameText')}>
                              {Helper.i(spec.name)}
                            </Text>
                            {that.props.from == 'device' ? (
                              <TouchableOpacity
                                activeOpacity={0.8}
                                hitSlop={{
                                  top: 20,
                                  right: 20,
                                  bottom: 20,
                                  left: 20,
                                }}
                                onPress={that.clickMenu.bind(
                                  that,
                                  spec.value_id,
                                )}>
                                <MCIcons
                                  name="dots-horizontal"
                                  size={20}
                                  color={Colors.MainColor}
                                />
                              </TouchableOpacity>
                            ) : null}
                          </View>
                          <DeviceSummanyItemWindowCovering
                            key={'spec_' + Math.random().toString()}
                            parent={that.props.parent}
                            sn={that.props.sn}
                            navigation={that.props.navigation}
                            spec={spec}
                            device={that.props.device}
                            time={spec.u_t}
                          />
                        </View>
                      </ShadowView>
                    </View>
                  );
                } else {
                  return null;
                }
              case 'Camera':
                return (
                  <View
                    style={{ marginBottom: 20, marginHorizontal: 8 }}
                    key={'spec_' + spec.value_id}>
                    <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
                      <View style={GetStyle('spec')}>
                        <View
                          style={{
                            marginBottom: 16,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}>
                          <Text style={{ color: Tme('cardTextColor') }}>
                            {spec.video_url}
                          </Text>
                          {that.props.from == 'device' ? (
                            <TouchableOpacity
                              activeOpacity={0.8}
                              hitSlop={{
                                top: 20,
                                right: 20,
                                bottom: 20,
                                left: 20,
                              }}
                              onPress={that.clickMenu.bind(
                                that,
                                spec.value_id,
                              )}>
                              <MCIcons
                                name="dots-horizontal"
                                size={20}
                                color={Colors.MainColor}
                              />
                            </TouchableOpacity>
                          ) : null}
                        </View>
                        <View>
                          <TouchableOpacity
                            activeOpacity={0.8}
                            onPress={that.cameraPlay.bind(that)}
                            style={{
                              flexDirection: 'row',
                              justifyContent: 'center',
                              alignItems: 'center',
                              backgroundColor: Colors.MainColor,
                              borderRadius: 4,
                              paddingVertical: 10,
                            }}>
                            <Text style={{ color: 'white' }}>
                              {I18n.t('device.play')}
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </ShadowView>
                  </View>
                );
              default:
                if (spec.dv_type == 'tuya') {
                  return (
                    <DeviceSummanyItemOther
                      navigation={that.props.navigation}
                      spec={spec}
                      key={'spec_' + spec.value_id}
                    />
                  );
                } else {
                  return null;
                }
            }
          // }
          case 3:
            return (
              <View
                style={{ marginBottom: 20, marginHorizontal: 8 }}
                key={'spec_' + spec.value_id}>
                <ShadowView viewStyle={{ alignSelf: 'stretch' }}>
                  <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={that.onClick.bind(that, spec)}
                    style={[GetStyle('spec')]}>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}>
                      <Text style={GetStyle('specNameText')}>
                        {Helper.i(spec.name)}
                      </Text>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}>
                        <Text style={[{ textAlign: 'center' }]}>
                          {Helper.formatAllTime(spec.u_t)}
                        </Text>
                        <MaterialIcons
                          name="keyboard-arrow-right"
                          size={20}
                          color={Tme('textColor')}
                        />
                      </View>
                    </View>
                  </TouchableOpacity>
                </ShadowView>
              </View>
            );
        }
      });
    } else {
      content = null;
    }

    return (
      <View style={{ paddingHorizontal: 12, marginTop: 10 }}>
        {content}
        <ActionSheet
          ref={o => (this.actionSheet = o)}
          options={this.state.actionSheet}
          cancelButtonIndex={0}
          userInterfaceStyle={IsDark() ? 'dark' : 'light'}
          theme="ios"
          styles={{
            cancelButtonBox: {
              height: 50,
              marginTop: 6,
              alignItems: 'center',
              justifyContent: 'center',
            },
            buttonBox: {
              height: 50,
              alignItems: 'center',
              justifyContent: 'center',
            },
          }}
          onPress={index => {
            this.sheetClick(index - 1);
          }}
        />
      </View>
    );
  }

  sheetClick(index) {
    if (index == 0) {
      this.onMenu();
    }
  }

  clickMenu(value_id) {
    var that = this;
    var favors = this.props.device.favors;
    var include = _.includes(favors, value_id);
    this.setState({ value_id: value_id, isFavor: include }, () => {
      that.setState(
        {
          actionSheet: [
            I18n.t('home.cancel'),
            include ? I18n.t('home.remove_qiock') : I18n.t('home.add_qiock'),
          ],
        },
        () => {
          this.actionSheet.show();
        },
      );
    });
  }

  onMenu() {
    Helper.httpPOST(
      '/partner/devices/spec_favor',
      {
        ensure: () => {
          Toast.show();
        },
        success: data => {
          this.props.parent.doFetchData();
        },
        error: data => {
          AlertModal.alert(I18n.t('home.try_again'));
        },
      },
      {
        uuid: this.props.device.uuid,
        value_id: this.state.value_id,
        is_favor: this.state.isFavor,
      },
    );
  }

  cameraPlay() {
    const scan = IpcScanner.getScanResults();
    const item = this.props.device;
    let index = 0;
    if (HelperMemo.ipc_is_local_play) {
      index = _.findIndex(scan, function (o) { return o.sn === item.sn; });
    } else {
      index = 0;
    }

    if (index !== -1) {
      this.props.navigation.push('LocalCameraShow', {
        host: scan[index].ip,
        clientid: item.ipc.webrtc_uuid,
        node: item,
        title: I18n.t('ipc.local_play'),
      });
    } else {
      this.props.navigation.push('IpcCameraShow', {
        title: item.display_name,
        node: item,
      });
    }

  }

  handleSuccess() {
    Toast.show();
  }
}
