/* eslint-disable radix */
import React from 'react';
import {Text, View} from 'react-native';
import {Helper} from '../Helper';
import Slider from '@react-native-community/slider';
import {Tme, Colors} from '../ThemeStyle';
import DeviceControl from '../DeviceControl';

export default class DeviceSummanyItemColorTemperature extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      initialValue: this.parseInitValue(),
      value: this.props.spec.value ? this.props.spec.value : 0,
      time: this.props.time,
      max: this.props.spec.max,
      min: this.props.spec.min,
    };
  }

  parseInitValue() {
    if (this.props.spec.dv_type == 'zigbee') {
      return parseInt((this.props.spec.value ? this.props.spec.value : 0) / 10);
    } else {
      return parseInt(this.props.spec.value);
    }
  }

  render() {
    return (
      <View>
        <View style={{marginTop: 8}}>
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 8,
              marginTop: 20,
            }}>
            <Text
              style={{
                fontSize: 22,
                fontWeight: '500',
                color: Tme('cardTextColor'),
              }}>
              {this.state.initialValue}
            </Text>
          </View>
          <Slider
            minimumValue={Number(this.state.min)}
            maximumValue={
              Number(this.state.max) > 0 ? Number(this.state.max) : 99
            }
            step={1}
            minimumTrackTintColor={Colors.MainColor}
            value={this.state.initialValue}
            onChange={this.handleSliderChange.bind(this)}
            onSlidingComplete={this.sliderDidChange.bind(this)}
          />
          <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
            <Text style={{color: Tme('cardTextColor')}}>{this.state.min}</Text>
            <Text style={{color: Tme('cardTextColor')}}>
              {Number(this.state.max) > 0 ? Number(this.state.max) : 99}
            </Text>
          </View>
        </View>
      </View>
    );
  }

  sliderDidChange(e) {
    this.handleSliderChange(e);
    var param = this.parseParam(e);
    var deviceControl = new DeviceControl({
      spec: this.props.spec,
      param: param,
      sn_id: this.props.device.sn_id,
      runCMD: true,
    });
    deviceControl.color_Temperature();
  }

  handleSliderChange(e) {
    this.setState({
      initialValue: e,
      value: e,
      time: Helper.utc(),
    });
  }

  parseParam(param) {
    if (this.props.spec.dv_type == 'zigbee') {
      return parseInt(param * 10);
    } else {
      return param;
    }
  }
}
