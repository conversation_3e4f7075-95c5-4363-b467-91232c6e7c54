import React from 'react';
import { Text, View } from 'react-native';
import { Tme } from '../ThemeStyle';
import { getDeviceType } from '../Tools';

export default class DeviceType extends React.Component {
  constructor(props) {
    super(props);
  }

  render() {
    return (
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <Text style={{ fontSize: 14, color: Tme('cardTextColor') }}>
          {this.props.device
            ? getDeviceType(this.props.device.dv_type)
            : null}
        </Text>
      </View>
    );
  }
}
