import React from 'react';
import {Helper} from '../Helper';
import I18n from '../I18n';
import SegmentedControl from '@react-native-segmented-control/segmented-control';
import DeviceControl from '../DeviceControl';
import {Colors, Tme} from '../ThemeStyle';

export default class DeviceSummaryItemSwitchAll extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      value: this.props.spec.value == 'Off' ? 0 : 1,
      time: this.props.time,
    };
  }

  render() {
    return (
      <SegmentedControl
        tintColor={Colors.MainColor}
        values={[I18n.t('spec.off_b'), I18n.t('spec.on_b')]}
        selectedIndex={this.state.value}
        fontStyle={{color: Tme('cardTextColor'), fontSize: 16}}
        activeFontStyle={{color: '#ffffff', fontSize: 16}}
        style={{flex: 1, height: 42}}
        onChange={this.switchToggle.bind(this)}
      />
    );
  }

  switchToggle(e) {
    var status = e.nativeEvent.selectedSegmentIndex;
    // if (status == this.props.spec.value) return;

    this.setState({
      value: status,
      time: Helper.utc(),
    });

    // var param = status == "Off" ? "SetOff()" : "SetOn()";
    var param = status == 1 ? 0 : 255;
    new DeviceControl({
      spec: this.props.spec,
      param: param,
      sn_id: this.props.device.sn_id,
      runCMD: true,
    }).switch();
  }
}
