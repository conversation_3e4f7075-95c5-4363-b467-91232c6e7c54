import {
  observable,
} from 'mobx';
import _ from 'lodash';

export default class Notify {

  @observable notifys = [];
  @observable scenes = [];

  @observable device_ids = [];
  @observable scene_ids = [];
  @observable audio_path = '';
  @observable from = '';

  constructor(props) {
    Object.assign(this, {}, props);
  }

  set_device_ids(devices) {
    _.each(devices, (spec, k) => {
      this.device_ids.push('spec_' + spec.index + '_' + spec.value_id + '_' + spec.sn_id + '_' + spec.spec_name);
    });
  }
}
