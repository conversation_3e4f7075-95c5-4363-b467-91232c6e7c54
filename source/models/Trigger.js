import {
  observable,
  action,
} from 'mobx';
import {
  DeviceTarget,
} from './DeviceTarget';
import _ from 'lodash';
import {
  DeviceCondition,
} from './DeviceCondition';

export default class Trigger {
  @observable triggers = [];
  @observable name = '';
  @observable id = '';
  @observable scene_ids = [];
  @observable is_enabled = true;
  @observable scenes = [];
  @observable targets = [];
  @observable conditions = [];
  @observable view_show = false;
  @observable from = '2019';

  constructor(props) {

  }

  set spec_targets(props) {
    if (props) {
      var targets = [];
      _.each(props, (v, k) => {
        targets.push(new DeviceTarget(v));
      });
      this.targets = targets;
    }
  }

  set spec_conditions(props) {
    if (props) {
      var conditions = [];
      _.each(props, (v, k) => {
        conditions.push(new DeviceCondition(v));
      });
      this.conditions = conditions;
    }
  }

  @action op_scene_names(name) {
    if (_.includes(this.scene_names, name)) {
      this.scene_names.remove(name);
    } else {
      this.scene_names.push(name);
    }
  }

}
