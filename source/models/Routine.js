import {observable} from 'mobx';

import {DeviceTarget} from './DeviceTarget';
import _ from 'lodash';

export default class Routine {
  @observable routins = [];
  @observable scenes = [];
  @observable actions = [];
  @observable devices = [];
  @observable rooms = [];
  @observable radio_scenes = [];

  @observable name = '';
  @observable is_enabled = true;
  @observable type = 'time';

  @observable week_day = [];
  @observable is_cycle = true;
  @observable is_returned = true;
  @observable is_sunrise = true;
  @observable begin_at = '00:00';
  @observable end_at = '23:59';

  @observable lat = '';
  @observable lng = '';
  @observable address = '';
  @observable location = [];

  @observable uuid = '';
  @observable scene_ids = [];

  @observable target_uuid = '';
  @observable target_type = 'device';
  @observable select_sn = '';

  @observable targets = [];
  @observable conditions = [];

  @observable home_address = {};
  @observable external_data = {
    app_url: '',
    delay: 0,
  };

  constructor(props) {
    Object.assign(this, {}, props);
  }

  spec_targets(props) {
    if (props) {
      var targets = [];
      _.each(props, (v, k) => {
        targets.push(new DeviceTarget(v));
      });
      this.targets = targets;
    }
  }

  spec_conditions(props) {
    if (props) {
      var conditions = [];
      _.each(props, (v, k) => {
        conditions.push(new DeviceTarget(v));
      });
      this.conditions = conditions;
    }
  }
}
