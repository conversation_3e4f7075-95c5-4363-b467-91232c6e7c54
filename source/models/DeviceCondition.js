import {
  observable,
} from 'mobx';

export class DeviceCondition {
  @observable checked = false;
  @observable node_id = '';
  @observable instance_id = '';
  @observable spec_cc = '';
  @observable spec_name = '';
  @observable spec_value = '';
  @observable logic = '';
  @observable params = '';
  @observable commands = '';
  @observable sn_id = '';
  @observable sn = '';
  @observable home_id = '';
  @observable target_type = '';
  type = 'condition';
  new = false;

  constructor(props) {
    Object.assign(this, {}, props);
    if (props) {
      if (props.node_id) {
        this.checked = true;
      }
    }
  }
}
