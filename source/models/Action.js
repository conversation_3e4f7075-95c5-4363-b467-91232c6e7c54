import {
  observable,
  action,
} from 'mobx';

import {
  DeviceTarget,
} from './DeviceTarget';
import _ from 'lodash';

export default class Action {

  @observable actions = [];
  @observable name = '';
  @observable targets = [];
  @observable check_all = [];
  @observable icon = 'icons8-confetti-96.png';
  id = '';

  @observable updateKey = '';

  @action renderTrigger = () => {
    this.updateKey = Math.random();
  };

  constructor(props) {
    Object.assign(this, {}, props);
  }

  spec_targets(props) {
    if (props) {
      var targets = [];
      _.each(props, (v, k) => {
        targets.push(new DeviceTarget(v));
      });
      this.targets = targets;
    }
  }

}
