import React from 'react';
import NetInfo from '@react-native-community/netinfo';
import {HelperMemo} from './Helper';

export default class NetworkHandler extends React.Component {
  constructor(props) {
    super(props);
  }

  componentDidMount() {
    // whether internet is connected
    // none - device is offline
    // wifi - device is online and connected via wifi, or is the iOS simulator
    // cellular - device is connected via Edge, 3G, WiMax, or LTE
    // unknown
    NetInfo.fetch().then(state => {
      HelperMemo.isConnected = state.isConnected;
      HelperMemo.connectionType = state.type.toLowerCase();
    });
    this.unsubscribe = NetInfo.addEventListener(state => {
      HelperMemo.connectionType = state.type.toLowerCase();
    });
  }

  componentWillUnmount() {
    this.unsubscribe();
  }

  render() {
    return null;
  }
}
