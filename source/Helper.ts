/* eslint-disable no-useless-escape */
/* eslint-disable radix */
import {
  Alert,
  Dimensions,
  NativeModules,
  PixelRatio,
  Platform,
  StatusBar,
} from 'react-native';
import _ from 'lodash';
import I18n from './I18n';
import DeviceInfo from 'react-native-device-info';
import BackgroundGeolocation from 'react-native-background-geolocation';
import AppConfig from '../app_config';
import {NotificationCenter, DASHBOARD_REFRESH} from './NotificationCenter';
import momentTZ from 'moment-timezone';
import * as zh_cn from './moment/zh-cn';
import * as en from './moment/en-gb';
import * as de from './moment/de';
import * as fr from './moment/fr';
import * as ru from './moment/ru';
import * as pt from './moment/pt';
import * as es from './moment/es';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios, {AxiosRequestConfig, CancelTokenSource} from 'axios';
import Scode from './Scode';
import AlertModal from './share/AlertModal';
import PubSub from 'pubsub-js';
import {PubSubEvent} from './types/PubSubEvent';

const TRANS_MISSING = 'missing';
const innerLinkRE =
  /(^127\.)|(^192\.168\.)|(^10\.)|(^172\.1[6-9]\.)|(^172\.2[0-9]\.)|(^172\.3[0-1]\.)|(^::1$)|(^[fF][cCdD])/;

interface Callbacks {
  success?: (data: any) => void;
  error?: (errors: any, type?: string, errorType?: string) => void;
  ensure?: () => void;
  systemError?: () => void;
  timeoutError?: () => void;
  ignore_error?: boolean;
  apiPrefix?: string;
  apiUrl?: string;
  successCb?: boolean;
}

interface ApiHeaders {
  headers: {
    X_IOT_UUID?: string;
    X_IOT_HOME?: string;
    X_IOT_UA?: string;
    X_IOT_SOURCE?: string;
    sessionid?: string | '';
    'Content-Type': string;
  };
}

interface BackgroundGeolocationConfig {
  url: string;
  params: {
    os: string;
    user_uuid: string;
    home_id: string;
    device_token: string;
  };
  stopOnTerminate: boolean;
  debug: boolean;
  startOnBoot: boolean;
  desiredAccuracy: number;
  distanceFilter: number;
  disableLocationAuthorizationAlert: boolean;
  autoSync: boolean;
  geofenceModeHighAccuracy: boolean;
  geofenceInitialTriggerEntry: boolean;
  notification: {
    title: string;
    text: string;
    priority: number;
    smallIcon: string;
  };
}

function isIphoneX(): boolean {
  const dimen = Dimensions.get('window');
  return (
    Platform.OS === 'ios' &&
    !Platform.isPad &&
    !Platform.isTV &&
    (dimen.height === 812 ||
      dimen.width === 812 ||
      dimen.height === 844 ||
      dimen.width === 844 ||
      dimen.height === 896 ||
      dimen.width === 896 ||
      dimen.height === 926 ||
      dimen.width === 926 ||
      dimen.height === 852 ||
      dimen.width === 852)
  );
}

function getStatusBarHeight(): number {
  return isIphoneX() ? 46 : 30;
}

const status_bar: number =
  Platform.OS === 'ios' ? getStatusBarHeight() : StatusBar.currentHeight || 0;
const bottom_bar: number = Platform.OS == 'ios' ? (isIphoneX() ? 34 : 0) : 0;

export const HelperMemo = {
  STATUS_BAR_HEIGHT: status_bar,
  NAV_BAR_HEIGHT: Platform.OS == 'ios' ? 42 : 56,
  DASHBOARD_SN_ID: '_dashboard_',
  BOTTOM_BAR_HEIGHT: bottom_bar,
  WiNDOW_HEIGHT:
    Dimensions.get('window').height -
    (Platform.OS == 'ios' ? status_bar + 44 + bottom_bar : 56),
  theme: undefined as 'unspecified' | 'light' | 'dark' | undefined,
  user_data: undefined as any,
  lang: undefined as string | undefined,
  device_token: undefined as string | undefined,
  dashboard_sessionid: undefined as string | undefined,
  dashboard_ip: undefined as string | undefined,
  message: undefined as any,
  unify_commands_schema: undefined as any,
  select_home: '' as string,
  ipc_is_local_play: false as boolean,
  dashboard: undefined as {sn: string; ip: string} | undefined,
};

const version = DeviceInfo.getVersion();

export const Helper = {
  BackgroundGeolocationRadius: 200,

  _api_headers(): ApiHeaders {
    return {
      headers: {
        X_IOT_UUID: HelperMemo.user_data ? HelperMemo.user_data.uuid : '',
        X_IOT_HOME: HelperMemo.user_data ? HelperMemo.user_data.home_id : '',
        X_IOT_UA: `presen/${Platform.OS}/${version}`,
        X_IOT_SOURCE: 'api',
        'Content-Type': 'application/json',
      },
    };
  },

  async _sendRequest(
    method: string,
    path: string,
    callbacks?: Callbacks,
    body?: any,
  ): Promise<void> {
    if (!callbacks) {
      callbacks = {};
    }
    const url = await this.hostByNetwork(path, callbacks);
    const time = new Date().getTime();
    const tempUrl = url.split('?')[0];
    const tempUrl2 = tempUrl.split('iot')[1];
    const tempDate = `apikey${AppConfig.api_key}method${method}path/iot${tempUrl2}timestamp${time}`;
    const source: CancelTokenSource = axios.CancelToken.source();

    setTimeout(() => {
      source.cancel();
    }, 15000);

    console.log('url', url);

    Scode.code(tempDate.toLocaleUpperCase(), (scode: string) => {
      const options: AxiosRequestConfig = {
        timeout: 16000,
        method: method,
        url: url,
        cancelToken: source.token,
      };

      const params = {
        lang: HelperMemo.lang,
        scopes: AppConfig.scopes,
        scode: scode.toLocaleUpperCase(),
        apikey: AppConfig.api_key,
        timestamp: time,
      };

      Object.assign(options, this._api_headers());

      if (method == 'POST') {
        if (body) {
          options.data = Object.assign(params, body);
        } else {
          options.data = params;
        }
      }
      if (method == 'GET') {
        options.params = params;
      }

      axios(options)
        .then(response => {
          if (response.status == 200) {
            const data = response.data;
            if (data.status == 'ok') {
              if (callbacks?.success) {
                // 成功时发布隐藏错误视图事件
                PubSub.publish(PubSubEvent.HIDE_ERROR);
                callbacks.success(data.data);
              }
            } else {
              if (data.type == 'force_login') {
                AlertModal.alert('', I18n.t('global.force_login'), [
                  {
                    text: I18n.t('home.confirm'),
                    onPress: () => {
                      this.httpPOST(
                        '/users/signout',
                        {
                          success: () => {
                            AsyncStorage.removeItem('user_data', () => {
                              HelperMemo.user_data = null;
                              PubSub.publish(PubSubEvent.RESTART_APP);
                            });
                          },
                        },
                        {device_type: Platform.OS},
                      );
                    },
                  },
                  {userInterfaceStyle: HelperMemo.theme},
                ]);
              } else {
                if (callbacks?.error) {
                  callbacks.error(data.errors, data.type);
                } else {
                  if (data.errors) {
                    AlertModal.alert(
                      I18n.t('home.warning_message'),
                      _.flatten([data.errors]).join('\n'),
                    );
                  } else {
                    Alert.alert(
                      I18n.t('home.warning_message'),
                      'Network issue, please try again',
                      undefined,
                      {
                        userInterfaceStyle: HelperMemo.theme,
                      },
                    );
                  }
                }
              }
            }
            if (callbacks?.ensure) {
              callbacks.ensure();
            }
          } else {
            if (callbacks?.systemError) {
              callbacks.systemError();
            } else {
              AlertModal.alert(
                I18n.t('home.warning_message'),
                'Network issue, please try again',
              );
            }

            if (callbacks?.ensure) {
              callbacks.ensure();
            }
          }
        })
        .catch(error => {
          console.log('netWorkError', error);
          if (!callbacks?.ignore_error) {
            if (error.code === 'ECONNABORTED' || error.message === 'canceled') {
              if (callbacks?.timeoutError) {
                callbacks.timeoutError();
              } else {
                AlertModal.alert(
                  I18n.t('home.network_issue'),
                  I18n.t('home.network_issue_desp'),
                );
              }
            } else {
              if (callbacks?.error) {
                callbacks.error([error.message], 'inner');
              } else {
                AlertModal.alert(I18n.t('home.warning_message'), error.message);
              }
            }
          }
          if (callbacks?.ensure) {
            callbacks.ensure();
          }
        });
    });
  },

  async hostByNetwork(path: string, callbacks?: Callbacks): Promise<string> {
    let api_url: string;
    let api_prefix = '/iot';

    if (callbacks?.apiPrefix != undefined) {
      api_prefix = callbacks.apiPrefix;
    }
    const data = await AsyncStorage.getItem('user_data');
    const user_data = data ? JSON.parse(data) : null;
    if (callbacks?.apiUrl) {
      api_url = callbacks.apiUrl;
    } else if (this.isUsingLocalNetwork()) {
      // SHOULD removed
      api_url = 'http://' + HelperMemo.user_data.network + `:5006${api_prefix}`;
    } else {
      try {
        api_url = user_data.user.dc_config.cloud.host + api_prefix;
      } catch {
        // old logic
        const dc_name = user_data.user.dc_name;
        api_url =
          user_data.user.cloud.dc_config.service_groups[dc_name].cloud.host +
          api_prefix;
      }
    }
    return api_url + path;
  },

  isUsingLocalNetwork(): boolean {
    return Boolean(HelperMemo.user_data && HelperMemo.user_data.network);
  },

  httpGET(path: string, callbacks?: Callbacks): void {
    this._sendRequest('GET', path, callbacks, null);
  },

  httpPOST(path: string, callbacks?: Callbacks, body?: any): void {
    this._sendRequest('POST', path, callbacks, body);
  },

  async backgroundGeolocationConfig(): Promise<BackgroundGeolocationConfig> {
    const url = await this.hostByNetwork(
      '/partner/automations/exec_gps_routine',
    );
    return {
      url: url,
      params: {
        os: Platform.OS,
        user_uuid: HelperMemo.user_data ? HelperMemo.user_data.uuid : '',
        home_id: HelperMemo.user_data ? HelperMemo.user_data.home_id : '',
        device_token: HelperMemo.device_token || '',
      },
      ...this._api_headers(),
      stopOnTerminate: false,
      debug: false,
      startOnBoot: true,
      desiredAccuracy: BackgroundGeolocation.DESIRED_ACCURACY_HIGH,
      distanceFilter: 10,
      disableLocationAuthorizationAlert: true,
      autoSync: true,
      geofenceModeHighAccuracy: true,
      geofenceInitialTriggerEntry: false,
      notification: {
        title: I18n.t('global.auto'),
        text: I18n.t('global.location_desp'),
        priority: BackgroundGeolocation.NOTIFICATION_PRIORITY_MIN,
        smallIcon: 'drawable/push_small',
      },
    };
  },

  urlWithQuery(url: string, obj?: Record<string, any>): string {
    const params = [];
    for (const key in obj) {
      params.push(key + '=' + obj[key]);
    }

    if (/\?/.test(url)) {
      return url + '&' + params.join('&');
    } else {
      return url + '?' + params.join('&');
    }
  },

  packFormData(dataObj: Record<string, any>): FormData {
    const formData = new FormData();
    for (const key in dataObj) {
      if (!Array.isArray(dataObj[key])) {
        if (_.isObject(dataObj[key])) {
          for (const key2 in dataObj[key]) {
            formData.append(
              `${key}[${key2}]`,
              (dataObj[key] as Record<string, any>)[key2],
            );
          }
        } else {
          formData.append(key, dataObj[key]);
        }
      } else {
        if (dataObj[key][0]) {
          if (_.isObject(dataObj[key][0])) {
            const arr = dataObj[key];
            for (let i = 0; i < arr.length; i++) {
              for (const k in arr[i]) {
                formData.append(`${key}[][${k}]`, arr[i][k]);
              }
            }
          } else {
            for (let i = 0; i < dataObj[key].length; i++) {
              formData.append(key + '[]', dataObj[key][i]);
            }
          }
        }
      }
    }
    return formData;
  },

  getStatusBarHeight(callback?: (height: number) => void): number | void {
    if (Platform.OS == 'android') {
      HelperMemo.STATUS_BAR_HEIGHT = StatusBar.currentHeight || 0;
      return HelperMemo.STATUS_BAR_HEIGHT;
    } else {
      HelperBridge.getStatusBarHeight(
        (error: Error | null, result: {height: number}) => {
          if (error) {
            console.error(error);
          } else {
            HelperMemo.STATUS_BAR_HEIGHT = result.height;
            if (callback) {
              callback(HelperMemo.STATUS_BAR_HEIGHT);
            }
          }
        },
      );
    }
  },

  utc(): number {
    const date = new Date();
    const y = date.getUTCFullYear();
    const m = date.getUTCMonth();
    const d = date.getUTCDate();
    const h = date.getUTCHours();
    const M = date.getUTCMinutes();
    const s = date.getUTCSeconds();
    return Date.UTC(y, m, d, h, M, s) / 1000;
  },

  isLocalURL(url: string): boolean {
    if (/^\//.test(url)) {
      return true;
    }

    url = url.replace(/^https?:\/\//, '');
    return innerLinkRE.test(url);
  },

  i(key: string | number | undefined): string {
    if (!key) {
      return '';
    }

    let processedKey = key.toString();

    if (/^[A-Z\s\d\-\_]*$/.test(processedKey)) {
      processedKey = processedKey.toLowerCase();
    }

    if (/^\d/.test(processedKey)) {
      return processedKey;
    } else {
      processedKey = processedKey
        .replace(/^\s*/g, '')
        .replace(/\s*$/g, '')
        .replace(/\s{2,}/g, ' ')
        .replace(/(\-|\/|\s)/g, '_')
        .replace(/(.)([A-Z])/g, '$1_$2')
        .replace(/\_{2,}/g, '_')
        .toLowerCase();
      if (['yes', 'no', 'on', 'off'].indexOf(processedKey) !== -1) {
        processedKey = processedKey + '_b';
      }

      const res = I18n.t('spec.' + processedKey);
      if (res.indexOf(TRANS_MISSING) !== -1) {
        return key.toString();
      } else {
        return res;
      }
    }
  },

  runCMD(cmd: any[], sn_id: string, callbacks?: Callbacks): void {
    callbacks = callbacks || {};
    if (sn_id == HelperMemo.DASHBOARD_SN_ID) {
      DashboardHelper.httpPOST(
        '/control',
        {
          ensure: () => {},
          success: () => {
            if (callbacks?.successCb !== false) {
              setTimeout(() => {
                NotificationCenter.dispatchEvent(DASHBOARD_REFRESH);
              }, 1000);
            }
          },
          error: () => {
            AlertModal.alert(
              I18n.t('home.warning_message'),
              I18n.t('home.try_again'),
            );
          },
        },
        {type: 'node', cmd: JSON.stringify(cmd[0])},
      );
    } else {
      let path = '';
      if (this.isUsingLocalNetwork()) {
        path = 'http://' + HelperMemo.user_data.network + ':5006';
      } else {
        path = HelperMemo.user_data.user.dc_config.cloud.host;
      }
      const time = new Date().getTime();
      const tempDate = `apikey${AppConfig.api_key}methodpostpath/iot/partner/controllers/cmdtimestamp${time}`;
      Scode.code(tempDate.toLocaleUpperCase(), (scode: string) => {
        const options: AxiosRequestConfig = {
          method: 'POST',
          url: path + '/iot/partner/controllers/cmd?scopes=' + AppConfig.scopes,
          data: {
            commands: cmd,
            sn_id: sn_id,
            scode: scode.toLocaleUpperCase(),
            apikey: AppConfig.api_key,
            timestamp: time,
          },
        };
        Object.assign(options, this._api_headers());

        axios(options)
          .then(function (response) {
            if (response.status == 200) {
              const data = response.data;
              if (data.status == 'ok') {
                if (callbacks?.successCb !== false) {
                }
              } else {
                if (data.error) {
                  AlertModal.alert(
                    I18n.t('home.warning_message'),
                    _.flatten([data.error]).join('\n'),
                  );
                } else {
                  AlertModal.alert(
                    I18n.t('home.warning_message'),
                    _.flatten([data.errors]).join('\n'),
                  );
                }
              }
            } else {
              AlertModal.alert(
                I18n.t('home.warning_message'),
                'Server error, please try again later!',
              );
            }
          })
          .catch(error => {
            AlertModal.alert(I18n.t('home.warning_message'), error);
          });
      });
    }
  },

  temperatureScale(
    value: number,
    spec_unit_index: number,
    is_decode: boolean,
  ): number {
    let data = value;
    const user_scale = HelperMemo.user_data.user.scale;
    if (spec_unit_index == 0) {
      if (user_scale == 'F') {
        if (is_decode) {
          data = (parseInt(value.toString()) - 32) / 1.8;
        } else {
          data = parseInt(value.toString()) * 1.8 + 32;
        }
      }
    } else if (spec_unit_index == 1) {
      if (user_scale == 'C') {
        if (is_decode) {
          data = parseInt(value.toString()) * 1.8 + 32;
        } else {
          data = (parseInt(value.toString()) - 32) / 1.8;
        }
      }
    }
    return parseInt(data.toString());
  },

  formatAllTime(time: number): string {
    const lang =
      I18n.locale.indexOf('-') === -1
        ? I18n.locale
        : I18n.locale.substr(0, I18n.locale.indexOf('-'));
    switch (lang) {
      case 'zh_cn':
        momentTZ.updateLocale('zh-cn', zh_cn);
        break;
      case 'en':
        momentTZ.updateLocale('en', en);
        break;
      case 'de':
        momentTZ.updateLocale('de', de);
        break;
      case 'fr':
        momentTZ.updateLocale('fr', fr);
        break;
      case 'ru':
        momentTZ.updateLocale('ru', ru);
        break;
      case 'es':
        momentTZ.updateLocale('es', es);
        break;
      case 'pt':
        momentTZ.updateLocale('pt', pt);
        break;
      default:
        break;
    }
    const temp = momentTZ(time * 1000).tz(HelperMemo.user_data.user.timezone);
    const newTime = momentTZ(new Date())
      .tz(HelperMemo.user_data.user.timezone)
      .format('YYYY-MM-DD');
    if (temp.format('YYYY-MM-DD') == newTime) {
      return temp.format('HH:mm');
    } else {
      return temp.format('HH:mm, dddd, MMMM Do');
    }
  },

  isLanguageCh(): boolean {
    const lang = I18n.locale;
    return lang == 'zh_cn';
  },

  locationFormat(location: string | string[]): string[] {
    if (typeof location == 'string') {
      return location.replace(/\s*/g, '').split(',');
    }
    return location;
  },

  dpToPx(dp: number): number {
    const scale = PixelRatio.get();
    return dp * scale;
  },
};

export const DashboardHelper = {
  _api_headers(): ApiHeaders {
    console.log('memo.dashboard_sessionid', HelperMemo.dashboard_sessionid);
    return {
      headers: {
        sessionid: HelperMemo.dashboard_sessionid || '',
        'Content-Type': 'multipart/form-data',
      },
    };
  },

  async _sendRequest(
    method: string,
    path: string,
    callbacks?: Callbacks,
    body?: any,
  ): Promise<void> {
    if (!callbacks) {
      callbacks = {};
    }

    path = Helper.urlWithQuery(path, {
      lang: HelperMemo.lang,
      scopes: AppConfig.scopes,
    });

    const url = 'http://' + HelperMemo.dashboard_ip + ':8890' + path;

    let options: AxiosRequestConfig = {
      method: method,
      url: url,
      timeout: 15000,
    };
    options = Object.assign({}, options, this._api_headers());
    if (body && method == 'POST') {
      options = Object.assign({}, options, {
        data: Helper.packFormData(body),
      });
    }
    console.log('options', url);
    axios(options)
      .then(function (response) {
        if (response.status == 200) {
          const data = response.data;
          if (data.status == 'ok') {
            if (callbacks?.success) {
              callbacks.success(data.data);
            }
          } else {
            if (callbacks?.error) {
              callbacks.error(data.data, data.type);
            } else {
              AlertModal.alert(
                I18n.t('home.warning_message'),
                _.flatten([data.errors]).join('\n'),
              );
            }
          }
          if (callbacks?.ensure) {
            callbacks.ensure();
          }
        } else {
          AlertModal.alert(
            I18n.t('home.warning_message'),
            'Server error, please try again later!',
          );

          if (callbacks?.ensure) {
            callbacks.ensure();
          }
        }
      })
      .catch(error => {
        console.log(error);
        if (!callbacks?.ignore_error) {
          if (error.code === 'ECONNABORTED') {
            AlertModal.alert(
              I18n.t('home.network_issue'),
              I18n.t('home.network_issue_desp'),
            );
          } else {
            AlertModal.alert(I18n.t('home.warning_message'), error.message);
          }
        }
        if (callbacks?.ensure) {
          callbacks.ensure();
        }
      });
  },

  httpGET(path: string, callbacks?: Callbacks): void {
    this._sendRequest('GET', path, callbacks, null);
  },

  httpPOST(path: string, callbacks?: Callbacks, body?: any): void {
    this._sendRequest('POST', path, callbacks, body);
  },
};
export const DEVICE_WIDTH = Dimensions.get('window').width;
export const DEVICE_HEIGHT = Dimensions.get('window').height;
export const HelperBridge = NativeModules.Helper;
