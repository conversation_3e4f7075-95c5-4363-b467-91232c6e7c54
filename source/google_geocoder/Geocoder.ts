import axios from 'axios';

// 定义接口类型
export interface GeocoderParams {
  lat: number;
  lng: number;
}

export interface GeocoderResponse {
  status: 'ok' | 'error';
  data?: string;
  error?: string | Error;
}

export type GeocoderCallback = (response: GeocoderResponse) => void;

// TypeScript 版本的 Geocoder
class Geocoder {
  /**
   * 获取地理位置的地址信息
   * @param params 包含经纬度的参数对象
   * @param callback 回调函数，返回地址信息或错误
   */
  static Coder(params: GeocoderParams, callback: GeocoderCallback): void {
    axios
      .get('https://maps.googleapis.com/maps/api/geocode/json', {
        params: {
          latlng: `${params.lat},${params.lng}`,
          key: 'AIzaSyCXOum142DBBxqDpXtu3wTClOJBDg81Jww',
        },
      })
      .then(response => {
        if (response.status === 200) {
          callback({
            status: 'ok',
            data: response.data.results[0].formatted_address,
          });
        } else {
          callback({
            status: 'error',
            error: 'Server error, please try again later!',
          });
        }
      })
      .catch(err => {
        callback({
          status: 'error',
          error: err,
        });
      });
  }
}

export default Geocoder;
