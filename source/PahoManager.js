import {
  NotificationCenter,
  EVENT_GRPC_NODE_CHANGE,
} from './NotificationCenter';
import Paho from 'paho-mqtt';
import I18n from './I18n';
import AlertModal from './share/AlertModal';
import { Toast } from './Toast';
import PubSub from 'pubsub-js';
import { EVENT_GRPC_CONTROLLER_CHANGE, PubSubEvent } from './types/PubSubEvent';

export default class PahoManager {
  constructor(props) {
    if (props) {
      this.onConnect = props.onConnect;
      this.mqtt = props.mqtt;
      this.navigation = props.navigation;
    }

    this.retry_counter = 0;
    this.toast = null;
    this.firstConnect = true;
    this.client = null;
  }

  close() {
    if (this.client.isConnected()) {
      this.client.disconnect();
    }
  }

  onConnectionLost(responseObject) {
    console.log(responseObject);
    if (responseObject.errorCode !== 0) {
      Toast.show(responseObject.errorMessage);
    }
  }

  mount() {
    PubSub.subscribe(PubSubEvent.EVENT_APP_STATE_ACTIVE, () => {
      this._doConnect();
    });

    PubSub.subscribe(PubSubEvent.EVENT_APP_STATE_BACKGROUND, () => {
      this.close();
    });

    this.client = new Paho.Client(
      this.mqtt.dc_config.host,
      Number(this.mqtt.dc_config.mqtt_websocket_port),
      this.mqtt.dc_config.mqtt_websocket_path,
      this.mqtt.dc_config.client_id,
    );
    this.client.onConnectionLost = this.onConnectionLost.bind(this);
    this.client.onMessageArrived = this.onMessageArrived.bind(this);
    this._doConnect();
  }

  _doConnect() {
    console.log('doConnect');
    this.client.connect({
      //"iot:c111",
      userName:
        this.mqtt.dc_config.virtual_host + ':' + this.mqtt.dc_config.username,
      password: this.mqtt.dc_config.password,
      keepAliveInterval: 10,
      useSSL: this.mqtt.dc_config.mqtt_websocket_ssl,
      reconnect: true,
      onFailure: this.onError.bind(this),
      onSuccess: this.onConnectSuccess.bind(this),
    });
  }

  onMessageArrived(message) {
    const rep = JSON.parse(message.payloadString);
    console.log(`Event: ${new Date().getTime()}------`, rep);
    switch (rep.queue) {
      case 'devices':
        NotificationCenter.dispatchEvent(EVENT_GRPC_NODE_CHANGE, rep);
        break;
      case 'controller':
        PubSub.publish(EVENT_GRPC_CONTROLLER_CHANGE, rep);
        break;
    }
  }

  onError(e) {
    console.log('onError', e);
    if (e.errorCode === 1) {
      AlertModal.alert(I18n.t('global.time_out'), '', [
        {
          text: I18n.t('home.confirm'),
          onPress: () => {
            this.navigation.goBack();
          },
        },
      ]);
    } else {
      AlertModal.alert(I18n.t('home.error'), e.errorMessage, [
        {
          text: I18n.t('home.confirm'),
          onPress: () => {
            this.navigation.goBack();
          },
        },
      ]);
    }
  }

  onConnectSuccess() {
    //"topic" +"home_id" + "sn"
    let topic = this.mqtt.dc_config.topic + '/' + this.mqtt.home_id;
    if (this.mqtt.sn) {
      topic += '/' + this.mqtt.sn;
    }
    this.client.subscribe(topic, {
      qos: 1,
      onSuccess: this.onMessageSuccess.bind(this),
    });
    console.log('onConnectSuccess');
    if (this.firstConnect) {
      console.log('onConnectSuccess first');
      if (this.onConnect) {
        console.log('onConnectSuccess function');
        this.onConnect();
      }
    }
  }

  onMessageSuccess(rep) {
    console.log('onMessageSuccess: ', rep);
  }

  unmount() {
    if (this.client) {
      this.close();
      this.client = null;
    }
    PubSub.unsubscribe('EVENT_APP_STATE_ACTIVE');
    PubSub.unsubscribe('EVENT_APP_STATE_BACKGROUND');
  }
}
