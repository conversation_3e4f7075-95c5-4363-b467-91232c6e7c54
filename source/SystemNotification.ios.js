import React from 'react';
import { Platform } from 'react-native';
import PushNotificationIOS from '@react-native-community/push-notification-ios';

import { Helper, HelperMemo } from './Helper';
import { Router } from './Router';
import I18n from './I18n';
import _ from 'lodash';
import PubSub from 'pubsub-js';
import { EVENT_APP_STATE_ACTIVE, PubSubEvent } from './types/PubSubEvent';
// 使用：
// 直接在首页中
// <SystemNotification />
//
export default class SystemNotification extends React.Component {
  constructor(props) {
    super(props);
    // 在构造函数中绑定回调
    this._onRegistered = this._onRegistered.bind(this);
  }

  componentDidMount() {
    console.log('SystemNotification componentDidMount');
    PubSub.subscribe(EVENT_APP_STATE_ACTIVE, () => {
      PushNotificationIOS.setApplicationIconBadgeNumber(0);
    });

    // app退出的情况，原生情况下是需要在 didFinishLaunchingWithOptions 中捕获
    PushNotificationIOS.getInitialNotification().then(notification => {
      if (notification != null) {
        var data = notification.getData();
        console.log(data);
        if (data.notify_id) {
          if (!_.includes(HelperMemo.notifies, data.notify_id)) {
            if (HelperMemo.notifies) {
              HelperMemo.notifies.push(data.notify_id);
            } else {
              HelperMemo.notifies = [data.notify_id];
            }
            this._onRemoteNotification(notification);
          }
        }
      }
    });

    // 添加更多日志以便调试
    console.log('准备注册推送通知...');

    PushNotificationIOS.addEventListener('register', this._onRegistered);
    PushNotificationIOS.addEventListener(
      'notification',
      this._onRemoteNotification.bind(this),
    );
    PushNotificationIOS.addEventListener(
      'localNotification',
      this._onLocalNotification.bind(this),
    );

    // 添加权限请求的回调，以便调试
    PushNotificationIOS.requestPermissions().then(
      (permissions) => {
        console.log('通知权限已请求:', permissions);
      },
      (error) => {
        console.error('通知权限请求失败:', error);
      }
    );
  }

  componentWillUnmount() {
    PubSub.unsubscribe(EVENT_APP_STATE_ACTIVE);
    PushNotificationIOS.removeEventListener('register', this._onRegistered);
    PushNotificationIOS.removeEventListener(
      'notification',
      this._onRemoteNotification,
    );
    PushNotificationIOS.removeEventListener(
      'localNotification',
      this._onLocalNotification,
    );
  }

  render() {
    return null;
  }

  _onRegistered(deviceToken) {
    console.log('通知注册了: ' + deviceToken);
    HelperMemo.device_token = deviceToken;
    // set badge to zero
    PushNotificationIOS.setApplicationIconBadgeNumber(0);
    // send device token, and device's badge number will be set to 0 in backend.
    if (HelperMemo.user_data && !HelperMemo.user_data.network) {
      Helper.httpPOST(
        '/device_tokens',
        {
          ignore_error: true,
          success: data => {
            HelperMemo.token_id = data.device_token.id;
            console.log('token_id 已保存:', data.device_token.id);
          },
          error: (e) => {
            console.error('发送设备token失败:', e);
          },
        },
        {
          token: deviceToken,
          device_type: Platform.OS,
        },
      );
    }
  }

  _onRemoteNotification(notification) {
    this.notificationPush(notification);
  }

  _onLocalNotification(notification) {
    this.notificationPush(notification);
  }

  notificationPush(notification) {
    const isClicked = notification.getData().userInteraction === 1;
    var data = notification.getData();
    if (isClicked) {
      if (data.type == 'spec_change') {
        // 新增：特殊处理IPC设备通知
        if (data.urgent === 'true' || data.urgent === true) {
          setTimeout(() => {
            this.props.navigation.popToTop();
            HelperMemo.message = data;
            this.props.navigation.navigate('Tabs', {
              screen: 'homeScreen',
            });
            PubSub.publish(PubSubEvent.EVENT_APP_NOTIFY);
          }, 100);
        } else {
          if (data.dv_type) {
            if (data.dv_type === 'ipc') {
              setTimeout(() => {
                this.props.navigation.navigate('Tabs', {
                  screen: 'homeScreen',
                });
                this.props.navigation.push('IpcList', {
                  initialTab: 'events',
                  initialDeviceUUID: data.uuid,
                  title: I18n.t('home.event'),
                });
              }, 500);
            } else {
              setTimeout(() => {
                this.props.navigation.navigate('Tabs', {
                  screen: 'homeScreen',
                });
                this.props.navigation.push('eventScreen', {
                  title: I18n.t('home.event'),
                });
              }, 500);
            }
          } else {
            setTimeout(() => {
              this.props.navigation.navigate('Tabs', {
                screen: 'homeScreen',
              });
              Router.pushDeviceShow(this.props.navigation, {
                data: { uuid: data.uuid, notify: true },
              });
            }, 500);
          }
        }
      } else if (data.type === 'event') {
        setTimeout(() => {
          this.props.navigation.navigate('Tabs', {
            screen: 'homeScreen',
          });
          this.props.navigation.push('eventScreen', {
            sn_id: data.sn_id,
            title: I18n.t('home.event'),
          });
        }, 500);
      } else {
        console.log('未知的通知类型或无效数据结构:', JSON.stringify(data));
      }
    }
  }
}
