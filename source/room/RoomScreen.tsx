import React, {useState, useEffect, useRef, useCallback} from 'react';
import {View, Text, FlatList, TouchableOpacity} from 'react-native';
import {Helper} from '../Helper';
import _ from 'lodash';
import CardView from '../share/CardView';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {isOwnerOrAdmin, Router} from '../Router';
import {Tme, IsDark} from '../ThemeStyle';
import Ionicons from 'react-native-vector-icons/Ionicons';
import I18n from '../I18n';
import {Toast} from '../Toast';
import {SkeletionDevice} from '../share/Skeletion';
import ActionSheet from '@alessiocancian/react-native-actionsheet';
import {getImageFromKey, mainRadius, mainTitle} from '../Tools';
import AlertModal from '../share/AlertModal';
import GrayscaledImage from '../share/GrayscaledImage';
import DeviceControl from '../DeviceControl';
import {hideLoading, showLoading} from '../../ILoading';
import PubSub from 'pubsub-js';
import {IconView} from '../setting/SettingScreen';
import {PubSubEvent, SELECT_COLOR_DONE} from '../types/PubSubEvent';

interface Room {
  name: string;
  uuid: string;
  icon: string;
  device_ids: string[];
  switch_specs: any[];
  color_switch_specs: any[];
}

interface Device {
  display_name: string;
  uuid: string;
  index: number;
}

interface RoomScreenProps {
  navigation: any;
  context: {
    winWidth: number;
  };
}

const RoomScreen: React.FC<RoomScreenProps> = ({navigation, context}) => {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [checkDevices, setCheckDevices] = useState<Device[]>([]);
  const [show, setShow] = useState(false);
  const [showAdd, setShowAdd] = useState(false);
  const [selectRoom, setSelectRoom] = useState<Room | null>(null);
  const [firstFetch, setFirstFetch] = useState(true);

  const actionSheetRef = useRef<ActionSheet>(null);
  const flatListRef = useRef<FlatList>(null);
  // 添加一个ref来存储当前操作的房间数据
  const currentRoomRef = useRef<Room | null>(null);

  // 添加这个 useEffect 来加载初始数据
  useEffect(() => {
    doFetchData('initial');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const doFetchData = useCallback(
    (from: string) => {
      if (from === 'refresh') {
        setIsRefreshing(true);
      }

      Helper.httpGET(Helper.urlWithQuery('/rooms', {}), {
        success: (data: {devices: Device[]; rooms: Room[]}) => {
          const temp = data.devices.map(v => ({
            display_name: v.display_name,
            uuid: v.uuid,
            index: v.index,
          }));

          setShowAdd(isOwnerOrAdmin());
          setRooms(data.rooms);
          setCheckDevices(temp);
        },
        ensure: () => {
          setShow(true);
          if (from === 'refresh') {
            setIsRefreshing(false);
          } else if (firstFetch) {
            setFirstFetch(false);
            hideLoading();
          }
        },
      });
    },
    [firstFetch],
  );

  const saveColor = useCallback(
    (color: string) => {
      const rgb = color.split(',');

      if (selectRoom) {
        selectRoom.color_switch_specs.forEach(v => {
          const switchSpec = _.filter(
            selectRoom.switch_specs,
            s => s.device_id === v.device_id,
          );
          if (switchSpec.length > 0) {
            new DeviceControl({
              spec: switchSpec[0],
              param: 255,
              sn_id: switchSpec[0].sn_id,
              runCMD: true,
            }).switch();
          }
          new DeviceControl({
            spec: v,
            color: rgb,
            sn_id: v.sn_id,
            runCMD: true,
          }).colorSwitch();
        });
      }
    },
    [selectRoom],
  );

  useEffect(() => {
    const token1 = PubSub.subscribe(
      PubSubEvent.EVENT_ADD_SETTING_ROOM_SUCCESS,
      () => {
        doFetchData('refresh');
      },
    );

    const token2 = PubSub.subscribe(SELECT_COLOR_DONE, (msg, data) => {
      saveColor(data);
    });

    const token3 = PubSub.subscribe(PubSubEvent.ERROR_RE, () => {
      doFetchData('ERROR_REFETCH');
    });

    return () => {
      PubSub.unsubscribe(token1);
      PubSub.unsubscribe(token2);
      PubSub.unsubscribe(token3);
    };
  }, [doFetchData, saveColor]);

  const doRefreshRoom = () => {
    setIsRefreshing(true);
    doFetchData('refresh');
  };

  const sheetClick = (index: number) => {
    if (index === 0) {
      setting(currentRoomRef.current);
    } else if (index === 1) {
      remove(currentRoomRef.current);
    }
  };

  const setting = (room?: Room | null) => {
    // 优先使用传入的参数，如果没有则使用state中的clickRow
    const roomToUse = room || currentRoomRef.current;
    if (!roomToUse) {
      return;
    }

    navigation.push('settingRoom', {
      devices: checkDevices,
      device_ids: roomToUse.device_ids,
      name: roomToUse.name,
      uuid: roomToUse.uuid,
      icon: roomToUse.icon,
      type: 'edit',
      title: I18n.t('room.setting_room'),
    });
  };

  const remove = (room?: Room | null) => {
    // 优先使用传入的参数，如果没有则使用state中的clickRow
    const roomToUse = room || currentRoomRef.current;
    if (!roomToUse) {
      return;
    }

    const body = {
      uuid: roomToUse.uuid,
    };

    AlertModal.alert(
      I18n.t('home.remove_btn'),
      I18n.t('global.activate_sure'),
      [
        {
          text: I18n.t('home.cancel'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: I18n.t('home.confirm'),
          onPress: () => {
            showLoading();
            Helper.httpPOST(
              '/rooms/delete',
              {
                ensure: () => {
                  hideLoading();
                },
                success: () => {
                  doFetchData('refresh');
                  Toast.show();
                },
              },
              body,
            );
          },
        },
      ],
    );
  };

  const sheetShow = (item: Room) => {
    currentRoomRef.current = item;
    setTimeout(() => {
      actionSheetRef.current?.show();
    }, 10);
  };

  const roomShow = (room: Room) => {
    Router.push(
      navigation,
      {
        check_devices: checkDevices,
        device_ids: room.device_ids,
        roomName: room.name,
        title: room.name,
        uuid: room.uuid,
        icon: room.icon,
      },
      'roomShow',
    );
  };

  const addRoom = () => {
    navigation.push('settingRoom', {
      devices: checkDevices,
      type: 'new',
      title: I18n.t('room.add_room'),
    });
  };

  const renderAddRoom = () => {
    if (!showAdd) {
      return null;
    }

    return (
      <View style={{marginTop: 20}}>
        <CardView
          onChange={addRoom}
          styles={[
            {
              width: context.winWidth - 40,
              height: 60,
              borderRadius: mainRadius(),
            },
          ]}>
          <View style={{flex: 1}}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                flex: 1,
              }}>
              <IconView>
                <Ionicons
                  name="add-outline"
                  size={20}
                  color={Tme('cardTextColor')}
                />
              </IconView>
              <Text
                style={{
                  fontSize: mainTitle(),
                  color: Tme('cardTextColor'),
                  marginLeft: 6,
                  fontWeight: '500',
                }}>
                {I18n.t('room.add_room')}
              </Text>
            </View>
          </View>
        </CardView>
      </View>
    );
  };

  const renderRow = ({item, index}: {item: Room; index: number}) => {
    const image = getImageFromKey(item.icon).value;

    return (
      <View style={{marginBottom: 1}}>
        <CardView
          onChange={() => roomShow(item)}
          styles={[
            {
              borderRadius: 0,
              paddingBottom: 20,
              width: context.winWidth - 40,
            },
            index === 0 && {
              marginTop: 20,
              borderTopLeftRadius: mainRadius(),
              borderTopRightRadius: 16,
            },
            index + 1 === rooms.length && {
              borderBottomLeftRadius: mainRadius(),
              borderBottomRightRadius: mainRadius(),
            },
          ]}>
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              flexDirection: 'row',
            }}>
            <View
              style={{
                flex: 1,
                marginRight: 16,
              }}>
              <View
                style={{
                  marginTop: 20,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'flex-end',
                }}>
                {isOwnerOrAdmin() ? (
                  <TouchableOpacity
                    onPress={() => sheetShow(item)}
                    activeOpacity={0.8}
                    hitSlop={{top: 20, right: 20, bottom: 20, left: 20}}>
                    <MCIcons
                      name="dots-horizontal"
                      size={22}
                      color={Tme('smallTextColor')}
                    />
                  </TouchableOpacity>
                ) : (
                  <View style={{height: 20}} />
                )}
              </View>
              <View
                style={{
                  flex: 1,
                  flexDirection: 'row',
                }}>
                <View
                  style={{
                    backgroundColor: 'rgba(0,0,0,0.1)',
                    width: 48,
                    height: 48,
                    borderRadius: 22,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginLeft: 16,
                    marginRight: 16,
                  }}>
                  <GrayscaledImage
                    source={image}
                    style={{width: 30, height: 30}}
                  />
                </View>
                <View style={{flex: 1}}>
                  <Text
                    numberOfLines={1}
                    style={{
                      fontSize: mainTitle(),
                      color: Tme('cardTextColor'),
                      fontWeight: '500',
                    }}>
                    {item.name}
                  </Text>
                  {item.device_ids.length > 0 && (
                    <View
                      style={{
                        paddingVertical: 4,
                        flexDirection: 'row',
                      }}>
                      <Text
                        style={{
                          color: Tme('smallTextColor'),
                          fontSize: 12,
                          marginRight: 2,
                        }}>
                        {item.device_ids.length}
                      </Text>
                      <Text
                        style={{
                          color: Tme('smallTextColor'),
                          fontSize: 12,
                          marginRight: 4,
                        }}>
                        {I18n.t('global.device_count')}
                      </Text>
                    </View>
                  )}

                  <View style={{marginTop: 16}}>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'flex-end',
                      }}>
                      {item.switch_specs.length > 0 && (
                        <>
                          <TouchableOpacity
                            onPress={() => allOn(item)}
                            activeOpacity={0.8}
                            style={{
                              opacity: 0.8,
                              borderColor: Tme('smallTextColor'),
                              borderWidth: 1,
                              borderStyle: 'solid',
                              alignItems: 'center',
                              paddingHorizontal: 20,
                              paddingVertical: 10,
                              marginLeft: 12,
                              borderRadius: 14,
                            }}>
                            <Text
                              style={{
                                fontSize: 14,
                                fontWeight: '700',
                                color: Tme('smallTextColor'),
                              }}>
                              {I18n.t('room.all_on')}
                            </Text>
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => allOFF(item)}
                            activeOpacity={0.8}
                            style={{
                              opacity: 0.8,
                              borderColor: Tme('smallTextColor'),
                              borderWidth: 1,
                              borderStyle: 'solid',
                              alignItems: 'center',
                              paddingHorizontal: 20,
                              paddingVertical: 10,
                              marginLeft: 12,
                              borderRadius: 14,
                            }}>
                            <Text
                              style={{
                                fontSize: 14,
                                fontWeight: '700',
                                color: Tme('smallTextColor'),
                              }}>
                              {I18n.t('room.all_off')}
                            </Text>
                          </TouchableOpacity>
                        </>
                      )}
                      {item.color_switch_specs.length > 0 && (
                        <TouchableOpacity
                          onPress={() => settingColor(item)}
                          activeOpacity={0.8}
                          style={{
                            opacity: 0.8,
                            borderColor: Tme('smallTextColor'),
                            borderWidth: 1,
                            borderStyle: 'solid',
                            paddingHorizontal: 20,
                            paddingVertical: 10,
                            marginLeft: 12,
                            borderRadius: 14,
                          }}>
                          <Text
                            style={{
                              fontSize: 14,
                              fontWeight: '700',
                              color: Tme('smallTextColor'),
                            }}>
                            {I18n.t('room.light_color')}
                          </Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </CardView>
      </View>
    );
  };

  const allOn = (item: Room) => {
    showLoading();
    item.switch_specs.forEach(v => {
      new DeviceControl({
        spec: v,
        param: 255,
        sn_id: v.sn_id,
        runCMD: true,
      }).switch();
    });
    setTimeout(() => {
      hideLoading();
      Toast.show();
    }, 500);
  };

  const allOFF = (item: Room) => {
    showLoading();
    item.switch_specs.forEach(v => {
      new DeviceControl({
        spec: v,
        param: 0,
        sn_id: v.sn_id,
        runCMD: true,
      }).switch();
    });
    setTimeout(() => {
      hideLoading();
      Toast.show();
    }, 500);
  };

  const settingColor = (item: Room) => {
    setSelectRoom(item);
    navigation.push('SelectColorDrawer', {
      r: '',
      g: '',
      b: '',
      from: 'room',
    });
  };

  if (!show) {
    return <SkeletionDevice />;
  }

  const options = [
    I18n.t('home.cancel'),
    I18n.t('home.edit'),
    I18n.t('home.remove_btn'),
  ];

  return (
    <>
      <FlatList
        showsVerticalScrollIndicator={false}
        style={{
          flex: 1,
          paddingHorizontal: 20,
        }}
        ListHeaderComponent={renderAddRoom}
        ref={flatListRef}
        data={rooms}
        renderItem={renderRow}
        onEndReachedThreshold={0.1}
        refreshing={isRefreshing}
        onRefresh={doRefreshRoom}
        keyExtractor={(_item, index) => index.toString()}
      />
      <ActionSheet
        ref={actionSheetRef}
        userInterfaceStyle={IsDark() ? 'dark' : 'light'}
        options={options}
        cancelButtonIndex={0}
        destructiveButtonIndex={2}
        theme="ios"
        styles={{
          cancelButtonBox: {
            height: 50,
            marginTop: 6,
            alignItems: 'center',
            justifyContent: 'center',
          },
          buttonBox: {
            height: 50,
            alignItems: 'center',
            justifyContent: 'center',
          },
        }}
        onPress={index => {
          sheetClick(index - 1);
        }}
      />
    </>
  );
};

export default RoomScreen;
