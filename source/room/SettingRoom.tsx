/* eslint-disable react/no-unstable-nested-components */
import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  TextInput,
  ScrollView,
  SafeAreaView,
  Keyboard,
  TouchableOpacity,
} from 'react-native';
import _ from 'lodash';
import {Helper} from '../Helper';
import I18n from '../I18n';
import CheckBox from '../share/CheckBox';
import {Tme, Colors} from '../ThemeStyle';
import {Toast} from '../Toast';
import AlertModal from '../share/AlertModal';
import GrayscaledImage from '../share/GrayscaledImage';
import {getImageFromKey} from '../Tools';
import HeaderRightBtn from '../share/HeaderRightBtn';
import {hideLoading, showLoading} from '../../ILoading';
import PubSub from 'pubsub-js';
import {PubSubEvent} from '../types/PubSubEvent';
import { Device } from '../types/home';

interface SettingRoomProps {
  navigation: any;
  route: {
    params: {
      name: string;
      device_ids?: string[];
      devices: Device[];
      icon?: string;
      uuid: string;
    };
  };
}

const SettingRoom: React.FC<SettingRoomProps> = ({navigation, route}) => {
  const [name, setName] = useState(route.params.name);
  const [deviceIds, setDeviceIds] = useState<string[]>(
    route.params.device_ids || [],
  );
  const [devices] = useState<Device[]>(route.params.devices || []);
  const [icon, setIcon] = useState(route.params.icon || '');

  const handleSave = useCallback(() => {
    const errors = [];

    if (_.isEmpty(deviceIds)) {
      errors.push(I18n.t('room.room_no_device'));
    }
    if (_.isEmpty(name)) {
      errors.push(I18n.t('room.room_no_name'));
    }
    if (errors.length > 0) {
      AlertModal.alert(I18n.t('home.warning_message'), errors.join('\n'));
      return;
    }

    showLoading();
    const body = {
      name,
      device_ids: deviceIds,
      uuid: route.params.uuid,
      icon,
    };

    Helper.httpPOST(
      '/rooms',
      {
        ensure: () => {
          hideLoading();
        },
        success: () => {
          PubSub.publish(PubSubEvent.EVENT_ADD_SETTING_ROOM_SUCCESS);
          navigation.goBack();
          Toast.show();
        },
      },
      body,
    );
  }, [name, deviceIds, icon, route.params.uuid, navigation]);

  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn text={I18n.t('home.save')} rightClick={handleSave} />
      ),
    });

    const subscription = PubSub.subscribe(
      PubSubEvent.SELECT_SCENE_ICON,
      (_msg, data) => {
        setIcon(data.icon);
      },
    );

    return () => {
      PubSub.unsubscribe(subscription);
    };
  }, [navigation, handleSave]);

  const handleCheckboxClick = useCallback(
    (deviceId: string) => {
      if (deviceIds.length === 0) {
        setDeviceIds([deviceId]);
      } else {
        const index = _.findIndex(deviceIds, id => id === deviceId);
        if (index === -1) {
          const temp = [...deviceIds];
          temp.push(deviceId);
          setDeviceIds(temp);
        } else {
          const newDeviceIds = deviceIds.filter(id => id !== deviceId);
          setDeviceIds(newDeviceIds);
        }
      }
    },
    [deviceIds],
  );

  const showIcons = useCallback(() => {
    navigation.push('SelectSceneIcon', {
      icon,
      title: I18n.t('scene.select_icon'),
    });
  }, [navigation, icon]);

  const renderCheckboxes = useCallback(() => {
    if (!devices || devices.length === 0) {
      return null;
    }

    const filteredDevices = devices.filter(data => data.index !== '1');
    const length = filteredDevices.length - 1;
    console.log('filteredDevices', filteredDevices);
    return filteredDevices.map((data, index) => (
      <View key={data.uuid} style={{backgroundColor: Tme('cardColor')}}>
        <View style={{paddingHorizontal: 20}}>
          <CheckBox
            isLast={index === length}
            value={data.display_name}
            index={data.uuid}
            isChecked={_.includes(deviceIds, data.uuid)}
            onClick={handleCheckboxClick}
          />
        </View>
      </View>
    ));
  }, [devices, deviceIds, handleCheckboxClick]);

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: Tme('bgColor')}}>
      <ScrollView showsVerticalScrollIndicator={false} style={{flex: 1}}>
        <View
          style={{
            backgroundColor: Tme('bgColor'),
            marginTop: 20,
            marginBottom: 20,
          }}>
          <View
            style={{
              paddingHorizontal: 20,
              paddingVertical: 10,
              backgroundColor: Tme('cardColor'),
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <TouchableOpacity
              onPress={showIcons}
              activeOpacity={0.8}
              style={{
                marginRight: 4,
                borderWidth: 1,
                borderColor: Colors.MainColor,
                borderRadius: 6,
                justifyContent: 'center',
                alignItems: 'center',
                width: 34,
                height: 34,
              }}>
              <GrayscaledImage
                source={getImageFromKey(icon).value}
                style={{width: 22, height: 22}}
              />
            </TouchableOpacity>
            <TextInput
              placeholderTextColor={Tme('placeholder')}
              style={[
                Colors.TextInputStyle(),
                {flex: 1, fontWeight: '600', fontSize: 16},
              ]}
              autoCapitalize="none"
              underlineColorAndroid="transparent"
              placeholder={I18n.t('room.room_name')}
              value={name}
              onChangeText={setName}
              onBlur={() => {
                Keyboard.dismiss();
              }}
            />
          </View>
        </View>

        <View>
          <View style={{padding: 20}}>
            <Text style={{color: Tme('cardTextColor'), fontSize: 12}}>
              {I18n.t('room.device_in_room')}
            </Text>
          </View>
          <View style={{backgroundColor: Tme('cardColor')}}>
            {renderCheckboxes()}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default SettingRoom;
