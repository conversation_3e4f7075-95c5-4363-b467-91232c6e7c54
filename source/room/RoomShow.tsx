/* eslint-disable react/no-unstable-nested-components */
import React, {useState, useEffect, useRef, useCallback, useContext} from 'react';
import {View, Text, FlatList} from 'react-native';
import {Helper} from '../Helper';
import I18n from '../I18n';
import {Tme, IsDark} from '../ThemeStyle';
import DeviceList from '../device/DeviceList';
import {Toast} from '../Toast';
import {isOwnerOrAdmin} from '../Router';
import {mainRadius} from '../Tools';
import AlertModal from '../share/AlertModal';
import {hideLoading, showLoading} from '../../ILoading';
import HeaderRightBtn from '../share/HeaderRightBtn';
import ActionSheet from '@alessiocancian/react-native-actionsheet';
import {PubSubEvent} from '../types/PubSubEvent';
import PubSub from 'pubsub-js';
import ScreenSizeContext from '../../WindowResizeContext';
import { Device } from '../types/home';

interface RoomShowProps {
  navigation: any;
  route: {
    params: {
      uuid: string;
      roomName: string;
      device_ids: string[];
      check_devices: any[];
      icon: string;
    };
  };
}

const RoomShow: React.FC<RoomShowProps> = ({navigation, route}) => {
  const [dataSource, setDataSource] = useState<Device[]>([]);
  const [reFresh, setReFresh] = useState(false);
  const actionSheetRef = useRef<ActionSheet>(null);
  const flatListRef = useRef<FlatList>(null);
  const screenContext = useContext(ScreenSizeContext);

  const doFetchData = useCallback(() => {
    showLoading();
    Helper.httpGET(Helper.urlWithQuery('/rooms/' + route.params.uuid), {
      context: screenContext,
      success: (data: {devices: Device[]}) => {
        setDataSource(data.devices);
      },
      ensure: () => {
        hideLoading();
        setReFresh(false);
      },
    });
  }, [route.params.uuid, screenContext]);

  useEffect(() => {
    const focusListener = navigation.addListener('focus', () => {
      doFetchData();
    });

    return () => {
      focusListener();
    };
  }, [navigation, doFetchData]);

  useEffect(() => {
    navigation.setOptions({
      headerRight: () => {
        if (isOwnerOrAdmin()) {
          return (
            <HeaderRightBtn
              rightClick={() => actionSheetRef.current?.show()}
              icon={{name: 'dots-horizontal', icon: 'MCIcons'}}
            />
          );
        }
        return null;
      },
    });
  }, [navigation]);

  const sheetClick = (index: number) => {
    if (index === 0) {
      setting();
    } else if (index === 1) {
      remove();
    }
  };

  const setting = () => {
    navigation.push('settingRoom', {
      devices: route.params.check_devices,
      device_ids: route.params.device_ids,
      name: route.params.roomName,
      uuid: route.params.uuid,
      icon: route.params.icon,
      type: 'edit',
      title: I18n.t('room.setting_room'),
    });
  };

  const remove = () => {
    const body = {
      uuid: route.params.uuid,
    };

    AlertModal.alert(
      I18n.t('home.remove_btn'),
      I18n.t('global.activate_sure'),
      [
        {
          text: I18n.t('home.cancel'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: I18n.t('home.confirm'),
          onPress: () => {
            showLoading();
            Helper.httpPOST(
              '/rooms/delete',
              {
                ensure: () => {
                  hideLoading();
                },
                success: () => {
                  navigation.popToTop();
                  PubSub.publish(PubSubEvent.EVENT_ADD_SETTING_ROOM_SUCCESS);
                  Toast.show();
                },
              },
              body,
            );
          },
        },
      ],
    );
  };

  const options = [
    I18n.t('home.cancel'),
    I18n.t('home.edit'),
    I18n.t('home.remove_btn'),
  ];

  return (
    <View style={{flex: 1, backgroundColor: Tme('bgColor')}}>
      <FlatList
        ListHeaderComponent={() => (
          <View style={{marginBottom: 20}}>
            <View
              style={{
                paddingTop: 10,
                paddingHorizontal: 16,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            />
            <View
              style={{
                padding: 16,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'flex-start',
              }}>
              <Text style={{color: Tme('textColor')}}>
                {dataSource.length} {I18n.t('home.device')}
              </Text>
            </View>
          </View>
        )}
        style={{
          flex: 1,
          paddingHorizontal: 16,
          backgroundColor: Tme('cardColor2'),
          borderRadius: mainRadius(),
        }}
        columnWrapperStyle={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
        ref={flatListRef}
        data={dataSource}
        renderItem={({item}) => (
          <DeviceList item={item} navigation={navigation} />
        )}
        numColumns={2}
        onEndReachedThreshold={0.1}
        keyExtractor={(_, index) => index.toString()}
        refreshing={reFresh}
        onRefresh={()=> {
          setReFresh(true);
          doFetchData();
        }}
      />

      <ActionSheet
        ref={actionSheetRef}
        options={options}
        userInterfaceStyle={IsDark() ? 'dark' : 'light'}
        cancelButtonIndex={0}
        destructiveButtonIndex={2}
        theme="ios"
        styles={{
          cancelButtonBox: {
            height: 50,
            marginTop: 6,
            alignItems: 'center',
            justifyContent: 'center',
          },
          buttonBox: {
            height: 50,
            alignItems: 'center',
            justifyContent: 'center',
          },
        }}
        onPress={index => {
          sheetClick(index - 1);
        }}
      />
    </View>
  );
};

export default RoomShow;
