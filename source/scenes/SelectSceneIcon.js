/* eslint-disable react/no-unstable-nested-components */
import React, {Component} from 'react';
import {FlatList, TouchableOpacity, View} from 'react-native';
import I18n from '../I18n';
import Images from '../share/Images';
import {Colors, Tme} from '../ThemeStyle';
import GrayscaledImage from '../share/GrayscaledImage';
import HeaderRightBtn from '../share/HeaderRightBtn';
import PubSub from 'pubsub-js';
import {PubSubEvent} from '../types/PubSubEvent';

export default class SelectSceneIcon extends Component {
  constructor(props) {
    super(props);
    this.state = {
      images: Images,
      icon: this.props.icon,
    };
    this.props.navigation.setOptions({
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  rightClick() {
    PubSub.publish(PubSubEvent.SELECT_SCENE_ICON, {
      icon: this.state.icon,
    });

    this.props.navigation.goBack();
  }

  _onPress(item) {
    this.setState({
      icon: item.key,
    });
  }

  _renderRow({item, index}) {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={this._onPress.bind(this, item)}
        style={{
          flex: 1,
          alignItems: 'center',
          marginBottom: 20,
        }}>
        <View
          style={[
            {width: 50, height: 50, alignItems: 'center'},
            this.state.icon == item.key && {
              borderWidth: 1,
              borderColor: Colors.MainColor,
              borderRadius: 8,
            },
          ]}>
          <GrayscaledImage
            source={item.value}
            style={{width: 45, height: 45}}
          />
        </View>
      </TouchableOpacity>
    );
  }

  render() {
    return (
      <FlatList
        style={{
          flex: 1,
          paddingTop: 20,
          backgroundColor: Tme('bgColor'),
        }}
        ref={flatList => (this._flatList = flatList)}
        data={this.state.images}
        renderItem={this._renderRow.bind(this)}
        numColumns={4}
        onEndReachedThreshold={0.1}
        keyExtractor={(item, index) => index.toString()}
      />
    );
  }
}
