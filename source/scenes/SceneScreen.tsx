/* eslint-disable react/no-unstable-nested-components */
import React, {
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {
  View,
  Text,
  FlatList,
  Platform,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {Helper, HelperMemo} from '../Helper';
import {getImageFromKey, getSceneName, mainRadius, mainTitle} from '../Tools';
import I18n from '../I18n';
import {Tme, Colors, IsDark} from '../ThemeStyle';
import ActionSheet from '@alessiocancian/react-native-actionsheet';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import CardView from '../share/CardView';
import {isOwnerOrAdmin} from '../Router';
import {Toast} from '../Toast';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {SkeletionDevice} from '../share/Skeletion';
import AlertModal from '../share/AlertModal';
import GrayscaledImage from '../share/GrayscaledImage';
import PubSub from 'pubsub-js';
import {hideLoading, showLoading} from '../../ILoading';
import {PubSubEvent} from '../types/PubSubEvent';
import ActionBottomDrawer from '../share/ActionBottomDrawer';
import {SceneItem, SmartScreenProps} from '../types/smart';

interface SceneScreenProps extends SmartScreenProps {
  parents?: any;
  jumpTo?: (index: number) => void;
  scenes?: SceneItem[];
}

const SceneScreen = forwardRef<any, SceneScreenProps>(
  ({navigation, scenes = []}, ref) => {
    const [showBtn, setShowBtn] = useState(false);
    const [refreshing, setRefreshing] = useState(false);
    const [dataSource, setDataSource] = useState<SceneItem[]>(scenes || []);
    const [winWidth, setWinWidth] = useState(
      initWidth(Dimensions.get('window')),
    );
    const [selectedScene, setSelectedScene] = useState<SceneItem | null>(null);
    // 添加 optionsState 状态来强制刷新 ActionSheet 选项
    const [optionsState, setOptionsState] = useState<string[]>([]);

    const firstFetch = useRef(true);
    const actionSheet = useRef<any>(null);
    const clickRowRef = useRef<SceneItem | null>(null);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      setScenes: (data: SceneItem[]) => {
        if (firstFetch.current) {
          firstFetch.current = false;
          setDataSource(data);
          setShowBtn(true);
        }
      },
    }));

    useEffect(() => {
      const dimensionsEvent = Dimensions.addEventListener('change', onResize);

      PubSub.subscribe(PubSubEvent.EVENT_SCENE, () => {
        doFetchData();
      });
      PubSub.subscribe(PubSubEvent.EVENT_HOME_SCENE, () => {
        doFetchData();
      });

      PubSub.subscribe(PubSubEvent.ERROR_REFETCH, () => {
        doFetchData();
      });

      return () => {
        PubSub.unsubscribe(PubSubEvent.ERROR_REFETCH);
        PubSub.unsubscribe(PubSubEvent.EVENT_SCENE);
        PubSub.unsubscribe(PubSubEvent.EVENT_HOME_SCENE);
        if (dimensionsEvent) {
          dimensionsEvent.remove();
        }
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    function initWidth(window: any) {
      if (Platform.OS == 'android') {
        return window.width;
      } else {
        if (window.width > window.height) {
          return (
            window.width -
            HelperMemo.STATUS_BAR_HEIGHT -
            HelperMemo.BOTTOM_BAR_HEIGHT -
            22
          );
        } else {
          return window.width;
        }
      }
    }

    const onResize = ({window}: any) => {
      setWinWidth(initWidth(window));
    };

    if (!showBtn) {
      return <SkeletionDevice />;
    }

    const renderRow = ({item}: {item: SceneItem}) => {
      if (item.type === 'add') {
        return (
          <View style={{marginHorizontal: 4, marginBottom: 12}}>
            <CardView
              onChange={addScene}
              styles={[
                {
                  width: winWidth / 2 - 26,
                  height: 78,
                  borderRadius: mainRadius(),
                  padding: 12,
                  flexDirection: 'row',
                  alignItems: 'center',
                },
              ]}>
              <View
                style={{
                  width: 32,
                  height: 32,
                  marginVertical: 2,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Ionicons
                  name="add-outline"
                  size={22}
                  color={Tme('cardTextColor')}
                />
              </View>
              <Text
                style={{
                  fontSize: mainTitle(),
                  color: Tme('cardTextColor'),
                  marginLeft: 8,
                  fontWeight: '500',
                }}>
                {I18n.t('scene.add_scene')}
              </Text>
            </CardView>
          </View>
        );
      }

      return (
        <View style={{marginHorizontal: 4, marginBottom: 12}}>
          <CardView
            onChange={() => {
              setSelectedScene(item);
            }}
            styles={[
              {
                width: winWidth / 2 - 26,
                height: 78,
                borderRadius: mainRadius(),
                padding: 12,
                backgroundColor: Tme('cardColor2'),
              },
            ]}>
            <View style={{flexDirection: 'row', flex: 1}}>
              <View style={{flex: 1, flexDirection: 'row'}}>
                <GrayscaledImage
                  source={getImageFromKey(item.icon!).value}
                  style={{width: 30, height: 30}}
                />

                <View
                  style={{
                    marginLeft: 10,
                    position: 'relative',
                    justifyContent: 'center',
                    flex: 1,
                  }}>
                  <Text
                    numberOfLines={2}
                    style={[
                      {
                        width: winWidth / 2 - 120,
                        color: Tme('cardTextColor'),
                      },
                      Colors.CardFontStyle,
                    ]}>
                    {getSceneName(item.name)}
                  </Text>
                  <View style={{flexDirection: 'row', marginTop: 6}}>
                    <Text
                      style={{
                        color: Tme('smallTextColor'),
                        marginRight: 5,
                        fontSize: 11,
                      }}>
                      {item.target_length}
                    </Text>

                    <Text
                      style={{
                        fontSize: 11,
                        color: Tme('smallTextColor'),
                      }}>
                      {I18n.t('global.device_count')}
                    </Text>
                  </View>

                  {item.is_enabled && (
                    <View
                      style={{
                        height: 6,
                        backgroundColor: Colors.MainColor,
                        position: 'absolute',
                        top: 15,
                        left: -8,
                        width: 6,
                        borderRadius: 3,
                      }}
                    />
                  )}
                </View>
              </View>
              {isOwnerOrAdmin() ? (
                <TouchableOpacity
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  onPress={() => sheetShow(item)}
                  activeOpacity={0.8}
                  hitSlop={{top: 20, right: 20, bottom: 20, left: 20}}>
                  <MCIcons
                    name="dots-vertical"
                    size={22}
                    color={Tme('smallTextColor')}
                  />
                </TouchableOpacity>
              ) : (
                <View style={{height: 20}} />
              )}
            </View>
          </CardView>
        </View>
      );
    };

    const sheetShow = (rowData: SceneItem) => {
      clickRowRef.current = rowData;
      // 更新选项状态，强制重新渲染
      setOptionsState([
        I18n.t('home.cancel'),
        I18n.t('home.edit'),
        rowData.is_favor
          ? I18n.t('action.remove_favorite')
          : I18n.t('action.add_favorite'),
        I18n.t('home.remove_btn'),
      ]);
      actionSheet.current?.show();
    };

    const sheetClick = (index: number) => {
      if (!clickRowRef.current) {
        return;
      }

      if (index === 0) {
        sceneSetting(clickRowRef.current);
      }
      if (index == 1) {
        favorite(clickRowRef.current);
      }
      if (index == 2) {
        remove(clickRowRef.current);
      }
    };

    const remove = (data: SceneItem) => {
      const re = /^_/;
      if (re.test(data.name)) {
        setTimeout(() => {
          AlertModal.alert(I18n.t('global.scene_delete'));
        }, 100);
      } else {
        setTimeout(() => {
          AlertModal.alert(I18n.t('global.activate_sure'), '', [
            {
              text: I18n.t('home.cancel'),
              onPress: () => {},
            },
            {
              text: I18n.t('home.confirm'),
              onPress: () => {
                Helper.httpPOST(
                  '/partner/scenes/delete',
                  {
                    success: (resp: any) => {
                      Toast.show();
                      let TempScenes = resp.scenes;
                      if (isOwnerOrAdmin()) {
                        TempScenes.unshift({type: 'add'});
                      }
                      setDataSource(TempScenes);
                    },
                  },
                  {uuid: data.uuid},
                );
              },
            },
          ]);
        }, 100);
      }
    };

    const favorite = (data: SceneItem) => {
      setTimeout(() => {
        showLoading();
        Helper.httpPOST(
          '/partner/scenes/favor',
          {
            ensure: () => {
              hideLoading();
            },
            success: (resp: any) => {
              let TempScenes = resp.scenes;
              if (isOwnerOrAdmin()) {
                TempScenes.unshift({type: 'add'});
              }

              // 更新当前选中项和选项
              if (clickRowRef.current) {
                const updatedItem = TempScenes.find(
                  (item: any) => item.uuid === clickRowRef.current?.uuid,
                );
                if (updatedItem) {
                  clickRowRef.current = updatedItem;
                  // 更新 options
                  setOptionsState([
                    I18n.t('home.cancel'),
                    I18n.t('home.edit'),
                    updatedItem.is_favor
                      ? I18n.t('action.remove_favorite')
                      : I18n.t('action.add_favorite'),
                    I18n.t('home.remove_btn'),
                  ]);
                }
              }

              Toast.show();
              setDataSource(TempScenes);
            },
          },
          {uuid: data.uuid},
        );
      }, 100);
    };

    const sceneSetting = (data: SceneItem) => {
      const re = /^_/;
      navigation.push('sceneView', {
        uuid: data.uuid,
        name: data.name,
        system: re.test(data.name),
        title: I18n.t('home.scene'),
      });
    };

    const runScene = (v: SceneItem) => {
      showLoading();
      Helper.httpPOST(
        '/partner/scenes/run',
        {
          ensure: () => {
            hideLoading();
          },
          success: (resp: any) => {
            Toast.show();
            let TempScenes = resp.scenes;
            if (isOwnerOrAdmin()) {
              TempScenes.unshift({type: 'add'});
            }
            setDataSource(TempScenes);
            setSelectedScene(null);
          },
        },
        {uuid: v.uuid, type: 'scene'},
      );
    };

    const addScene = () => {
      navigation.push('sceneView', {
        title: I18n.t('home.scene'),
      });
    };

    const doRefreshData = () => {
      doFetchData('refresh');
    };

    const doFetchData = (type?: string) => {
      if (type == 'refresh') {
        setRefreshing(true);
      }

      Helper.httpGET(Helper.urlWithQuery('/partner/scenes'), {
        success: (resp: any) => {
          let TempScenes = resp.scenes;
          if (isOwnerOrAdmin()) {
            TempScenes.unshift({type: 'add'});
          }
          setDataSource(TempScenes);
        },
        ensure: () => {
          setShowBtn(true);
          if (type == 'refresh') {
            setRefreshing(false);
          } else {
            if (firstFetch.current) {
              firstFetch.current = false;
            }
          }
        },
      });
    };

    return (
      <View
        testID="sceneScreen"
        style={{flex: 1, backgroundColor: 'trasparent'}}>
        <FlatList
          style={{
            flex: 1,
            paddingHorizontal: 16,
            paddingTop: 20,
          }}
          columnWrapperStyle={{justifyContent: 'space-between'}}
          data={dataSource}
          renderItem={renderRow}
          numColumns={2}
          onEndReachedThreshold={0.1}
          refreshing={refreshing}
          ListFooterComponent={() => <View style={{height: 20, width: 1}} />}
          onRefresh={doRefreshData}
          keyExtractor={(_, index) => index.toString()}
        />
        {selectedScene && (
          <ActionBottomDrawer
            viewHeight={250}
            title={getSceneName(selectedScene.name)}
            desp={I18n.t('global.activate_scene')}
            confirm={I18n.t('home.confirm')}
            cancel={I18n.t('home.cancel')}
            action="scene"
            uuid={selectedScene.uuid}
            type="screen"
            onClose={() => setSelectedScene(null)}
            onConfirm={() => runScene(selectedScene)}
          />
        )}
        <ActionSheet
          ref={actionSheet}
          userInterfaceStyle={IsDark() ? 'dark' : 'light'}
          options={
            optionsState.length > 0
              ? optionsState
              : [
                  I18n.t('home.cancel'),
                  I18n.t('home.edit'),
                  I18n.t('home.favorite'),
                  I18n.t('home.remove_btn'),
                ]
          }
          theme="ios"
          styles={{
            cancelButtonBox: {
              height: 50,
              marginTop: 6,
              alignItems: 'center',
              justifyContent: 'center',
            },
            buttonBox: {
              height: 50,
              alignItems: 'center',
              justifyContent: 'center',
            },
          }}
          cancelButtonIndex={0}
          destructiveButtonIndex={3}
          onPress={index => {
            sheetClick(index - 1);
          }}
        />
      </View>
    );
  },
);

export default SceneScreen;
