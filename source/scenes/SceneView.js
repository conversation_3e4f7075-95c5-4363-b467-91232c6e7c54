import React, { Component } from 'react';
import {
  View,
  TextInput,
  // Image,
  TouchableOpacity,
  Keyboard,
} from 'react-native';
import I18n from '../I18n';
import { Helper } from '../Helper';
import { observer } from 'mobx-react/native';
import SelectDevice from '../select_device_spec/SelectDevice';
import _ from 'lodash';
import Scene from '../models/Scene';
import Device from '../models/Device';
import { Tme, Colors } from '../ThemeStyle';
import NavBarView from '../share/NavBarView';
import { toJS } from 'mobx';
import { Toast } from '../Toast';
import { getImageFromKey } from '../Tools';
import AlertModal from '../share/AlertModal';
import GrayscaledImage from '../share/GrayscaledImage';
import HeaderRightBtn from '../share/HeaderRightBtn';
import { hideLoading, showLoading } from '../../ILoading';
import { PubSubEvent } from '../types/PubSubEvent';
import PubSub from 'pubsub-js';
@observer
class SceneView extends Component {
  scene = new Scene();
  device = new Device();
  constructor(props) {
    super(props);

    this.state = {
      targets: [],
      viewShow: false,
      rooms: [],
    };
    this.inputRef = React.createRef();

    this.props.navigation.setOptions({
      // eslint-disable-next-line react/no-unstable-nested-components
      headerRight: () => (
        <HeaderRightBtn
          text={I18n.t('home.save')}
          rightClick={this.rightClick.bind(this)}
        />
      ),
    });
  }

  componentDidMount() {
    this.doFetchData();
    this.scene.is_system = this.props.route.params.system;
    this.scene.name = this.props.route.params.name;

    PubSub.subscribe(PubSubEvent.SELECT_SCENE_ICON, (msg, data) => {
      this.scene.icon = data.icon;
    });
  }

  componentWillUnmount() {
    PubSub.unsubscribe(PubSubEvent.SELECT_SCENE_ICON);

  }

  rightClick() {
    var that = this;
    if (this.scene.name == undefined) {
      AlertModal.alert(I18n.t('scene.scene_input'));
    } else {
      if (this.scene.name != '') {
        showLoading();
        var targets = _.groupBy(this.scene.targets, 'checked').true;
        if (targets == undefined) {
          targets = [];
        } else {
          var temp = [];
          _.forEach(targets, function (v, k) {
            temp.push(toJS(v));
          });
          targets = temp;
        }

        Helper.httpPOST(
          '/partner/scenes',
          {
            ensure: () => {
              hideLoading();
            },
            success: data => {
              PubSub.publish(PubSubEvent.EVENT_SCENE);
              that.props.navigation.goBack();
              Toast.show();
            },
            error: data => {
              AlertModal.alert(_.uniq(data).join('\n'));
            },
          },
          {
            name: this.scene.name,
            uuid: this.props.route.params.uuid,
            icon: this.scene.icon,
            targets: targets,
          },
        );
      } else {
        AlertModal.alert(I18n.t('home.scene'), I18n.t('scene.scene_input'));
      }
    }
  }

  doFetchData() {
    showLoading();
    Helper.httpGET(
      Helper.urlWithQuery(
        '/partner/scenes/data',
        this.props.route.params.uuid
          ? { uuid: this.props.route.params.uuid }
          : '',
      ),
      {
        success: data => {
          if (data.scene) {
            if (data.scene.targets) {
              this.scene.spec_targets(data.scene.targets);
            }
            this.scene.icon = data.scene.icon;
          }
          var devices = [];
          _.forEach(data.devices, function (v, k) {
            if (v.index != 1) {
              devices.push(v);
            }
          });

          this.device.devices = devices;
          this.setState(
            {
              rooms: data.rooms,
              viewShow: true,
            },
            () => {
              if (_.isEmpty(this.props.route.params.uuid)) {
                setTimeout(() => {
                  this.inputRef.current.focus();
                }, 500);
              }
            },
          );
        },
        ensure: () => {
          hideLoading();
        },
      },
    );
  }

  showIcons(icon) {
    this.props.navigation.push('SelectSceneIcon', {
      icon: icon,
      title: I18n.t('scene.select_icon'),
    });
  }

  render() {
    return (
      <NavBarView>
        {this.state.viewShow ? (
          <>
            <View
              style={{
                backgroundColor: Tme('bgColor'),
                marginTop: 20,
                marginBottom: 20,
              }}>
              <View
                style={{
                  paddingHorizontal: 20,
                  paddingVertical: 10,
                  backgroundColor: Tme('cardColor'),
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <TouchableOpacity
                  onPress={this.showIcons.bind(this, this.scene.icon)}
                  activeOpacity={0.8}
                  style={{
                    marginRight: 4,
                    borderWidth: 1,
                    borderColor: Colors.MainColor,
                    borderRadius: 6,
                    width: 34,
                    height: 34,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <GrayscaledImage
                    source={getImageFromKey(this.scene.icon).value}
                    style={{ width: 22, height: 22 }}
                  />
                </TouchableOpacity>
                <TextInput
                  ref={this.inputRef}
                  testID="sceneInput"
                  placeholderTextColor={Tme('placeholder')}
                  style={[
                    Colors.TextInputStyle(),
                    { flex: 1, fontWeight: '600', fontSize: 16 },
                  ]}
                  autoCapitalize="none"
                  editable={!this.scene.is_system}
                  underlineColorAndroid="transparent"
                  placeholder={I18n.t('scene.scene_name')}
                  value={this.scene.name}
                  onBlur={() => {
                    Keyboard.dismiss();
                  }}
                  onChangeText={name => {
                    this.scene.name = name;
                  }}
                />
              </View>
            </View>
            <SelectDevice
              showTitle={true}
              rooms={this.state.rooms}
              product="scene"
              spec_settings={this.scene}
              device={this.device}
              navigation={this.props.navigation}
              type="target"
            />
          </>
        ) : null}
      </NavBarView>
    );
  }
}
export default SceneView;
