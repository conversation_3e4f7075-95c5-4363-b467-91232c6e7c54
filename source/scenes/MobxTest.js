import React from 'react';
import {View, Text, Button} from 'react-native';
import AppState from '../models/AppState';

import {observer} from 'mobx-react/native';
@observer
class MobxTest extends React.Component {
  render() {
    return (
      <View>
        <Text>当前的数是: {AppState.timer}</Text>
        <TestView app_state={AppState} />
      </View>
    );
  }
}
export default MobxTest;

class TestView extends React.Component {
  render() {
    return (
      <View>
        <Button onPress={() => this.props.app_state.addTimers(1)} title="add" />
        <Button
          onPress={() => this.props.app_state.resetTimer()}
          title="reset"
        />
      </View>
    );
  }
}
