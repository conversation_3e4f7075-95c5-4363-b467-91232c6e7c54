import {NativeModules} from 'react-native';
import AppConfig from '../app_config';

const ScodeModule = NativeModules.ScodeModule;

type ScodeCallback = (scode: string) => void;

export default class Scode {
  static code(data: string, callback: ScodeCallback): void {
    ScodeModule.Scode(AppConfig.is_connect_prd ? '1' : '0', data, (v: any, d: { scode: string }) => {
      callback(d.scode);
    });
  }
}
