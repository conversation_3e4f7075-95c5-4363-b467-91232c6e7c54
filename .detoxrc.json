{"testRunner": "jest", "runnerConfig": "e2e/config.json", "configurations": {"ios.sim.debug": {"binaryPath": "ios/build/Build/Products/Debug-iphonesimulator/presen.app", "build": "xcodebuild -workspace ios/presen.xcworkspace -scheme presen -configuration Debug -sdk iphonesimulator -derivedDataPath ios/build", "type": "ios.simulator", "device": {"type": "iPhone 12"}}, "android.attacher": {"binaryPath": "android/app/build/outputs/apk/debug/app-debug.apk", "build": "cd android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug && cd ..", "type": "android.attached", "device": {"avdName": "vivo X9"}}, "android.emu.debug": {"binaryPath": "android/app/build/outputs/apk/debug/app-debug.apk", "build": "cd android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug && cd ..", "type": "android.emulator", "device": {"avdName": "Pixel_3_XL_API_29"}}, "android.emu.release": {"binaryPath": "android/app/build/outputs/apk/release/app-release.apk", "build": "cd android && ./gradlew assembleRelease assembleAndroidTest -DtestBuildType=release && cd ..", "type": "android.emulator", "utilBinaryPaths": ["e2e/android/test-butler-app-2.1.0.apk"], "device": {"avdName": "Pixel_3_XL_API_29"}}}}