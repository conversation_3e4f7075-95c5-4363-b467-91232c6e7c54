import dgram from 'react-native-udp';
import {decode} from 'base-64';
import _ from 'lodash';

interface Device {
  ip: string;
  sn: string;
  timestamp: number;
  type: string;
  version: string;
}

interface ScannerStatus {
  isScanning: boolean;
  deviceCount: number;
  hasSocket: boolean;
}

type ScanCallback = (results: Device[], isComplete?: boolean) => void;

interface IpcScanner {
  startScan: (callback?: ScanCallback, scanTime?: number) => void;
  stopScan: () => void;
  closeScanner: () => void;
  getScanResults: () => Device[];
  clearScanResults: () => void;
  getStatus: () => ScannerStatus;
}

// 简化全局变量
let socket: dgram.Socket | null = null;
let scanResults: Device[] = [];
let scanTimer: NodeJS.Timeout | null = null;
let isScanning: boolean = false;
const DEFAULT_SCAN_TIMEOUT = 5000; // 默认扫描5秒

/**
 * 判断两个设备是否相同
 * @param device1 - 第一个设备
 * @param device2 - 第二个设备
 * @returns 如果设备相同返回 true，否则返回 false
 */
const isSameDevice = (device1: Device, device2: Device): boolean => {
  return device1.sn === device2.sn || device1.ip === device2.ip;
};

/**
 * 添加不重复设备到扫描结果中
 * @param newDevice - 要添加的新设备
 * @returns 如果设备是新添加的返回 true，否则返回 false
 */
const addUniqueDevice = (newDevice: Device): boolean => {
  const exists = scanResults.some(device => isSameDevice(device, newDevice));

  if (!exists) {
    scanResults.push(newDevice);
    return true;
  }
  return false;
};

/**
 * 开始扫描设备
 * @param callback - 扫描结果回调函数
 * @param scanTime - 扫描超时时间（毫秒）
 */
const startScan = (callback: ScanCallback | null = null, scanTime: number = DEFAULT_SCAN_TIMEOUT): void => {
  if (isScanning) {
    return;
  }

  isScanning = true;

  if (!socket) {
    socket = dgram.createSocket({
      type: 'udp4',
      debug: false,
    });

    socket.on('error', (err: Error) => {
      console.log('Socket错误:', err);
    });

    socket.on('listening', () => {
      socket?.setBroadcast(true);
      socket?.addMembership('***********');
    });

    socket.on('message', (msg: Buffer, rinfo: dgram.RemoteInfo) => {
      const ms = msg.toString();
      if (ms.indexOf('RSKJ') > -1) {
        const data = ms.substring(8);
        const dData = decode(data);
        if (dData) {
          try {
            const td = JSON.parse(dData);
            const device: Device = {
              ip: rinfo.address,
              sn: td.sn,
              timestamp: Date.now(),
              type: td.type || 'unknown',
              version: td.version,
            };

            const isNew = addUniqueDevice(device);
            if (isNew && callback) {
              callback(getScanResults());
            }
          } catch (error) {
            console.error('解析设备数据失败:', error);
          }
        }
      }
    });

    socket.bind(3703);
  }

  if (scanTimer) {
    clearTimeout(scanTimer);
  }

  scanTimer = setTimeout(() => {
    stopScan();
    if (callback) {
      callback(getScanResults(), true);
    }
  }, scanTime);
};

/**
 * 停止扫描
 */
const stopScan = (): void => {
  if (!isScanning) {
    return;
  }
  isScanning = false;

  if (scanTimer) {
    clearTimeout(scanTimer);
    scanTimer = null;
  }

  // 关闭socket以停止接收广播消息
  if (socket) {
    try {
      socket.close(() => {
        socket = null;
      });
    } catch (error) {
      socket = null;
    }
  }
};

/**
 * 完全关闭扫描器（只在应用退出时调用）
 */
const closeScanner = (): void => {
  stopScan();

  if (socket) {
    try {
      socket.close(() => {
        socket = null;
      });
    } catch (error) {
      socket = null;
    }
  }

  clearScanResults();
};

/**
 * 获取排序后的扫描结果
 * @returns 按时间戳降序排序的设备列表
 */
const getScanResults = (): Device[] => {
  return _.orderBy(scanResults, ['timestamp'], ['desc']);
};

/**
 * 清除扫描结果
 */
const clearScanResults = (): void => {
  scanResults = [];
};

/**
 * 获取扫描器状态
 * @returns 扫描器状态对象
 */
const getStatus = (): ScannerStatus => {
  return {
    isScanning,
    deviceCount: scanResults.length,
    hasSocket: !!socket,
  };
};

const scanner: IpcScanner = {
  startScan,
  stopScan,
  closeScanner,
  getScanResults,
  clearScanResults,
  getStatus,
};

export default scanner;

