import { AppRegistry } from 'react-native';
import App from './App';
import { name as appName } from './app.json';
import { backgroundMessageHandler } from './source/FcmNotification';
import { getMessaging } from '@react-native-firebase/messaging';
import { getApp } from '@react-native-firebase/app';

// 使用新的模块化 API
const app = getApp();
const messagingInstance = getMessaging(app);
messagingInstance.setBackgroundMessageHandler(backgroundMessageHandler);

AppRegistry.registerComponent(appName, () => App);
