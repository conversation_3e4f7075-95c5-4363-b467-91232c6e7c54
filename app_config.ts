import AppConfigDev from './app_config.dev';

interface ApiConfig {
  api: string;
  cn_api: string;
  api_key: string;
  is_connect_prd: boolean;
  scopes: string;
  stripe_key: string;
  api_gcs_url: string;
  api_gcs_name: string;
  api_gcs_pwd: string;
  gaode_api_url: string;
  gapde_api_key: string;
  api_post_headers: {
    Accept: string;
    'Content-Type': string;
  };
  api_get_headers: {
    Accept: string;
  };
}

interface UiConfig {
  theme_color: string;
  navbar_text_color: string;
  backgroud_border: string;
  background_color: string;
}

interface CmdConfig {
  zn: string;
  zc: string;
  cm: string;
}

interface NavbarStyleOptions {
  background?: {
    color?: string;
  };
  title?: {
    color?: string;
  };
}

interface AppConfigType extends ApiConfig {
  ui: UiConfig;
  cmd: CmdConfig;
  navbar_style: (extend_style?: NavbarStyleOptions) => NavbarStyleOptions;
}

const AppConfig: AppConfigType = {
  // production
  api: 'https://api.smarthomesdk.com',
  // api: 'http://***********:5008',
  cn_api: 'https://api.smarthomesdk.com', // cn_api: 'https://api-cn.smarthomesdk.com',
  // cn_api: 'http://***********:5008',
  api_key: '797d0c4206ea43c088c59feec433af4a',
  is_connect_prd: true,
  scopes: 'presen',
  stripe_key:
    'pk_test_51PNPuFRqdVamKVacrDn3om7ApLj3hHZbwarMFtF4ZEkwugTD0zpXfcSS3PmGTNwp0bWeQibSFCp1wqk3r89O60Sp006nfpxRG2',

  api_gcs_url: 'https://gcs.smarthomesdk.com:50055',
  api_gcs_name: 'gcs-web-api',
  api_gcs_pwd: '22cbbbfccb05438d86871393f1381ebd',

  gaode_api_url: 'https://restapi.amap.com',
  gapde_api_key: '81feff419bd3b34a55d5c133b1b09dc7',

  api_post_headers: {
    Accept: 'application/json',
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  api_get_headers: {
    Accept: 'application/json',
  },

  ui: {
    theme_color: '#ffffff',
    navbar_text_color: '#111111',
    backgroud_border: '#fc577a',
    background_color: '#f6f9fa',
  },

  navbar_style(extend_style: NavbarStyleOptions = {}) {
    return {
      background: {
        color: this.ui.theme_color,
        ...extend_style.background,
      },
      title: {
        color: this.ui.backgroud_border,
        ...extend_style.title,
      },
      ...extend_style,
    };
  },

  cmd: {
    zn: '__Z__n,',
    zc: '__Z__c,',
    cm: '__CM__',
  },
};

// Overwrite with local dev configs for debug(development) mode.
if (__DEV__) {
  Object.assign(AppConfig, AppConfigDev);
}

export default AppConfig;
