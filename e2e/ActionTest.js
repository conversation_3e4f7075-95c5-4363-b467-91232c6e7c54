const { elementByText, elementById, sleep } = require('./utils/utils');

export const ActionTest = () => {
  beforeEach(async () => {
    await device.reloadReactNative();
    await expect(elementByText('Favorite scenes')).toBeVisible();
    await elementById('AUTOMACTION').tap();
    await elementById('smartView').atIndex(1).tap();
  });

  it('create action screen', async () => {
    await elementById('topMenu').tap();
    await elementById('menuItem').atIndex(1).tap();
    await elementById('actionInput').typeText('test action');
    await elementById('selectDeviceItem').atIndex(0).multiTap(2);
    await elementById('selectDeviceItemRow').atIndex(0).tap();
    await elementByText('Next').tap();
    await elementByText('Save').tap();
    sleep(1000);
    await elementById('HOME').tap();
    sleep(1000);
    await expect(element(by.text('test action'))).toExist();
  });

  it('remove action screen', async () => {
    await element(
      by
        .id('cardMenu')
        .withAncestor(by.id('cardView').withDescendant(by.text('test action'))),
    ).tap();
    await elementByText('Remove').tap();
    await elementByText('Confirm').tap();
  });
};
