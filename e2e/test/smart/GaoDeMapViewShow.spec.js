import GaoDeMapViewShow from "../../../source/smart/GaoDeMapViewShow";
import React from "react";
import Enzyme, { shallow } from 'enzyme';
import Adapter from 'enzyme-adapter-react-16';

Enzyme.configure({ adapter: new Adapter() });

describe("GaoDeMap get GPS to address", function () {
  it("Screen", () => {
    const wrapper = shallow(<GaoDeMapViewShow />);
    expect(wrapper.instance().state.address).toBe("");
    const data = new Promise((resolve) => {
      resolve(wrapper.instance().gaoDeGpsToAdd("112.578615,33.027071"));
    });
    return data.then((response) => {
      expect(wrapper.instance().state.address).not.toBeNull();
    });
  });
});
