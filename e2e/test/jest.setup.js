import mockRNDeviceInfo from 'react-native-device-info/jest/react-native-device-info-mock';
import mockAsyncStorage from '@react-native-community/async-storage/jest/async-storage-mock';

jest.mock('react-native-device-info', () => mockRNDeviceInfo);
jest.mock('@react-native-community/async-storage', () => mockAsyncStorage);

jest.mock('react-native-amap-geolocation', () => ({
  init: jest.fn(),
  setLocatingWithReGeocode: jest.fn(),
  addLocationListener: jest.fn(),
  stop: jest.fn(),
  start: jest.fn(),
}));
jest.mock('react-native-amap3d', () => {});
