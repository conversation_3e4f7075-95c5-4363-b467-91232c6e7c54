import axios from 'axios';
import AppConfig from "../../../app_config";

describe("3rd api test", () => {
  it("gaode latlng to address", async () => {
    const latlng = "112.578615,33.027071";
    const { data } = await axios.get(AppConfig.gaode_api_url +
      `/v3/geocode/regeo?key=${AppConfig.gapde_api_key}&poitype=&radius=1000&extensions=all&batch=false&roadlevel=0&location=${latlng}`);
    expect(data.status).toBe("1");
    expect(data.regeocode.formatted_address).not.toBeNull();
  });

  it("gao de address to gps", async () => {
    const url = AppConfig.gaode_api_url +
      "/v3/geocode/geo?key=" + AppConfig.gapde_api_key + "&address=南阳";
    const { data } = await axios.get(encodeURI(url));
    expect(data.status).toBe("1");
    expect(data.geocodes.length > 0).toBe(true);
  });
});
