const { elementByText, elementById, sleep } = require('./utils/utils');

export const SettingsText = () => {
  beforeEach(async () => {
    await device.reloadReactNative();
    await sleep(2000);
    await expect(elementByText('Favorite scenes')).toExist();
    await elementById('SETTING').tap();
  });

  it('should have setting screen', async () => {
    await expect(elementByText('Settings')).toBeVisible();
    await elementByText('Services').tap();
    await expect(elementByText('Google Assistant')).toBeVisible();
    await elementById('google_assistant').tap();
    await sleep(3000);
    await expect(element(by.id('webView').and(by.text('google_assistant')))).toExist();
  });
};
