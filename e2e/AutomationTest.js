const {
  getDateTimePickerIOS,
} = require('./utils/datePicker');
const {
  sleep,
  elementByText,
  elementById,
} = require('./utils/utils');

export const AutomationText = () => {
  beforeEach(async () => {
    await device.reloadReactNative();
    await sleep(2000);
    await elementById('AUTOMACTION').tap();
    await elementById('smartView').atIndex(2).tap();
  });

  it('create automation screen by time', async () => {
    await sleep(2000);
    await elementById('topMenu').tap();
    await elementById('menuItem').atIndex(2).tap();
    await sleep(1000);

    await elementByText("By time").tap();

    await elementById('autoInput').typeText('test auto');

    await elementByText('Next').tap();
    await elementById('dateBtn').tap();
    await sleep(3000);

    const testElement = getDateTimePickerIOS();
    await testElement.setColumnToValue(0, '11');
    await testElement.setColumnToValue(1, '11');
    await testElement.setColumnToValue(2, 'AM');
    await elementById('dateClose').tap();
    await expect(elementById('dateText')).toHaveText('11:11');

    await elementByText('Next').tap();
    await elementByText('Next').tap();
    await elementByText('Control devices').tap();
    await elementByText('Next').tap();
    await elementById('selectDeviceItem').atIndex(0).tap();
    await elementById('selectDeviceItemRow').atIndex(0).tap();
    await elementByText('Next').tap();
    await elementByText('Save').tap();
  });

  it('create automation screen by device', async () => {
    await elementById('topMenu').tap();
    await elementById('menuItem').atIndex(2).tap();
    await sleep(1000);
    await elementByText("By device changes").tap();

    await elementById('autoInput').typeText('test auto by device');
    await elementByText('Next').tap();

    await elementById('selectDeviceItem').atIndex(0).tap();
    await elementById('selectDeviceItemRow').atIndex(0).tap();
    await elementByText('Next').tap();
    await elementByText('Next').tap();

    await elementById('week').tap();

    await elementById('radioBtn').atIndex(0).tap();
    await elementById('radioBtn').atIndex(5).tap();
    await elementByText('Next').tap();

    await elementById('dateBtn').atIndex(0).tap();
    await sleep(3000);

    const testElement = getDateTimePickerIOS();
    await testElement.setColumnToValue(0, '11');
    await testElement.setColumnToValue(1, '11');
    await testElement.setColumnToValue(2, 'AM');
    await elementById('dateClose').tap();
    await expect(elementById('dateText').atIndex(0)).toHaveText('11:11');


    await elementById('dateBtn').atIndex(1).tap();
    await sleep(3000);

    const testElement_1 = getDateTimePickerIOS();
    await testElement_1.setColumnToValue(0, '11');
    await testElement_1.setColumnToValue(1, '11');
    await testElement_1.setColumnToValue(2, 'PM');
    await elementById('dateClose').tap();
    await expect(elementById('dateText').atIndex(1)).toHaveText('23:11');


    await elementByText('Next').tap();

    await elementByText('Control devices').tap();
    await elementByText('Next').tap();
    await elementById('selectDeviceItem').atIndex(0).tap();
    await elementById('selectDeviceItemRow').atIndex(0).tap();
    await elementByText('Next').tap();
    await elementByText('Save').tap();
  });

  it('edit test auto by action', async () => {
    await element(by.id('autoMeun').withAncestor(by.id('cardView').withDescendant(by.text('test auto')))).tap();
    await elementByText('Edit').tap();
    await expect(elementByText('Automation type')).not.toBeVisible();
    await elementByText('Next').tap();
    await elementById('radioBtn').atIndex(0).tap();
    await elementById('radioBtn').atIndex(5).tap();
    await elementByText('Next').tap();
    await elementByText('Scene').tap();
    await elementById('checkbox').atIndex(0).tap();
    await elementByText('Next').tap();
    await elementByText('Next').tap();
    await elementByText('Run an action').tap();
    await elementByText('Next').tap();
    await elementById('radioBtn').atIndex(0).tap();
    await elementByText('Save').tap();
  });

  it('edit test auto by scene', async () => {
    await element(by.id('autoMeun').withAncestor(by.id('cardView').withDescendant(by.text('test auto')))).tap();
    await elementByText('Edit').tap();
    await expect(elementByText('Automation type')).not.toBeVisible();
    await elementByText('Next').tap();
    await elementById('radioBtn').atIndex(1).tap();
    await elementById('checkbox').atIndex(5).tap();
    await elementByText('Next').tap();
    await elementByText('Scene').tap();
    await elementById('checkbox').atIndex(0).tap();
    await elementByText('Next').tap();
    await elementByText('Next').tap();
    await elementByText('Activate a scene').tap();
    await elementByText('Next').tap();
    await elementById('radioBtn').atIndex(1).tap();
    await elementByText('Save').tap();
  });

  it("create automation by map", async () => {
    await sleep(2000);
    await elementById('topMenu').tap();
    await elementById('menuItem').atIndex(2).tap();
    await sleep(1000);
    await elementByText('By GPS location').tap();

    await elementById('autoInput').typeText('test auto by map');
    await elementByText('Next').tap();
    await elementById('radioBtn').atIndex(1).tap();
    await elementByText('Next').tap();
    await elementByText('Next').tap();

    await elementByText('Control devices').tap();
    await elementByText('Next').tap();
    await elementById('selectDeviceItem').atIndex(0).tap();
    await elementById('selectDeviceItemRow').atIndex(0).tap();
    await elementByText('Next').tap();
    await elementByText('Save').tap();

    sleep(2000);
    await expect(element(by.text('test auto by map'))).toExist();
  });

  it('remove automation screen', async () => {
    await element(
      by.id('autoMeun').withAncestor(by.id('cardView').withDescendant(by.text('test auto')))
    ).tap();
    await elementByText('Remove').tap();
    await sleep(1000);
    await elementByText('Confirm').tap();
    await sleep(1000);
    await element(
      by.id('autoMeun').withAncestor(by.id('cardView').withDescendant(by.text('test auto by map')))
    ).tap();
    await elementByText('Remove').tap();
    await sleep(1000);
    await elementByText('Confirm').tap();
    await sleep(1000);
    await element(
      by.id('autoMeun').withAncestor(by.id('cardView').withDescendant(by.text('test auto by device')))
    ).tap();
    await elementByText('Remove').tap();
    await sleep(1000);
    await elementByText('Confirm').tap();
  });
};
