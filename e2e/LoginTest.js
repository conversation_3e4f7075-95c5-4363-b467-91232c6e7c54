const { elementByText, elementById, sleep } = require('./utils/utils');
const { AppConfig } = require('./app_config');

export const LoginTest = () => {
  beforeEach(async () => {
    await device.reloadReactNative();
  });
  it('should have login screen', async () => {
    await expect(elementByText('Login')).toBeVisible();
    await elementById('email').typeText(AppConfig.testEmail);
    await elementById('password').typeText(`${AppConfig.testPassword}\n`);
    await sleep(2000);
    await elementByText('Cancel').tap();
  });
};
