const { elementByText, elementById, sleep } = require('./utils/utils');

export const SceneTest = () => {
  beforeEach(async () => {
    await device.reloadReactNative();
    await expect(elementByText('Favorite scenes')).toBeVisible();
    await elementById('AUTOMACTION').tap();
  });

  it('create scene screen', async () => {
    await elementById('topMenu').tap();
    await elementById('menuItem').atIndex(0).tap();
    await elementById('sceneInput').typeText('test scene');
    await elementById('selectDeviceItem').atIndex(0).multiTap(2);
    await elementById('selectDeviceItemRow').atIndex(0).tap();
    await elementByText('Next').tap();
    await elementByText('Save').tap();
    await elementById('HOME').tap();
    sleep(2000);
    await expect(element(by.text('test scene'))).toExist();
  });

  it('remove scene screen', async () => {
    await element(
      by
        .id('cardMenu')
        .withAncestor(by.id('cardView').withDescendant(by.text('test scene'))),
    ).tap();
    await elementByText('Remove').tap();
    await elementByText('Confirm').tap();
  });
};
