## Setup

复制本地开发配置文件 app_config.sample.js 为`app_config.dev.js`

然后

```shell
$yarn install
$npx pod-install
```

M1上安装pod需要：

```
arch -x86_64 pod install
```

## Test

- 环境配置: <https://github.com/wix/Detox/blob/master/docs/Introduction.IosDevEnv.md>
- 需要复制测试配置文件`e2e/app_config.sample.js`为`e2e/app_config.js`

单元测试:

```shell
$yarn test
```

E2E 测试:

```shell
# 如果有改动原生代码需要先运行
$yarn build:ios
# 运行测试
$yarn test:ios

# 或者直接运行
$yarn e2e:ios
```

## 发布

安卓发布打包时需要通过环境变量区分是国区还是国外: **MARKET=cn**，一次来解决推送问题，国内使用极光，国外使用 GCM，但是国内的极光需要考虑升级为付费用户

## Docs

金色: rgb(225,191,92), rgb(248,214,117)

### 图标库

- <https://iconify.design/>
- <https://oblador.github.io/react-native-vector-icons/>

# Code Push

## install

```shell
npm install -g appcenter-cli
```

## use code push

```shell
appcenter login
```

## Push Code

```shell
appcenter codepush release-react -a presen-dev-qq.com/presen-ios -d Production
appcenter codepush release-react -a presen-dev-qq.com/presen-android -d Production
# -m 强制更新
```

## ipc

```shell
  s3 upload presenipc firmware
```
