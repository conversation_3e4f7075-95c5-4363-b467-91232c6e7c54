{"@react-native-community/art@1.2.0": {"licenses": "MIT", "licenseUrl": "https://github.com/react-native-community/react-native-art", "parents": "presen"}, "@react-native-community/async-storage@1.12.1": {"licenses": "MIT", "repository": "https://github.com/react-native-community/react-native-async-storage", "licenseUrl": "https://github.com/react-native-community/react-native-async-storage/raw/master/LICENSE", "parents": "presen"}, "@react-native-community/cameraroll@4.0.4": {"licenses": "MIT", "repository": "https://github.com/react-native-community/react-native-cameraroll", "licenseUrl": "https://github.com/react-native-community/react-native-cameraroll/raw/master/LICENCE", "parents": "presen"}, "@react-native-community/datetimepicker@3.0.6": {"licenses": "MIT", "repository": "https://github.com/react-native-community/datetimepicker", "licenseUrl": "https://github.com/react-native-community/datetimepicker/raw/master/LICENSE.md", "parents": "presen"}, "@react-native-community/geolocation@2.0.2": {"licenses": "MIT", "licenseUrl": "https://github.com/react-native-community/react-native-geolocation#README.md", "parents": "presen"}, "@react-native-community/netinfo@5.9.7": {"licenses": "MIT", "repository": "https://github.com/react-native-community/react-native-netinfo", "licenseUrl": "https://github.com/react-native-community/react-native-netinfo/raw/master/LICENSE", "parents": "presen"}, "@react-native-community/picker@1.8.1": {"licenses": "MIT", "repository": "https://github.com/react-native-community/react-native-picker", "licenseUrl": "https://github.com/react-native-community/react-native-picker/raw/master/LICENSE", "parents": "presen"}, "@react-native-community/push-notification-ios@1.8.0": {"licenses": "MIT", "repository": "https://github.com/react-native-community/push-notification-ios", "licenseUrl": "https://github.com/react-native-community/push-notification-ios/raw/master/LICENSE", "parents": "presen"}, "@react-native-community/segmented-control@2.2.1": {"licenses": "MIT", "repository": "https://github.com/react-native-community/segmented-control", "licenseUrl": "https://github.com/react-native-community/segmented-control/raw/master/LICENSE", "parents": "presen"}, "@react-native-community/slider@3.0.3": {"licenses": "MIT", "repository": "https://github.com/react-native-community/react-native-slider", "licenseUrl": "https://github.com/react-native-community/react-native-slider", "parents": "presen"}, "@react-native-community/viewpager@4.2.0": {"licenses": "MIT", "repository": "https://github.com/callstack/react-native-viewpager", "licenseUrl": "https://github.com/callstack/react-native-viewpager/raw/master/LICENSE", "parents": "presen"}, "@types/markdown-it@10.0.3": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/raw/master/LICENSE", "parents": "presen"}, "@types/react-native@0.63.36": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/raw/master/LICENSE", "parents": "presen"}, "axios@0.21.0": {"licenses": "MIT", "repository": "https://github.com/axios/axios", "licenseUrl": "https://github.com/axios/axios/raw/master/LICENSE", "parents": "presen"}, "base-64@1.0.0": {"licenses": "MIT", "repository": "https://github.com/mathiasbynens/base64", "licenseUrl": "https://github.com/mathiasbynens/base64/raw/master/LICENSE-MIT.txt", "parents": "presen"}, "immer@8.0.1": {"licenses": "MIT", "repository": "https://github.com/immerjs/immer", "licenseUrl": "https://github.com/immerjs/immer/raw/master/LICENSE", "parents": "presen"}, "lodash@4.17.20": {"licenses": "MIT", "repository": "https://github.com/lodash/lodash", "licenseUrl": "https://github.com/lodash/lodash/raw/master/LICENSE", "parents": "presen"}, "memoize-one@5.1.1": {"licenses": "MIT", "repository": "https://github.com/alexreardon/memoize-one", "licenseUrl": "https://github.com/alexreardon/memoize-one/raw/master/LICENSE", "parents": "presen"}, "mobx-react@5.4.4": {"licenses": "MIT", "repository": "https://github.com/mobxjs/mobx-react", "licenseUrl": "https://github.com/mobxjs/mobx-react/raw/master/LICENSE", "parents": "presen"}, "mobx@5.15.7": {"licenses": "MIT", "repository": "https://github.com/mobxjs/mobx", "licenseUrl": "https://github.com/mobxjs/mobx/raw/master/LICENSE", "parents": "presen"}, "moment-timezone@0.5.27": {"licenses": "MIT", "repository": "https://github.com/moment/moment-timezone", "licenseUrl": "https://github.com/moment/moment-timezone/raw/master/LICENSE", "parents": "presen"}, "moment@2.29.1": {"licenses": "MIT", "repository": "https://github.com/moment/moment", "licenseUrl": "https://github.com/moment/moment/raw/master/LICENSE", "parents": "presen"}, "paho-mqtt@1.1.0": {"licenses": "EPL-1.0", "repository": "https://github.com/eclipse/paho.mqtt.javascript", "licenseUrl": "https://github.com/eclipse/paho.mqtt.javascript", "parents": "presen"}, "prop-types@15.7.2": {"licenses": "MIT", "repository": "https://github.com/facebook/prop-types", "licenseUrl": "https://github.com/facebook/prop-types/raw/master/LICENSE", "parents": "presen"}, "react-dom@16.13.1": {"licenses": "MIT", "repository": "https://github.com/facebook/react", "licenseUrl": "https://github.com/facebook/react/raw/master/LICENSE", "parents": "presen"}, "react-mixin@5.0.0": {"licenses": "MIT", "repository": "https://github.com/brigand/react-mixin", "licenseUrl": "https://github.com/brigand/react-mixin/raw/master/LICENSE", "parents": "presen"}, "react-native-actionsheet@2.4.2": {"licenses": "MIT", "repository": "https://github.com/beefe/react-native-actionsheet", "licenseUrl": "https://github.com/beefe/react-native-actionsheet/raw/master/LICENSE", "parents": "presen"}, "react-native-amap-geolocation@1.1.2": {"licenses": "MIT", "repository": "https://github.com/qiuxiang/react-native-amap-geolocation", "licenseUrl": "https://github.com/qiuxiang/react-native-amap-geolocation/raw/master/license", "parents": "presen"}, "react-native-amap3d@1.1.1": {"licenses": "MIT", "repository": "https://github.com/qiuxiang/react-native-amap3d", "licenseUrl": "https://github.com/qiuxiang/react-native-amap3d/raw/master/license", "parents": "presen"}, "react-native-android-wifi@0.0.41": {"licenses": "ISC", "repository": "https://github.com/devstepbcn/react-native-android-wifi", "licenseUrl": "https://github.com/devstepbcn/react-native-android-wifi/raw/master/LICENSE", "parents": "presen"}, "react-native-camera-kit@10.0.0": {"licenses": "MIT", "parents": "presen"}, "react-native-device-info@7.1.0": {"licenses": "MIT", "repository": "https://github.com/react-native-device-info/react-native-device-info", "licenseUrl": "https://github.com/react-native-device-info/react-native-device-info/raw/master/LICENSE", "parents": "presen"}, "react-native-fast-image@8.3.4": {"licenses": "(MIT AND Apache-2.0)", "repository": "https://github.com/DylanVann/react-native-fast-image", "licenseUrl": "https://github.com/DylanVann/react-native-fast-image/raw/master/LICENSE", "parents": "presen"}, "react-native-file-md5-android@0.1.0": {"licenses": "MIT", "repository": "https://github.com/j010wdz/react-native-file-md5-android", "licenseUrl": "https://github.com/j010wdz/react-native-file-md5-android", "parents": "presen"}, "react-native-fs@2.17.0": {"licenses": "MIT", "repository": "https://github.com/itinance/react-native-fs", "licenseUrl": "https://github.com/itinance/react-native-fs/raw/master/LICENSE", "parents": "presen"}, "react-native-geolocation-service@5.0.0": {"licenses": "MIT", "repository": "https://github.com/Agontuk/react-native-geolocation-service", "licenseUrl": "https://github.com/Agontuk/react-native-geolocation-service/raw/master/LICENSE", "parents": "presen"}, "react-native-gesture-handler@1.8.0": {"licenses": "MIT", "repository": "https://github.com/software-mansion/react-native-gesture-handler", "licenseUrl": "https://github.com/software-mansion/react-native-gesture-handler/raw/master/LICENSE", "parents": "presen"}, "react-native-i18n@2.0.15": {"licenses": "MIT", "repository": "github.com/<PERSON>/react-native-i18n", "licenseUrl": "github.com/<PERSON><PERSON><PERSON><PERSON>/react-native-i18n/raw/master/LICENSE.md", "parents": "presen"}, "react-native-idle-timer@2.1.6": {"licenses": "UNKNOWN", "repository": "https://github.com/marcshilling/react-native-idle-timer", "licenseUrl": "https://github.com/marcshilling/react-native-idle-timer/raw/master/LICENSE.md", "parents": "presen"}, "react-native-image-zoom-viewer@3.0.1": {"licenses": "MIT", "repository": "https://github.com/ascoders/react-native-image-viewer", "licenseUrl": "https://github.com/ascoders/react-native-image-viewer/raw/master/LICENSE", "parents": "presen"}, "react-native-keyboard-aware-scroll-view@0.9.3": {"licenses": "MIT", "repository": "https://github.com/APSL/react-native-keyboard-aware-scroll-view", "licenseUrl": "https://github.com/APSL/react-native-keyboard-aware-scroll-view/raw/master/LICENSE", "parents": "presen"}, "react-native-linear-gradient@2.5.6": {"licenses": "MIT", "repository": "https://github.com/react-native-community/react-native-linear-gradient", "licenseUrl": "https://github.com/react-native-community/react-native-linear-gradient/raw/master/LICENSE", "parents": "presen"}, "react-native-localize@2.0.3": {"licenses": "MIT", "repository": "https://github.com/zoontek/react-native-localize", "licenseUrl": "https://github.com/zoontek/react-native-localize/raw/master/LICENSE", "parents": "presen"}, "react-native-maps@0.27.1": {"licenses": "MIT*", "repository": "https://github.com/react-native-community/react-native-maps", "licenseUrl": "https://github.com/react-native-community/react-native-maps/raw/master/LICENSE", "parents": "presen"}, "react-native-markdown-display@6.1.6": {"licenses": "MIT", "repository": "https://github.com/iamacup/react-native-markdown-display", "licenseUrl": "https://github.com/iamacup/react-native-markdown-display/raw/master/LICENSE", "parents": "presen"}, "react-native-orientation-locker@1.2.0": {"licenses": "MIT", "repository": "https://github.com/wonday/react-native-orientation-locker", "licenseUrl": "https://github.com/wonday/react-native-orientation-locker/raw/master/LICENSE", "parents": "presen"}, "react-native-progress@4.1.2": {"licenses": "MIT", "repository": "https://github.com/oblador/react-native-progress", "licenseUrl": "https://github.com/oblador/react-native-progress/raw/master/LICENSE", "parents": "presen"}, "react-native-qrcode-svg@6.1.1": {"licenses": "MIT", "repository": "https://github.com/awesomejerry/react-native-qrcode-svg", "licenseUrl": "https://github.com/awesomejerry/react-native-qrcode-svg/raw/master/LICENSE", "parents": "presen"}, "react-native-reanimated@1.13.2": {"licenses": "MIT", "repository": "https://github.com/software-mansion/react-native-reanimated", "licenseUrl": "https://github.com/software-mansion/react-native-reanimated/raw/master/LICENSE", "parents": "presen"}, "react-native-scrollable-tab-view@1.0.0": {"licenses": "MIT", "repository": "https://github.com/brentvatne/react-native-scrollable-tab-view", "licenseUrl": "https://github.com/brentvatne/react-native-scrollable-tab-view", "parents": "presen"}, "react-native-svg@12.1.0": {"licenses": "MIT", "repository": "https://github.com/react-native-community/react-native-svg", "licenseUrl": "https://github.com/react-native-community/react-native-svg/raw/master/LICENSE", "parents": "presen"}, "react-native-udp@4.0.3": {"licenses": "MIT", "repository": "https://github.com/tradle/react-native-udp", "licenseUrl": "https://github.com/tradle/react-native-udp/raw/master/LICENSE", "parents": "presen"}, "react-native-vector-icons@7.1.0": {"licenses": "MIT", "repository": "https://github.com/oblador/react-native-vector-icons", "licenseUrl": "https://github.com/oblador/react-native-vector-icons/raw/master/LICENSE", "parents": "presen"}, "react-native-webview@10.10.2": {"licenses": "MIT", "repository": "https://github.com/react-native-community/react-native-webview", "licenseUrl": "https://github.com/react-native-community/react-native-webview/raw/master/LICENSE", "parents": "presen"}, "react-native-windows@0.63.10": {"licenses": "MIT", "repository": "https://github.com/microsoft/react-native-windows", "licenseUrl": "https://github.com/microsoft/react-native-windows", "parents": "presen"}, "react-native@0.63.3": {"licenses": "MIT", "repository": "https://github.com/facebook/react-native", "licenseUrl": "https://github.com/facebook/react-native/raw/master/LICENSE", "parents": "presen"}, "react@16.13.1": {"licenses": "MIT", "repository": "https://github.com/facebook/react", "licenseUrl": "https://github.com/facebook/react/raw/master/LICENSE", "parents": "presen"}, "tinycolor2@1.4.2": {"licenses": "MIT", "repository": "https://github.com/bgrins/TinyColor", "licenseUrl": "https://github.com/bgrins/TinyColor/raw/master/LICENSE", "parents": "presen"}, "typescript@4.1.2": {"licenses": "Apache-2.0", "repository": "https://github.com/Microsoft/TypeScript", "licenseUrl": "https://github.com/Microsoft/TypeScript/raw/master/LICENSE.txt", "parents": "presen"}, "use-immer@0.4.2": {"licenses": "MIT", "repository": "https://github.com/mweststrate/use-immer", "licenseUrl": "https://github.com/mweststrate/use-immer/raw/master/LICENSE", "parents": "presen"}}