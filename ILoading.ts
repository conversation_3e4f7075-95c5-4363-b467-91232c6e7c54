import PubSub from 'pubsub-js';
import { PubSubEvent } from './source/types/PubSubEvent';

type LoadingCallback = (visible: boolean) => void;

class LoadingManagerClass {
  private _loadingCount: number;
  private _subscribers: Set<LoadingCallback>;
  private _isVisible: boolean;
  private _timeoutId: NodeJS.Timeout | null = null;
  private _defaultTimeout: number = 10000; // 默认10秒超时

  constructor() {
    this._loadingCount = 0;
    this._subscribers = new Set();
    this._isVisible = false;
  }

  // 设置默认超时时间（毫秒）
  setDefaultTimeout(timeout: number): void {
    if (timeout > 0) {
      this._defaultTimeout = timeout;
    }
  }

  // 添加状态变化订阅
  subscribe(callback: LoadingCallback): () => void {
    this._subscribers.add(callback);
    // 立即通知订阅者当前状态
    if (callback && typeof callback === 'function') {
      callback(this._isVisible);
    }
    return () => this._subscribers.delete(callback);
  }

  // 通知所有订阅者
  private _notifySubscribers(visible: boolean): void {
    this._isVisible = visible;
    // 安全地调用订阅者回调
    this._subscribers.forEach(callback => {
      try {
        if (callback && typeof callback === 'function') {
          callback(visible);
        }
      } catch (error) {
        console.error('Loading callback error:', error);
      }
    });

    // 安全地发布 PubSub 事件
    try {
      PubSub.publish(PubSubEvent.SHOW_LOADING, visible);
    } catch (error) {
      console.error('PubSub publish error:', error);
    }
  }

  // 清除已有的超时定时器
  private _clearTimeout(): void {
    if (this._timeoutId) {
      clearTimeout(this._timeoutId);
      this._timeoutId = null;
    }
  }

  // 设置超时定时器
  private _setTimeout(timeout?: number): void {
    this._clearTimeout();

    const duration = timeout && timeout > 0 ? timeout : this._defaultTimeout;

    this._timeoutId = setTimeout(() => {
      console.warn(`Loading timeout reached after ${duration}ms, forcing hide`);
      this._loadingCount = 0; // 重置计数器，确保强制隐藏
      this._notifySubscribers(false);
      this._timeoutId = null;
    }, duration);
  }

  // 显示loading
  show(timeout?: number): void {
    this._loadingCount++;

    if (this._loadingCount === 1) {
      // 第一次显示时通知订阅者
      this._notifySubscribers(true);
      // 设置超时
      this._setTimeout(timeout);
    } else if (!this._isVisible) {
      // 如果当前显示状态为 false，但 loadingCount > 0，确保更新显示状态
      this._notifySubscribers(true);
    }
  }

  // 隐藏loading
  hide(force: boolean = false): void {
    if (force) {
      this._loadingCount = 0;
      this._notifySubscribers(false);
      this._clearTimeout();
    } else {
      this._loadingCount = Math.max(0, this._loadingCount - 1);

      if (this._loadingCount === 0) {
        this._notifySubscribers(false);
        this._clearTimeout();
      }
    }
  }

  // 获取当前loading状态
  isVisible(): boolean {
    return this._isVisible;
  }
}

// 创建单例
export const LoadingManager = new LoadingManagerClass();

// 保持原有API不变
export function showLoading(): void {
  LoadingManager.show();
}

export function hideLoading(force: boolean = false): void {
  LoadingManager.hide(force);
}
