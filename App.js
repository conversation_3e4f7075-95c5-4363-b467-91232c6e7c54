import 'react-native-gesture-handler';
import React, { createRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import I18n, { getLanguages } from 'react-native-i18n';
import {
  NotificationCenter,
} from './source/NotificationCenter';
import {
  Platform,
  Appearance,
  View,
  Image,
  Text,
  StatusBar,
} from 'react-native';
import RNRestart from 'react-native-restart';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MCIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { Tme, Colors, IsDark } from './source/ThemeStyle';
import { HelperMemo } from './source/Helper';
import _ from 'lodash';
import {
  NavigationContainer,
  DefaultTheme,
  DarkTheme,
} from '@react-navigation/native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import HomeScreen from './source/home/<USER>';
import DeviceScreen from './source/device/DeviceScreen';
import SmartScreen from './source/smart/SmartScreen';
import SettingScreen from './source/setting/SettingScreen';
import LoginScreen from './source/session/LoginScreen';
import { InputModal } from './source/share/DelayInputModal';

import AddController from './source/setting/AddController';
import SceneView from './source/scenes/SceneView';
import DetectController from './source/wifi_link/DetectController';
import PhotoList from './source/setting/PhotoList';
import { HintView } from './source/share/HintView';
import ActionView from './source/action/ActionView';
import AutomationName from './source/smart/AutomationName';
import { AddAutomationMenu } from './source/smart/AddAutomationMenu';
import { Toast } from './source/Toast';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import SignUpView from './source/session/SignUpView';
import HeaderLeftBtn, { HeaderLeftModalBtn } from './source/share/HeaderLeftBtn';
import SignUpSetting from './source/session/SignUpSetting';
import SelectCountry from './source/session/SelectCountry';
import TimezoneList from './source/profile/TimezoneList';
import LangScreen from './source/profile/LangScreen';
import LoginPrivacy from './source/session/LoginPrivacy';
import { registerScreens } from './register';
import WifiSetting from './source/wifi_link/WifiSetting';
import SelectColorDrawer from './source/share/SelectColorDrawer';
import { LinkOpenAlert } from './source/share/LinkOpenAlert';
import ControllerList from './source/dashboard/ControllerList';
import SunScreen from './source/sun/SunScreen';
import AutomationTimeView from './source/smart/AutomationTimeView';
import SmartDo from './source/smart/SmartDo';
import SelectTargetType from './source/smart/SelectTargetType';
import SmartSelectCondition from './source/smart/SmartSelectCondition';
import AutomationDevice from './source/smart/AutomationDevice';
import AutomationMap from './source/smart/AutomationMap';
import DeviceShow from './source/device/DeviceShow';
import { ScreenSizeProvider } from './WindowResizeContext';
import { AddDeviceMenu } from './source/device/AddDeviceMenu';
import { OverflowMenuProvider } from 'react-navigation-header-buttons';
import GaoDeMapViewShow from './source/smart/GaoDeMapViewShow';
import MapViewShow from './source/smart/MapViewShow';
import DeviceSpecSetting from './source/select_device_spec/DeviceSpecSetting';
import DashboardSignUp from './source/dashboard/DashboardSignUp';
import DashboardScreen from './source/dashboard/DashboardScreen';
import DashboardLogin from './source/dashboard/DashboardLogin';
import DashboardInfo from './source/dashboard/DashboardInfo';
import DashboardDevicesView from './source/dashboard/DashboardDevicesView';
import IpcFullscreenView from './source/device/ipc/IpcFullscreenView';
import IpcAddMenu from './source/device/IpcAddMenu';
import { hideLoading } from './ILoading';
import IpcList from './source/device/IpcList';
import { PaperProvider } from 'react-native-paper';
import IpcAlarmSetting from './source/device/ipc/IpcAlarmSetting';
import IpcImageSetting from './source/device/ipc/IpcImageSetting';

const Tab = createBottomTabNavigator();
const Stack = createNativeStackNavigator();

import LoadingView from './source/components/LoadingView';
import AlarmWeek from './source/device/ipc/AlarmWeek';
import { ToastProvider } from './source/Toast';
import MultipleButton from './source/device/ipc/MultipleButton';

import PubSub from 'pubsub-js';
import { PubSubEvent } from './source/types/PubSubEvent';

class App extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      is_login: 'wait',
      scheme: 'L',
      currentScheme: Appearance.getColorScheme(),
      loading: false,
    };

    this.containerRef = createRef();
  }

  componentDidMount() {
    NotificationCenter.init();
    // 使用一个单独的方法处理loading状态，避免不必要的重新渲染
    this.setupLoadingHandler();
    Appearance.addChangeListener(
      _.throttle(
        async preferences => {
          if (this.state.currentScheme !== preferences.colorScheme) {
            this.setState({
              currentScheme: preferences.colorScheme,
            });
          }
        },
        500,
        {
          leading: false,
          trailing: true,
        },
      ),
    );
    setTimeout(() => {
      this.initData();
    }, 1500);

    PubSub.subscribe(PubSubEvent.RESTART_APP, () => {
      RNRestart.restart();
    });

    PubSub.subscribe(PubSubEvent.EVENT_LOGGED_IN, () => {
      this.initData();
    });
  }

  // 单独处理loading状态的方法
  setupLoadingHandler() {
    // 使用实例变量存储订阅标识，以便在组件卸载时正确取消订阅
    this.loadingSubscriptionToken = PubSub.subscribe(PubSubEvent.SHOW_LOADING, (_msg, value) => {
      if (this.state && this.state.loading !== value) {
        this.setState({
          loading: value,
        });
      }
    });
  }

  componentWillUnmount() {
    PubSub.unsubscribe(PubSubEvent.RESTART_APP);
    // 确保使用正确的订阅标识取消订阅
    if (this.loadingSubscriptionToken) {
      PubSub.unsubscribe(this.loadingSubscriptionToken);
    }
    PubSub.unsubscribe(PubSubEvent.EVENT_LOGGED_IN);
  }

  async initData() {
    this.getLange();
    const user = await this.loadUserDataFromDevice();
    const theme = await this.getTheme();
    this.changeScheme(theme);
    this.setState(
      {
        is_login: user ? 'login' : 'not_login',
      },
      () => {
        hideLoading();
      },
    );
  }

  changeScheme(theme) {
    let scheme = '';
    let currentScheme = '';
    if (theme === 'system_with') {
      switch (this.state.currentScheme) {
        case 'dark':
          // GetuiPush.setNavigationBarColor('bgColor');
          currentScheme = 'dark';
          scheme = 'D';
          break;
        case 'light':
          // GetuiPush.setNavigationBarColor('white');
          currentScheme = 'light';
          scheme = 'L';
          break;
        default:
      }
    } else {
      switch (theme) {
        case 'dark':
          // GetuiPush.setNavigationBarColor('bgColor');
          currentScheme = 'dark';
          scheme = 'D';
          break;
        case 'light':
          // GetuiPush.setNavigationBarColor('white');
          currentScheme = 'light';
          scheme = 'L';
          break;
        default:
      }
    }
    this.setState({
      scheme,
      currentScheme: currentScheme,
    });
  }

  async getLange() {
    try {
      const re = await AsyncStorage.getItem('lang');
      if (re !== null) {
        I18n.locale = HelperMemo.lang = JSON.parse(re);
      } else {
        getLanguages().then(languages => {
          var lan = languages[0];
          lan = lan.split('-')[0];

          if (lan === 'zh') {
            lan = 'zh_cn';
          }
          I18n.locale = HelperMemo.lang = lan;
          AsyncStorage.setItem('lang', JSON.stringify(lan), () => { });
        });
      }
    } catch (e) {
      console.error(e);
    }
  }

  async loadUserDataFromDevice() {
    try {
      const re = await AsyncStorage.getItem('user_data');
      if (re !== null) {
        HelperMemo.user_data = JSON.parse(re);
      } else {
        HelperMemo.user_data = null;
      }
      return HelperMemo.user_data;
    } catch (e) {
      console.error(e);
    }
  }

  async getTheme() {
    try {
      const re = await AsyncStorage.getItem('theme');
      if (re !== null) {
        const r = JSON.parse(re);
        HelperMemo.theme = r;
        return r;
      } else {
        HelperMemo.theme = Appearance.getColorScheme();
        return 'system_with';
      }
    } catch (e) {
      HelperMemo.theme = Appearance.getColorScheme();
      return 'system_with';
    }
  }

  StartLoggedinScreen() {
    const html = [];
    const temp = registerScreens();
    const screens = _.uniqBy(temp, 'name');
    _.forEach(screens, item => {
      html.push(
        <Stack.Screen
          name={item.name}
          key={item.name}
          component={item.screen}
        />,
      );
    });

    return (
      <Stack.Navigator
        initialRouteName="Tabs"
        screenOptions={({ route, navigation }) => ({
          headerShadowVisible: false,
          navigationBarColor: 'transparent',
          headerBackTitleVisible: true,
          title: route.params?.title ? route.params.title : '',
          headerTitleStyle: {
            color: Colors.MainColor,
          },
          headerStyle: {
            backgroundColor: Tme('bgColor', this.state.scheme),
          },
          // eslint-disable-next-line react/no-unstable-nested-components
          headerLeft: () => <HeaderLeftBtn navigation={navigation} />,
        })}>
        <Stack.Screen
          options={{ headerShown: false }}
          name="Tabs"
          component={Tabs}
        />
        {html}
        <Stack.Screen name="deviceShow" component={DeviceShow} />
        <Stack.Screen name="IpcList" component={IpcList} />
        <Stack.Screen name="IpcAlarmSetting" component={IpcAlarmSetting} />
        <Stack.Screen name="IpcFullscreenView" component={IpcFullscreenView} />
        <Stack.Screen name="IpcAddMenu" component={IpcAddMenu} />
        <Stack.Screen name="MultipleButton" component={MultipleButton} />
        <Stack.Screen name="WifiSetting" component={WifiSetting} />
        <Stack.Screen name="AddController" component={AddController} />
        <Stack.Screen name="DetectController" component={DetectController} />
        <Stack.Screen name="IpcImageSetting" component={IpcImageSetting} />
        <Stack.Screen name="AlarmWeek" component={AlarmWeek} />
        <Stack.Group
          screenOptions={() => ({
            presentation: 'modal',
          })}>
          <Stack.Screen name="actionView" component={ActionView} />
          <Stack.Screen name="sceneView" component={SceneView} />
          <Stack.Screen name="ControllerList" component={ControllerList} />
          <Stack.Screen name="AutomationName" component={AutomationName} />
          <Stack.Screen name="DashboardSignUp" component={DashboardSignUp} />
          <Stack.Screen name="DashboardScreen" component={DashboardScreen} />
          <Stack.Screen name="DashboardLogin" component={DashboardLogin} />
          <Stack.Screen name="DashboardInfo" component={DashboardInfo} />
          <Stack.Screen
            name="DashboardDevicesView"
            component={DashboardDevicesView}
          />
        </Stack.Group>

        <Stack.Group
          screenOptions={({ route, navigation }) => ({
            presentation: 'modal',
            // eslint-disable-next-line react/no-unstable-nested-components
            headerLeft: () => <HeaderLeftModalBtn navigation={navigation} />,
          })}>
          <Stack.Screen name="SunScreen" component={SunScreen} />
          <Stack.Screen
            name="AutomationTimeView"
            component={AutomationTimeView}
          />
          <Stack.Screen name="AutomationMap" component={AutomationMap} />
          <Stack.Screen name="AutomationDevice" component={AutomationDevice} />
          <Stack.Screen
            name="SmartSelectCondition"
            component={SmartSelectCondition}
          />
          <Stack.Screen name="SelectTargetType" component={SelectTargetType} />
          <Stack.Screen name="SmartDo" component={SmartDo} />
          <Stack.Screen name="GaoDeMapViewShow" component={GaoDeMapViewShow} />
          <Stack.Screen name="MapViewShow" component={MapViewShow} />
          <Stack.Screen
            name="DeviceSpecSetting"
            component={DeviceSpecSetting}
          />
        </Stack.Group>

        <Stack.Group
          screenOptions={() => ({
            presentation: 'containedTransparentModal',
            animation: 'fade',
            headerShown: false,
          })}>
          <Stack.Screen name="AddDeviceMenu" component={AddDeviceMenu} />
          <Stack.Screen name="InputModal" component={InputModal} />
          <Stack.Screen name="PhotoList" component={PhotoList} />
          <Stack.Screen
            name="SelectColorDrawer"
            component={SelectColorDrawer}
          />
          <Stack.Screen
            name="AddAutomationMenu"
            component={AddAutomationMenu}
          />
          <Stack.Screen name="Toast" component={Toast} />
          <Stack.Screen name="LinkOpenAlert" component={LinkOpenAlert} />
          <Stack.Screen name="HintView" component={HintView} />
        </Stack.Group>
      </Stack.Navigator>
    );
  }

  StartLoginScreen() {
    return (
      <Stack.Navigator
        initialRouteName="loginScreen"
        screenOptions={({ route, navigation }) => ({
          headerShadowVisible: false,
          headerBackTitleVisible: false,
          title: route.params?.title ? route.params.title : '',
          headerTitleStyle: {
            color: Colors.MainColor,
          },
          // eslint-disable-next-line react/no-unstable-nested-components
          headerLeft: () => <HeaderLeftBtn navigation={navigation} />,
        })}>
        <Stack.Screen
          name="loginScreen"
          component={LoginScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen name="signUpView" component={SignUpView} />
        <Stack.Screen name="SignUpSetting" component={SignUpSetting} />
        <Stack.Screen name="SelectCountry" component={SelectCountry} />
        <Stack.Screen name="timezoneList" component={TimezoneList} />
        <Stack.Screen name="langScreen" component={LangScreen} />
        <Stack.Screen name="LoginPrivacy" component={LoginPrivacy} />
        <Stack.Group
          screenOptions={() => ({
            presentation: 'transparentModal',
            animation: 'fade',
            headerShown: false,
          })}>
          <Stack.Screen name="HintView" component={HintView} />
        </Stack.Group>
      </Stack.Navigator>
    );
  }

  splash() {
    if (Platform.OS === 'ios') {
      return null;
    }
    return (
      <Stack.Navigator>
        <Stack.Screen
          name="Splash"
          options={{ animationEnabled: false, header: () => null }}
          component={SplashScreen}
        />
      </Stack.Navigator>
    );
  }

  render() {
    return (
      <SafeAreaProvider
        style={{
          backgroundColor: Tme('bgColor', this.state.scheme),
        }}>
        <NavigationContainer ref={this.containerRef}
          theme={IsDark() ? DarkTheme : DefaultTheme}>
          {Platform.OS === 'android' && (
            <>
              <StatusBar
                backgroundColor="transparent"
                translucent={true}
                barStyle={IsDark() ? 'light-content' : 'dark-content'}
              />
            </>
          )}
          <OverflowMenuProvider>
            <GestureHandlerRootView style={{ flex: 1 }}>
              <ScreenSizeProvider>
                <PaperProvider>
                  <ToastProvider>
                    <>
                      <LoadingView visible={this.state.loading} />
                    </>
                    {this.state.is_login === 'wait' && this.splash()}
                    {this.state.is_login === 'login' && this.StartLoggedinScreen()}
                    {this.state.is_login === 'not_login' && this.StartLoginScreen()}
                  </ToastProvider>
                </PaperProvider>
              </ScreenSizeProvider>
            </GestureHandlerRootView>
          </OverflowMenuProvider>
        </NavigationContainer>
      </SafeAreaProvider>
    );
  }
}

function SplashScreen() {
  return (
    <SafeAreaView style={{ backgroundColor: Tme('cardColor'), flex: 1 }}>
      <View style={{ flex: 1 }} />
      <View style={{ alignItems: 'center', marginBottom: 20 }}>
        <Image
          source={require('./img/App_512x512.png')}
          style={{ width: 100, height: 100, alignSelf: 'center' }}
        />
        <Text
          style={{ color: Tme('cardTextColor'), fontSize: 20, marginTop: 20 }}>
          MAKE LIVE SIMPLE
        </Text>
      </View>
    </SafeAreaView>
  );
}

function Tabs() {
  return (
    <Tab.Navigator
      initialRouteName="homeScreen"
      screenOptions={({ route }) => ({
        // eslint-disable-next-line react/no-unstable-nested-components
        tabBarIcon: ({ focused, color, size }) => {
          switch (route.name) {
            case 'homeScreen':
              return <MCIcons name="home" size={size} color={color} />;
            case 'deviceScreen':
              return <FontAwesome name="hdd-o" size={size} color={color} />;
            case 'SmartScreen':
              return <MCIcons name="clock-outline" size={size} color={color} />;
            case 'SettingScreen':
              return <MCIcons name="cog" size={size} color={color} />;
          }
        },
        tabBarActiveTintColor: Colors.MainColor,
        headerShown: false,
      })}>
      <Tab.Screen
        name="homeScreen"
        options={{
          title: Platform.isPad ? '' : I18n.t('home.home'),
        }}
        component={HomeScreen}
      // component={IpcCameraView}
      />
      <Tab.Screen
        options={{
          title: Platform.isPad ? '' : I18n.t('home.device'),
        }}
        name="deviceScreen"
        component={DeviceScreen}
      />
      <Tab.Screen
        options={{
          title: Platform.isPad ? '' : I18n.t('global.auto'),
        }}
        name="SmartScreen"
        component={SmartScreen}
      />
      <Tab.Screen
        options={{
          title: Platform.isPad ? '' : I18n.t('home.setting'),
        }}
        name="SettingScreen"
        component={SettingScreen}
      />
    </Tab.Navigator>
  );
}
export default App;
