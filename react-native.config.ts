interface PlatformConfig {
  platforms: {
    android?: null;
    ios?: null;
  };
}

interface DependenciesConfig {
  [key: string]: PlatformConfig;
}

interface ReactNativeConfig {
  assets: string[];
  dependencies: DependenciesConfig;
}

const config: ReactNativeConfig = {
  assets: [],
  dependencies: {
    'react-native-shared-group-preferences': {
      platforms: {
        android: null,
      },
    },
    'react-native-widget-center': {
      platforms: {
        android: null,
      },
    },
    'react-native-android-widget': {
      platforms: {
        ios: null,
      },
    },
    'react-native-vector-icons': {
      platforms: {
        ios: null,
      },
    },
    '@react-native-firebase/app': {
      platforms: {
        ios: null,
      },
    },
    '@react-native-firebase/messaging': {
      platforms: {
        ios: null,
      },
    },
    '@notifee/react-native': {
      platforms: {
        ios: null,
      },
    },
  },
};

export default config;
