diff --git a/node_modules/@alessiocancian/react-native-actionsheet/lib/ActionSheetCustom.js b/node_modules/@alessiocancian/react-native-actionsheet/lib/ActionSheetCustom.js
index 14b422d..7f46afb 100644
--- a/node_modules/@alessiocancian/react-native-actionsheet/lib/ActionSheetCustom.js
+++ b/node_modules/@alessiocancian/react-native-actionsheet/lib/ActionSheetCustom.js
@@ -184,11 +184,14 @@ class ActionSheet extends React.Component {
     const darkMode = this._isDarkMode()
     return (
       <Modal visible={visible}
-        animationType='none'
         transparent
         supportedOrientations={this.props.supportedOrientations || this.defaultOrientations}
         onRequestClose={this._cancel}
-        statusBarTranslucent={this.props.statusBarTranslucent}
+        // statusBarTranslucent={this.props.statusBarTranslucent}
+        animationType="fade"
+        presentationStyle="overFullScreen"
+        statusBarTranslucent
+        navigationBarTranslucent
       >
         <SafeAreaView style={[styles.wrapper]}>
           <Animated.Text
@@ -201,7 +204,7 @@ class ActionSheet extends React.Component {
             style={[
               iosStyle ? styles.bodyIos : (darkMode ? styles.bodyDark : styles.body),
               {
-                opacity: translateY ? 1 : 0,
+                opacity: sheetAnim.interpolate({ inputRange: [0, 1], outputRange: [1, 0] }),
                 maxHeight: getMaxHeight() + 1,
                 transform: [{
                   translateY: sheetAnim.interpolate({
