<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16097" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="480" height="480"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="welcome_icon" translatesAutoresizingMaskIntoConstraints="NO" id="v3X-VZ-hLB">
                    <rect key="frame" x="198" y="313" width="84" height="84"/>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="MAKE LIVE SIMPLE" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9pw-Py-DpJ">
                    <rect key="frame" x="162" y="409" width="156.5" height="21"/>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                    <color key="textColor" red="0.49803921568627452" green="0.5607843137254902" blue="0.64313725490196072" alpha="1" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
            <constraints>
                <constraint firstItem="9pw-Py-DpJ" firstAttribute="top" secondItem="v3X-VZ-hLB" secondAttribute="bottom" constant="12" id="22e-aI-KPA"/>
                <constraint firstItem="v3X-VZ-hLB" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="6Kg-AD-H7w"/>
                <constraint firstItem="9pw-Py-DpJ" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="M3R-hK-Dl2"/>
                <constraint firstAttribute="bottom" secondItem="9pw-Py-DpJ" secondAttribute="bottom" constant="50" id="sUL-7e-E4r"/>
            </constraints>
            <nil key="simulatedStatusBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="548" y="455"/>
        </view>
    </objects>
    <resources>
        <image name="welcome_icon" width="84" height="84"/>
    </resources>
</document>
