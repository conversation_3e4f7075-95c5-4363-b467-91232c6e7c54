//
//  TuyaSmartCameraService.h
//  presen
//
//  Created by bll on 2021/3/30.
//

#import <Foundation/Foundation.h>

@interface TuyaSmartCameraService : NSObject

+ (instancetype)sharedService;

- (NSInteger)definitionForCamera:(NSString *)devId;

- (void)setDefinition:(NSInteger)definition forCamera:(NSString *)devId;

- (NSInteger)audioModeForCamera:(NSString *)devId;

- (void)setAudioMode:(NSInteger)mode forCamera:(NSString *)devId;
    
- (BOOL)couldChangeAudioMode:(NSString *)devId;

- (void)setCouldChangedAudioMode:(BOOL)couldChange forCamera:(NSString *)devId;

- (void)observeDoorbellCall:(void(^)(NSString *devId, NSString *type))callback;

- (NSString *)thumbnailDirectoryForDevice:(NSString *)devId;

- (void)removeAllThumbnailsForDevice:(NSString *)devId;

@end

