//
//  TuyaModule.h
//  presen
//
//  Created by shitou on 2021/3/29.
//

#import <Foundation/Foundation.h>
#import <React/RCTBridgeModule.h>
#import <React/RCTEventEmitter.h>
#import <TuyaSmartHomeKit/TuyaSmartKit.h>
#import "TuyaSmartCamera.h"

NS_ASSUME_NONNULL_BEGIN

@interface TuyaModule : RCTEventEmitter <RCTBridgeModule, TuyaSmartDeviceDelegate,TuyaSmartActivatorDelegate> {
}

@property(nonatomic, readwrite, strong) TuyaSmartCamera *camera;
@property(nonatomic, assign) NSInteger startTime;
@property(nonatomic, assign) NSInteger stopTime;

@property (nonatomic, strong) TuyaSmartDeviceModel *modelDevice;

@end

NS_ASSUME_NONNULL_END
