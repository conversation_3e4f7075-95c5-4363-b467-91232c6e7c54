//
//  TuyaModule+Camera.m
//  presen
//
//  Created by shitou on 2021/3/31.
//

#import "TuyaModule+Camera.h"
#import "TYPermissionUtil.h"

static TuyaSmartCamera *cameraInstance;

@implementation TuyaModule (Camera)


+ (TuyaSmartCamera *)getCameraInstance {
  return cameraInstance;
}

RCT_EXPORT_METHOD(initPlayBack:(NSString *)startTime endTime:(NSString *)endTime callback:(RCTResponseSenderBlock)callback){
  [self.camera addObserver:self];
  [self connectCamera:^(BOOL success) {
      if (success) {
        self.startTime = [startTime intValue];
        self.stopTime = [endTime intValue];
        callback(@[[NSNull null], @{@"status": @"ok"}]);
      }else {
        callback(@[[NSNull null], @{@"status": @"error"}]);
      }
  }];
}

RCT_EXPORT_METHOD(playBackStart){
  [self.camera startPlaybackWithPlayTime:self.startTime startTime:self.startTime stopTime:self.stopTime success:^{
    NSLog(@"tuya start success");
  } failure:^(NSError *error) {
    NSLog(@"tuya start error %@", error);
  }];
}

RCT_EXPORT_METHOD(playBackStop){
  [self.camera removeObserver:self];
  [self.camera stopPlayback];
}

RCT_EXPORT_METHOD(stopClick:(RCTResponseSenderBlock)callback){
  [self.camera stopPlayback];
  callback(@[[NSNull null], @{@"status": @"ok"}]);
}

RCT_EXPORT_METHOD(pauseClick:(RCTResponseSenderBlock)callback){
  [self.camera pausePlayback:^{
    callback(@[[NSNull null], @{@"status": @"ok"}]);
  } failure:^(NSError *error) {
    callback(@[[NSNull null], @{@"status": @"error", @"data": error}]);
  }];
}
RCT_EXPORT_METHOD(resumeClick:(RCTResponseSenderBlock)callback){
  [self.camera resumePlayback:^{
    callback(@[[NSNull null], @{@"status": @"ok"}]);
  } failure:^(NSError *error) {
    callback(@[[NSNull null], @{@"status": @"error", @"data": error}]);
  }];
}

RCT_EXPORT_METHOD(initCameraView:(NSString *)devId homeId:(NSString *)homeId callback:(RCTResponseSenderBlock)callback) {
  NSLog(@"tuya devID: %@", devId);
  
  [[TuyaSmartHomeManager new] getHomeListWithSuccess:^(NSArray<TuyaSmartHomeModel *> *homes) {
      [homes enumerateObjectsUsingBlock:^(TuyaSmartHomeModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
          TuyaSmartHome *home = [TuyaSmartHome homeWithHomeId:obj.homeId];
          [home getHomeDetailWithSuccess:^(TuyaSmartHomeModel *homeModel) {
              [home.deviceList enumerateObjectsUsingBlock:^(TuyaSmartDeviceModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                  if ([obj.category isEqualToString:@"sp"] && [obj.devId isEqualToString:devId]) {
                      NSLog(@"%@ 是一个智能摄像机设备", obj.name);
                    self.modelDevice = obj;
                    self.camera = [[TuyaSmartCamera alloc] initWithDeviceId:devId];
                    cameraInstance = self.camera;
                    [self connectCamera:^(BOOL success) {
                        if (success) {
                          callback(@[[NSNull null], @{@"status": @"ok"}]);
                        }else {
                          callback(@[[NSNull null], @{@"status": @"error"}]);
                        }
                    }];
                    *stop = YES;
                  }
          }];
          } failure:^(NSError *error) {

          }];
      }];
  } failure:^(NSError *error) {

  }];
}

RCT_EXPORT_METHOD(tuyaCameraVideoStart){
  [self.camera.dpManager addObserver:self];
  [self.camera addObserver:self];
  [self startPreview];
}

RCT_EXPORT_METHOD(p2pDestroy){
  [self.camera disConnect];
}

RCT_EXPORT_METHOD(tuyaCameraVideoStop){
  [self.camera stopPreview];
  [self.camera removeObserver:self];
  [self.camera.dpManager removeObserver:self];
}

RCT_EXPORT_METHOD(muteClick:(RCTResponseSenderBlock)callback) {
  [self.camera enableMute:!self.camera.isMuted success:^{
    callback(@[[NSNull null], @{@"status": @"ok", @"data": @(!self.camera.isMuted)}]);
  } failure:^(NSError *error) {
    callback(@[[NSNull null], @{@"status": @"error", @"data": error}]);
  }];
}

RCT_EXPORT_METHOD(setVideoClarity:(RCTResponseSenderBlock)callback){
  [self.camera enableHD:!self.camera.isHD success:^{
    callback(@[[NSNull null], @{@"status": @"ok", @"data": @(!self.camera.isHD)}]);
  } failure:^(NSError *error) {
    callback(@[[NSNull null], @{@"status": @"error", @"data": error}]);
  }];
}

RCT_EXPORT_METHOD(speakClick:(RCTResponseSenderBlock)callback){
  if ([TYPermissionUtil microNotDetermined]) {
      [TYPermissionUtil requestAccessForMicro:^(BOOL result) {
          if (result) {
            if (self.camera.isTalking) {
                [self.camera stopTalk];
              callback(@[[NSNull null], @{@"status": @"ok", @"data": @(false)}]);
            }else {
                [self.camera startTalk:^{
                  callback(@[[NSNull null], @{@"status": @"ok", @"data": @(true)}]);
                } failure:^(NSError *error) {
                  callback(@[[NSNull null], @{@"status": @"error", @"data": error}]);
                }];
            }
          }
      }];
  }else if ([TYPermissionUtil microDenied]) {
    callback(@[[NSNull null], @{@"status": @"error"}]);
  }else {
    if (self.camera.isTalking) {
        [self.camera stopTalk];
      callback(@[[NSNull null], @{@"status": @"ok", @"data": @(false)}]);
    }else {
        [self.camera startTalk:^{
          callback(@[[NSNull null], @{@"status": @"ok", @"data": @(true)}]);
        } failure:^(NSError *error) {
          callback(@[[NSNull null], @{@"status": @"error", @"data": error}]);
        }];
    }
  }
}

RCT_EXPORT_METHOD(snapShotClick:(RCTResponseSenderBlock)callback){
  [self checkPhotoPermision:^(BOOL result) {
      if (result) {
          [self.camera snapShoot:^{
            callback(@[[NSNull null], @{@"status": @"ok"}]);
          } failure:^(NSError *error) {
            callback(@[[NSNull null], @{@"status": @"error", @"data": error}]);
          }];
      }
  }];
}

RCT_EXPORT_METHOD(recordClick:(RCTResponseSenderBlock)callback){
  [self checkPhotoPermision:^(BOOL result) {
      if (result) {
          if (self.camera.isRecording) {
              [self.camera stopRecord:^{
                callback(@[[NSNull null], @{@"status": @"ok", @"data": @(false)}]);
              } failure:^(NSError *error) {
                callback(@[[NSNull null], @{@"status": @"error", @"data": error}]);
              }];
          }else {
              [self.camera startRecord:^{
                callback(@[[NSNull null], @{@"status": @"ok", @"data": @(true)}]);
              } failure:^(NSError *error) {
                callback(@[[NSNull null], @{@"status": @"error", @"data": error}]);
              }];
          }
      }
  }];
}
RCT_EXPORT_METHOD(onLeft:(RCTResponseSenderBlock)callback){
  if ([self.camera.dpManager isSupportDP:TuyaSmartCameraPTZControlDPName]) {
          [self.camera.dpManager setValue:TuyaSmartCameraPTZDirectionLeft forDP:TuyaSmartCameraPTZControlDPName success:^(id result) {
            callback(@[[NSNull null], @{@"status": @"ok"}]);
          } failure:^(NSError *error) {
            callback(@[[NSNull null], @{@"status": @"error"}]);
          }];
      }
}
RCT_EXPORT_METHOD(onRight:(RCTResponseSenderBlock)callback){
  if ([self.camera.dpManager isSupportDP:TuyaSmartCameraPTZControlDPName]) {
          [self.camera.dpManager setValue:TuyaSmartCameraPTZDirectionRight forDP:TuyaSmartCameraPTZControlDPName success:^(id result) {
            callback(@[[NSNull null], @{@"status": @"ok"}]);
          } failure:^(NSError *error) {
            callback(@[[NSNull null], @{@"status": @"error"}]);
          }];
      }
}

RCT_EXPORT_METHOD(onUp:(RCTResponseSenderBlock)callback){
  if ([self.camera.dpManager isSupportDP:TuyaSmartCameraPTZControlDPName]) {
          [self.camera.dpManager setValue:TuyaSmartCameraPTZDirectionUp forDP:TuyaSmartCameraPTZControlDPName success:^(id result) {
            callback(@[[NSNull null], @{@"status": @"ok"}]);
          } failure:^(NSError *error) {
            callback(@[[NSNull null], @{@"status": @"error"}]);
          }];
      }
}

RCT_EXPORT_METHOD(onDown:(RCTResponseSenderBlock)callback){
  if ([self.camera.dpManager isSupportDP:TuyaSmartCameraPTZControlDPName]) {
          [self.camera.dpManager setValue:TuyaSmartCameraPTZDirectionDown forDP:TuyaSmartCameraPTZControlDPName success:^(id result) {
            callback(@[[NSNull null], @{@"status": @"ok"}]);
          } failure:^(NSError *error) {
            callback(@[[NSNull null], @{@"status": @"error"}]);
          }];
      }
}

RCT_EXPORT_METHOD(onStop:(RCTResponseSenderBlock)callback){
  if ([self.camera.dpManager isSupportDP:TuyaSmartCameraPTZControlDPName]) {
    [self.camera.dpManager setValue:@(YES) forDP:TuyaSmartCameraPTZStopDPName success:^(id result) {
            callback(@[[NSNull null], @{@"status": @"ok"}]);
          } failure:^(NSError *error) {
            callback(@[[NSNull null], @{@"status": @"error"}]);
          }];
      }
}

RCT_EXPORT_METHOD(getSdCardStatus:(RCTResponseSenderBlock)callback){
  if ([self.camera.dpManager isSupportDP:TuyaSmartCameraSDCardStatusDPName]) {
    callback(@[[NSNull null], @{@"status": @"ok",@"data": @(
                                  [[self.camera.dpManager valueForDP:TuyaSmartCameraSDCardStatusDPName] tysdk_toInt])}]);
  }else{
    callback(@[[NSNull null], @{@"status": @"error"}]);
  }
}

RCT_EXPORT_METHOD(formatSdCard:(RCTResponseSenderBlock)callback){
  if([self.camera.dpManager isSupportDP:TuyaSmartCameraSDCardFormatDPName]){
    callback(@[[NSNull null], @{@"status": @"ok",@"data": @(
                                  [[self.camera.dpManager valueForDP:TuyaSmartCameraSDCardFormatDPName] tysdk_toBool])}]);
  }else{
    callback(@[[NSNull null], @{@"status": @"error"}]);
  }
}

RCT_EXPORT_METHOD(formatSdCardStatus:(RCTResponseSenderBlock)callback){
  if([self.camera.dpManager isSupportDP:TuyaSmartCameraSDCardFormatStateDPName]){
    callback(@[[NSNull null], @{@"status": @"ok",@"data": @(
                                  [[self.camera.dpManager valueForDP:TuyaSmartCameraSDCardFormatStateDPName] tysdk_toInt])}]);
  }else{
    callback(@[[NSNull null], @{@"status": @"error"}]);
  }
}
RCT_EXPORT_METHOD(getSdStorage:(RCTResponseSenderBlock)callback){
  if([self.camera.dpManager isSupportDP:TuyaSmartCameraSDCardStorageDPName]){
    callback(@[[NSNull null], @{@"status": @"ok",@"data":[[self.camera.dpManager valueForDP:TuyaSmartCameraSDCardStorageDPName] tysdk_toString]}]);
  }else{
    callback(@[[NSNull null], @{@"status": @"error"}]);
  }
}

RCT_EXPORT_METHOD(setSdRecordSwitch:(BOOL)dps callback:(RCTResponseSenderBlock)callback){
  if([self.camera.dpManager isSupportDP:TuyaSmartCameraSDCardRecordDPName]){
    [self.camera.dpManager setValue:dps ? @(YES) : @(NO)  forDP:TuyaSmartCameraSDCardRecordDPName success:^(id result) {
      callback(@[[NSNull null], @{@"status": @"ok"}]);
    } failure:^(NSError *error) {
      callback(@[[NSNull null], @{@"status": @"error"}]);
    }];
  }else{
    callback(@[[NSNull null], @{@"status": @"error"}]);
  }
}

RCT_EXPORT_METHOD(getPlayBackData:(NSString *)y m:(NSString *)m d:(NSString *)d callback:(RCTResponseSenderBlock)callback){
  [self.camera requestTimeSliceWithPlaybackDate:[TuyaSmartPlaybackDate new] complete:^(TYDictArray *result) {
    callback(@[[NSNull null], @{@"status": @"ok", @"data": @{@"items": result}}]);
  }];
}

RCT_EXPORT_METHOD(getWifiSignal){
  cameraInstance.device.delegate = self;
  [self.camera.device getWifiSignalStrengthWithSuccess:^{
      NSLog(@"get wifi signal strength success");
    } failure:^(NSError *error) {
      NSLog(@"get wifi signal strength failure: %@", error);
    }];
}

RCT_EXPORT_METHOD(setPir:(NSString *)dps callback:(RCTResponseSenderBlock)callback){
  if ([self.camera.dpManager isSupportDP:TuyaSmartCameraBasicPIRDPName]) {
          [self.camera.dpManager setValue:dps forDP:TuyaSmartCameraBasicPIRDPName success:^(id result) {
            callback(@[[NSNull null], @{@"status": @"ok"}]);
          } failure:^(NSError *error) {
            callback(@[[NSNull null], @{@"status": @"error"}]);
          }];
      }
}

RCT_EXPORT_METHOD(setMotionSwitch:(BOOL)dps callback:(RCTResponseSenderBlock)callback){
  if ([self.camera.dpManager isSupportDP:TuyaSmartCameraMotionDetectDPName]) {
    [self.camera.dpManager setValue:dps ? @(YES) : @(NO) forDP:TuyaSmartCameraMotionDetectDPName success:^(id result) {
            callback(@[[NSNull null], @{@"status": @"ok"}]);
          } failure:^(NSError *error) {
            NSLog(@"setMotionSwitch failure: %@", error);
            callback(@[[NSNull null], @{@"status": @"error"}]);
          }];
      }
}

RCT_EXPORT_METHOD(setMotionSensitivity:(NSString *)dps callback:(RCTResponseSenderBlock)callback){
  if ([self.camera.dpManager isSupportDP:TuyaSmartCameraMotionSensitivityDPName]) {
    [self.camera.dpManager setValue:dps forDP:TuyaSmartCameraMotionSensitivityDPName success:^(id result) {
            callback(@[[NSNull null], @{@"status": @"ok"}]);
          } failure:^(NSError *error) {
            NSLog(@"setMotionSensitivity failure: %@", error);
            callback(@[[NSNull null], @{@"status": @"error"}]);
          }];
      }
}

RCT_EXPORT_METHOD(setDecibelSwitch:(BOOL)dps callback:(RCTResponseSenderBlock)callback){
  if ([self.camera.dpManager isSupportDP:TuyaSmartCameraDecibelDetectDPName]) {
    [self.camera.dpManager setValue:dps ? @(YES) : @(NO) forDP:TuyaSmartCameraDecibelDetectDPName success:^(id result) {
            callback(@[[NSNull null], @{@"status": @"ok"}]);
          } failure:^(NSError *error) {
            callback(@[[NSNull null], @{@"status": @"error"}]);
          }];
      }
}

RCT_EXPORT_METHOD(setDecibelSensitivity:(NSString *)dps callback:(RCTResponseSenderBlock)callback){
  if ([self.camera.dpManager isSupportDP:TuyaSmartCameraDecibelSensitivityDPName]) {
    [self.camera.dpManager setValue:dps forDP:TuyaSmartCameraDecibelSensitivityDPName success:^(id result) {
            callback(@[[NSNull null], @{@"status": @"ok"}]);
          } failure:^(NSError *error) {
            callback(@[[NSNull null], @{@"status": @"error"}]);
          }];
      }
}
#pragma mark - TuyaSmartDeviceDelegate
- (void)device:(TuyaSmartDevice *)device signal:(NSString *)signal {
  NSLog(@"tuya signal %@", signal);
  [self sendEventWithName:@"getWifiSignal" body:@{@"status": @"ok", @"data": signal}];
}

#pragma mark - camera methods
- (void)connectCamera:(void(^)(BOOL success))complete {
    NSLog(@"tuya connect");
  if (self.camera.isConnecting){
    NSLog(@"tuya connect isConnecting");
    return;
  }
    if (self.camera.isConnected) {
      NSLog(@"tuya device isconnected");
        complete(YES);
        return;
    }
    NSLog(@"tuya camera connect");
    [self.camera connect:^{
      NSLog(@"Tuya connect success");
      complete(YES);
    } failure:^(NSError *error) {
      NSLog(@"Tuya connect error %@", error);
      complete(NO);
    }];
}

- (void)startPreview {
    [self.camera startPreview:^{
      [self sendEventWithName:@"onChange" body:@{@"status": @"ok"}];
    } failure:^(NSError *error) {
      [self sendEventWithName:@"onChange" body:@{@"status": @"error", @"data": error}];
    }];
}

- (void)checkPhotoPermision:(void(^)(BOOL result))complete {
    if ([TYPermissionUtil isPhotoLibraryNotDetermined]) {
        [TYPermissionUtil requestPhotoPermission:complete];
    }else if ([TYPermissionUtil isPhotoLibraryDenied]) {
        !complete?:complete(NO);
    }else {
        !complete?:complete(YES);
    }
}

#pragma mark - TuyaSmartCameraObserver
- (void)cameraDidDisconnected:(TuyaSmartCamera *)camera {
    
}

- (void)camera:(TuyaSmartCamera *)camera didReceiveMuteState:(BOOL)isMuted {
    
}

- (void)camera:(TuyaSmartCamera *)camera didReceiveDefinitionState:(BOOL)isHd {
    
}

- (void)camera:(TuyaSmartCamera *)camera didReceiveVideoFrame:(CMSampleBufferRef)sampleBuffer frameInfo:(TuyaSmartVideoFrameInfo)frameInfo {
}

@end
