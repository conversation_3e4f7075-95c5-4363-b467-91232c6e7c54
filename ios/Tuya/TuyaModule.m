//
//  TuyaModule.m
//  presen
//
//  Created by shitou on 2021/3/29.
//

#import "TuyaModule.h"
#import <TuyaSmartDeviceKit/TuyaSmartHome.h>
#import <TuyaSmartDeviceKit/TuyaSmartHomeManager.h>
#import <TuyaSmartCameraKit/TuyaSmartCameraAbility.h>
#import <TuyaSmartActivatorKit/TuyaSmartActivatorKit.h>
#import "AppDelegate.h"

@implementation TuyaModule

static NSString * const kTuyaCameraEvent = @"TuyaCameraEvent";

RCT_EXPORT_MODULE(TuyaModule);

- (NSArray<NSString *> *)supportedEvents {
  return @[kTuyaCameraEvent, @"onChange", @"getWifiSignal"];
}

RCT_EXPORT_METHOD(loginTuya:(NSString *)userType countryCode:(NSString *)countryCode username:(NSString *)username password:(NSString *)password callback:(RCTResponseSenderBlock)callback){
  if ([userType isEqualToString:@"email"]){
    [[TuyaSmartUser sharedInstance] loginByEmail:countryCode email:username password:password success:^{
      callback(@[[NSNull null], @{@"status": @"ok"}]);
    } failure:^(NSError *error) {
      callback(@[[NSNull null], @{@"status": @"error"}]);
    }];
  }else if ([userType isEqualToString:@"cellphone"]){
    [[TuyaSmartUser sharedInstance] loginByPhone:countryCode phoneNumber:username password:password success:^{
      callback(@[[NSNull null], @{@"status": @"ok"}]);
    } failure:^(NSError *error) {
      callback(@[[NSNull null], @{@"status": @"error"}]);
    }];
  }else{
    NSLog(@"login uid: %@", username);
    NSLog(@"login password %@", password);
    [[TuyaSmartUser sharedInstance] loginOrRegisterWithCountryCode:countryCode uid:username password:password createHome:false success:^(id result) {
      callback(@[[NSNull null], @{@"status": @"ok"}]);
    } failure:^(NSError *error) {
      callback(@[[NSNull null], @{@"status": @"error"}]);
    }];
  }
}

RCT_EXPORT_METHOD(getToken:(NSString *)homeId callback:(RCTResponseSenderBlock)callback){
  [[TuyaSmartActivator sharedInstance] getTokenWithHomeId:[homeId longLongValue] success:^(NSString *token) {
    callback(@[[NSNull null], @{@"status": @"ok", @"data": token}]);
    } failure:^(NSError *error) {
      callback(@[[NSNull null], @{@"status": @"error", @"errors": error.localizedFailureReason}]);
    }];
}

RCT_EXPORT_METHOD(getCameraQR:(NSString *)ssid passwod:(NSString *)password token:(NSString *)token callback:(RCTResponseSenderBlock)callback){
  [TuyaSmartActivator sharedInstance].delegate = self;
  [[TuyaSmartActivator sharedInstance] startConfigWiFi:TYActivatorModeQRCode ssid:ssid password:password token:token timeout:60];
  NSDictionary *dictionary = @{
    @"s": ssid,
    @"p": password,
    @"t": token
  };
  NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dictionary options:0 error:nil];
  callback(@[[NSNull null], @{@"status": @"qr", @"data": [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding]}]);
}

RCT_EXPORT_METHOD(tuyaCameraAddSuccess){
  [TuyaSmartActivator sharedInstance].delegate = nil;
  [[TuyaSmartActivator sharedInstance] stopConfigWiFi];
}

RCT_EXPORT_METHOD(logoutTuya:(RCTResponseSenderBlock)callback){
  [[TuyaSmartUser sharedInstance] loginOut:^{
    callback(@[[NSNull null], @{@"status": @"ok"}]);
    } failure:^(NSError *error) {
      callback(@[[NSNull null], @{@"status": @"error"}]);
    }];
}

RCT_EXPORT_METHOD(tuyaAP:(NSString *)ssid password:(NSString *)password token:(NSString *)token ){
  // 设置 TuyaSmartActivator 的 delegate，并实现 delegate 方法
    [TuyaSmartActivator sharedInstance].delegate = self;

    // 开始配网，热点模式对应 mode 为 TYActivatorModeAP
    [[TuyaSmartActivator sharedInstance] startConfigWiFi:TYActivatorModeAP ssid:ssid password:password token:token timeout:60];
}

RCT_EXPORT_METHOD(tuyaEZ:(NSString *)ssid password:(NSString *)password token:(NSString *)token ){
  // 设置 TuyaSmartActivator 的 delegate，并实现 delegate 方法
    [TuyaSmartActivator sharedInstance].delegate = self;

    // 开始配网，热点模式对应 mode 为 TYActivatorModeAP
    [[TuyaSmartActivator sharedInstance] startConfigWiFi:TYActivatorModeEZ ssid:ssid password:password token:token timeout:60];
}

RCT_EXPORT_METHOD(getCameraInfo:(NSString *)devid callback:(RCTResponseSenderBlock)callback){
  TuyaSmartDevice *device = [TuyaSmartDevice deviceWithDeviceId:devid];
  TuyaSmartDeviceModel *deviceModel = device.deviceModel;
  TuyaSmartCameraAbility *cameraAbility =  [TuyaSmartCameraAbility cameraAbilityWithDeviceModel:deviceModel];
  
  NSDictionary *dictionary = @{
    @"videoNum": @(cameraAbility.videoNum),
    @"defaultNum": @(cameraAbility.defaultDefinition),
    @"isSupportSpeaker": @(cameraAbility.isSupportSpeaker),
    @"isSupportPickup": @(cameraAbility.isSupportPickup),
    @"defaultTalkMode": @(cameraAbility.defaultTalkbackMode),
    @"isChangeTalkMode": @(cameraAbility.couldChangeTalkbackMode),
  };
  NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dictionary options:0 error:nil];
  callback(@[[NSNull null], @{@"status": @"ok", @"data": [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding]}]);
}

RCT_EXPORT_METHOD(getPIRValue:(NSString *)devid callback:(RCTResponseSenderBlock)callback){
  TuyaSmartCameraDPManager *dpManager = [[TuyaSmartCameraDPManager alloc] initWithDeviceId:devid];
  callback(@[[NSNull null], @{@"status": @"ok", @"data": [dpManager valueForDP:TuyaSmartCameraBasicPIRDPName] }]);
}

RCT_EXPORT_METHOD(getMotionSwitch:(NSString *)devid callback:(RCTResponseSenderBlock)callback){
  TuyaSmartCameraDPManager *dpManager = [[TuyaSmartCameraDPManager alloc] initWithDeviceId:devid];
  callback(@[[NSNull null], @{@"status": @"ok", @"data": [dpManager valueForDP:TuyaSmartCameraMotionDetectDPName] }]);
}

RCT_EXPORT_METHOD(getMotionTracking:(NSString *)devid callback:(RCTResponseSenderBlock)callback){
  TuyaSmartPTZManager *ptzManager = [[TuyaSmartPTZManager alloc] initWithDeviceId:devid];
  if ([ptzManager isSupportMotionTracking]) {
    if ([ptzManager isOpenMotionTracking]) {
      callback(@[[NSNull null], @{@"status": @"ok", @"data": @(YES)}]);
    } else {
      callback(@[[NSNull null], @{@"status": @"ok", @"data": @(NO)}]);
    }
  } else {
    callback(@[[NSNull null], @{@"status": @"ok", @"data": @""}]);
  }
}

RCT_EXPORT_METHOD(setMotionTracking:(NSString *)devid dps:(BOOL)dps callback:(RCTResponseSenderBlock)callback){
  TuyaSmartPTZManager *ptzManager = [[TuyaSmartPTZManager alloc] initWithDeviceId:devid];
  [ptzManager setMotionTrackingState:dps ? YES : NO success:^{
    NSLog(@"setMotionTracking success");
  } failure:^(NSError *error) {
    NSLog(@"setMotionTracking failure: %@", error);
  }];
  callback(@[[NSNull null], @{@"status": @"ok"}]);
}

RCT_EXPORT_METHOD(getMotionSensitivity:(NSString *)devid callback:(RCTResponseSenderBlock)callback){
  TuyaSmartCameraDPManager *dpManager = [[TuyaSmartCameraDPManager alloc] initWithDeviceId:devid];
  callback(@[[NSNull null], @{@"status": @"ok", @"data": [dpManager valueForDP:TuyaSmartCameraMotionSensitivityDPName] }]);
}

RCT_EXPORT_METHOD(getDecibelSwitch:(NSString *)devid callback:(RCTResponseSenderBlock)callback){
  TuyaSmartCameraDPManager *dpManager = [[TuyaSmartCameraDPManager alloc] initWithDeviceId:devid];
  callback(@[[NSNull null], @{@"status": @"ok", @"data": [dpManager valueForDP:TuyaSmartCameraDecibelDetectDPName] }]);
}

RCT_EXPORT_METHOD(getDecibelSensitivity:(NSString *)devid callback:(RCTResponseSenderBlock)callback){
  TuyaSmartCameraDPManager *dpManager = [[TuyaSmartCameraDPManager alloc] initWithDeviceId:devid];
  callback(@[[NSNull null], @{@"status": @"ok", @"data": [dpManager valueForDP:TuyaSmartCameraDecibelSensitivityDPName] }]);
}

RCT_EXPORT_METHOD(getControl:(NSString *)devid callback:(RCTResponseSenderBlock)callback){
  TuyaSmartCameraDPManager *dpManager = [[TuyaSmartCameraDPManager alloc] initWithDeviceId:devid];
  callback(@[[NSNull null], @{@"status": @"ok", @"data": [dpManager valueForDP:TuyaSmartCameraPTZControlDPName] }]);
}

- (void)activator:(TuyaSmartActivator *)activator didReceiveDevice:(TuyaSmartDeviceModel *)deviceModel error:(NSError *)error {
  if (deviceModel && error == nil) {
    NSLog(@"success add: %@", deviceModel.devId);
    [self sendEventWithName:kTuyaCameraEvent body:@{@"status": @"ok", @"data": deviceModel.devId}];
  }
  
  [self sendEventWithName:kTuyaCameraEvent body:@{@"status": @"ok"}];
  if(error){
    [self sendEventWithName:kTuyaCameraEvent body:@{@"errors": error.localizedFailureReason, @"status": @"error"}];
  }
}

@end
