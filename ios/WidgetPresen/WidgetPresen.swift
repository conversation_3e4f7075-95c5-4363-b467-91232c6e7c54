//
//  WidgetPresen.swift
//  WidgetPresen
//
//  Created by bll on 2023/2/21.
//

import WidgetKit
import SwiftUI


struct ItemData:Codable {
  let name: String,
      uuid: String,
      icon: String,
      type: String
  let isEnabled: Bool
}

struct ShareData:Codable {
  let data: [ItemData]
}

var image: UIImage? = nil
var displayWidth: CGFloat = 0;
var displayHeight: CGFloat = 0;

//struct WidgetRquest {
//  static func request(sharedata: SharedData, completion: @escaping (Result<RequestData, Error>) -> Void) {
//    let url = sharedata.host + sharedata.api_prefix + sharedata.data_path
//
//    guard let host = URL(string: url) else {
//      return
//    }
//    var request = URLRequest(url: host)
//    request.setValue(sharedata.api_key, forHTTPHeaderField: "X_IOT_API_KEY")
//    request.setValue("presen", forHTTPHeaderField: "X_IOT_SCOPES")
//    request.setValue(sharedata.uuid, forHTTPHeaderField: "X_IOT_UUID")
//    request.setValue(sharedata.home_id, forHTTPHeaderField: "X_IOT_HOME")
//    let task = URLSession.shared.dataTask(with: request) { (data, response, error) in
//      if let data = data {
//        do {
//          let res = try JSONDecoder().decode(FetchData.self, from: data)
//          if (res.status == "ok"){
//            completion(.success(res.data))
//          }else {
//            return
//          }
//        } catch let error {
//          print(error)
//        }
//      }
//    }
//    task.resume()
//  }
//}
func linkUrl(item: ItemData) -> String {
  return "com.presen://" + item.uuid + "," + item.type
}

func getGroupsData() -> ShareData {
  let DATE_EVENTS_VIEW_GROUP_IDENTIFER = "group.com.presen.WidgetPresen"
  let sharedDefaults = UserDefaults.init(suiteName: DATE_EVENTS_VIEW_GROUP_IDENTIFER)
  if sharedDefaults != nil {
    do{
      let shared = sharedDefaults?.string(forKey: "SceneData")
      if(shared != nil){
        let jsonData = shared!.data(using: .utf8)!
        let decoder = JSONDecoder()
        let tableData = try decoder.decode(ShareData.self, from: jsonData)
        return tableData
      }
    }
    catch let jsonError {
      print(jsonError)
    }
  }
  
  return ShareData(data: [])
}


struct Provider: TimelineProvider {
  func placeholder(in context: Context) -> SimpleEntry {
    SimpleEntry(date: Date())
  }
  
  func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
    let entry = SimpleEntry(date: Date())
    completion(entry)
  }
  
  func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
    // Generate a timeline consisting of five entries an hour apart, starting from the current date.
    let currentDate = Date()
    let entryDate = Calendar.current.date(byAdding: .minute, value: 5, to: currentDate)!
    
    //    WidgetRquest.request(sharedata: t){ result in
    //      let requestData: RequestData
    //      if case .success(let data) = result {
    //        requestData = data
    //      }else {
    //        requestData = RequestData(scenes: [SceneData(name: "", id: "", uuid: "",is_enabled: false)], actions: [ActionData(name: "", id: "", uuid: "")])
    //      }
    
    let entry = SimpleEntry(date: currentDate)
    let timeline = Timeline(entries: [entry], policy: .after(entryDate))
    completion(timeline)
    //    }
  }
}

struct SimpleEntry: TimelineEntry {
  let date: Date
}

struct WidgetPresenEntryView : View {
  var entry: Provider.Entry
  
  let columnGrid = [
    GridItem(.flexible(), spacing: 10),
    GridItem(.flexible(), spacing: 10),
    GridItem(.flexible(), spacing: 10),
    GridItem(.flexible(), spacing: 10),
  ]
  
  var t = getGroupsData()
  
  let placeholderOne = UIImage(systemName: "square.and.arrow.up")
  var body: some View {
    GeometryReader(content: { geometry in
      VStack(alignment: .leading) {
        HStack(alignment: .top){
          Label {
            Text("Presen").font(.system(size: 16))
          } icon: {
            Image(uiImage: UIImage(named: "presen-logo")!).resizable().frame(width: 24,height: 24)
              .cornerRadius(6)
          }
        }
        .offset(y: -8)
        if(t.data.count == 0){
          HStack(alignment: .center){
            Text(LocalizedStringKey("empty_desp"))
          }.frame(width: geometry.size.width - 30, height: geometry.size.height/2-10)
        }else {
          LazyVGrid(columns: columnGrid, alignment: .leading, spacing: 1
          ){
            ForEach((t.data), id: \.uuid) {item in
              Link(destination: URL(string: linkUrl(item: item))!){
                
                VStack(alignment: .center) {
                  Image(uiImage: UIImage(named: item.icon)!)
                    .resizable()
                    .frame(width: 30,height: 30)
                  HStack(alignment: .center){
                    item.isEnabled ? Circle().fill(Color.red)
                      .frame(width: 4.0, height: 4.0)
                    : nil
                    Text(LocalizedStringKey(item.name)).font(.system(size: 12)).lineLimit(1)
                  }
                  .padding(.horizontal)
                }.frame(width: geometry.size.width/4 - 10, height: geometry.size.height/2 )
                  .foregroundColor(Color("TextColor"))
                  .background(Color("CardColor"))
                  .cornerRadius(10)
              }
            }
          }
//          .padding(.top, 8.0)
        }
      }
//      .padding(.all)
      .frame(maxWidth: .infinity, maxHeight: .infinity)
//      .background(
//        Image(uiImage: UIImage(named: "background")!)
//          .resizable()
//          .scaledToFill()
//          .blur(radius: 8)
//      )
    })
  }
}


struct WidgetPresen: Widget {
  let kind: String = "WidgetPresen"
  
  var body: some WidgetConfiguration {
    StaticConfiguration(kind: kind, provider: Provider()) { entry in
      WidgetPresenEntryView(entry: entry)
    }
    .configurationDisplayName("Presen Widget")
    .description("PresenSmartHome Widget.")
    .supportedFamilies([.systemMedium])
  }
}

struct WidgetPresen_Previews: PreviewProvider {
  static var previews: some View {
    WidgetPresenEntryView(entry: SimpleEntry(date: Date()))
      .previewContext(WidgetPreviewContext(family: .systemMedium))
  }
}


