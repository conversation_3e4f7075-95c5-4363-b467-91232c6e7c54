//
//  ScodeModule.m
//  presen
//
//  Created by bll on 2021/12/16.
//

#import "ScodeModule.h"
#import <CommonCrypto/CommonDigest.h>

@implementation ScodeModule

RCT_EXPORT_MODULE(ScodeModule);

RCT_EXPORT_METHOD(Scode:(NSString *)env data:(NSString*)data callback:(RCTResponseSenderBlock)callback){
  NSString *key= @"";
  if ([env isEqual:@"0"]){
    key = [NSString stringWithFormat:@"5565b79e2ef34b62b7216cee05ae8571"];
  } else {
    key = [NSString stringWithFormat:@"e1e1d4f9449c4e738047ad3e68352bb6"];
  }
  NSString *code = [key substringWithRange : NSMakeRange(2,6)];
  NSString *str=[ NSString stringWithFormat:@"%@%@%@",code,data,code];
  callback(@[[NSNull null], @{@"scode": [ScodeModule md5: [str uppercaseString]]}]);
}

+ (NSString *) md5:(NSString *)str{
  const char *cStr = [str UTF8String];
  unsigned char result[16];
  CC_MD5( cStr, strlen(cStr), result );
  return [NSString stringWithFormat:@"%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X",
    result[0], result[1], result[2], result[3],
    result[4], result[5], result[6], result[7],
    result[8], result[9], result[10], result[11],
    result[12], result[13], result[14], result[15]
  ];
}
@end
