import UIKit
import React
import React_RCTAppDelegate
import ReactAppDependencyProvider
import UserNotifications
import GoogleMaps
import AMapFoundationKit
import TSBackgroundFetch


@main
class AppDelegate: UIResponder,UIApplicationDelegate ,UNUserNotificationCenterDelegate {
  var isInBackgroundMode: Bool = false
      var allowRotation: Bool = false
  var window: UIWindow?

    var reactNativeDelegate: ReactNativeDelegate?
    var reactNativeFactory: RCTReactNativeFactory?

  func application(
      _ application: UIApplication,
      didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil
    ) -> Bool {
      let delegate = ReactNativeDelegate()
      let factory = RCTReactNativeFactory(delegate: delegate)
      delegate.dependencyProvider = RCTAppDependencyProvider()

      reactNativeDelegate = delegate
      reactNativeFactory = factory
      
      GMSServices.provideAPIKey("AIzaSyDgdTKqxuhwI5ntYvufVkyI_YuKvnChTQY")
      
      // 高德地图配置
      AMapServices.shared().apiKey = "0ddf54287a39e8e14b2f127ba63f7678"
      
      // 推送通知配置
      let center = UNUserNotificationCenter.current()
      center.delegate = self
      
      // // 注册远程通知 - 明确请求推送权限
      // center.requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
      //   print("推送权限请求结果: \(granted), 错误: \(String(describing: error))")
        
      //   DispatchQueue.main.async {
      //     application.registerForRemoteNotifications()
      //   }
      // }
      
      // 后台任务配置
      TSBackgroundFetch.sharedInstance().didFinishLaunching()
      
      window = UIWindow(frame: UIScreen.main.bounds)

      factory.startReactNative(
        withModuleName: "presen",
        in: window,
        launchOptions: launchOptions
      )

      return true
    }
  
  // 添加必要的通知处理方法
  func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
    print("成功获取设备token")
    RNCPushNotificationIOS.didRegisterForRemoteNotifications(withDeviceToken: deviceToken)
  }
  
  func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
    print("远程通知注册失败: \(error.localizedDescription)")
    RNCPushNotificationIOS.didFailToRegisterForRemoteNotificationsWithError(error)
  }
  
  func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
    RNCPushNotificationIOS.didReceiveRemoteNotification(userInfo, fetchCompletionHandler: completionHandler)
  }
  
  func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
    let userInfo = notification.request.content.userInfo
    print("APP_PUSH from foreground", userInfo)
    
    RNCPushNotificationIOS.didReceiveRemoteNotification(userInfo) { _ in }
    completionHandler([.alert, .sound, .badge])
  }

  func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
    RNCPushNotificationIOS.didReceive(response)
    completionHandler()
  }
}

class ReactNativeDelegate: RCTDefaultReactNativeFactoryDelegate {
  override func sourceURL(for bridge: RCTBridge) -> URL? {
    self.bundleURL()
  }

  override func bundleURL() -> URL? {
#if DEBUG
    RCTBundleURLProvider.sharedSettings().jsBundleURL(forBundleRoot: "index")
#else
    Bundle.main.url(forResource: "main", withExtension: "jsbundle")
#endif
  }
  
  
  // MARK: - Deep Linking
  func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
    return RCTLinkingManager.application(app, open: url, options: options)
  }
  
  func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
      return RCTLinkingManager.application(application, continue: userActivity, restorationHandler: restorationHandler)
  }
  
  // MARK: - 屏幕旋转
  func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
      return Orientation.getOrientation()
  }
}
