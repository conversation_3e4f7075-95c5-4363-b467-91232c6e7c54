<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>com.transistorsoft.fetch</string>
		<string>com.transistorsoft.auto</string>
	</array>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Presen</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.presen</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.presen.widget</string>
				<string>presen</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>CodePushDeploymentKey</key>
	<string>$(CODEPUSH_KEY)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>We need to use your Bluetooth to make the bluetooth device work.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>Need to use your bluetooth to make bluetooth devices work, such as bluetooth switches or bluetooth sensors, this feature will be available in the near future.</string>
	<key>NSBonjourServices</key>
	<array>
		<string>_presenctl._tcp.</string>
	</array>
	<key>NSMicrophoneUsageDescription</key>
	<string>We need to use microphone access for notifications.</string>
	<key>NSCameraUsageDescription</key>
	<string>We need to use your camera to scan the device code.</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>We need to use your local network to get the controller(s) in your home.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We need to track your location to make automation work.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>We need to track your location to make automation work.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We need to track your location to make automation work.</string>
	<key>NSMainNibFile</key>
	<string>LaunchScreen</string>
	<key>NSMotionUsageDescription</key>
	<string>We need to use motion activity to make automation work.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need to use your photos to let you customize your app background.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>We need to use your photos to let you customize your app background.</string>
	<key>UIAppFonts</key>
	<array>
		<string>anticon.ttf</string>
		<string>antfill.ttf</string>
		<string>antoutline.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>location</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
</dict>
</plist>
