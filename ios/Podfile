
def node_require(script)
  # Resolve script with node to allow for hoisting
  require Pod::Executable.execute_command('node', ['-p',
    "require.resolve(
      '#{script}',
      {paths: [process.argv[1]]},
    )", __dir__]).strip
end

node_require('react-native/scripts/react_native_pods.rb')
node_require('react-native-permissions/scripts/setup.rb')

platform :ios, min_ios_version_supported
prepare_react_native_project!

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end


target 'presen' do

  config = use_native_modules!
  # permissions_path = '../node_modules/react-native-permissions/ios'
  rn_maps_path = '../node_modules/react-native-maps'

  $RNVideoUseVideoCaching=true
  # Flags change depending on the env values.
  # flags = get_default_flags()
  setup_permissions([
    'Camera',
    'Bluetooth',
    'LocationAccuracy',
    'LocationAlways',
    'LocationWhenInUse',
    'Microphone',
    'PhotoLibrary',
    'PhotoLibraryAddOnly',
  ])
  # pod 'Permission-Camera', :path => "#{permissions_path}/Camera"
  # pod 'Permission-BluetoothPeripheral', :path => "#{permissions_path}/BluetoothPeripheral"
  # pod 'Permission-LocationAccuracy', :path => "#{permissions_path}/LocationAccuracy"
  # pod 'Permission-LocationAlways', :path => "#{permissions_path}/LocationAlways"
  # pod 'Permission-LocationWhenInUse', :path => "#{permissions_path}/LocationWhenInUse"
  # pod 'Permission-Microphone', :path => "#{permissions_path}/Microphone"
  # pod 'Permission-PhotoLibrary', :path => "#{permissions_path}/PhotoLibrary"
  # pod 'Permission-PhotoLibraryAddOnly', :path => "#{permissions_path}/PhotoLibraryAddOnly"

  pod 'react-native-google-maps', :path => rn_maps_path
  # pod 'TuyaSmartHomeKit','~> 3.24.0'
  # pod 'TuyaSmartCameraKit','~> 4.31.5'
  # pod 'TuyaSmartSweeperKit','~> 2.0.0'
  # pod 'TuyaSmartLockKit','~> 1.0.2'
  #
  pod 'SPTPersistentCache',:modular_headers => true;
  pod 'DVAssetLoaderDelegate',:modular_headers => true;

#   use_react_native!(
#     :path => config[:reactNativePath],
#     # Hermes is now enabled by default. Disable by setting this flag to false.
#     # Upcoming versions of React Native may rely on get_default_flags(), but
#     # we make it explicit here to aid in the React Native upgrade process.
#     :hermes_enabled => true,
#     :fabric_enabled => flags[:fabric_enabled],
#     # Enables Flipper.
#     #
#     # Note that if you have use_frameworks! enabled, Flipper will not work and
#     # you should disable the next line.
# #    :flipper_configuration => FlipperConfiguration.enabled,
#     # An absolute path to your application root.
#     :app_path => "#{Pod::Config.instance.installation_root}/.."
#   )
#
  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  post_install do |installer|
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
        # config.build_settings["ONLY_ACTIVE_ARCH"] = "NO"
        # config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.4'
        # config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)', '_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION']
      end
    end

    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )

    # react_native_post_install(
    #   installer,
    #   # Set `mac_catalyst_enabled` to `true` in order to apply patches
    #   # necessary for Mac Catalyst builds
    #   :mac_catalyst_enabled => false
    # )
    # __apply_Xcode_12_5_M1_post_install_workaround(installer)
  end
end
