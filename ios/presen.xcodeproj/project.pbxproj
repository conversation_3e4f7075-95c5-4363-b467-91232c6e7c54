// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		761780ED2CA45674006654EE /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 761780EC2CA45674006654EE /* AppDelegate.swift */; };
		8C8720EAB4207C7E2C997FB2 /* libPods-presen.a in Frameworks */ = {isa = PBXBuildFile; fileRef = EAB4D7234DE480ACA2B86B65 /* libPods-presen.a */; };
		A54BDAD06D9C47DF87F00186 /* BuildFile in Resources */ = {isa = PBXBuildFile; };
		C799C0445D9644B2868AAEEB /* BuildFile in Resources */ = {isa = PBXBuildFile; };
		E98EB7CC0A59A9F1D6B1FBA8 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 415380139190363E2C2A876E /* PrivacyInfo.xcprivacy */; };
		EB14793F29B02A7000ACB387 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = EB14794129B02A7000ACB387 /* Localizable.strings */; };
		EB15326A29A45B0B0087DF2D /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EB15326929A45B0B0087DF2D /* WidgetKit.framework */; };
		EB15326C29A45B0B0087DF2D /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EB15326B29A45B0B0087DF2D /* SwiftUI.framework */; };
		EB15326F29A45B0B0087DF2D /* WidgetPresenBundle.swift in Sources */ = {isa = PBXBuildFile; fileRef = EB15326E29A45B0B0087DF2D /* WidgetPresenBundle.swift */; };
		EB15327329A45B0B0087DF2D /* WidgetPresen.swift in Sources */ = {isa = PBXBuildFile; fileRef = EB15327229A45B0B0087DF2D /* WidgetPresen.swift */; };
		EB15327529A45B0C0087DF2D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = EB15327429A45B0C0087DF2D /* Assets.xcassets */; };
		EB15327929A45B0C0087DF2D /* WidgetPresenExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = EB15326829A45B0B0087DF2D /* WidgetPresenExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		EB44274C2C0991130067BF2A /* AVFAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EB44274B2C0991120067BF2A /* AVFAudio.framework */; };
		EB85870929AC90890035390F /* icons8-birthday-cake-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85870829AC90890035390F /* icons8-birthday-cake-96.png */; };
		EB85870C29AC97330035390F /* background.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85870B29AC97330035390F /* background.png */; };
		EB85870E29AC9D750035390F /* presen-logo.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85870D29AC9D750035390F /* presen-logo.png */; };
		EB85871029AD8CAB0035390F /* icons8-confetti-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85870F29AD8CAB0035390F /* icons8-confetti-96.png */; };
		EB85871229AD8CBB0035390F /* icons8-food-and-wine-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85871129AD8CBA0035390F /* icons8-food-and-wine-96.png */; };
		EB85871729AD8CCE0035390F /* icons8-home-page-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85871329AD8CCE0035390F /* icons8-home-page-96.png */; };
		EB85871829AD8CCE0035390F /* icons8-holiday-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85871429AD8CCE0035390F /* icons8-holiday-96.png */; };
		EB85871929AD8CCE0035390F /* icons8-game-controller-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85871529AD8CCE0035390F /* icons8-game-controller-96.png */; };
		EB85871A29AD8CCE0035390F /* icons8-keyboard-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85871629AD8CCE0035390F /* icons8-keyboard-96.png */; };
		EB85872429AD8CF60035390F /* icons8-sunny-side-up-eggs-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85871B29AD8CF50035390F /* icons8-sunny-side-up-eggs-96.png */; };
		EB85872529AD8CF60035390F /* icons8-party-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85871C29AD8CF50035390F /* icons8-party-96.png */; };
		EB85872629AD8CF60035390F /* icons8-traveler-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85871D29AD8CF50035390F /* icons8-traveler-96.png */; };
		EB85872729AD8CF60035390F /* icons8-violin-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85871E29AD8CF50035390F /* icons8-violin-96.png */; };
		EB85872829AD8CF60035390F /* icons8-sofa-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85871F29AD8CF50035390F /* icons8-sofa-96.png */; };
		EB85872929AD8CF60035390F /* icons8-ladybird-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85872029AD8CF50035390F /* icons8-ladybird-96.png */; };
		EB85872A29AD8CF60035390F /* icons8-restaurant-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85872129AD8CF50035390F /* icons8-restaurant-96.png */; };
		EB85872B29AD8CF60035390F /* icons8-moon-and-stars-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85872229AD8CF50035390F /* icons8-moon-and-stars-96.png */; };
		EB85872C29AD8CF60035390F /* icons8-romance-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85872329AD8CF50035390F /* icons8-romance-96.png */; };
		EB85872F29AD8D130035390F /* icons8-clock-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85872D29AD8D130035390F /* icons8-clock-96.png */; };
		EB85873029AD8D130035390F /* icons8-guard-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85872E29AD8D130035390F /* icons8-guard-96.png */; };
		EB85873329AD8D280035390F /* icons8-airplane-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85873129AD8D270035390F /* icons8-airplane-96.png */; };
		EB85873429AD8D280035390F /* icons8-sedan-96.png in Resources */ = {isa = PBXBuildFile; fileRef = EB85873229AD8D270035390F /* icons8-sedan-96.png */; };
		EB98DA3C2C250B5C00D72340 /* AntDesign.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA282C250B5C00D72340 /* AntDesign.ttf */; };
		EB98DA3D2C250B5C00D72340 /* Entypo.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA292C250B5C00D72340 /* Entypo.ttf */; };
		EB98DA3E2C250B5C00D72340 /* EvilIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA2A2C250B5C00D72340 /* EvilIcons.ttf */; };
		EB98DA3F2C250B5C00D72340 /* Feather.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA2B2C250B5C00D72340 /* Feather.ttf */; };
		EB98DA402C250B5C00D72340 /* FontAwesome.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA2C2C250B5C00D72340 /* FontAwesome.ttf */; };
		EB98DA412C250B5C00D72340 /* FontAwesome5_Brands.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA2D2C250B5C00D72340 /* FontAwesome5_Brands.ttf */; };
		EB98DA422C250B5C00D72340 /* FontAwesome5_Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA2E2C250B5C00D72340 /* FontAwesome5_Regular.ttf */; };
		EB98DA432C250B5C00D72340 /* FontAwesome5_Solid.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA2F2C250B5C00D72340 /* FontAwesome5_Solid.ttf */; };
		EB98DA442C250B5C00D72340 /* FontAwesome6_Brands.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA302C250B5C00D72340 /* FontAwesome6_Brands.ttf */; };
		EB98DA452C250B5C00D72340 /* FontAwesome6_Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA312C250B5C00D72340 /* FontAwesome6_Regular.ttf */; };
		EB98DA462C250B5C00D72340 /* FontAwesome6_Solid.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA322C250B5C00D72340 /* FontAwesome6_Solid.ttf */; };
		EB98DA472C250B5C00D72340 /* Fontisto.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA332C250B5C00D72340 /* Fontisto.ttf */; };
		EB98DA482C250B5C00D72340 /* Foundation.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA342C250B5C00D72340 /* Foundation.ttf */; };
		EB98DA492C250B5C00D72340 /* Ionicons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA352C250B5C00D72340 /* Ionicons.ttf */; };
		EB98DA4A2C250B5C00D72340 /* MaterialCommunityIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA362C250B5C00D72340 /* MaterialCommunityIcons.ttf */; };
		EB98DA4B2C250B5C00D72340 /* MaterialIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA372C250B5C00D72340 /* MaterialIcons.ttf */; };
		EB98DA4C2C250B5C00D72340 /* Octicons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA382C250B5C00D72340 /* Octicons.ttf */; };
		EB98DA4D2C250B5C00D72340 /* SimpleLineIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA392C250B5C00D72340 /* SimpleLineIcons.ttf */; };
		EB98DA4E2C250B5C00D72340 /* Zocial.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EB98DA3A2C250B5C00D72340 /* Zocial.ttf */; };
		EBB02FC629650B2D00CF1C4E /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = EBB02FC129650B2B00CF1C4E /* LaunchScreen.xib */; };
		EBB02FC729650B2D00CF1C4E /* HelpersModule.m in Sources */ = {isa = PBXBuildFile; fileRef = EBB02FC229650B2B00CF1C4E /* HelpersModule.m */; };
		EBB02FCB29650B4100CF1C4E /* ScodeModule.m in Sources */ = {isa = PBXBuildFile; fileRef = EBB02FCA29650B4000CF1C4E /* ScodeModule.m */; };
		EBB02FD129650CBB00CF1C4E /* urgent_alarm.aif in Resources */ = {isa = PBXBuildFile; fileRef = EBB02FCD29650CBB00CF1C4E /* urgent_alarm.aif */; };
		EBB02FD229650CBB00CF1C4E /* anticon.ttf in Resources */ = {isa = PBXBuildFile; fileRef = EBB02FCE29650CBB00CF1C4E /* anticon.ttf */; };
		EBB02FD329650CBB00CF1C4E /* t_s.bmp in Resources */ = {isa = PBXBuildFile; fileRef = EBB02FCF29650CBB00CF1C4E /* t_s.bmp */; };
		EBB02FD429650CBB00CF1C4E /* server.pem in Resources */ = {isa = PBXBuildFile; fileRef = EBB02FD029650CBB00CF1C4E /* server.pem */; };
		EBD96A532BC8BFB5005D46D5 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EBD96A522BC8BFB5005D46D5 /* AudioToolbox.framework */; };
		EBD96A552BC8BFBF005D46D5 /* GLKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EBD96A542BC8BFBF005D46D5 /* GLKit.framework */; };
		EBD96A5B2BC8BFF8005D46D5 /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EBD96A5A2BC8BFF8005D46D5 /* CFNetwork.framework */; };
		EBD96A5D2BC8C000005D46D5 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EBD96A5C2BC8C000005D46D5 /* CoreGraphics.framework */; };
		EBD96A5F2BC8C00A005D46D5 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EBD96A5E2BC8C00A005D46D5 /* CoreMedia.framework */; };
		EBD96A612BC8C018005D46D5 /* Metal.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EBD96A602BC8C018005D46D5 /* Metal.framework */; };
		EBD96A632BC8C01F005D46D5 /* MetalKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EBD96A622BC8C01F005D46D5 /* MetalKit.framework */; };
		EBD96A652BC8C027005D46D5 /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EBD96A642BC8C027005D46D5 /* OpenGLES.framework */; };
		EBD96A672BC8C02E005D46D5 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EBD96A662BC8C02E005D46D5 /* Security.framework */; };
		EBD96A692BC8C037005D46D5 /* VideoToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EBD96A682BC8C037005D46D5 /* VideoToolbox.framework */; };
		EBD96A702BC8F66E005D46D5 /* libbz2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = EBD96A562BC8BFC9005D46D5 /* libbz2.tbd */; };
		EBD96A712BC8F67D005D46D5 /* libicucore.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = EBD96A582BC8BFE1005D46D5 /* libicucore.tbd */; };
		EBD96A722BC8F687005D46D5 /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = EBD96A592BC8BFEE005D46D5 /* libz.tbd */; };
		EBD96A732BC8F690005D46D5 /* libiconv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = EBD96A572BC8BFD2005D46D5 /* libiconv.tbd */; };
		EBD96A772BCE1476005D46D5 /* sha256.m in Sources */ = {isa = PBXBuildFile; fileRef = EBD96A762BCE1476005D46D5 /* sha256.m */; };
		EBF31B8229BED6A2008F3332 /* bg_0.png in Resources */ = {isa = PBXBuildFile; fileRef = EBF31B8129BED6A2008F3332 /* bg_0.png */; };
		EBF31B8429BED6EF008F3332 /* bg_2.png in Resources */ = {isa = PBXBuildFile; fileRef = EBF31B8329BED6EF008F3332 /* bg_2.png */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		EB15327729A45B0C0087DF2D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EB15326729A45B0B0087DF2D;
			remoteInfo = WidgetPresenExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		EB15327A29A45B0C0087DF2D /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				EB15327929A45B0C0087DF2D /* WidgetPresenExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
		EB44275A2C0EEB4D0067BF2A /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		13B07F961A680F5B00A75B9A /* presen.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = presen.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = presen/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = presen/Info.plist; sourceTree = "<group>"; };
		415380139190363E2C2A876E /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = presen/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		761780EC2CA45674006654EE /* AppDelegate.swift */ = {isa = PBXFileReference; explicitFileType = sourcecode.swift; name = AppDelegate.swift; path = presen/AppDelegate.swift; sourceTree = "<group>"; };
		A81D772DDD7B0E1A9CA6D321 /* Pods-presen.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-presen.release.xcconfig"; path = "Target Support Files/Pods-presen/Pods-presen.release.xcconfig"; sourceTree = "<group>"; };
		ACF8BB858C4E56BB21B9937D /* Pods-presen.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-presen.debug.xcconfig"; path = "Target Support Files/Pods-presen/Pods-presen.debug.xcconfig"; sourceTree = "<group>"; };
		EAB4D7234DE480ACA2B86B65 /* libPods-presen.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-presen.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		EB14794029B02A7000ACB387 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		EB14794229B02A7600ACB387 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		EB15326829A45B0B0087DF2D /* WidgetPresenExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = WidgetPresenExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		EB15326929A45B0B0087DF2D /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		EB15326B29A45B0B0087DF2D /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		EB15326E29A45B0B0087DF2D /* WidgetPresenBundle.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WidgetPresenBundle.swift; sourceTree = "<group>"; };
		EB15327229A45B0B0087DF2D /* WidgetPresen.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WidgetPresen.swift; sourceTree = "<group>"; };
		EB15327429A45B0C0087DF2D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		EB15327629A45B0C0087DF2D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		EB15327E29A45B440087DF2D /* WidgetPresenExtension.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = WidgetPresenExtension.entitlements; sourceTree = "<group>"; };
		EB44274B2C0991120067BF2A /* AVFAudio.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFAudio.framework; path = System/Library/Frameworks/AVFAudio.framework; sourceTree = SDKROOT; };
		EB85870829AC90890035390F /* icons8-birthday-cake-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "icons8-birthday-cake-96.png"; sourceTree = "<group>"; };
		EB85870B29AC97330035390F /* background.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = background.png; path = ../../../img/background.png; sourceTree = "<group>"; };
		EB85870D29AC9D750035390F /* presen-logo.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "presen-logo.png"; path = "../../../img/presen-logo.png"; sourceTree = "<group>"; };
		EB85870F29AD8CAB0035390F /* icons8-confetti-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-confetti-96.png"; path = "../../../img/icons/scene/icons8-confetti-96.png"; sourceTree = "<group>"; };
		EB85871129AD8CBA0035390F /* icons8-food-and-wine-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-food-and-wine-96.png"; path = "../../../img/icons/scene/icons8-food-and-wine-96.png"; sourceTree = "<group>"; };
		EB85871329AD8CCE0035390F /* icons8-home-page-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-home-page-96.png"; path = "../../../img/icons/scene/icons8-home-page-96.png"; sourceTree = "<group>"; };
		EB85871429AD8CCE0035390F /* icons8-holiday-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-holiday-96.png"; path = "../../../img/icons/scene/icons8-holiday-96.png"; sourceTree = "<group>"; };
		EB85871529AD8CCE0035390F /* icons8-game-controller-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-game-controller-96.png"; path = "../../../img/icons/scene/icons8-game-controller-96.png"; sourceTree = "<group>"; };
		EB85871629AD8CCE0035390F /* icons8-keyboard-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-keyboard-96.png"; path = "../../../img/icons/scene/icons8-keyboard-96.png"; sourceTree = "<group>"; };
		EB85871B29AD8CF50035390F /* icons8-sunny-side-up-eggs-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-sunny-side-up-eggs-96.png"; path = "../../../img/icons/scene/icons8-sunny-side-up-eggs-96.png"; sourceTree = "<group>"; };
		EB85871C29AD8CF50035390F /* icons8-party-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-party-96.png"; path = "../../../img/icons/scene/icons8-party-96.png"; sourceTree = "<group>"; };
		EB85871D29AD8CF50035390F /* icons8-traveler-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-traveler-96.png"; path = "../../../img/icons/scene/icons8-traveler-96.png"; sourceTree = "<group>"; };
		EB85871E29AD8CF50035390F /* icons8-violin-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-violin-96.png"; path = "../../../img/icons/scene/icons8-violin-96.png"; sourceTree = "<group>"; };
		EB85871F29AD8CF50035390F /* icons8-sofa-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-sofa-96.png"; path = "../../../img/icons/scene/icons8-sofa-96.png"; sourceTree = "<group>"; };
		EB85872029AD8CF50035390F /* icons8-ladybird-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-ladybird-96.png"; path = "../../../img/icons/scene/icons8-ladybird-96.png"; sourceTree = "<group>"; };
		EB85872129AD8CF50035390F /* icons8-restaurant-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-restaurant-96.png"; path = "../../../img/icons/scene/icons8-restaurant-96.png"; sourceTree = "<group>"; };
		EB85872229AD8CF50035390F /* icons8-moon-and-stars-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-moon-and-stars-96.png"; path = "../../../img/icons/scene/icons8-moon-and-stars-96.png"; sourceTree = "<group>"; };
		EB85872329AD8CF50035390F /* icons8-romance-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-romance-96.png"; path = "../../../img/icons/scene/icons8-romance-96.png"; sourceTree = "<group>"; };
		EB85872D29AD8D130035390F /* icons8-clock-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-clock-96.png"; path = "../../../img/icons/auto/icons8-clock-96.png"; sourceTree = "<group>"; };
		EB85872E29AD8D130035390F /* icons8-guard-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-guard-96.png"; path = "../../../img/icons/auto/icons8-guard-96.png"; sourceTree = "<group>"; };
		EB85873129AD8D270035390F /* icons8-airplane-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-airplane-96.png"; path = "../../../img/icons/scene/icons8-airplane-96.png"; sourceTree = "<group>"; };
		EB85873229AD8D270035390F /* icons8-sedan-96.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "icons8-sedan-96.png"; path = "../../../img/icons/scene/icons8-sedan-96.png"; sourceTree = "<group>"; };
		EB98DA282C250B5C00D72340 /* AntDesign.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = AntDesign.ttf; sourceTree = "<group>"; };
		EB98DA292C250B5C00D72340 /* Entypo.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Entypo.ttf; sourceTree = "<group>"; };
		EB98DA2A2C250B5C00D72340 /* EvilIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = EvilIcons.ttf; sourceTree = "<group>"; };
		EB98DA2B2C250B5C00D72340 /* Feather.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Feather.ttf; sourceTree = "<group>"; };
		EB98DA2C2C250B5C00D72340 /* FontAwesome.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome.ttf; sourceTree = "<group>"; };
		EB98DA2D2C250B5C00D72340 /* FontAwesome5_Brands.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome5_Brands.ttf; sourceTree = "<group>"; };
		EB98DA2E2C250B5C00D72340 /* FontAwesome5_Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome5_Regular.ttf; sourceTree = "<group>"; };
		EB98DA2F2C250B5C00D72340 /* FontAwesome5_Solid.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome5_Solid.ttf; sourceTree = "<group>"; };
		EB98DA302C250B5C00D72340 /* FontAwesome6_Brands.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome6_Brands.ttf; sourceTree = "<group>"; };
		EB98DA312C250B5C00D72340 /* FontAwesome6_Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome6_Regular.ttf; sourceTree = "<group>"; };
		EB98DA322C250B5C00D72340 /* FontAwesome6_Solid.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome6_Solid.ttf; sourceTree = "<group>"; };
		EB98DA332C250B5C00D72340 /* Fontisto.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Fontisto.ttf; sourceTree = "<group>"; };
		EB98DA342C250B5C00D72340 /* Foundation.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Foundation.ttf; sourceTree = "<group>"; };
		EB98DA352C250B5C00D72340 /* Ionicons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Ionicons.ttf; sourceTree = "<group>"; };
		EB98DA362C250B5C00D72340 /* MaterialCommunityIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = MaterialCommunityIcons.ttf; sourceTree = "<group>"; };
		EB98DA372C250B5C00D72340 /* MaterialIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = MaterialIcons.ttf; sourceTree = "<group>"; };
		EB98DA382C250B5C00D72340 /* Octicons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Octicons.ttf; sourceTree = "<group>"; };
		EB98DA392C250B5C00D72340 /* SimpleLineIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = SimpleLineIcons.ttf; sourceTree = "<group>"; };
		EB98DA3A2C250B5C00D72340 /* Zocial.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Zocial.ttf; sourceTree = "<group>"; };
		EBB02FA42965081200CF1C4E /* presen.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = presen.entitlements; path = presen/presen.entitlements; sourceTree = "<group>"; };
		EBB02FBD29650B2800CF1C4E /* presen-Bridging-Header.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "presen-Bridging-Header.h"; sourceTree = "<group>"; };
		EBB02FBE29650B2900CF1C4E /* HelpersModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HelpersModule.h; sourceTree = "<group>"; };
		EBB02FC129650B2B00CF1C4E /* LaunchScreen.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = LaunchScreen.xib; sourceTree = "<group>"; };
		EBB02FC229650B2B00CF1C4E /* HelpersModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HelpersModule.m; sourceTree = "<group>"; };
		EBB02FC929650B4000CF1C4E /* ScodeModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = ScodeModule.h; path = presen/ScodeModule.h; sourceTree = "<group>"; };
		EBB02FCA29650B4000CF1C4E /* ScodeModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = ScodeModule.m; path = presen/ScodeModule.m; sourceTree = "<group>"; };
		EBB02FCD29650CBB00CF1C4E /* urgent_alarm.aif */ = {isa = PBXFileReference; lastKnownFileType = file; path = urgent_alarm.aif; sourceTree = "<group>"; };
		EBB02FCE29650CBB00CF1C4E /* anticon.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = anticon.ttf; sourceTree = "<group>"; };
		EBB02FCF29650CBB00CF1C4E /* t_s.bmp */ = {isa = PBXFileReference; lastKnownFileType = image.bmp; path = t_s.bmp; sourceTree = "<group>"; };
		EBB02FD029650CBB00CF1C4E /* server.pem */ = {isa = PBXFileReference; lastKnownFileType = text; path = server.pem; sourceTree = "<group>"; };
		EBD96A522BC8BFB5005D46D5 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		EBD96A542BC8BFBF005D46D5 /* GLKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GLKit.framework; path = System/Library/Frameworks/GLKit.framework; sourceTree = SDKROOT; };
		EBD96A562BC8BFC9005D46D5 /* libbz2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libbz2.tbd; path = usr/lib/libbz2.tbd; sourceTree = SDKROOT; };
		EBD96A572BC8BFD2005D46D5 /* libiconv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libiconv.tbd; path = usr/lib/libiconv.tbd; sourceTree = SDKROOT; };
		EBD96A582BC8BFE1005D46D5 /* libicucore.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libicucore.tbd; path = usr/lib/libicucore.tbd; sourceTree = SDKROOT; };
		EBD96A592BC8BFEE005D46D5 /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		EBD96A5A2BC8BFF8005D46D5 /* CFNetwork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CFNetwork.framework; path = System/Library/Frameworks/CFNetwork.framework; sourceTree = SDKROOT; };
		EBD96A5C2BC8C000005D46D5 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		EBD96A5E2BC8C00A005D46D5 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		EBD96A602BC8C018005D46D5 /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		EBD96A622BC8C01F005D46D5 /* MetalKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalKit.framework; path = System/Library/Frameworks/MetalKit.framework; sourceTree = SDKROOT; };
		EBD96A642BC8C027005D46D5 /* OpenGLES.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGLES.framework; path = System/Library/Frameworks/OpenGLES.framework; sourceTree = SDKROOT; };
		EBD96A662BC8C02E005D46D5 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		EBD96A682BC8C037005D46D5 /* VideoToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = VideoToolbox.framework; path = System/Library/Frameworks/VideoToolbox.framework; sourceTree = SDKROOT; };
		EBD96A752BCE1476005D46D5 /* sha256.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = sha256.h; sourceTree = "<group>"; };
		EBD96A762BCE1476005D46D5 /* sha256.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = sha256.m; sourceTree = "<group>"; };
		EBF31B8129BED6A2008F3332 /* bg_0.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = bg_0.png; path = ../../../img/bg_0.png; sourceTree = "<group>"; };
		EBF31B8329BED6EF008F3332 /* bg_2.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = bg_2.png; path = ../../../img/bg_2.png; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EBD96A732BC8F690005D46D5 /* libiconv.tbd in Frameworks */,
				EBD96A722BC8F687005D46D5 /* libz.tbd in Frameworks */,
				EB44274C2C0991130067BF2A /* AVFAudio.framework in Frameworks */,
				EBD96A712BC8F67D005D46D5 /* libicucore.tbd in Frameworks */,
				EBD96A702BC8F66E005D46D5 /* libbz2.tbd in Frameworks */,
				EBD96A632BC8C01F005D46D5 /* MetalKit.framework in Frameworks */,
				EBD96A5D2BC8C000005D46D5 /* CoreGraphics.framework in Frameworks */,
				EBD96A672BC8C02E005D46D5 /* Security.framework in Frameworks */,
				EBD96A552BC8BFBF005D46D5 /* GLKit.framework in Frameworks */,
				EBD96A652BC8C027005D46D5 /* OpenGLES.framework in Frameworks */,
				EBD96A532BC8BFB5005D46D5 /* AudioToolbox.framework in Frameworks */,
				EBD96A692BC8C037005D46D5 /* VideoToolbox.framework in Frameworks */,
				EBD96A5B2BC8BFF8005D46D5 /* CFNetwork.framework in Frameworks */,
				EBD96A612BC8C018005D46D5 /* Metal.framework in Frameworks */,
				EBD96A5F2BC8C00A005D46D5 /* CoreMedia.framework in Frameworks */,
				8C8720EAB4207C7E2C997FB2 /* libPods-presen.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EB15326529A45B0B0087DF2D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EB15326C29A45B0B0087DF2D /* SwiftUI.framework in Frameworks */,
				EB15326A29A45B0B0087DF2D /* WidgetKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		13B07FAE1A68108700A75B9A /* presen */ = {
			isa = PBXGroup;
			children = (
				EBB02FC929650B4000CF1C4E /* ScodeModule.h */,
				EBB02FCA29650B4000CF1C4E /* ScodeModule.m */,
				EBB02FBE29650B2900CF1C4E /* HelpersModule.h */,
				EBB02FC229650B2B00CF1C4E /* HelpersModule.m */,
				EBB02FC129650B2B00CF1C4E /* LaunchScreen.xib */,
				EBB02FA42965081200CF1C4E /* presen.entitlements */,
				761780EC2CA45674006654EE /* AppDelegate.swift */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				415380139190363E2C2A876E /* PrivacyInfo.xcprivacy */,
			);
			name = presen;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				EB44274B2C0991120067BF2A /* AVFAudio.framework */,
				EBD96A682BC8C037005D46D5 /* VideoToolbox.framework */,
				EBD96A662BC8C02E005D46D5 /* Security.framework */,
				EBD96A642BC8C027005D46D5 /* OpenGLES.framework */,
				EBD96A622BC8C01F005D46D5 /* MetalKit.framework */,
				EBD96A602BC8C018005D46D5 /* Metal.framework */,
				EBD96A5E2BC8C00A005D46D5 /* CoreMedia.framework */,
				EBD96A5C2BC8C000005D46D5 /* CoreGraphics.framework */,
				EBD96A5A2BC8BFF8005D46D5 /* CFNetwork.framework */,
				EBD96A592BC8BFEE005D46D5 /* libz.tbd */,
				EBD96A582BC8BFE1005D46D5 /* libicucore.tbd */,
				EBD96A572BC8BFD2005D46D5 /* libiconv.tbd */,
				EBD96A562BC8BFC9005D46D5 /* libbz2.tbd */,
				EBD96A542BC8BFBF005D46D5 /* GLKit.framework */,
				EBD96A522BC8BFB5005D46D5 /* AudioToolbox.framework */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				EB15326929A45B0B0087DF2D /* WidgetKit.framework */,
				EB15326B29A45B0B0087DF2D /* SwiftUI.framework */,
				EAB4D7234DE480ACA2B86B65 /* libPods-presen.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		534CAA68E8434AB6B66F0904 /* Resources */ = {
			isa = PBXGroup;
			children = (
			);
			name = Resources;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				EB98DA3B2C250B5C00D72340 /* Fonts */,
				EBD96A742BCE1462005D46D5 /* sha256 */,
				EB15327E29A45B440087DF2D /* WidgetPresenExtension.entitlements */,
				EBB02FCE29650CBB00CF1C4E /* anticon.ttf */,
				EBB02FD029650CBB00CF1C4E /* server.pem */,
				EBB02FCF29650CBB00CF1C4E /* t_s.bmp */,
				EBB02FCD29650CBB00CF1C4E /* urgent_alarm.aif */,
				13B07FAE1A68108700A75B9A /* presen */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				EB15326D29A45B0B0087DF2D /* WidgetPresen */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				534CAA68E8434AB6B66F0904 /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* presen.app */,
				EB15326829A45B0B0087DF2D /* WidgetPresenExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				ACF8BB858C4E56BB21B9937D /* Pods-presen.debug.xcconfig */,
				A81D772DDD7B0E1A9CA6D321 /* Pods-presen.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		EB15326D29A45B0B0087DF2D /* WidgetPresen */ = {
			isa = PBXGroup;
			children = (
				EB14794129B02A7000ACB387 /* Localizable.strings */,
				EB85870A29AC91280035390F /* image */,
				EB15326E29A45B0B0087DF2D /* WidgetPresenBundle.swift */,
				EBB02FBD29650B2800CF1C4E /* presen-Bridging-Header.h */,
				EB15327229A45B0B0087DF2D /* WidgetPresen.swift */,
				EB15327429A45B0C0087DF2D /* Assets.xcassets */,
				EB15327629A45B0C0087DF2D /* Info.plist */,
			);
			path = WidgetPresen;
			sourceTree = "<group>";
		};
		EB85870A29AC91280035390F /* image */ = {
			isa = PBXGroup;
			children = (
				EB85870829AC90890035390F /* icons8-birthday-cake-96.png */,
				EB85870F29AD8CAB0035390F /* icons8-confetti-96.png */,
				EB85871529AD8CCE0035390F /* icons8-game-controller-96.png */,
				EB85871429AD8CCE0035390F /* icons8-holiday-96.png */,
				EB85871329AD8CCE0035390F /* icons8-home-page-96.png */,
				EB85872029AD8CF50035390F /* icons8-ladybird-96.png */,
				EB85872229AD8CF50035390F /* icons8-moon-and-stars-96.png */,
				EB85871C29AD8CF50035390F /* icons8-party-96.png */,
				EBF31B8129BED6A2008F3332 /* bg_0.png */,
				EB85872129AD8CF50035390F /* icons8-restaurant-96.png */,
				EB85872329AD8CF50035390F /* icons8-romance-96.png */,
				EBF31B8329BED6EF008F3332 /* bg_2.png */,
				EB85872D29AD8D130035390F /* icons8-clock-96.png */,
				EB85872E29AD8D130035390F /* icons8-guard-96.png */,
				EB85871F29AD8CF50035390F /* icons8-sofa-96.png */,
				EB85873129AD8D270035390F /* icons8-airplane-96.png */,
				EB85873229AD8D270035390F /* icons8-sedan-96.png */,
				EB85871B29AD8CF50035390F /* icons8-sunny-side-up-eggs-96.png */,
				EB85871D29AD8CF50035390F /* icons8-traveler-96.png */,
				EB85871E29AD8CF50035390F /* icons8-violin-96.png */,
				EB85871629AD8CCE0035390F /* icons8-keyboard-96.png */,
				EB85871129AD8CBA0035390F /* icons8-food-and-wine-96.png */,
				EB85870D29AC9D750035390F /* presen-logo.png */,
				EB85870B29AC97330035390F /* background.png */,
			);
			path = image;
			sourceTree = "<group>";
		};
		EB98DA3B2C250B5C00D72340 /* Fonts */ = {
			isa = PBXGroup;
			children = (
				EB98DA282C250B5C00D72340 /* AntDesign.ttf */,
				EB98DA292C250B5C00D72340 /* Entypo.ttf */,
				EB98DA2A2C250B5C00D72340 /* EvilIcons.ttf */,
				EB98DA2B2C250B5C00D72340 /* Feather.ttf */,
				EB98DA2C2C250B5C00D72340 /* FontAwesome.ttf */,
				EB98DA2D2C250B5C00D72340 /* FontAwesome5_Brands.ttf */,
				EB98DA2E2C250B5C00D72340 /* FontAwesome5_Regular.ttf */,
				EB98DA2F2C250B5C00D72340 /* FontAwesome5_Solid.ttf */,
				EB98DA302C250B5C00D72340 /* FontAwesome6_Brands.ttf */,
				EB98DA312C250B5C00D72340 /* FontAwesome6_Regular.ttf */,
				EB98DA322C250B5C00D72340 /* FontAwesome6_Solid.ttf */,
				EB98DA332C250B5C00D72340 /* Fontisto.ttf */,
				EB98DA342C250B5C00D72340 /* Foundation.ttf */,
				EB98DA352C250B5C00D72340 /* Ionicons.ttf */,
				EB98DA362C250B5C00D72340 /* MaterialCommunityIcons.ttf */,
				EB98DA372C250B5C00D72340 /* MaterialIcons.ttf */,
				EB98DA382C250B5C00D72340 /* Octicons.ttf */,
				EB98DA392C250B5C00D72340 /* SimpleLineIcons.ttf */,
				EB98DA3A2C250B5C00D72340 /* Zocial.ttf */,
			);
			name = Fonts;
			path = "../node_modules/react-native-vector-icons/Fonts";
			sourceTree = "<group>";
		};
		EBD96A742BCE1462005D46D5 /* sha256 */ = {
			isa = PBXGroup;
			children = (
				EBD96A752BCE1476005D46D5 /* sha256.h */,
				EBD96A762BCE1476005D46D5 /* sha256.m */,
			);
			path = sha256;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* presen */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "presen" */;
			buildPhases = (
				AE4AB81AB017686AA187A3A1 /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				EB15327A29A45B0C0087DF2D /* Embed Foundation Extensions */,
				EB44275A2C0EEB4D0067BF2A /* Embed Frameworks */,
				5E67D7F4F4554D0592F58658 /* [CP] Embed Pods Frameworks */,
				7406DC4C920189EDF24E574E /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				EB15327829A45B0C0087DF2D /* PBXTargetDependency */,
			);
			name = presen;
			productName = presen;
			productReference = 13B07F961A680F5B00A75B9A /* presen.app */;
			productType = "com.apple.product-type.application";
		};
		EB15326729A45B0B0087DF2D /* WidgetPresenExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EB15327D29A45B0C0087DF2D /* Build configuration list for PBXNativeTarget "WidgetPresenExtension" */;
			buildPhases = (
				EB15326429A45B0B0087DF2D /* Sources */,
				EB15326529A45B0B0087DF2D /* Frameworks */,
				EB15326629A45B0B0087DF2D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = WidgetPresenExtension;
			productName = WidgetPresenExtension;
			productReference = EB15326829A45B0B0087DF2D /* WidgetPresenExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1420;
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1420;
					};
					EB15326729A45B0B0087DF2D = {
						CreatedOnToolsVersion = 14.2;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "presen" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* presen */,
				EB15326729A45B0B0087DF2D /* WidgetPresenExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EB98DA402C250B5C00D72340 /* FontAwesome.ttf in Resources */,
				EB98DA4B2C250B5C00D72340 /* MaterialIcons.ttf in Resources */,
				EB98DA422C250B5C00D72340 /* FontAwesome5_Regular.ttf in Resources */,
				EB98DA3E2C250B5C00D72340 /* EvilIcons.ttf in Resources */,
				EB98DA4D2C250B5C00D72340 /* SimpleLineIcons.ttf in Resources */,
				EB98DA3D2C250B5C00D72340 /* Entypo.ttf in Resources */,
				EB98DA482C250B5C00D72340 /* Foundation.ttf in Resources */,
				EB98DA472C250B5C00D72340 /* Fontisto.ttf in Resources */,
				EBB02FD129650CBB00CF1C4E /* urgent_alarm.aif in Resources */,
				EB98DA4C2C250B5C00D72340 /* Octicons.ttf in Resources */,
				EBB02FD229650CBB00CF1C4E /* anticon.ttf in Resources */,
				EB98DA3C2C250B5C00D72340 /* AntDesign.ttf in Resources */,
				EBB02FD329650CBB00CF1C4E /* t_s.bmp in Resources */,
				EB98DA412C250B5C00D72340 /* FontAwesome5_Brands.ttf in Resources */,
				EBB02FD429650CBB00CF1C4E /* server.pem in Resources */,
				EB98DA3F2C250B5C00D72340 /* Feather.ttf in Resources */,
				EB98DA452C250B5C00D72340 /* FontAwesome6_Regular.ttf in Resources */,
				EB98DA462C250B5C00D72340 /* FontAwesome6_Solid.ttf in Resources */,
				EB98DA4E2C250B5C00D72340 /* Zocial.ttf in Resources */,
				EBB02FC629650B2D00CF1C4E /* LaunchScreen.xib in Resources */,
				EB98DA492C250B5C00D72340 /* Ionicons.ttf in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				E98EB7CC0A59A9F1D6B1FBA8 /* PrivacyInfo.xcprivacy in Resources */,
				EB98DA442C250B5C00D72340 /* FontAwesome6_Brands.ttf in Resources */,
				EB98DA4A2C250B5C00D72340 /* MaterialCommunityIcons.ttf in Resources */,
				EB98DA432C250B5C00D72340 /* FontAwesome5_Solid.ttf in Resources */,
				C799C0445D9644B2868AAEEB /* BuildFile in Resources */,
				A54BDAD06D9C47DF87F00186 /* BuildFile in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EB15326629A45B0B0087DF2D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EB85871029AD8CAB0035390F /* icons8-confetti-96.png in Resources */,
				EB85872929AD8CF60035390F /* icons8-ladybird-96.png in Resources */,
				EB85873029AD8D130035390F /* icons8-guard-96.png in Resources */,
				EB15327529A45B0C0087DF2D /* Assets.xcassets in Resources */,
				EB85872429AD8CF60035390F /* icons8-sunny-side-up-eggs-96.png in Resources */,
				EB85872A29AD8CF60035390F /* icons8-restaurant-96.png in Resources */,
				EBF31B8229BED6A2008F3332 /* bg_0.png in Resources */,
				EB85870E29AC9D750035390F /* presen-logo.png in Resources */,
				EB85871729AD8CCE0035390F /* icons8-home-page-96.png in Resources */,
				EB85870C29AC97330035390F /* background.png in Resources */,
				EB85871A29AD8CCE0035390F /* icons8-keyboard-96.png in Resources */,
				EB85872829AD8CF60035390F /* icons8-sofa-96.png in Resources */,
				EB85872729AD8CF60035390F /* icons8-violin-96.png in Resources */,
				EB85870929AC90890035390F /* icons8-birthday-cake-96.png in Resources */,
				EB14793F29B02A7000ACB387 /* Localizable.strings in Resources */,
				EB85873429AD8D280035390F /* icons8-sedan-96.png in Resources */,
				EB85871229AD8CBB0035390F /* icons8-food-and-wine-96.png in Resources */,
				EB85872C29AD8CF60035390F /* icons8-romance-96.png in Resources */,
				EB85872B29AD8CF60035390F /* icons8-moon-and-stars-96.png in Resources */,
				EB85872F29AD8D130035390F /* icons8-clock-96.png in Resources */,
				EB85871929AD8CCE0035390F /* icons8-game-controller-96.png in Resources */,
				EB85873329AD8D280035390F /* icons8-airplane-96.png in Resources */,
				EB85872629AD8CF60035390F /* icons8-traveler-96.png in Resources */,
				EB85871829AD8CCE0035390F /* icons8-holiday-96.png in Resources */,
				EB85872529AD8CF60035390F /* icons8-party-96.png in Resources */,
				EBF31B8429BED6EF008F3332 /* bg_2.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		5E67D7F4F4554D0592F58658 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-presen/Pods-presen-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-presen/Pods-presen-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-presen/Pods-presen-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		7406DC4C920189EDF24E574E /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-presen/Pods-presen-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-presen/Pods-presen-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-presen/Pods-presen-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		AE4AB81AB017686AA187A3A1 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-presen-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EBD96A772BCE1476005D46D5 /* sha256.m in Sources */,
				EBB02FCB29650B4100CF1C4E /* ScodeModule.m in Sources */,
				EBB02FC729650B2D00CF1C4E /* HelpersModule.m in Sources */,
				761780ED2CA45674006654EE /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EB15326429A45B0B0087DF2D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EB15326F29A45B0B0087DF2D /* WidgetPresenBundle.swift in Sources */,
				EB15327329A45B0B0087DF2D /* WidgetPresen.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		EB15327829A45B0C0087DF2D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EB15326729A45B0B0087DF2D /* WidgetPresenExtension */;
			targetProxy = EB15327729A45B0C0087DF2D /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		EB14794129B02A7000ACB387 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				EB14794029B02A7000ACB387 /* en */,
				EB14794229B02A7600ACB387 /* zh-Hans */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ACF8BB858C4E56BB21B9937D /* Pods-presen.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODEPUSH_KEY = "";
				CODE_SIGN_ENTITLEMENTS = presen/presen.entitlements;
				CURRENT_PROJECT_VERSION = 370;
				DEVELOPMENT_TEAM = L6XXXXBNNN;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = presen/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Presen;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.9.1;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = com.presensmarthome.iosapp;
				PRODUCT_NAME = presen;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "WidgetPresen/presen-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A81D772DDD7B0E1A9CA6D321 /* Pods-presen.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODEPUSH_KEY = FVZuIt2Pzeu3VIMgIclxPrrbpGpAx2we3IOMT;
				CODE_SIGN_ENTITLEMENTS = presen/presen.entitlements;
				CURRENT_PROJECT_VERSION = 370;
				DEVELOPMENT_TEAM = L6XXXXBNNN;
				INFOPLIST_FILE = presen/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Presen;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.9.1;
				OTHER_LDFLAGS = "$(inherited)";
				PRODUCT_BUNDLE_IDENTIFIER = com.presensmarthome.iosapp;
				PRODUCT_NAME = presen;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "WidgetPresen/presen-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		EB15327B29A45B0C0087DF2D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = WidgetPresenExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 370;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = L6XXXXBNNN;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = WidgetPresen/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Presen;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 3.9.1;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.presensmarthome.iosapp.WidgetPresen;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		EB15327C29A45B0C0087DF2D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = WidgetPresenExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 370;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = L6XXXXBNNN;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = WidgetPresen/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Presen;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 3.9.1;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.presensmarthome.iosapp.WidgetPresen;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "presen" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "presen" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EB15327D29A45B0C0087DF2D /* Build configuration list for PBXNativeTarget "WidgetPresenExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EB15327B29A45B0C0087DF2D /* Debug */,
				EB15327C29A45B0C0087DF2D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
