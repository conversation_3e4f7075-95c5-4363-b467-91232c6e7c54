//
//  HelpersModule.m
//  presen
//
//  Created by shitou on 2020/2/3.
//  Copyright © 2020 Facebook. All rights reserved.
//

#import "HelpersModule.h"

@implementation HelpersModule

- (dispatch_queue_t)methodQueue {
  return dispatch_get_main_queue();
}

RCT_EXPORT_MODULE();

RCT_EXPORT_METHOD(openUniversalLink:(NSString *)url callback:(RCTResponseSenderBlock)callback) {
  NSURL *u = [NSURL URLWithString:url];
  [[UIApplication sharedApplication] openURL:u options:@{UIApplicationOpenURLOptionUniversalLinksOnly: @YES} completionHandler:^(BOOL success) {
    callback(@[[NSNull null], @{@"status": (success ? @"yes" : @"no")}]);
  }];
}

@end
