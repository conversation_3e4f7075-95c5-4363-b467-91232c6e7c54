declare module 'react-native-udp' {
  interface SocketOptions {
    type: 'udp4' | 'udp6';
    debug?: boolean;
  }

  interface RemoteInfo {
    address: string;
    family: string;
    port: number;
    size: number;
  }

  interface Socket {
    bind(port: number, address?: string, callback?: () => void): void;
    close(callback?: () => void): void;
    setBroadcast(flag: boolean): void;
    addMembership(multicastAddress: string, multicastInterface?: string): void;
    on(event: 'error', listener: (err: Error) => void): this;
    on(event: 'listening', listener: () => void): this;
    on(event: 'message', listener: (msg: Buffer, rinfo: RemoteInfo) => void): this;
  }

  export function createSocket(options: SocketOptions): Socket;
}
