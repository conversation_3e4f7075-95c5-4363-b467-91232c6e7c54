let plugins = [
  '@babel/plugin-transform-flow-strip-types',
  [
    '@babel/plugin-proposal-decorators',
    {
      legacy: true,
    },
  ],
  [
    '@babel/plugin-proposal-class-properties',
    {
      loose: true,
    },
  ],
];

if (
  process.env.NODE_ENV === 'production' ||
  process.env.BABEL_ENV === 'production'
) {
  plugins.push(['transform-remove-console']);
}

plugins.push('react-native-reanimated/plugin');

const config = {
  presets: ['module:@react-native/babel-preset'],
  env: {
    production: {
      plugins: ['react-native-paper/babel'],
    },
  },
  plugins: plugins,
  sourceMap: true,
};

module.exports = config;
